/**
 * 统一请求管理器
 * 支持H5和小程序，统一调用jeecg-boot后台API
 */

import { getApiBaseUrl, getRequestHeaders } from '@/config/api.js'
import { getToken, removeToken, removeUserInfo } from '@/utils/auth.js'

// 获取全局应用实例
function getGlobalApp() {
  try {
    return getApp()
  } catch (e) {
    console.warn('获取全局应用实例失败:', e)
    return null
  }
}

// 获取基础URL - 统一使用配置
function getBaseUrl() {
  return getApiBaseUrl()
}

// 获取请求头 - 使用统一配置
function getHeaders(customHeaders = {}) {
  return getRequestHeaders(customHeaders)
}

// 处理响应数据 - jeecg-boot标准格式
function handleResponse(res, resolve, reject) {
  console.log('📥 响应数据:', res)

  if (res.statusCode === 200) {
    // jeecg-boot标准响应格式: { success: true/false, result: {}, message: '', code: 200 }
    if (res.data) {
      if (res.data.success === true || res.data.code === 200) {
        // 成功响应
        resolve({
          success: true,
          data: res.data.result || res.data.data || res.data,
          message: res.data.message || 'success',
          code: res.data.code || 200
        })
      } else {
        // 业务逻辑错误
        const errorMsg = res.data.message || '请求失败'
        console.error('❌ 业务错误:', errorMsg)

        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        })

        reject(new Error(errorMsg))
      }
    } else {
      // 无数据响应
      resolve({
        success: true,
        data: null,
        message: 'success',
        code: 200
      })
    }
  } else if (res.statusCode === 401) {
    // 认证失败 - 自动跳转到登录页面
    console.error('🔐 认证失败，清除登录状态')

    // 使用认证管理器清除登录状态
    removeToken()
    removeUserInfo()

    // 清除全局状态
    const app = getGlobalApp()
    if (app && app.clearLoginInfo) {
      app.clearLoginInfo()
    }

    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none',
      duration: 2000
    })

    // 安全获取当前页面路径，用于登录后跳转回来
    let currentRoute = ''
    try {
      const pages = getCurrentPages()
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        currentRoute = currentPage && currentPage.route ? currentPage.route : ''
      }
    } catch (error) {
      console.warn('获取当前页面路径失败:', error)
    }

    // 延迟跳转到登录页面
    setTimeout(() => {
      const redirectUrl = currentRoute && currentRoute !== 'pages/login/login'
        ? encodeURIComponent(`/${currentRoute}`)
        : ''

      uni.reLaunch({
        url: `/pages/login/login${redirectUrl ? `?redirect=${redirectUrl}` : ''}`
      })
    }, 2000)

    reject(new Error('登录已过期'))
  } else if (res.statusCode === 403) {
    // 权限不足
    uni.showToast({
      title: '权限不足',
      icon: 'none'
    })
    reject(new Error('权限不足'))
  } else if (res.statusCode >= 500) {
    // 服务器错误
    uni.showToast({
      title: '服务器错误，请稍后重试',
      icon: 'none'
    })
    reject(new Error(`服务器错误: ${res.statusCode}`))
  } else {
    // 其他错误
    const errorMsg = res.data?.message || `请求失败: ${res.statusCode}`
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    })
    reject(new Error(errorMsg))
  }
}

// 处理请求失败
function handleRequestFail(err, reject) {
  console.error('📡 请求失败:', err)
  
  let errorMessage = '网络请求失败'
  
  if (err.errMsg) {
    if (err.errMsg.includes('timeout')) {
      errorMessage = '请求超时，请检查网络连接'
    } else if (err.errMsg.includes('fail')) {
      errorMessage = '网络连接失败，请检查网络设置'
    } else if (err.errMsg.includes('abort')) {
      errorMessage = '请求被取消'
    }
  }
  
  // 记录错误到全局状态
  const app = getGlobalApp()
  if (app && app.addError) {
    app.addError({
      type: 'network_error',
      message: errorMessage,
      details: err,
      timestamp: Date.now()
    })
  }
  
  uni.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000
  })
  
  reject(new Error(errorMessage))
}

/**
 * 统一请求方法
 * @param {Object} options 请求配置
 * @param {String} options.url 请求地址
 * @param {String} options.method 请求方法
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 * @param {Number} options.timeout 超时时间
 * @param {Boolean} options.showLoading 是否显示加载提示
 * @param {String} options.loadingText 加载提示文字
 */
export function request(options = {}) {
  return new Promise((resolve, reject) => {
    // 构建完整的请求URL
    const baseUrl = getBaseUrl()
    const url = options.url.startsWith('http') ? options.url : baseUrl + options.url
    
    // 获取请求头
    const headers = getHeaders(options.header)
    
    // 请求配置
    const requestConfig = {
      url,
      method: options.method || 'GET',
      data: options.data || {},
      header: headers,
      timeout: options.timeout || 10000
    }
    
    console.log('📤 发起请求:', {
      url: requestConfig.url,
      method: requestConfig.method,
      data: requestConfig.data,
      header: requestConfig.header
    })
    
    // 显示加载提示
    if (options.showLoading !== false) {
      uni.showLoading({
        title: options.loadingText || '加载中...',
        mask: true
      })
    }
    
    // 发起请求
    uni.request({
      ...requestConfig,
      success: (res) => {
        if (options.showLoading !== false) {
          uni.hideLoading()
        }
        handleResponse(res, resolve, reject)
      },
      fail: (err) => {
        if (options.showLoading !== false) {
          uni.hideLoading()
        }
        handleRequestFail(err, reject)
      }
    })
  })
}

/**
 * GET请求
 */
export function get(url, params = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data: params,
    ...options
  })
}

/**
 * POST请求
 */
export function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求
 */
export function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 */
export function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

/**
 * 文件上传
 */
export function upload(url, filePath, name = 'file', formData = {}) {
  return new Promise((resolve, reject) => {
    const baseUrl = getBaseUrl()
    const uploadUrl = url.startsWith('http') ? url : baseUrl + url
    const headers = getHeaders()
    
    console.log('📤 上传文件:', {
      url: uploadUrl,
      filePath,
      name,
      formData
    })
    
    uni.showLoading({
      title: '上传中...',
      mask: true
    })
    
    uni.uploadFile({
      url: uploadUrl,
      filePath,
      name,
      formData,
      header: headers,
      success: (res) => {
        uni.hideLoading()
        
        try {
          const data = JSON.parse(res.data)
          if (data.success !== false) {
            resolve(data)
          } else {
            reject(new Error(data.message || '上传失败'))
          }
        } catch (e) {
          reject(new Error('响应数据格式错误'))
        }
      },
      fail: (err) => {
        uni.hideLoading()
        handleRequestFail(err, reject)
      }
    })
  })
}

// 默认导出
export default {
  request,
  get,
  post,
  put,
  del,
  upload
}
