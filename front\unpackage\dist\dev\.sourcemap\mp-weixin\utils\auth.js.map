{"version": 3, "file": "auth.js", "sources": ["utils/auth.js"], "sourcesContent": ["/**\n * jeecg-boot认证管理器\n * 处理用户登录、token管理、权限验证等\n */\n\nimport { userApi } from '@/api/index.js'\n\n// Token存储键名\nconst TOKEN_KEY = 'X-Access-Token'\nconst USER_INFO_KEY = 'userInfo'\nconst PERMISSIONS_KEY = 'permissions'\nconst ROLES_KEY = 'roles'\n\n/**\n * 安全获取本地存储数据\n * @param {string} key 存储键名\n * @param {*} defaultValue 默认值\n * @param {string} expectedType 期望的数据类型 'object' | 'array' | 'string'\n */\nfunction safeGetStorage(key, defaultValue = null, expectedType = 'object') {\n  try {\n    const data = uni.getStorageSync(key)\n\n    if (!data) {\n      return defaultValue\n    }\n\n    // 如果数据类型符合期望，直接返回\n    if (expectedType === 'object' && typeof data === 'object' && !Array.isArray(data)) {\n      return data\n    }\n\n    if (expectedType === 'array' && Array.isArray(data)) {\n      return data\n    }\n\n    if (expectedType === 'string' && typeof data === 'string') {\n      return data\n    }\n\n    // 如果是字符串，尝试解析JSON\n    if (typeof data === 'string') {\n      try {\n        const parsed = JSON.parse(data)\n\n        // 验证解析后的数据类型\n        if (expectedType === 'object' && typeof parsed === 'object' && !Array.isArray(parsed)) {\n          return parsed\n        }\n\n        if (expectedType === 'array' && Array.isArray(parsed)) {\n          return parsed\n        }\n\n        if (expectedType === 'string') {\n          return parsed\n        }\n\n        // 类型不匹配，返回默认值\n        console.warn(`存储数据类型不匹配，期望: ${expectedType}, 实际: ${typeof parsed}`, key, parsed)\n        return defaultValue\n\n      } catch (parseError) {\n        console.error(`JSON解析失败 [${key}]:`, parseError, '原始数据:', data)\n        return defaultValue\n      }\n    }\n\n    // 其他情况，返回默认值\n    console.warn(`存储数据类型异常 [${key}]:`, typeof data, data)\n    return defaultValue\n\n  } catch (error) {\n    console.error(`获取存储数据失败 [${key}]:`, error)\n    return defaultValue\n  }\n}\n\n/**\n * 安全设置本地存储数据\n * @param {string} key 存储键名\n * @param {*} data 要存储的数据\n */\nfunction safeSetStorage(key, data) {\n  try {\n    if (data === null || data === undefined) {\n      uni.removeStorageSync(key)\n      return true\n    }\n\n    // 对象和数组转换为JSON字符串存储\n    if (typeof data === 'object') {\n      uni.setStorageSync(key, JSON.stringify(data))\n    } else {\n      uni.setStorageSync(key, data)\n    }\n\n    return true\n  } catch (error) {\n    console.error(`设置存储数据失败 [${key}]:`, error)\n    return false\n  }\n}\n\n/**\n * 获取Token\n */\nexport function getToken() {\n  try {\n    return uni.getStorageSync(TOKEN_KEY)\n  } catch (error) {\n    console.error('获取Token失败:', error)\n    return null\n  }\n}\n\n/**\n * 设置Token\n */\nexport function setToken(token) {\n  try {\n    uni.setStorageSync(TOKEN_KEY, token)\n    \n    // 同步到全局数据\n    const app = getApp()\n    if (app && app.globalData) {\n      app.globalData.token = token\n    }\n    \n    return true\n  } catch (error) {\n    console.error('设置Token失败:', error)\n    return false\n  }\n}\n\n/**\n * 移除Token\n */\nexport function removeToken() {\n  try {\n    uni.removeStorageSync(TOKEN_KEY)\n    \n    // 清除全局数据\n    const app = getApp()\n    if (app && app.globalData) {\n      app.globalData.token = null\n    }\n    \n    return true\n  } catch (error) {\n    console.error('移除Token失败:', error)\n    return false\n  }\n}\n\n/**\n * 获取用户信息\n */\nexport function getUserInfo() {\n  return safeGetStorage(USER_INFO_KEY, null, 'object')\n}\n\n/**\n * 设置用户信息\n */\nexport function setUserInfo(userInfo) {\n  try {\n    if (!userInfo || typeof userInfo !== 'object') {\n      console.error('用户信息格式错误:', userInfo)\n      return false\n    }\n\n    // 使用安全存储函数\n    const success = safeSetStorage(USER_INFO_KEY, userInfo)\n\n    if (success) {\n      // 同步到全局数据\n      const app = getApp()\n      if (app && app.globalData) {\n        app.globalData.userInfo = userInfo\n        app.globalData.isLogin = true  // 使用App.vue中定义的字段名\n        app.globalData.isLoggedIn = true  // 兼容性字段\n      }\n\n      console.log('✅ 用户信息保存成功:', {\n        username: userInfo.username,\n        realname: userInfo.realname,\n        id: userInfo.id\n      })\n    }\n\n    return success\n  } catch (error) {\n    console.error('设置用户信息失败:', error)\n    return false\n  }\n}\n\n/**\n * 移除用户信息\n */\nexport function removeUserInfo() {\n  try {\n    uni.removeStorageSync(USER_INFO_KEY)\n    \n    // 清除全局数据\n    const app = getApp()\n    if (app && app.globalData) {\n      app.globalData.userInfo = null\n      app.globalData.isLogin = false  // 使用App.vue中定义的字段名\n      app.globalData.isLoggedIn = false  // 兼容性字段\n    }\n    \n    return true\n  } catch (error) {\n    console.error('移除用户信息失败:', error)\n    return false\n  }\n}\n\n/**\n * 获取用户权限\n */\nexport function getPermissions() {\n  return safeGetStorage(PERMISSIONS_KEY, [], 'array')\n}\n\n/**\n * 设置用户权限\n */\nexport function setPermissions(permissions) {\n  if (!Array.isArray(permissions)) {\n    console.error('权限信息必须是数组:', permissions)\n    return false\n  }\n  return safeSetStorage(PERMISSIONS_KEY, permissions)\n}\n\n/**\n * 获取用户角色\n */\nexport function getRoles() {\n  return safeGetStorage(ROLES_KEY, [], 'array')\n}\n\n/**\n * 设置用户角色\n */\nexport function setRoles(roles) {\n  if (!Array.isArray(roles)) {\n    console.error('角色信息必须是数组:', roles)\n    return false\n  }\n  return safeSetStorage(ROLES_KEY, roles)\n}\n\n/**\n * 检查是否已登录\n */\nexport function isLoggedIn() {\n  const token = getToken()\n  const userInfo = getUserInfo()\n  return !!(token && userInfo)\n}\n\n/**\n * 用户登录\n */\nexport async function login(loginData) {\n  try {\n    console.log('🔐 开始登录:', loginData)\n    \n    const response = await userApi.login(loginData)\n    \n    if (response.success) {\n      const { token, userInfo } = response.result || response.data\n      \n      // 保存认证信息\n      setToken(token)\n      setUserInfo(userInfo)\n      \n      // 获取用户权限和角色（如果有）\n      if (userInfo.permissions) {\n        setPermissions(userInfo.permissions)\n      }\n      if (userInfo.roles) {\n        setRoles(userInfo.roles)\n      }\n      \n      console.log('✅ 登录成功:', userInfo)\n      \n      return {\n        success: true,\n        data: userInfo,\n        token: token\n      }\n    } else {\n      console.error('❌ 登录失败:', response.message)\n      return {\n        success: false,\n        message: response.message || '登录失败'\n      }\n    }\n  } catch (error) {\n    console.error('❌ 登录异常:', error)\n    return {\n      success: false,\n      message: error.message || '登录异常，请重试'\n    }\n  }\n}\n\n/**\n * 用户登出\n */\nexport async function logout() {\n  try {\n    console.log('🚪 开始登出')\n    \n    // 调用后端登出接口\n    try {\n      await userApi.logout()\n    } catch (error) {\n      console.warn('后端登出接口调用失败:', error)\n    }\n    \n    // 清除本地存储\n    removeToken()\n    removeUserInfo()\n    uni.removeStorageSync(PERMISSIONS_KEY)\n    uni.removeStorageSync(ROLES_KEY)\n    \n    console.log('✅ 登出成功')\n    \n    return { success: true }\n  } catch (error) {\n    console.error('❌ 登出异常:', error)\n    return {\n      success: false,\n      message: error.message || '登出异常'\n    }\n  }\n}\n\n/**\n * 刷新用户信息\n */\nexport async function refreshUserInfo() {\n  try {\n    console.log('🔄 刷新用户信息')\n    \n    const response = await userApi.getUserInfo()\n    \n    if (response.success) {\n      const userInfo = response.result || response.data\n      setUserInfo(userInfo)\n      \n      console.log('✅ 用户信息刷新成功')\n      return { success: true, data: userInfo }\n    } else {\n      console.error('❌ 用户信息刷新失败:', response.message)\n      return { success: false, message: response.message }\n    }\n  } catch (error) {\n    console.error('❌ 用户信息刷新异常:', error)\n    \n    // 如果是401错误，清除登录状态\n    if (error.statusCode === 401) {\n      await logout()\n      return { success: false, message: '登录已过期，请重新登录', needLogin: true }\n    }\n    \n    return { success: false, message: error.message || '刷新失败' }\n  }\n}\n\n/**\n * 检查权限\n */\nexport function hasPermission(permission) {\n  const permissions = getPermissions()\n  return permissions.includes(permission)\n}\n\n/**\n * 检查角色\n */\nexport function hasRole(role) {\n  const roles = getRoles()\n  return roles.includes(role)\n}\n\n/**\n * 检查多个权限（AND关系）\n */\nexport function hasAllPermissions(permissions) {\n  const userPermissions = getPermissions()\n  return permissions.every(permission => userPermissions.includes(permission))\n}\n\n/**\n * 检查多个权限（OR关系）\n */\nexport function hasAnyPermission(permissions) {\n  const userPermissions = getPermissions()\n  return permissions.some(permission => userPermissions.includes(permission))\n}\n\n/**\n * 获取用户显示名称\n */\nexport function getUserDisplayName() {\n  const userInfo = getUserInfo()\n  if (!userInfo) return '未登录'\n  \n  return userInfo.realname || userInfo.username || '用户'\n}\n\n/**\n * 获取用户头像URL\n */\nexport function getUserAvatar() {\n  const userInfo = getUserInfo()\n  if (!userInfo || !userInfo.avatar) {\n    return '/static/images/default-avatar.png'\n  }\n\n  // 如果是相对路径，添加基础URL\n  if (userInfo.avatar.startsWith('/')) {\n    return `${getApiBaseUrl()}${userInfo.avatar}`\n  }\n\n  return userInfo.avatar\n}\n\n/**\n * 检查是否需要登录\n * 如果未登录，自动跳转到登录页面\n */\nexport function requireLogin(currentRoute = '') {\n  if (!isLoggedIn()) {\n    console.log('🔐 需要登录，跳转到登录页面')\n\n    const redirectUrl = currentRoute && currentRoute !== 'pages/login/login'\n      ? encodeURIComponent(`/${currentRoute}`)\n      : ''\n\n    uni.reLaunch({\n      url: `/pages/login/login${redirectUrl ? `?redirect=${redirectUrl}` : ''}`\n    })\n\n    return false\n  }\n\n  return true\n}\n\n/**\n * 导出认证管理器\n */\nexport default {\n  getToken,\n  setToken,\n  removeToken,\n  getUserInfo,\n  setUserInfo,\n  removeUserInfo,\n  getPermissions,\n  setPermissions,\n  getRoles,\n  setRoles,\n  isLoggedIn,\n  login,\n  logout,\n  refreshUserInfo,\n  hasPermission,\n  hasRole,\n  hasAllPermissions,\n  hasAnyPermission,\n  getUserDisplayName,\n  getUserAvatar,\n  requireLogin\n}\n"], "names": ["uni", "userApi"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAQA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,kBAAkB;AACxB,MAAM,YAAY;AAQlB,SAAS,eAAe,KAAK,eAAe,MAAM,eAAe,UAAU;AACzE,MAAI;AACF,UAAM,OAAOA,cAAAA,MAAI,eAAe,GAAG;AAEnC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACR;AAGD,QAAI,iBAAiB,YAAY,OAAO,SAAS,YAAY,CAAC,MAAM,QAAQ,IAAI,GAAG;AACjF,aAAO;AAAA,IACR;AAED,QAAI,iBAAiB,WAAW,MAAM,QAAQ,IAAI,GAAG;AACnD,aAAO;AAAA,IACR;AAED,QAAI,iBAAiB,YAAY,OAAO,SAAS,UAAU;AACzD,aAAO;AAAA,IACR;AAGD,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI;AACF,cAAM,SAAS,KAAK,MAAM,IAAI;AAG9B,YAAI,iBAAiB,YAAY,OAAO,WAAW,YAAY,CAAC,MAAM,QAAQ,MAAM,GAAG;AACrF,iBAAO;AAAA,QACR;AAED,YAAI,iBAAiB,WAAW,MAAM,QAAQ,MAAM,GAAG;AACrD,iBAAO;AAAA,QACR;AAED,YAAI,iBAAiB,UAAU;AAC7B,iBAAO;AAAA,QACR;AAGDA,sBAAAA,MAAa,MAAA,QAAA,uBAAA,iBAAiB,YAAY,SAAS,OAAO,MAAM,IAAI,KAAK,MAAM;AAC/E,eAAO;AAAA,MAER,SAAQ,YAAY;AACnBA,sBAAAA,MAAA,MAAA,SAAA,uBAAc,aAAa,GAAG,MAAM,YAAY,SAAS,IAAI;AAC7D,eAAO;AAAA,MACR;AAAA,IACF;AAGDA,kBAAAA,2CAAa,aAAa,GAAG,MAAM,OAAO,MAAM,IAAI;AACpD,WAAO;AAAA,EAER,SAAQ,OAAO;AACdA,wBAAA,MAAA,SAAA,uBAAc,aAAa,GAAG,MAAM,KAAK;AACzC,WAAO;AAAA,EACR;AACH;AAOA,SAAS,eAAe,KAAK,MAAM;AACjC,MAAI;AACF,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvCA,oBAAG,MAAC,kBAAkB,GAAG;AACzB,aAAO;AAAA,IACR;AAGD,QAAI,OAAO,SAAS,UAAU;AAC5BA,oBAAG,MAAC,eAAe,KAAK,KAAK,UAAU,IAAI,CAAC;AAAA,IAClD,OAAW;AACLA,0BAAI,eAAe,KAAK,IAAI;AAAA,IAC7B;AAED,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,wBAAA,MAAA,SAAA,wBAAc,aAAa,GAAG,MAAM,KAAK;AACzC,WAAO;AAAA,EACR;AACH;AAKO,SAAS,WAAW;AACzB,MAAI;AACF,WAAOA,cAAG,MAAC,eAAe,SAAS;AAAA,EACpC,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,wBAAc,cAAc,KAAK;AACjC,WAAO;AAAA,EACR;AACH;AAKO,SAAS,SAAS,OAAO;AAC9B,MAAI;AACFA,wBAAI,eAAe,WAAW,KAAK;AAGnC,UAAM,MAAM,OAAQ;AACpB,QAAI,OAAO,IAAI,YAAY;AACzB,UAAI,WAAW,QAAQ;AAAA,IACxB;AAED,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,wBAAc,cAAc,KAAK;AACjC,WAAO;AAAA,EACR;AACH;AAKO,SAAS,cAAc;AAC5B,MAAI;AACFA,kBAAG,MAAC,kBAAkB,SAAS;AAG/B,UAAM,MAAM,OAAQ;AACpB,QAAI,OAAO,IAAI,YAAY;AACzB,UAAI,WAAW,QAAQ;AAAA,IACxB;AAED,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,wBAAc,cAAc,KAAK;AACjC,WAAO;AAAA,EACR;AACH;AAKO,SAAS,cAAc;AAC5B,SAAO,eAAe,eAAe,MAAM,QAAQ;AACrD;AAKO,SAAS,YAAY,UAAU;AACpC,MAAI;AACF,QAAI,CAAC,YAAY,OAAO,aAAa,UAAU;AAC7CA,oBAAAA,MAAc,MAAA,SAAA,wBAAA,aAAa,QAAQ;AACnC,aAAO;AAAA,IACR;AAGD,UAAM,UAAU,eAAe,eAAe,QAAQ;AAEtD,QAAI,SAAS;AAEX,YAAM,MAAM,OAAQ;AACpB,UAAI,OAAO,IAAI,YAAY;AACzB,YAAI,WAAW,WAAW;AAC1B,YAAI,WAAW,UAAU;AACzB,YAAI,WAAW,aAAa;AAAA,MAC7B;AAEDA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,eAAe;AAAA,QACzB,UAAU,SAAS;AAAA,QACnB,UAAU,SAAS;AAAA,QACnB,IAAI,SAAS;AAAA,MACrB,CAAO;AAAA,IACF;AAED,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,wBAAc,aAAa,KAAK;AAChC,WAAO;AAAA,EACR;AACH;AAKO,SAAS,iBAAiB;AAC/B,MAAI;AACFA,kBAAG,MAAC,kBAAkB,aAAa;AAGnC,UAAM,MAAM,OAAQ;AACpB,QAAI,OAAO,IAAI,YAAY;AACzB,UAAI,WAAW,WAAW;AAC1B,UAAI,WAAW,UAAU;AACzB,UAAI,WAAW,aAAa;AAAA,IAC7B;AAED,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,wBAAc,aAAa,KAAK;AAChC,WAAO;AAAA,EACR;AACH;AAKO,SAAS,iBAAiB;AAC/B,SAAO,eAAe,iBAAiB,CAAE,GAAE,OAAO;AACpD;AAKO,SAAS,eAAe,aAAa;AAC1C,MAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAC/BA,kBAAAA,MAAA,MAAA,SAAA,wBAAc,cAAc,WAAW;AACvC,WAAO;AAAA,EACR;AACD,SAAO,eAAe,iBAAiB,WAAW;AACpD;AAKO,SAAS,WAAW;AACzB,SAAO,eAAe,WAAW,CAAE,GAAE,OAAO;AAC9C;AAKO,SAAS,SAAS,OAAO;AAC9B,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzBA,kBAAAA,MAAA,MAAA,SAAA,wBAAc,cAAc,KAAK;AACjC,WAAO;AAAA,EACR;AACD,SAAO,eAAe,WAAW,KAAK;AACxC;AAKO,SAAS,aAAa;AAC3B,QAAM,QAAQ,SAAU;AACxB,QAAM,WAAW,YAAa;AAC9B,SAAO,CAAC,EAAE,SAAS;AACrB;AAKO,SAAe,MAAM,WAAW;AAAA;AACrC,QAAI;AACFA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,YAAY,SAAS;AAEjC,YAAM,WAAW,MAAMC,kBAAQ,MAAM,SAAS;AAE9C,UAAI,SAAS,SAAS;AACpB,cAAM,EAAE,OAAO,SAAQ,IAAK,SAAS,UAAU,SAAS;AAGxD,iBAAS,KAAK;AACd,oBAAY,QAAQ;AAGpB,YAAI,SAAS,aAAa;AACxB,yBAAe,SAAS,WAAW;AAAA,QACpC;AACD,YAAI,SAAS,OAAO;AAClB,mBAAS,SAAS,KAAK;AAAA,QACxB;AAEDD,sBAAAA,2CAAY,WAAW,QAAQ;AAE/B,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM;AAAA,UACN;AAAA,QACD;AAAA,MACP,OAAW;AACLA,sBAAc,MAAA,MAAA,SAAA,wBAAA,WAAW,SAAS,OAAO;AACzC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,SAAS,WAAW;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAA,MAAA,SAAA,wBAAc,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAC3B;AAAA,IACF;AAAA,EACH;AAAA;AAKO,SAAe,SAAS;AAAA;AAC7B,QAAI;AACFA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,SAAS;AAGrB,UAAI;AACF,cAAMC,UAAAA,QAAQ,OAAQ;AAAA,MACvB,SAAQ,OAAO;AACdD,sBAAAA,MAAa,MAAA,QAAA,wBAAA,eAAe,KAAK;AAAA,MAClC;AAGD,kBAAa;AACb,qBAAgB;AAChBA,oBAAG,MAAC,kBAAkB,eAAe;AACrCA,oBAAG,MAAC,kBAAkB,SAAS;AAE/BA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,QAAQ;AAEpB,aAAO,EAAE,SAAS,KAAM;AAAA,IACzB,SAAQ,OAAO;AACdA,oBAAAA,MAAA,MAAA,SAAA,wBAAc,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAC3B;AAAA,IACF;AAAA,EACH;AAAA;AAKO,SAAe,kBAAkB;AAAA;AACtC,QAAI;AACFA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,WAAW;AAEvB,YAAM,WAAW,MAAMC,UAAO,QAAC,YAAa;AAE5C,UAAI,SAAS,SAAS;AACpB,cAAM,WAAW,SAAS,UAAU,SAAS;AAC7C,oBAAY,QAAQ;AAEpBD,sBAAAA,MAAA,MAAA,OAAA,wBAAY,YAAY;AACxB,eAAO,EAAE,SAAS,MAAM,MAAM,SAAU;AAAA,MAC9C,OAAW;AACLA,mEAAc,eAAe,SAAS,OAAO;AAC7C,eAAO,EAAE,SAAS,OAAO,SAAS,SAAS,QAAS;AAAA,MACrD;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,wBAAA,eAAe,KAAK;AAGlC,UAAI,MAAM,eAAe,KAAK;AAC5B,cAAM,OAAQ;AACd,eAAO,EAAE,SAAS,OAAO,SAAS,eAAe,WAAW,KAAM;AAAA,MACnE;AAED,aAAO,EAAE,SAAS,OAAO,SAAS,MAAM,WAAW,OAAQ;AAAA,IAC5D;AAAA,EACH;AAAA;AAiEO,SAAS,aAAa,eAAe,IAAI;AAC9C,MAAI,CAAC,WAAU,GAAI;AACjBA,kBAAAA,MAAA,MAAA,OAAA,wBAAY,iBAAiB;AAE7B,UAAM,cAAc,gBAAgB,iBAAiB,sBACjD,mBAAmB,IAAI,YAAY,EAAE,IACrC;AAEJA,kBAAAA,MAAI,SAAS;AAAA,MACX,KAAK,qBAAqB,cAAc,aAAa,WAAW,KAAK,EAAE;AAAA,IAC7E,CAAK;AAED,WAAO;AAAA,EACR;AAED,SAAO;AACT;;;;;;;;;;;;;;;;"}