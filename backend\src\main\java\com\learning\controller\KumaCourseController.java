package com.learning.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import com.learning.entity.KumaCourse;
import com.learning.service.IKumaCourseService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: kuma_course
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Api(tags="kuma_course")
@RestController
@RequestMapping("/learning/kumaCourse")
@Slf4j
public class KumaCourseController extends JeecgController<KumaCourse, IKumaCourseService> {
	@Autowired
	private IKumaCourseService kumaCourseService;
	
	/**
	 * 分页列表查询
	 *
	 * @param kumaCourse
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "kuma_course-分页列表查询")
	@ApiOperation(value="kuma_course-分页列表查询", notes="kuma_course-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<KumaCourse>> queryPageList(KumaCourse kumaCourse,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<KumaCourse> queryWrapper = QueryGenerator.initQueryWrapper(kumaCourse, req.getParameterMap());
		Page<KumaCourse> page = new Page<KumaCourse>(pageNo, pageSize);
		IPage<KumaCourse> pageList = kumaCourseService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param kumaCourse
	 * @return
	 */
	@AutoLog(value = "kuma_course-添加")
	@ApiOperation(value="kuma_course-添加", notes="kuma_course-添加")
	@RequiresPermissions("learning:kuma_course:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody KumaCourse kumaCourse) {
		kumaCourseService.save(kumaCourse);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param kumaCourse
	 * @return
	 */
	@AutoLog(value = "kuma_course-编辑")
	@ApiOperation(value="kuma_course-编辑", notes="kuma_course-编辑")
	@RequiresPermissions("learning:kuma_course:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody KumaCourse kumaCourse) {
		kumaCourseService.updateById(kumaCourse);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "kuma_course-通过id删除")
	@ApiOperation(value="kuma_course-通过id删除", notes="kuma_course-通过id删除")
	@RequiresPermissions("learning:kuma_course:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		kumaCourseService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "kuma_course-批量删除")
	@ApiOperation(value="kuma_course-批量删除", notes="kuma_course-批量删除")
	@RequiresPermissions("learning:kuma_course:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.kumaCourseService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "kuma_course-通过id查询")
	@ApiOperation(value="kuma_course-通过id查询", notes="kuma_course-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<KumaCourse> queryById(@RequestParam(name="id",required=true) String id) {
		KumaCourse kumaCourse = kumaCourseService.getById(id);
		if(kumaCourse==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(kumaCourse);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param kumaCourse
    */
    @RequiresPermissions("learning:kuma_course:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, KumaCourse kumaCourse) {
        return super.exportXls(request, kumaCourse, KumaCourse.class, "kuma_course");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("learning:kuma_course:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, KumaCourse.class);
    }

}
