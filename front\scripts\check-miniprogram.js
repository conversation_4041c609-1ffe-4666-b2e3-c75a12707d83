#!/usr/bin/env node

/**
 * 小程序配置检查脚本
 * 验证uni-app小程序配置是否正确
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 检查小程序配置...\n')

let allPassed = true
let warnings = []

// 检查配置文件
const configChecks = [
  {
    name: 'manifest.json',
    path: 'manifest.json',
    required: true,
    description: '应用配置文件'
  },
  {
    name: 'pages.json',
    path: 'pages.json',
    required: true,
    description: '页面路由配置'
  },
  {
    name: 'main.js',
    path: 'main.js',
    required: true,
    description: '应用入口文件'
  },
  {
    name: 'App.vue',
    path: 'App.vue',
    required: true,
    description: '应用根组件'
  },
  {
    name: 'utils/request.js',
    path: 'utils/request.js',
    required: true,
    description: '请求管理器'
  },
  {
    name: 'mixins/page-mixin.js',
    path: 'mixins/page-mixin.js',
    required: true,
    description: '页面混入'
  }
]

// 检查文件存在性
console.log('📁 检查核心文件...')
configChecks.forEach(check => {
  const filePath = path.join(process.cwd(), check.path)
  const exists = fs.existsSync(filePath)
  
  if (exists) {
    console.log(`✅ ${check.name} - ${check.description}`)
  } else if (check.required) {
    console.log(`❌ ${check.name} - ${check.description} (必需文件缺失)`)
    allPassed = false
  } else {
    console.log(`⚠️  ${check.name} - ${check.description} (可选文件缺失)`)
    warnings.push(check.name)
  }
})

console.log('\n📋 详细配置检查...\n')

// 检查manifest.json配置
try {
  const manifestPath = path.join(process.cwd(), 'manifest.json')
  if (fs.existsSync(manifestPath)) {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    
    // 检查Vue版本
    if (manifest.vueVersion === '3') {
      console.log('✅ manifest.json - Vue版本设置为3')
    } else {
      console.log(`❌ manifest.json - Vue版本设置为${manifest.vueVersion}，应该为3`)
      allPassed = false
    }
    
    // 检查微信小程序配置
    if (manifest['mp-weixin']) {
      console.log('✅ manifest.json - 微信小程序配置存在')
      
      if (manifest['mp-weixin'].usingComponents) {
        console.log('✅ manifest.json - 微信小程序启用组件支持')
      } else {
        console.log('⚠️  manifest.json - 微信小程序未启用组件支持')
        warnings.push('微信小程序组件支持')
      }
    } else {
      console.log('❌ manifest.json - 缺少微信小程序配置')
      allPassed = false
    }
    
    // 检查钉钉小程序配置
    if (manifest['mp-dingtalk']) {
      console.log('✅ manifest.json - 钉钉小程序配置存在')
      
      if (manifest['mp-dingtalk'].usingComponents) {
        console.log('✅ manifest.json - 钉钉小程序启用组件支持')
      } else {
        console.log('⚠️  manifest.json - 钉钉小程序未启用组件支持')
        warnings.push('钉钉小程序组件支持')
      }
    } else {
      console.log('⚠️  manifest.json - 缺少钉钉小程序配置')
      warnings.push('钉钉小程序配置')
    }
    
  }
} catch (error) {
  console.log('❌ manifest.json - 文件格式错误或无法读取')
  allPassed = false
}

// 检查package.json中的脚本
try {
  const packagePath = path.join(process.cwd(), 'package.json')
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    const scripts = packageJson.scripts || {}
    
    // 检查小程序开发脚本
    const requiredScripts = [
      'dev:mp-weixin',
      'dev:mp-dingtalk',
      'build:mp-weixin',
      'build:mp-dingtalk'
    ]
    
    requiredScripts.forEach(script => {
      if (scripts[script]) {
        console.log(`✅ package.json - ${script} 脚本存在`)
      } else {
        console.log(`❌ package.json - 缺少 ${script} 脚本`)
        allPassed = false
      }
    })
    
    // 检查uni-app依赖
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
    
    const requiredDeps = [
      '@dcloudio/uni-app',
      '@dcloudio/uni-mp-weixin',
      '@dcloudio/vite-plugin-uni',
      'vue'
    ]
    
    requiredDeps.forEach(dep => {
      if (dependencies[dep]) {
        console.log(`✅ package.json - ${dep} 依赖存在`)
      } else {
        console.log(`❌ package.json - 缺少 ${dep} 依赖`)
        allPassed = false
      }
    })
    
    // 检查Vue版本
    if (dependencies.vue && dependencies.vue.includes('3')) {
      console.log(`✅ package.json - Vue版本: ${dependencies.vue}`)
    } else {
      console.log(`❌ package.json - Vue版本不正确: ${dependencies.vue || '未找到'}`)
      allPassed = false
    }
    
  }
} catch (error) {
  console.log('❌ package.json - 文件格式错误或无法读取')
  allPassed = false
}

// 检查main.js中的Vue3语法
try {
  const mainPath = path.join(process.cwd(), 'main.js')
  if (fs.existsSync(mainPath)) {
    const mainContent = fs.readFileSync(mainPath, 'utf8')
    
    if (mainContent.includes('createSSRApp')) {
      console.log('✅ main.js - 使用Vue3的createSSRApp')
    } else if (mainContent.includes('createApp')) {
      console.log('✅ main.js - 使用Vue3的createApp')
    } else {
      console.log('❌ main.js - 未找到Vue3的应用创建方法')
      allPassed = false
    }
    
    // 检查是否导入了请求管理器
    if (mainContent.includes('request') || mainContent.includes('$request')) {
      console.log('✅ main.js - 包含请求管理器配置')
    } else {
      console.log('⚠️  main.js - 未找到请求管理器配置')
      warnings.push('请求管理器配置')
    }
  }
} catch (error) {
  console.log('❌ main.js - 文件读取失败')
  allPassed = false
}

// 检查页面配置
try {
  const pagesPath = path.join(process.cwd(), 'pages.json')
  if (fs.existsSync(pagesPath)) {
    const pagesConfig = JSON.parse(fs.readFileSync(pagesPath, 'utf8'))
    
    if (pagesConfig.pages && pagesConfig.pages.length > 0) {
      console.log(`✅ pages.json - 配置了 ${pagesConfig.pages.length} 个页面`)
    } else {
      console.log('❌ pages.json - 未配置页面')
      allPassed = false
    }
    
    if (pagesConfig.tabBar) {
      console.log('✅ pages.json - 配置了底部导航')
    } else {
      console.log('⚠️  pages.json - 未配置底部导航')
      warnings.push('底部导航配置')
    }
  }
} catch (error) {
  console.log('❌ pages.json - 文件格式错误或无法读取')
  allPassed = false
}

// 检查API接口文件
try {
  const apiPath = path.join(process.cwd(), 'api/index.js')
  if (fs.existsSync(apiPath)) {
    const apiContent = fs.readFileSync(apiPath, 'utf8')
    
    if (apiContent.includes('utils/request')) {
      console.log('✅ api/index.js - 使用新的请求管理器')
    } else {
      console.log('⚠️  api/index.js - 未使用新的请求管理器')
      warnings.push('API请求管理器')
    }
  }
} catch (error) {
  console.log('⚠️  api/index.js - 文件读取失败')
  warnings.push('API接口文件')
}

// 输出结果
console.log('\n' + '='.repeat(60))

if (allPassed) {
  console.log('🎉 小程序配置检查通过！')
  console.log('✅ 所有必需配置都正确')
  console.log('✅ Vue3配置完整')
  console.log('✅ 小程序平台支持完整')
  
  if (warnings.length > 0) {
    console.log('\n⚠️  可选配置建议:')
    warnings.forEach(warning => {
      console.log(`   - ${warning}`)
    })
  }
  
  console.log('\n🚀 可以开始小程序开发了！')
  console.log('\n📱 启动命令:')
  console.log('   微信小程序: npm run dev:mp-weixin')
  console.log('   钉钉小程序: npm run dev:mp-dingtalk')
  console.log('   H5调试:    npm run dev:h5')
  
} else {
  console.log('❌ 小程序配置检查失败！')
  console.log('请根据上述错误信息修复配置')
  console.log('\n📖 参考文档: docs/小程序架构重构指南.md')
  process.exit(1)
}

console.log('\n' + '='.repeat(60))
