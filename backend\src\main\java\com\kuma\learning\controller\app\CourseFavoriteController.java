package com.kuma.learning.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuma.learning.entity.KumaCourseFavorite;
import com.kuma.learning.service.IKumaCourseFavoriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = "APP-课程收藏管理")
@RestController
@RequestMapping("/app/course/favorite")
public class CourseFavoriteController {

    @Autowired
    private IKumaCourseFavoriteService favoriteService;

    @AutoLog(value = "课程收藏-分页列表查询")
    @ApiOperation(value = "课程收藏-分页列表查询", notes = "课程收藏-分页列表查询")
    @GetMapping("/list")
    public Result<IPage<KumaCourseFavorite>> queryPageList(
            @RequestParam(name = "userId") String userId,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        
        Page<KumaCourseFavorite> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<KumaCourseFavorite> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        queryWrapper.eq(KumaCourseFavorite::getUserId, userId)
                .orderByDesc(KumaCourseFavorite::getCreateTime);
        
        IPage<KumaCourseFavorite> pageList = favoriteService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog(value = "课程收藏-添加")
    @ApiOperation(value = "课程收藏-添加", notes = "课程收藏-添加")
    @PostMapping("/add")
    public Result<String> add(@RequestBody KumaCourseFavorite favorite) {
        // 检查是否已收藏
        LambdaQueryWrapper<KumaCourseFavorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCourseFavorite::getUserId, favorite.getUserId())
                .eq(KumaCourseFavorite::getCourseId, favorite.getCourseId());
        
        if (favoriteService.count(queryWrapper) > 0) {
            return Result.error("该课程已收藏！");
        }
        
        favoriteService.save(favorite);
        return Result.OK("收藏成功！");
    }

    @AutoLog(value = "课程收藏-取消收藏")
    @ApiOperation(value = "课程收藏-取消收藏", notes = "课程收藏-取消收藏")
    @DeleteMapping("/delete")
    public Result<String> delete(
            @RequestParam(name = "userId") String userId,
            @RequestParam(name = "courseId") String courseId) {
        
        LambdaQueryWrapper<KumaCourseFavorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCourseFavorite::getUserId, userId)
                .eq(KumaCourseFavorite::getCourseId, courseId);
        
        favoriteService.remove(queryWrapper);
        return Result.OK("取消收藏成功！");
    }

    @AutoLog(value = "课程收藏-检查是否已收藏")
    @ApiOperation(value = "课程收藏-检查是否已收藏", notes = "课程收藏-检查是否已收藏")
    @GetMapping("/check")
    public Result<Boolean> checkFavorite(
            @RequestParam(name = "userId") String userId,
            @RequestParam(name = "courseId") String courseId) {
        
        LambdaQueryWrapper<KumaCourseFavorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCourseFavorite::getUserId, userId)
                .eq(KumaCourseFavorite::getCourseId, courseId);
        
        boolean exists = favoriteService.count(queryWrapper) > 0;
        return Result.OK(exists);
    }
} 