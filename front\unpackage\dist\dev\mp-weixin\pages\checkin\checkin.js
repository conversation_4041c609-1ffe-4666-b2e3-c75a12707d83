"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 0,
      loading: true,
      isCheckedIn: false,
      continuousDays: 0,
      nextMilestone: 7,
      checkinDesc: "今天还没有学习记录",
      currentYear: (/* @__PURE__ */ new Date()).getFullYear(),
      currentMonth: (/* @__PURE__ */ new Date()).getMonth() + 1,
      completedDays: 0,
      totalDays: new Date((/* @__PURE__ */ new Date()).getFullYear(), (/* @__PURE__ */ new Date()).getMonth() + 1, 0).getDate(),
      remainingMakeups: 5,
      weekDays: ["日", "一", "二", "三", "四", "五", "六"],
      calendarDays: [],
      checkinHistory: [],
      // 打卡历史记录
      statistics: [
        {
          key: "total",
          label: "总打卡天数",
          value: "156 天",
          icon: "icon-calendar-check",
          color: "#16a34a"
        },
        {
          key: "max_continuous",
          label: "最长连续",
          value: "28 天",
          icon: "icon-fire",
          color: "#ea580c"
        },
        {
          key: "remaining_makeup",
          label: "剩余补打卡",
          value: "4 次",
          icon: "icon-redo",
          color: "#2563eb"
        },
        {
          key: "completion_rate",
          label: "本月完成率",
          value: "71%",
          icon: "icon-percentage",
          color: "#9333ea"
        }
      ]
    };
  },
  onLoad() {
    this.initPage();
  },
  onShow() {
    this.loadCheckinData();
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.loadCheckinData().finally(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  methods: {
    initPage() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight;
    },
    // 加载打卡数据
    loadCheckinData() {
      return __async(this, null, function* () {
        this.loading = true;
        try {
          const [todayResponse, historyResponse] = yield Promise.all([
            this.checkTodayCheckin(),
            this.getCheckinHistory()
          ]);
          this.generateCalendar();
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/checkin/checkin.vue:188", "加载打卡数据失败:", error);
          this.setDefaultData();
          this.generateCalendar();
          common_vendor.index.showToast({
            title: "加载失败，显示离线数据",
            icon: "none"
          });
        } finally {
          this.loading = false;
        }
      });
    },
    // 检查今日打卡状态
    checkTodayCheckin() {
      return __async(this, null, function* () {
        try {
          const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
          const response = yield api_index.userApi.getCheckinHistory({
            date: today
          });
          if (response.success) {
            this.isCheckedIn = response.data.isCheckedToday || false;
            this.continuousDays = response.data.continuousDays || 0;
            this.checkinDesc = response.data.todayStudyDesc || "今天还没有学习记录";
            this.calculateNextMilestone();
          }
          return response;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/checkin/checkin.vue:220", "检查今日打卡状态失败:", error);
          return { success: false };
        }
      });
    },
    // 获取打卡历史记录
    getCheckinHistory() {
      return __async(this, null, function* () {
        try {
          const response = yield api_index.userApi.getCheckinHistory({
            year: this.currentYear,
            month: this.currentMonth
          });
          if (response.success && response.data) {
            this.checkinHistory = response.data.checkinDays || [];
            this.completedDays = this.checkinHistory.length;
            this.remainingMakeups = response.data.remainingMakeups || 5;
            this.updateStatistics(response.data.statistics);
          }
          return response;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/checkin/checkin.vue:242", "获取打卡历史失败:", error);
          return { success: false };
        }
      });
    },
    // 设置默认数据
    setDefaultData() {
      this.isCheckedIn = false;
      this.continuousDays = 0;
      this.checkinHistory = [];
      this.completedDays = 0;
    },
    // 计算下一个里程碑
    calculateNextMilestone() {
      const milestones = [7, 15, 30, 50, 100, 200, 365];
      for (let milestone of milestones) {
        if (this.continuousDays < milestone) {
          this.nextMilestone = milestone - this.continuousDays;
          break;
        }
      }
    },
    generateCalendar() {
      const year = this.currentYear;
      const month = this.currentMonth;
      const firstDay = new Date(year, month - 1, 1).getDay();
      const daysInMonth = new Date(year, month, 0).getDate();
      const today = (/* @__PURE__ */ new Date()).getDate();
      this.calendarDays = [];
      for (let i = 0; i < firstDay; i++) {
        this.calendarDays.push({ date: null });
      }
      for (let date = 1; date <= daysInMonth; date++) {
        const dayData = {
          date,
          isToday: date === today,
          isChecked: this.isDateChecked(date),
          isMakeup: this.isDateMakeup(date)
        };
        this.calendarDays.push(dayData);
      }
    },
    isDateChecked(date) {
      return this.checkinHistory.some((record) => {
        const recordDate = new Date(record.checkinDate).getDate();
        return recordDate === date && !record.isMakeup;
      });
    },
    isDateMakeup(date) {
      return this.checkinHistory.some((record) => {
        const recordDate = new Date(record.checkinDate).getDate();
        return recordDate === date && record.isMakeup;
      });
    },
    getDayClass(day) {
      const classes = [];
      if (!day.date)
        return "empty";
      if (day.isToday)
        classes.push("today");
      if (day.isChecked)
        classes.push("checked");
      if (day.isMakeup)
        classes.push("makeup");
      return classes.join(" ");
    },
    handleCheckin() {
      if (this.isCheckedIn) {
        common_vendor.index.showToast({
          title: "今日已打卡",
          icon: "success"
        });
        return;
      }
      this.performCheckin();
    },
    performCheckin() {
      return __async(this, null, function* () {
        try {
          common_vendor.index.showLoading({ title: "打卡中..." });
          const response = yield api_index.userApi.checkin();
          if (response.success) {
            this.isCheckedIn = true;
            this.continuousDays = response.data.continuousDays || this.continuousDays + 1;
            this.checkinDesc = response.data.studyDesc || "完成今日学习任务";
            const today = /* @__PURE__ */ new Date();
            this.checkinHistory.push({
              checkinDate: today.toISOString(),
              isMakeup: false,
              studyDesc: this.checkinDesc
            });
            this.completedDays++;
            this.calculateNextMilestone();
            common_vendor.index.showToast({
              title: "打卡成功！",
              icon: "success"
            });
            this.generateCalendar();
          } else {
            throw new Error(response.message || "打卡失败");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/checkin/checkin.vue:365", "打卡失败:", error);
          common_vendor.index.showToast({
            title: error.message || "打卡失败，请重试",
            icon: "none"
          });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    },
    showMakeupDialog() {
      if (this.remainingMakeups <= 0) {
        common_vendor.index.showToast({
          title: "本月补打卡次数已用完",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showActionSheet({
        itemList: ["昨天", "前天", "3天前", "4天前", "5天前"],
        success: (res) => {
          this.performMakeup(res.tapIndex + 1);
        }
      });
    },
    performMakeup(daysAgo) {
      common_vendor.index.showModal({
        title: "确认补打卡",
        content: `确定要为${daysAgo}天前补打卡吗？将消耗1次补打卡机会。`,
        success: (res) => {
          if (res.confirm) {
            this.executeMakeup(daysAgo);
          }
        }
      });
    },
    executeMakeup(daysAgo) {
      return __async(this, null, function* () {
        try {
          common_vendor.index.showLoading({ title: "补打卡中..." });
          const makeupDate = /* @__PURE__ */ new Date();
          makeupDate.setDate(makeupDate.getDate() - daysAgo);
          const response = yield api_index.userApi.checkin({
            isMakeup: true,
            makeupDate: makeupDate.toISOString().split("T")[0]
          });
          if (response.success) {
            this.remainingMakeups--;
            this.checkinHistory.push({
              checkinDate: makeupDate.toISOString(),
              isMakeup: true,
              studyDesc: "补打卡"
            });
            this.completedDays++;
            common_vendor.index.showToast({
              title: "补打卡成功！",
              icon: "success"
            });
            this.updateStatistics();
            this.generateCalendar();
          } else {
            throw new Error(response.message || "补打卡失败");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/checkin/checkin.vue:444", "补打卡失败:", error);
          common_vendor.index.showToast({
            title: error.message || "补打卡失败，请重试",
            icon: "none"
          });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    },
    // 更新统计数据
    updateStatistics(apiStats) {
      if (apiStats) {
        this.statistics = [
          {
            key: "total",
            label: "总打卡天数",
            value: `${apiStats.totalCheckinDays || 0} 天`,
            icon: "icon-calendar-check",
            color: "#16a34a"
          },
          {
            key: "max_continuous",
            label: "最长连续",
            value: `${apiStats.maxContinuousDays || 0} 天`,
            icon: "icon-fire",
            color: "#ea580c"
          },
          {
            key: "remaining_makeup",
            label: "剩余补打卡",
            value: `${this.remainingMakeups} 次`,
            icon: "icon-redo",
            color: "#2563eb"
          },
          {
            key: "completion_rate",
            label: "本月完成率",
            value: `${Math.round(this.completedDays / this.totalDays * 100)}%`,
            icon: "icon-percentage",
            color: "#9333ea"
          }
        ];
      } else {
        this.statistics.forEach((stat) => {
          if (stat.key === "remaining_makeup") {
            stat.value = `${this.remainingMakeups} 次`;
          } else if (stat.key === "completion_rate") {
            stat.value = `${Math.round(this.completedDays / this.totalDays * 100)}%`;
          }
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: common_vendor.n($data.isCheckedIn ? "icon-check" : "icon-calendar"),
    c: $data.isCheckedIn ? 1 : "",
    d: common_vendor.o((...args) => $options.handleCheckin && $options.handleCheckin(...args)),
    e: common_vendor.t($data.isCheckedIn ? "今日已打卡！" : "点击完成打卡"),
    f: $data.isCheckedIn ? 1 : "",
    g: common_vendor.t($data.checkinDesc),
    h: $data.isCheckedIn
  }, $data.isCheckedIn ? {
    i: common_vendor.t($data.continuousDays),
    j: common_vendor.t($data.nextMilestone)
  } : {}, {
    k: common_vendor.t($data.currentYear),
    l: common_vendor.t($data.currentMonth),
    m: common_vendor.t($data.completedDays),
    n: common_vendor.t($data.totalDays),
    o: common_vendor.f($data.weekDays, (day, k0, i0) => {
      return {
        a: common_vendor.t(day),
        b: day
      };
    }),
    p: common_vendor.f($data.calendarDays, (day, index, i0) => {
      return {
        a: common_vendor.t(day.date || ""),
        b: index,
        c: common_vendor.n($options.getDayClass(day))
      };
    }),
    q: common_vendor.f($data.statistics, (stat, k0, i0) => {
      return {
        a: common_vendor.n(stat.icon),
        b: stat.color,
        c: common_vendor.t(stat.label),
        d: common_vendor.t(stat.value),
        e: stat.key
      };
    }),
    r: common_vendor.o((...args) => $options.showMakeupDialog && $options.showMakeupDialog(...args)),
    s: common_vendor.t($data.remainingMakeups)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-63c01bdb"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/checkin/checkin.js.map
