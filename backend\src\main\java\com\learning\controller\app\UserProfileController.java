package com.learning.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.learning.entity.KumaUserProfile;
import com.learning.service.IKumaUserProfileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Api(tags = "APP-用户信息管理")
@RestController
@RequestMapping("/app/user")
public class UserProfileController {

    @Autowired
    private IKumaUserProfileService userProfileService;
    
    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @AutoLog(value = "用户信息-获取当前用户信息")
    @ApiOperation(value = "用户信息-获取当前用户信息", notes = "用户信息-获取当前用户信息")
    @GetMapping("/profile")
    public Result<KumaUserProfile> getUserProfile(HttpServletRequest request) {
        LoginUser loginUser = sysBaseAPI.getLoginUser(request);
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        
        LambdaQueryWrapper<KumaUserProfile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaUserProfile::getUserId, loginUser.getId());
        
        KumaUserProfile userProfile = userProfileService.getOne(queryWrapper);
        if (userProfile == null) {
            // 如果用户信息不存在，创建默认信息
            userProfile = new KumaUserProfile();
            userProfile.setUserId(loginUser.getId());
            userProfile.setNickname(loginUser.getRealname());
            userProfile.setLevel("beginner");
            userProfile.setTotalStudyDays(0);
            userProfile.setContinuousDays(0);
            userProfile.setMaxContinuousDays(0);
            userProfile.setTotalScore(0);
            userProfile.setVipStatus(0);
            userProfile.setSysSign("KUMA");
            userProfileService.save(userProfile);
        }
        
        return Result.OK(userProfile);
    }

    @AutoLog(value = "用户信息-更新用户信息")
    @ApiOperation(value = "用户信息-更新用户信息", notes = "用户信息-更新用户信息")
    @PutMapping("/profile")
    public Result<String> updateUserProfile(@RequestBody KumaUserProfile userProfile, HttpServletRequest request) {
        LoginUser loginUser = sysBaseAPI.getLoginUser(request);
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        
        LambdaQueryWrapper<KumaUserProfile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaUserProfile::getUserId, loginUser.getId());
        
        KumaUserProfile existProfile = userProfileService.getOne(queryWrapper);
        if (existProfile != null) {
            userProfile.setId(existProfile.getId());
            userProfile.setUserId(loginUser.getId());
            userProfileService.updateById(userProfile);
        } else {
            userProfile.setUserId(loginUser.getId());
            userProfile.setSysSign("KUMA");
            userProfileService.save(userProfile);
        }
        
        return Result.OK("更新成功");
    }

    @AutoLog(value = "用户信息-获取学习统计")
    @ApiOperation(value = "用户信息-获取学习统计", notes = "用户信息-获取学习统计")
    @GetMapping("/statistics")
    public Result<KumaUserProfile> getUserStatistics(HttpServletRequest request) {
        LoginUser loginUser = sysBaseAPI.getLoginUser(request);
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        
        LambdaQueryWrapper<KumaUserProfile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaUserProfile::getUserId, loginUser.getId());
        
        KumaUserProfile userProfile = userProfileService.getOne(queryWrapper);
        if (userProfile == null) {
            return Result.error("用户信息不存在");
        }
        
        return Result.OK(userProfile);
    }
}
