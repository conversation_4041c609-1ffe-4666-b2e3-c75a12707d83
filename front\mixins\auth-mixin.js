/**
 * 认证状态混入
 * 为页面提供统一的登录状态检查和处理
 */

import { isLoggedIn, requireLogin, getUserInfo, getToken } from '@/utils/auth.js'

export default {
  data() {
    return {
      // 认证状态
      isAuthenticated: false,
      currentUser: null,
      authToken: null,
      
      // 登录检查状态
      authChecking: true,
      authRequired: true  // 页面是否需要登录，子页面可重写
    }
  },
  
  onLoad(options) {
    // 检查认证状态
    this.checkAuthStatus()
  },
  
  onShow() {
    // 页面显示时重新检查认证状态
    this.checkAuthStatus()
  },
  
  methods: {
    /**
     * 检查认证状态
     */
    checkAuthStatus() {
      console.log('🔐 检查认证状态')
      
      try {
        this.authChecking = true
        
        // 检查登录状态
        this.isAuthenticated = isLoggedIn()
        this.currentUser = getUserInfo()
        this.authToken = getToken()
        
        console.log('认证状态:', {
          isAuthenticated: this.isAuthenticated,
          hasUser: !!this.currentUser,
          hasToken: !!this.authToken
        })
        
        // 如果页面需要登录但用户未登录
        if (this.authRequired && !this.isAuthenticated) {
          this.handleAuthRequired()
          return false
        }
        
        // 认证检查完成
        this.onAuthCheckComplete()
        return true
        
      } catch (error) {
        console.error('❌ 认证状态检查失败:', error)
        this.handleAuthError(error)
        return false
        
      } finally {
        this.authChecking = false
      }
    },
    
    /**
     * 处理需要登录的情况
     */
    handleAuthRequired() {
      console.log('🔐 页面需要登录，跳转到登录页面')

      // 安全获取当前页面路径
      const currentRoute = this.getCurrentPageRoute()

      // 跳转到登录页面
      requireLogin(currentRoute)
    },
    
    /**
     * 处理认证错误
     */
    handleAuthError(error) {
      console.error('❌ 认证错误:', error)
      
      uni.showToast({
        title: '认证失败，请重新登录',
        icon: 'none',
        duration: 2000
      })
      
      // 如果页面需要登录，跳转到登录页面
      if (this.authRequired) {
        setTimeout(() => {
          this.handleAuthRequired()
        }, 2000)
      }
    },
    
    /**
     * 认证检查完成回调
     * 子页面可重写此方法
     */
    onAuthCheckComplete() {
      console.log('✅ 认证检查完成')
      
      // 如果子页面定义了认证完成回调，调用它
      if (this.onAuthReady && typeof this.onAuthReady === 'function') {
        this.onAuthReady()
      }
    },
    
    /**
     * 强制要求登录
     */
    forceLogin() {
      const currentRoute = this.getCurrentPageRoute()
      requireLogin(currentRoute)
    },
    
    /**
     * 检查是否有权限执行某个操作
     */
    checkPermission(permission) {
      if (!this.isAuthenticated) {
        this.showLoginRequired()
        return false
      }
      
      // 这里可以添加具体的权限检查逻辑
      return true
    },
    
    /**
     * 显示需要登录的提示
     */
    showLoginRequired() {
      uni.showModal({
        title: '需要登录',
        content: '此功能需要登录后才能使用，是否立即登录？',
        confirmText: '立即登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.forceLogin()
          }
        }
      })
    },
    
    /**
     * 安全获取当前页面路由
     */
    getCurrentPageRoute() {
      try {
        // 尝试从$mp.page获取
        if (this.$mp && this.$mp.page && this.$mp.page.route) {
          return this.$mp.page.route
        }

        // 尝试从getCurrentPages获取
        const pages = getCurrentPages()
        if (pages && pages.length > 0) {
          const currentPage = pages[pages.length - 1]
          return currentPage.route || ''
        }

        return ''
      } catch (error) {
        console.warn('获取页面路由失败:', error)
        return ''
      }
    },

    /**
     * 获取当前用户显示名称
     */
    getCurrentUserName() {
      if (!this.currentUser) return '未登录'
      return this.currentUser.realname || this.currentUser.username || '用户'
    },
    
    /**
     * 获取当前用户头像
     */
    getCurrentUserAvatar() {
      if (!this.currentUser || !this.currentUser.avatar) {
        return '/static/images/default-avatar.png'
      }
      return this.currentUser.avatar
    },
    
    /**
     * 刷新认证状态
     */
    refreshAuthStatus() {
      console.log('🔄 刷新认证状态')
      this.checkAuthStatus()
    },
    
    /**
     * 登出处理
     */
    async handleLogout() {
      try {
        // 这里可以调用登出接口
        // await logout()
        
        // 清除认证状态
        this.isAuthenticated = false
        this.currentUser = null
        this.authToken = null
        
        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        })
        
        // 跳转到首页或登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }, 1500)
        
      } catch (error) {
        console.error('❌ 登出失败:', error)
        uni.showToast({
          title: '登出失败',
          icon: 'none'
        })
      }
    }
  }
}
