/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.api-test-container.data-v-e7967972 {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
}
.test-header.data-v-e7967972 {
  text-align: center;
  margin-bottom: 40rpx;
}
.test-header .test-title.data-v-e7967972 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-e7967972 {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.test-section .section-title.data-v-e7967972 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 16rpx;
}
.config-item.data-v-e7967972 {
  margin-bottom: 24rpx;
}
.config-item .config-label.data-v-e7967972 {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.config-item .config-value.data-v-e7967972 {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}
.headers-display .header-item.data-v-e7967972 {
  display: flex;
  margin-bottom: 12rpx;
}
.headers-display .header-item .header-key.data-v-e7967972 {
  font-size: 24rpx;
  color: #666;
  width: 200rpx;
  flex-shrink: 0;
}
.headers-display .header-item .header-value.data-v-e7967972 {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
}
.test-buttons.data-v-e7967972 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.test-buttons .test-btn.data-v-e7967972 {
  height: 80rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.test-buttons .test-btn.data-v-e7967972:active {
  opacity: 0.8;
}
.result-container.data-v-e7967972 {
  height: 600rpx;
}
.result-container .result-item.data-v-e7967972 {
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}
.result-container .result-item .result-header.data-v-e7967972 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.result-container .result-item .result-header .result-time.data-v-e7967972 {
  font-size: 24rpx;
  color: #999;
}
.result-container .result-item .result-header .result-status.data-v-e7967972 {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}
.result-container .result-item .result-header .result-status.success.data-v-e7967972 {
  background: #f6ffed;
  color: #52c41a;
}
.result-container .result-item .result-header .result-status.error.data-v-e7967972 {
  background: #fff2f0;
  color: #ff4d4f;
}
.result-container .result-item .result-title.data-v-e7967972 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.result-container .result-item .result-message.data-v-e7967972 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.result-container .result-item .result-details .details-title.data-v-e7967972 {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}
.result-container .result-item .result-details .details-content.data-v-e7967972 {
  display: block;
  font-size: 22rpx;
  color: #666;
  background: #f5f5f5;
  padding: 8rpx;
  border-radius: 4rpx;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}