/**
 * 修复tabBar页面跳转错误
 * 清理编译缓存并检查跳转代码
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 修复tabBar页面跳转错误...')

// tabBar页面列表
const tabBarPages = [
  'pages/index/index',
  'pages/course/list',
  'pages/checkin/checkin',
  'pages/leaderboard/leaderboard',
  'pages/profile/profile'
]

// 1. 清理编译缓存
const unpackagePath = path.join(process.cwd(), 'unpackage')
if (fs.existsSync(unpackagePath)) {
  console.log('🗑️ 清理编译缓存...')
  try {
    fs.rmSync(unpackagePath, { recursive: true, force: true })
    console.log('✅ 编译缓存清理完成')
  } catch (error) {
    console.log('⚠️ 编译缓存清理失败:', error.message)
  }
}

// 2. 检查页面中的跳转代码
console.log('\n🔍 检查页面跳转代码...')

const filesToCheck = [
  'pages/index/index.vue',
  'pages/course/list.vue',
  'pages/course/detail.vue',
  'pages/checkin/checkin.vue',
  'pages/leaderboard/leaderboard.vue',
  'pages/profile/profile.vue',
  'pages/practice/practice.vue'
]

let hasIssues = false

filesToCheck.forEach(file => {
  const filePath = path.join(process.cwd(), file)
  
  if (fs.existsSync(filePath)) {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // 检查是否有navigateTo跳转到tabBar页面
      tabBarPages.forEach(tabBarPage => {
        const patterns = [
          new RegExp(`navigateTo.*${tabBarPage.replace('/', '\\/')}`, 'g'),
          new RegExp(`uni\\.navigateTo.*${tabBarPage.replace('/', '\\/')}`, 'g')
        ]
        
        patterns.forEach(pattern => {
          if (pattern.test(content)) {
            console.log(`❌ ${file} - 发现navigateTo跳转到tabBar页面: ${tabBarPage}`)
            hasIssues = true
          }
        })
      })
      
      // 检查是否有switchTab跳转到非tabBar页面
      const switchTabPattern = /switchTab.*url.*['"`]([^'"`]+)['"`]/g
      let match
      while ((match = switchTabPattern.exec(content)) !== null) {
        const url = match[1].replace(/^\//, '')
        if (!tabBarPages.includes(url)) {
          console.log(`❌ ${file} - 发现switchTab跳转到非tabBar页面: ${url}`)
          hasIssues = true
        }
      }
      
      if (!hasIssues) {
        console.log(`✅ ${file} - 跳转代码正确`)
      }
      
    } catch (error) {
      console.log(`⚠️ ${file} - 文件读取失败:`, error.message)
    }
  } else {
    console.log(`⚠️ ${file} - 文件不存在`)
  }
})

// 3. 检查pages.json中的tabBar配置
console.log('\n📋 检查tabBar配置...')

const pagesJsonPath = path.join(process.cwd(), 'pages.json')
if (fs.existsSync(pagesJsonPath)) {
  try {
    const pagesConfig = JSON.parse(fs.readFileSync(pagesJsonPath, 'utf8'))
    
    if (pagesConfig.tabBar && pagesConfig.tabBar.list) {
      console.log('✅ tabBar配置存在')
      
      const configuredTabBarPages = pagesConfig.tabBar.list.map(item => item.pagePath)
      console.log('📄 配置的tabBar页面:', configuredTabBarPages)
      
      // 检查是否与代码中的tabBar页面列表一致
      const missingPages = tabBarPages.filter(page => !configuredTabBarPages.includes(page))
      const extraPages = configuredTabBarPages.filter(page => !tabBarPages.includes(page))
      
      if (missingPages.length > 0) {
        console.log('⚠️ 代码中定义但配置中缺失的tabBar页面:', missingPages)
      }
      
      if (extraPages.length > 0) {
        console.log('⚠️ 配置中存在但代码中未定义的tabBar页面:', extraPages)
      }
      
      if (missingPages.length === 0 && extraPages.length === 0) {
        console.log('✅ tabBar页面列表一致')
      }
      
    } else {
      console.log('❌ pages.json中未找到tabBar配置')
    }
    
  } catch (error) {
    console.log('❌ pages.json解析失败:', error.message)
  }
} else {
  console.log('❌ pages.json文件不存在')
}

// 4. 提供修复建议
console.log('\n💡 修复建议:')

if (hasIssues) {
  console.log('1. 将navigateTo改为switchTab（对于tabBar页面）')
  console.log('2. 使用本地存储传递参数（tabBar页面不支持URL参数）')
  console.log('3. 清理编译缓存后重新编译')
} else {
  console.log('1. 代码检查通过，清理编译缓存后重新编译即可')
}

console.log('\n🚀 修复步骤:')
console.log('1. 停止开发服务器')
console.log('2. 删除unpackage目录（已完成）')
console.log('3. 重新运行: npm run dev:mp-weixin')
console.log('4. 测试页面跳转功能')

console.log('\n🎉 tabBar跳转错误修复完成！')

// 5. 创建智能跳转使用示例
console.log('\n📖 智能跳转使用示例:')
console.log(`
// 使用智能跳转工具
import { navigate } from '@/utils/index.js'

// 自动识别tabBar页面
navigate.to('pages/course/list', { level: 'beginner' })  // 自动使用switchTab

// 普通页面跳转
navigate.to('pages/course/detail', { id: 123 })  // 自动使用navigateTo

// 专门的tabBar跳转
navigate.switchTab('pages/course/list', { level: 'beginner' })
`)

process.exit(hasIssues ? 1 : 0)
