# 口语便利店小程序 🗾

> 专注于日语口语能力提升的学习平台

## 📖 项目简介

"口语便利店"是一款面向日语学习者的小程序，通过每日打卡机制，用户可以进行日语对话和新闻的听力与跟读练习，获得AI评分反馈，并与其他学习者进行简单互动。

## ✨ 核心功能

### 🎯 学习体系
- **四级课程体系**: 初级(N5-N4)、中级(N3-N2)、高级(N1)、专业级
- **免费+付费模式**: 基础课程免费，高级内容付费解锁
- **丰富学习资料**: 音频、文字稿、中文翻译、重点词汇

### 📅 打卡系统
- **每日打卡机制**: 培养学习习惯
- **补打卡功能**: 每月5次补打卡机会
- **学习统计**: 详细的学习数据分析

### 🤖 AI评分
- **智能语音评测**: 基于科大讯飞API
- **多维度评分**: 发音准确度、流利度、语调
- **个性化建议**: AI提供针对性改进建议

### 🏆 社交互动
- **排行榜系统**: 周榜、月榜、总榜
- **学习激励**: 连续打卡奖励机制

## 🎨 UI设计展示

本项目包含7个核心页面的完整UI设计：

1. **首页** - 课程级别选择和学习进度展示
2. **课程列表页** - 课程浏览和进度管理
3. **课程详情页** - 学习材料和内容展示
4. **练习页面** - 跟读练习和AI评分
5. **打卡页面** - 每日打卡和日历统计
6. **排行榜页面** - 用户排名和社交互动
7. **个人中心页面** - 用户信息和设置管理

### 设计特色
- ✅ **375x812px标准尺寸** - 适配主流手机屏幕
- ✅ **清新主义美学** - 柔和渐变配色，优雅视觉体验
- ✅ **Mock-up展示** - 逼真手机外壳效果
- ✅ **响应式设计** - 完美适配不同设备
- ✅ **无障碍设计** - 隐藏滚动条，流畅交互

## 📁 项目结构

```
uni-learningJP/
├── ui/                    # UI设计文件
│   ├── UI.html           # 🎨 UI展示页面 (主入口)
│   ├── 首页.html
│   ├── 课程列表页.html
│   ├── 课程详情页.html
│   ├── 练习页面.html
│   ├── 打卡页面.html
│   ├── 排行榜页面.html
│   └── 个人中心页面.html
├── front/                 # 前端文档
│   └── 产品设计文档.md
├── backend/               # 后端文档
│   └── API设计文档.md
├── sql/                   # 数据库设计
│   └── 数据库设计.sql
├── task.md               # 开发任务清单
└── README.md             # 项目说明文档
```

## 🚀 快速开始

### 查看UI设计
1. 打开 `ui/UI.html` 文件
2. 在浏览器中查看所有页面设计
3. 每个页面都可以独立预览

### 技术栈
- **前端**: UniApp (支持微信、钉钉小程序)
- **后端**: Jeecg-boot (Spring Boot + MyBatis Plus)
- **数据库**: MySQL 8.0
- **AI服务**: 科大讯飞开放平台
- **UI框架**: TailwindCSS + Font Awesome

## 📋 开发进度

- [x] 产品功能设计
- [x] 交互设计
- [x] UI设计 (7个页面)
- [x] UI展示页面
- [x] 代码自检
- [x] 目录构成
- [x] 数据库设计
- [x] API接口设计
- [x] 后端数据库设计
- [x] 后端API开发
- [x] 前端开发
- [x] AI语音评测集成
- [ ] 测试部署

## 🎯 下一步计划

1. ~~**后端数据库设计** - 基于jeecg-boot规范设计数据库~~ ✅ 已完成
2. ~~**前端开发** - 使用UniApp开发小程序~~ ✅ 已完成
3. ~~**后端API开发** - 基于jeecg-boot开发API服务~~ ✅ 已完成
4. ~~**AI语音评测集成** - 接入科大讯飞语音评测API~~ ✅ 已完成
5. **测试优化** - 用户体验测试和性能优化
6. **上线运营** - 小程序发布和推广

## 📞 联系方式

如有任何问题或建议，欢迎联系项目团队。

---

**项目状态**: 前端开发 + 后端开发 + AI集成已完成 ✅
**最后更新**: 2024年3月  
**版本**: v1.0.0
