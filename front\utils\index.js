// 工具函数集合

/**
 * 时间格式化
 */
export const formatTime = (timestamp, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hour = date.getHours().toString().padStart(2, '0')
  const minute = date.getMinutes().toString().padStart(2, '0')
  const second = date.getSeconds().toString().padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

/**
 * 相对时间格式化
 */
export const formatRelativeTime = (timestamp) => {
  if (!timestamp) return ''
  
  const now = new Date()
  const date = new Date(timestamp)
  const diff = now - date
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else {
    return formatTime(timestamp, 'MM-DD')
  }
}

/**
 * 防抖函数
 */
export const debounce = (func, wait, immediate = false) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func(...args)
  }
}

/**
 * 节流函数
 */
export const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 存储管理
 */
export const storage = {
  set(key, value) {
    try {
      uni.setStorageSync(key, JSON.stringify(value))
      return true
    } catch (e) {
      console.error('存储失败:', e)
      return false
    }
  },
  
  get(key, defaultValue = null) {
    try {
      const value = uni.getStorageSync(key)
      return value ? JSON.parse(value) : defaultValue
    } catch (e) {
      console.error('读取存储失败:', e)
      return defaultValue
    }
  },
  
  remove(key) {
    try {
      uni.removeStorageSync(key)
      return true
    } catch (e) {
      console.error('删除存储失败:', e)
      return false
    }
  },
  
  clear() {
    try {
      uni.clearStorageSync()
      return true
    } catch (e) {
      console.error('清空存储失败:', e)
      return false
    }
  }
}

/**
 * 数字格式化
 */
export const formatNumber = (num, precision = 2) => {
  if (isNaN(num)) return '0'
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(precision) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(precision) + 'K'
  } else {
    return num.toString()
  }
}

/**
 * 文件大小格式化
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * URL参数解析
 */
export const parseQuery = (url) => {
  const query = {}
  const queryString = url.split('?')[1]
  
  if (queryString) {
    queryString.split('&').forEach(param => {
      const [key, value] = param.split('=')
      query[decodeURIComponent(key)] = decodeURIComponent(value || '')
    })
  }
  
  return query
}

/**
 * 生成随机字符串
 */
export const randomString = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 验证手机号
 */
export const validatePhone = (phone) => {
  const reg = /^1[3-9]\d{9}$/
  return reg.test(phone)
}

/**
 * 验证邮箱
 */
export const validateEmail = (email) => {
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return reg.test(email)
}

/**
 * 获取系统信息
 */
export const getSystemInfo = () => {
  try {
    return uni.getSystemInfoSync()
  } catch (e) {
    console.error('获取系统信息失败:', e)
    return {}
  }
}

/**
 * 权限检查
 */
export const checkPermission = (scope) => {
  return new Promise((resolve, reject) => {
    uni.getSetting({
      success: (res) => {
        if (res.authSetting[scope]) {
          resolve(true)
        } else {
          uni.authorize({
            scope,
            success: () => resolve(true),
            fail: () => reject(false)
          })
        }
      },
      fail: () => reject(false)
    })
  })
}

/**
 * 图片压缩
 */
export const compressImage = (src, quality = 0.8) => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src,
      quality,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 选择图片
 */
export const chooseImage = (options = {}) => {
  const defaultOptions = {
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera']
  }
  
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      ...defaultOptions,
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 预览图片
 */
export const previewImage = (urls, current = 0) => {
  uni.previewImage({
    urls: Array.isArray(urls) ? urls : [urls],
    current: typeof current === 'number' ? urls[current] : current
  })
}

/**
 * 复制到剪贴板
 */
export const copyToClipboard = (text) => {
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data: text,
      success: () => {
        uni.showToast({
          title: '复制成功',
          icon: 'success'
        })
        resolve(true)
      },
      fail: reject
    })
  })
}

/**
 * 震动反馈
 */
export const vibrate = (type = 'short') => {
  if (type === 'long') {
    uni.vibrateLong()
  } else {
    uni.vibrateShort()
  }
}

/**
 * 页面跳转封装 - 智能识别tabBar页面
 */
export const navigate = {
  // tabBar页面列表
  tabBarPages: [
    'pages/index/index',
    'pages/course/list',
    'pages/checkin/checkin',
    'pages/leaderboard/leaderboard',
    'pages/profile/profile'
  ],

  // 智能跳转 - 自动识别tabBar页面
  to: (url, params = {}) => {
    const cleanUrl = url.startsWith('/') ? url.substring(1) : url
    const isTabBarPage = navigate.tabBarPages.includes(cleanUrl)

    if (isTabBarPage) {
      // tabBar页面使用switchTab，不支持参数
      if (Object.keys(params).length > 0) {
        console.warn('⚠️ tabBar页面不支持URL参数，参数将被忽略:', params)
        // 可以将参数存储到本地存储
        uni.setStorageSync('pageParams', {
          url: cleanUrl,
          params,
          timestamp: Date.now()
        })
      }

      uni.switchTab({
        url: `/${cleanUrl}`,
        success: () => {
          console.log('✅ 成功跳转到tabBar页面:', cleanUrl)
        },
        fail: (error) => {
          console.error('❌ 跳转tabBar页面失败:', error)
        }
      })
    } else {
      // 普通页面使用navigateTo
      const query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
      const fullUrl = query ? `/${cleanUrl}?${query}` : `/${cleanUrl}`

      uni.navigateTo({
        url: fullUrl,
        success: () => {
          console.log('✅ 成功跳转到普通页面:', fullUrl)
        },
        fail: (error) => {
          console.error('❌ 跳转普通页面失败:', error)
        }
      })
    }
  },

  back: (delta = 1) => {
    uni.navigateBack({ delta })
  },

  redirect: (url, params = {}) => {
    const cleanUrl = url.startsWith('/') ? url.substring(1) : url
    const query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
    const fullUrl = query ? `/${cleanUrl}?${query}` : `/${cleanUrl}`
    uni.redirectTo({ url: fullUrl })
  },

  reLaunch: (url, params = {}) => {
    const cleanUrl = url.startsWith('/') ? url.substring(1) : url
    const query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
    const fullUrl = query ? `/${cleanUrl}?${query}` : `/${cleanUrl}`
    uni.reLaunch({ url: fullUrl })
  },

  // 专门的tabBar跳转方法
  switchTab: (url, params = {}) => {
    const cleanUrl = url.startsWith('/') ? url.substring(1) : url

    if (Object.keys(params).length > 0) {
      // 将参数存储到本地存储
      uni.setStorageSync('pageParams', {
        url: cleanUrl,
        params,
        timestamp: Date.now()
      })
    }

    uni.switchTab({
      url: `/${cleanUrl}`,
      success: () => {
        console.log('✅ 成功跳转到tabBar页面:', cleanUrl)
      },
      fail: (error) => {
        console.error('❌ 跳转tabBar页面失败:', error)
      }
    })
  },
  
  switchTab: (url) => {
    uni.switchTab({ url })
  }
}
