/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.json-parse-test-container.data-v-f200c6d7 {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
}
.test-header.data-v-f200c6d7 {
  text-align: center;
  margin-bottom: 40rpx;
}
.test-header .test-title.data-v-f200c6d7 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-f200c6d7 {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.test-section .section-title.data-v-f200c6d7 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 16rpx;
}
.storage-status .status-item.data-v-f200c6d7, .storage-status .test-item.data-v-f200c6d7, .safe-get-test .status-item.data-v-f200c6d7, .safe-get-test .test-item.data-v-f200c6d7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.storage-status .status-item.data-v-f200c6d7:last-child, .storage-status .test-item.data-v-f200c6d7:last-child, .safe-get-test .status-item.data-v-f200c6d7:last-child, .safe-get-test .test-item.data-v-f200c6d7:last-child {
  border-bottom: none;
}
.storage-status .status-item .status-label.data-v-f200c6d7, .storage-status .status-item .test-label.data-v-f200c6d7, .storage-status .test-item .status-label.data-v-f200c6d7, .storage-status .test-item .test-label.data-v-f200c6d7, .safe-get-test .status-item .status-label.data-v-f200c6d7, .safe-get-test .status-item .test-label.data-v-f200c6d7, .safe-get-test .test-item .status-label.data-v-f200c6d7, .safe-get-test .test-item .test-label.data-v-f200c6d7 {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
  flex-shrink: 0;
}
.storage-status .status-item .status-value.data-v-f200c6d7, .storage-status .status-item .test-result.data-v-f200c6d7, .storage-status .test-item .status-value.data-v-f200c6d7, .storage-status .test-item .test-result.data-v-f200c6d7, .safe-get-test .status-item .status-value.data-v-f200c6d7, .safe-get-test .status-item .test-result.data-v-f200c6d7, .safe-get-test .test-item .status-value.data-v-f200c6d7, .safe-get-test .test-item .test-result.data-v-f200c6d7 {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}
.storage-status .status-item .status-value.success.data-v-f200c6d7, .storage-status .status-item .test-result.success.data-v-f200c6d7, .storage-status .test-item .status-value.success.data-v-f200c6d7, .storage-status .test-item .test-result.success.data-v-f200c6d7, .safe-get-test .status-item .status-value.success.data-v-f200c6d7, .safe-get-test .status-item .test-result.success.data-v-f200c6d7, .safe-get-test .test-item .status-value.success.data-v-f200c6d7, .safe-get-test .test-item .test-result.success.data-v-f200c6d7 {
  color: #52c41a;
}
.storage-status .status-item .status-value.error.data-v-f200c6d7, .storage-status .status-item .test-result.error.data-v-f200c6d7, .storage-status .test-item .status-value.error.data-v-f200c6d7, .storage-status .test-item .test-result.error.data-v-f200c6d7, .safe-get-test .status-item .status-value.error.data-v-f200c6d7, .safe-get-test .status-item .test-result.error.data-v-f200c6d7, .safe-get-test .test-item .status-value.error.data-v-f200c6d7, .safe-get-test .test-item .test-result.error.data-v-f200c6d7 {
  color: #ff4d4f;
}
.mock-test.data-v-f200c6d7, .recovery-test.data-v-f200c6d7 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.mock-test .test-btn.data-v-f200c6d7, .recovery-test .test-btn.data-v-f200c6d7 {
  height: 80rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}
.mock-test .test-btn.primary.data-v-f200c6d7, .recovery-test .test-btn.primary.data-v-f200c6d7 {
  background: #1890ff;
}
.mock-test .test-btn.success.data-v-f200c6d7, .recovery-test .test-btn.success.data-v-f200c6d7 {
  background: #52c41a;
}
.mock-test .test-btn.warning.data-v-f200c6d7, .recovery-test .test-btn.warning.data-v-f200c6d7 {
  background: #faad14;
}
.mock-test .test-btn.error.data-v-f200c6d7, .recovery-test .test-btn.error.data-v-f200c6d7 {
  background: #ff4d4f;
}
.mock-test .test-btn.data-v-f200c6d7:active, .recovery-test .test-btn.data-v-f200c6d7:active {
  opacity: 0.8;
}
.raw-data-container.data-v-f200c6d7 {
  height: 200rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 16rpx;
}
.raw-data-container .raw-data-item.data-v-f200c6d7 {
  margin-bottom: 16rpx;
}
.raw-data-container .raw-data-item .data-label.data-v-f200c6d7 {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.raw-data-container .raw-data-item .data-value.data-v-f200c6d7 {
  font-size: 22rpx;
  color: #333;
  font-family: monospace;
  word-break: break-all;
  background: #f5f5f5;
  padding: 8rpx;
  border-radius: 4rpx;
}
.log-container.data-v-f200c6d7 {
  height: 300rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 16rpx;
}
.log-container .log-item.data-v-f200c6d7 {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 24rpx;
}
.log-container .log-item .log-time.data-v-f200c6d7 {
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
}
.log-container .log-item .log-message.data-v-f200c6d7 {
  color: #333;
  word-break: break-all;
}
.log-container .log-item .log-message.success.data-v-f200c6d7 {
  color: #52c41a;
}
.log-container .log-item .log-message.error.data-v-f200c6d7 {
  color: #ff4d4f;
}
.log-container .log-item .log-message.warning.data-v-f200c6d7 {
  color: #faad14;
}
.log-container .log-item .log-message.info.data-v-f200c6d7 {
  color: #1890ff;
}