<template>
	<view class="audio-player">
		<!-- 播放器头部 -->
		<view class="player-header">
			<view class="player-info">
				<text class="player-title">{{ title }}</text>
				<text class="player-duration">时长: {{ formatTime(duration) }}</text>
			</view>
			<view class="play-button" @click="togglePlay">
				<text class="iconfont" :class="isPlaying ? 'icon-pause' : 'icon-play'"></text>
			</view>
		</view>
		
		<!-- 波形图/进度条 -->
		<view class="waveform" @click="seekAudio">
			<view class="waveform-progress" :style="{ width: progress + '%' }"></view>
		</view>
		
		<!-- 控制栏 -->
		<view class="player-controls">
			<text class="time-text">{{ formatTime(currentTime) }}</text>
			<view class="control-buttons">
				<text class="iconfont icon-backward" @click="seekBackward"></text>
				<text class="iconfont icon-forward" @click="seekForward"></text>
				<text class="iconfont icon-repeat" @click="toggleRepeat" :class="{ active: isRepeat }"></text>
			</view>
			<text class="time-text">{{ formatTime(duration) }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'AudioPlayer',
	props: {
		// 音频标题
		title: {
			type: String,
			default: '音频播放'
		},
		// 音频URL
		src: {
			type: String,
			required: true
		},
		// 是否自动播放
		autoplay: {
			type: Boolean,
			default: false
		},
		// 是否显示控制按钮
		showControls: {
			type: Boolean,
			default: true
		}
	},
	
	data() {
		return {
			audioContext: null,
			isPlaying: false,
			isRepeat: false,
			currentTime: 0,
			duration: 0,
			progress: 0,
			timer: null
		}
	},
	
	mounted() {
		this.initAudio()
	},
	
	beforeUnmount() {
		this.destroyAudio()
	},
	
	methods: {
		initAudio() {
			// 创建音频上下文
			this.audioContext = uni.createInnerAudioContext()
			this.audioContext.src = this.src
			
			// 监听事件
			this.audioContext.onCanplay(() => {
				this.duration = this.audioContext.duration
			})
			
			this.audioContext.onPlay(() => {
				this.isPlaying = true
				this.startProgressTimer()
				this.$emit('play')
			})
			
			this.audioContext.onPause(() => {
				this.isPlaying = false
				this.stopProgressTimer()
				this.$emit('pause')
			})
			
			this.audioContext.onStop(() => {
				this.isPlaying = false
				this.stopProgressTimer()
				this.$emit('stop')
			})
			
			this.audioContext.onEnded(() => {
				this.isPlaying = false
				this.stopProgressTimer()
				if (this.isRepeat) {
					this.play()
				}
				this.$emit('ended')
			})
			
			this.audioContext.onError((err) => {
				console.error('音频播放错误:', err)
				this.$emit('error', err)
			})
			
			// 自动播放
			if (this.autoplay) {
				this.play()
			}
		},
		
		destroyAudio() {
			if (this.audioContext) {
				this.audioContext.destroy()
				this.audioContext = null
			}
			this.stopProgressTimer()
		},
		
		play() {
			if (this.audioContext) {
				this.audioContext.play()
			}
		},
		
		pause() {
			if (this.audioContext) {
				this.audioContext.pause()
			}
		},
		
		stop() {
			if (this.audioContext) {
				this.audioContext.stop()
			}
		},
		
		togglePlay() {
			if (this.isPlaying) {
				this.pause()
			} else {
				this.play()
			}
		},
		
		seekTo(time) {
			if (this.audioContext) {
				this.audioContext.seek(time)
				this.currentTime = time
				this.updateProgress()
			}
		},
		
		seekBackward() {
			const newTime = Math.max(0, this.currentTime - 10)
			this.seekTo(newTime)
		},
		
		seekForward() {
			const newTime = Math.min(this.duration, this.currentTime + 10)
			this.seekTo(newTime)
		},
		
		toggleRepeat() {
			this.isRepeat = !this.isRepeat
			this.$emit('repeatChange', this.isRepeat)
		},
		
		seekAudio(e) {
			// 点击进度条跳转
			const rect = e.currentTarget.getBoundingClientRect()
			const clickX = e.detail.x - rect.left
			const progress = clickX / rect.width
			const newTime = this.duration * progress
			this.seekTo(newTime)
		},
		
		startProgressTimer() {
			this.stopProgressTimer()
			this.timer = setInterval(() => {
				if (this.audioContext) {
					this.currentTime = this.audioContext.currentTime
					this.updateProgress()
				}
			}, 1000)
		},
		
		stopProgressTimer() {
			if (this.timer) {
				clearInterval(this.timer)
				this.timer = null
			}
		},
		
		updateProgress() {
			if (this.duration > 0) {
				this.progress = (this.currentTime / this.duration) * 100
			}
		},
		
		formatTime(seconds) {
			if (!seconds || isNaN(seconds)) return '0:00'
			const mins = Math.floor(seconds / 60)
			const secs = Math.floor(seconds % 60)
			return `${mins}:${secs.toString().padStart(2, '0')}`
		}
	},
	
	watch: {
		src(newSrc) {
			if (this.audioContext) {
				this.audioContext.src = newSrc
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.audio-player {
	background: white;
	border-radius: 32rpx;
	padding: 48rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.player-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.player-info {
	flex: 1;
	
	.player-title {
		display: block;
		font-weight: bold;
		color: #333;
		font-size: 32rpx;
		margin-bottom: 8rpx;
	}
	
	.player-duration {
		color: #666;
		font-size: 28rpx;
	}
}

.play-button {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: transform 0.2s;
	
	&:active {
		transform: scale(0.95);
	}
	
	.iconfont {
		color: white;
		font-size: 48rpx;
	}
}

.waveform {
	height: 80rpx;
	background: #e5e7eb;
	border-radius: 40rpx;
	position: relative;
	overflow: hidden;
	margin: 32rpx 0;
	cursor: pointer;
	
	.waveform-progress {
		height: 100%;
		background: linear-gradient(90deg, #4facfe, #00f2fe);
		border-radius: 40rpx;
		transition: width 0.3s ease;
	}
}

.player-controls {
	display: flex;
	justify-content: space-between;
	align-items: center;
	
	.time-text {
		color: #666;
		font-size: 28rpx;
		min-width: 80rpx;
	}
	
	.control-buttons {
		display: flex;
		align-items: center;
		gap: 32rpx;
		
		.iconfont {
			color: #666;
			font-size: 36rpx;
			padding: 16rpx;
			transition: color 0.2s;
			
			&:active {
				color: #4facfe;
			}
			
			&.active {
				color: #4facfe;
			}
		}
	}
}

/* 字体图标 */
.icon-play::before { content: '\e006'; }
.icon-pause::before { content: '\e012'; }
.icon-backward::before { content: '\e013'; }
.icon-forward::before { content: '\e014'; }
.icon-repeat::before { content: '\e015'; }
</style>
