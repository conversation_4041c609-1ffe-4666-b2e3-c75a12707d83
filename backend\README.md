# 口语便利店小程序后端

基于jeecg-boot框架开发的日语学习打卡小程序后端服务。

## 项目结构

```
backend/
├── src/main/java/org/jeecg/modules/kuma/
│   ├── entity/           # 实体类
│   │   ├── KumaUserProfile.java
│   │   ├── KumaCourse.java
│   │   ├── KumaUserStudyRecord.java
│   │   ├── KumaCheckinRecord.java
│   │   ├── KumaPracticeRecord.java
│   │   ├── KumaOrder.java
│   │   ├── KumaPurchaseRecord.java
│   │   ├── KumaLeaderboard.java
│   │   ├── KumaUserFavorite.java
│   │   ├── KumaSystemConfig.java
│   │   └── KumaFeedback.java
│   ├── mapper/           # Mapper接口
│   ├── service/          # 服务层
│   ├── controller/       # 控制器
│   ├── vo/              # 视图对象
│   └── dto/             # 数据传输对象
├── src/main/resources/
│   ├── mapper/          # MyBatis映射文件
│   └── application.yml  # 配置文件
└── pom.xml              # Maven依赖
```

## 技术栈

- **框架**: jeecg-boot 3.x
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus
- **缓存**: Redis
- **消息队列**: RabbitMQ (可选)
- **文件存储**: 阿里云OSS / 本地存储
- **AI接口**: 科大讯飞语音评测API

## 核心功能模块

### 1. 用户管理模块
- 用户注册登录 (集成jeecg-boot用户系统)
- 用户信息管理
- 学习数据统计

### 2. 课程管理模块
- 课程CRUD操作
- 课程分级管理
- 学习进度跟踪

### 3. 打卡系统模块
- 每日打卡功能
- 打卡记录管理
- 补打卡机制
- 排行榜统计

### 4. 练习系统模块
- 录音上传处理
- AI语音评测集成
- 练习记录管理
- 成绩统计分析

### 5. 支付系统模块
- 订单管理
- 微信支付集成
- VIP会员管理

### 6. 系统管理模块
- 系统配置管理
- 意见反馈处理
- 数据统计分析

## 数据库设计

### 表结构规范
- 表名前缀: `kuma_`
- 主键类型: `VARCHAR(32)`
- 通用字段: `sys_sign VARCHAR(4)`
- 标准字段: `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`

### 核心表说明
1. **kuma_user_profile** - 用户扩展信息
2. **kuma_course** - 课程信息
3. **kuma_user_study_record** - 学习记录
4. **kuma_checkin_record** - 打卡记录
5. **kuma_practice_record** - 练习记录
6. **kuma_order** - 订单信息
7. **kuma_purchase_record** - 购买记录
8. **kuma_leaderboard** - 排行榜
9. **kuma_user_favorite** - 用户收藏
10. **kuma_system_config** - 系统配置
11. **kuma_feedback** - 意见反馈

## API接口设计

### 用户相关接口
- `POST /kuma/auth/login` - 用户登录
- `GET /kuma/user/profile` - 获取用户信息
- `PUT /kuma/user/profile` - 更新用户信息
- `POST /kuma/user/avatar` - 上传头像

### 课程相关接口
- `GET /kuma/course/list` - 获取课程列表
- `GET /kuma/course/{id}` - 获取课程详情
- `POST /kuma/course/progress` - 更新学习进度
- `POST /kuma/course/favorite` - 收藏/取消收藏

### 打卡相关接口
- `POST /kuma/checkin` - 每日打卡
- `POST /kuma/checkin/makeup` - 补打卡
- `GET /kuma/checkin/records` - 获取打卡记录
- `GET /kuma/checkin/statistics` - 获取打卡统计

### 练习相关接口
- `POST /kuma/practice/submit` - 提交练习录音
- `GET /kuma/practice/history` - 获取练习历史
- `GET /kuma/practice/statistics` - 获取练习统计

### 排行榜接口
- `GET /kuma/leaderboard` - 获取排行榜
- `GET /kuma/leaderboard/user` - 获取用户排名

### 支付相关接口
- `POST /kuma/order/create` - 创建订单
- `POST /kuma/payment/notify` - 支付回调
- `GET /kuma/order/status` - 查询订单状态

## 开发规范

### 代码规范
- 遵循jeecg-boot代码规范
- 使用MyBatis-Plus进行数据库操作
- 统一异常处理和返回格式
- 接口文档使用Swagger生成

### 安全规范
- 接口权限控制
- 参数校验和SQL注入防护
- 敏感数据加密存储
- 接口限流和防刷

### 性能优化
- 数据库查询优化
- Redis缓存策略
- 文件上传优化
- 接口响应时间监控

## 部署说明

### 环境要求
- JDK 1.8+
- MySQL 8.0+
- Redis 6.0+
- Nginx (反向代理)

### 配置文件
```yaml
# application.yml
server:
  port: 8080

spring:
  datasource:
    url: *********************************************
    username: root
    password: password
  redis:
    host: localhost
    port: 6379

# 业务配置
kuma:
  ai:
    provider: xunfei
    appid: ${AI_APPID}
    secret: ${AI_SECRET}
  payment:
    wechat:
      mchid: ${WECHAT_MCHID}
      key: ${WECHAT_KEY}
  file:
    upload-path: /data/uploads/
    domain: https://api.example.com
```

### 部署步骤
1. 创建数据库并执行SQL脚本
2. 配置application.yml
3. 打包项目: `mvn clean package`
4. 部署jar包: `java -jar kuma-backend.jar`
5. 配置Nginx反向代理

## 开发计划

### 第一阶段 - 基础功能
- [x] 数据库设计
- [ ] 实体类和Mapper
- [ ] 用户管理接口
- [ ] 课程管理接口
- [ ] 打卡功能接口

### 第二阶段 - 核心功能
- [ ] 练习系统接口
- [ ] AI语音评测集成
- [ ] 排行榜功能
- [ ] 文件上传处理

### 第三阶段 - 高级功能
- [ ] 支付系统集成
- [ ] VIP会员功能
- [ ] 数据统计分析
- [ ] 系统监控告警

### 第四阶段 - 优化部署
- [ ] 性能优化
- [ ] 安全加固
- [ ] 生产环境部署
- [ ] 运维监控

**当前状态**: 数据库设计完成 ✅

## 联系方式

如有问题或建议，请联系开发团队。
