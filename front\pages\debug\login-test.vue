<template>
	<view class="login-test-container">
		<view class="test-header">
			<text class="test-title">登录功能测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">认证状态</text>
			<view class="status-item">
				<text class="status-label">登录状态:</text>
				<text class="status-value" :class="isAuthenticated ? 'success' : 'error'">
					{{ isAuthenticated ? '已登录' : '未登录' }}
				</text>
			</view>
			<view class="status-item">
				<text class="status-label">用户信息:</text>
				<text class="status-value">{{ currentUser ? currentUser.username : '无' }}</text>
			</view>
			<view class="status-item">
				<text class="status-label">Token:</text>
				<text class="status-value token">{{ authToken ? authToken.substring(0, 20) + '...' : '无' }}</text>
			</view>
			<view class="status-item">
				<text class="status-label">验证码Path参数:</text>
				<text class="status-value">{{ captchaPathKey }}</text>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">请求头测试</text>
			<view class="headers-display">
				<view class="header-item">
					<text class="header-key">X-Access-Token:</text>
					<text class="header-value">{{ testHeaders['X-Access-Token'] || '无' }}</text>
				</view>
				<view class="header-item">
					<text class="header-key">X-Access-Client:</text>
					<text class="header-value">{{ testHeaders['X-Access-Client'] || '无' }}</text>
				</view>
				<view class="header-item">
					<text class="header-key">X-Tenant-Id:</text>
					<text class="header-value">{{ testHeaders['X-Tenant-Id'] || '无' }}</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">验证码测试</text>
			<view class="captcha-test">
				<view class="captcha-display">
					<image v-if="testCaptchaImage" :src="testCaptchaImage" class="test-captcha-img"></image>
					<text v-else class="no-captcha">暂无验证码</text>
				</view>
				<view class="captcha-actions">
					<button class="test-btn small" @tap="testGetCaptcha">获取验证码</button>
					<button class="test-btn small" @tap="testRefreshCaptcha">刷新验证码</button>
					<button class="test-btn small" @tap="testGeneratePathKey">生成新Path</button>
					<button class="test-btn small" @tap="testResetPathKey">重置Path</button>
				</view>
			</view>
		</view>

		<view class="test-section">
			<text class="section-title">功能测试</text>
			<view class="test-buttons">
				<button class="test-btn" @tap="testLogin">测试登录</button>
				<button class="test-btn" @tap="testLogout">测试登出</button>
				<button class="test-btn" @tap="testAuthRequired">测试需要登录</button>
				<button class="test-btn" @tap="testApiCall">测试API调用</button>
				<button class="test-btn" @tap="refreshStatus">刷新状态</button>
				<button class="test-btn danger" @tap="clearAuth">清除认证</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试日志</text>
			<scroll-view class="log-container" scroll-y>
				<view class="log-item" v-for="(log, index) in testLogs" :key="index">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message" :class="log.type">{{ log.message }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import authMixin from '@/mixins/auth-mixin.js'
import * as jeecgUser from '@/api/jeecg-user.js'
import { userApi } from '@/api/index.js'
import { getToken } from '@/utils/auth.js'
import { getCaptcha, refreshCaptcha, getCaptchaImageUrl, getCaptchaKey, getPathKey, setPathKey, generatePathKey } from '@/utils/captcha.js'

export default {
	mixins: [authMixin],
	
	data() {
		return {
			authRequired: false, // 此页面不需要登录
			testHeaders: {},
			testLogs: [],
			testCaptchaImage: '',
			captchaPathKey: '1629428467008'
		}
	},
	
	onLoad() {
		this.addLog('页面加载', 'info')
		this.updateTestHeaders()
	},
	
	methods: {
		// 认证检查完成回调
		onAuthReady() {
			this.addLog('认证检查完成', 'success')
			this.updateTestHeaders()
			this.updateCaptchaInfo()
		},
		
		// 更新测试请求头
		updateTestHeaders() {
			const token = getToken()
			this.testHeaders = {
				'X-Access-Token': token || '',
				'X-Access-Client': 'miniApp',
				'X-Tenant-Id': ''
			}
		},

		// 更新验证码信息
		updateCaptchaInfo() {
			this.captchaPathKey = getPathKey()
		},
		
		// 添加日志
		addLog(message, type = 'info') {
			const now = new Date()
			const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
			
			this.testLogs.unshift({
				time,
				message,
				type
			})
			
			// 限制日志数量
			if (this.testLogs.length > 50) {
				this.testLogs = this.testLogs.slice(0, 50)
			}
		},
		
		// 测试获取验证码
		async testGetCaptcha() {
			this.addLog('开始测试获取验证码', 'info')

			try {
				const result = await getCaptcha(true)

				if (result.success) {
					this.testCaptchaImage = getCaptchaImageUrl()
					this.addLog('验证码获取成功', 'success')
					this.addLog(`验证码Key: ${getCaptchaKey()}`, 'info')
					this.addLog(`图片格式: ${this.testCaptchaImage ? this.testCaptchaImage.substring(0, 30) + '...' : '无'}`, 'info')
				} else {
					this.addLog(`验证码获取失败: ${result.message}`, 'error')
				}
			} catch (error) {
				this.addLog(`验证码获取异常: ${error.message}`, 'error')
			}
		},

		// 测试刷新验证码
		async testRefreshCaptcha() {
			this.addLog('开始测试刷新验证码', 'info')

			try {
				const result = await refreshCaptcha()

				if (result.success) {
					this.testCaptchaImage = getCaptchaImageUrl()
					this.updateCaptchaInfo()
					this.addLog('验证码刷新成功', 'success')
				} else {
					this.addLog(`验证码刷新失败: ${result.message}`, 'error')
				}
			} catch (error) {
				this.addLog(`验证码刷新异常: ${error.message}`, 'error')
			}
		},

		// 测试生成新的Path参数
		testGeneratePathKey() {
			this.addLog('生成新的Path参数', 'info')

			const newKey = generatePathKey()
			setPathKey(newKey)
			this.updateCaptchaInfo()

			this.addLog(`新Path参数: ${newKey}`, 'success')
		},

		// 测试重置Path参数
		testResetPathKey() {
			this.addLog('重置Path参数为默认值', 'info')

			setPathKey('1629428467008')
			this.updateCaptchaInfo()

			this.addLog('Path参数已重置为: 1629428467008', 'success')
		},

		// 测试登录
		async testLogin() {
			this.addLog('开始测试登录', 'info')

			try {
				// 先获取验证码
				await this.testGetCaptcha()

				const result = await jeecgUser.login({
					username: 'admin',
					password: '123456',
					captcha: '1234',  // 测试验证码
					checkKey: getCaptchaKey()
				})

				if (result.success) {
					this.addLog('登录测试成功', 'success')
					this.refreshAuthStatus()
					this.updateTestHeaders()
				} else {
					this.addLog(`登录测试失败: ${result.message}`, 'error')
				}
			} catch (error) {
				this.addLog(`登录测试异常: ${error.message}`, 'error')
			}
		},
		
		// 测试登出
		async testLogout() {
			this.addLog('开始测试登出', 'info')
			
			try {
				const result = await jeecgUser.logout()
				
				if (result.success) {
					this.addLog('登出测试成功', 'success')
					this.refreshAuthStatus()
					this.updateTestHeaders()
				} else {
					this.addLog(`登出测试失败: ${result.message}`, 'error')
				}
			} catch (error) {
				this.addLog(`登出测试异常: ${error.message}`, 'error')
			}
		},
		
		// 测试需要登录的功能
		testAuthRequired() {
			this.addLog('测试需要登录的功能', 'info')
			
			if (this.checkPermission('test')) {
				this.addLog('权限检查通过', 'success')
			} else {
				this.addLog('权限检查失败，需要登录', 'warning')
			}
		},
		
		// 测试API调用
		async testApiCall() {
			this.addLog('开始测试API调用', 'info')
			
			try {
				const response = await userApi.getUserInfo()
				
				if (response.success) {
					this.addLog('API调用成功', 'success')
					this.addLog(`用户信息: ${JSON.stringify(response.data)}`, 'info')
				} else {
					this.addLog(`API调用失败: ${response.message}`, 'error')
				}
			} catch (error) {
				this.addLog(`API调用异常: ${error.message}`, 'error')
				
				// 如果是401错误，说明认证失败处理正常
				if (error.message.includes('登录已过期')) {
					this.addLog('认证失败处理正常', 'success')
				}
			}
		},
		
		// 刷新状态
		refreshStatus() {
			this.addLog('刷新认证状态', 'info')
			this.refreshAuthStatus()
			this.updateTestHeaders()
		},
		
		// 清除认证
		clearAuth() {
			this.addLog('清除认证信息', 'warning')
			
			uni.removeStorageSync('X-Access-Token')
			uni.removeStorageSync('userInfo')
			
			this.refreshAuthStatus()
			this.updateTestHeaders()
		},
		
		// 跳转到登录页面
		goToLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.login-test-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.test-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 24rpx;
		border-bottom: 2rpx solid #eee;
		padding-bottom: 16rpx;
	}
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
	
	.status-label {
		font-size: 28rpx;
		color: #666;
	}
	
	.status-value {
		font-size: 28rpx;
		color: #333;
		
		&.success {
			color: #52c41a;
		}
		
		&.error {
			color: #ff4d4f;
		}
		
		&.token {
			font-family: monospace;
			font-size: 24rpx;
			max-width: 300rpx;
			overflow: hidden;
		}
	}
}

.headers-display {
	.header-item {
		display: flex;
		margin-bottom: 16rpx;
		
		.header-key {
			font-size: 28rpx;
			color: #666;
			width: 200rpx;
			flex-shrink: 0;
		}
		
		.header-value {
			font-size: 28rpx;
			color: #333;
			font-family: monospace;
			word-break: break-all;
		}
	}
}

.captcha-test {
	.captcha-display {
		text-align: center;
		margin-bottom: 24rpx;

		.test-captcha-img {
			width: 200rpx;
			height: 80rpx;
			border: 1rpx solid #ddd;
			border-radius: 8rpx;
		}

		.no-captcha {
			display: inline-block;
			width: 200rpx;
			height: 80rpx;
			line-height: 80rpx;
			background: #f5f5f5;
			color: #999;
			font-size: 24rpx;
			border-radius: 8rpx;
		}
	}

	.captcha-actions {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 16rpx;
		justify-content: center;
	}
}

.test-buttons {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;

	.test-btn {
		height: 80rpx;
		background: #1890ff;
		color: white;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;

		&.small {
			height: 60rpx;
			font-size: 24rpx;
		}

		&.danger {
			background: #ff4d4f;
		}

		&:active {
			opacity: 0.8;
		}
	}
}

.log-container {
	height: 400rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	padding: 16rpx;
	
	.log-item {
		display: flex;
		margin-bottom: 12rpx;
		font-size: 24rpx;
		
		.log-time {
			color: #999;
			width: 120rpx;
			flex-shrink: 0;
		}
		
		.log-message {
			color: #333;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
			
			&.warning {
				color: #faad14;
			}
			
			&.info {
				color: #1890ff;
			}
		}
	}
}
</style>
