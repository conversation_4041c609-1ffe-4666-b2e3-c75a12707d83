package com.learning.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.learning.config.XunfeiConfig;
import com.learning.dto.IseRequest;
import com.learning.dto.IseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.websocket.*;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 科大讯飞WebSocket客户端
 * 注意：这是一个高级实现示例，需要添加WebSocket客户端依赖
 * <AUTHOR>
 * @since 2024-03-22
 */
@Slf4j
@Component
public class XunfeiWebSocketClient {
    
    @Autowired
    private XunfeiConfig xunfeiConfig;
    
    /**
     * 使用WebSocket进行语音评测
     * 注意：这需要添加WebSocket客户端依赖，如 Java-WebSocket
     */
    public CompletableFuture<IseResponse> evaluateAsync(IseRequest request) {
        CompletableFuture<IseResponse> future = new CompletableFuture<>();
        
        try {
            // 生成鉴权URL
            String authUrl = generateAuthUrl();
            
            // 创建WebSocket连接
            URI serverUri = new URI(authUrl);
            
            // 这里需要使用实际的WebSocket客户端库
            // 例如：Java-WebSocket、Tyrus等
            // 以下是伪代码示例
            
            /*
            WebSocketClient client = new WebSocketClient(serverUri) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info("WebSocket连接已建立");
                    
                    // 发送评测参数
                    JSONObject params = buildEvaluationParams(request);
                    send(params.toJSONString());
                    
                    // 发送音频数据
                    sendAudioData(request.getAudioData());
                }
                
                @Override
                public void onMessage(String message) {
                    log.info("收到消息: {}", message);
                    
                    try {
                        IseResponse response = JSON.parseObject(message, IseResponse.class);
                        if (response.getData() != null && response.getData().getStatus() == 2) {
                            // 收到最终结果
                            future.complete(response);
                            close();
                        }
                    } catch (Exception e) {
                        future.completeExceptionally(e);
                    }
                }
                
                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.info("WebSocket连接已关闭: code={}, reason={}", code, reason);
                }
                
                @Override
                public void onError(Exception ex) {
                    log.error("WebSocket连接错误", ex);
                    future.completeExceptionally(ex);
                }
            };
            
            client.connect();
            */
            
            // 由于没有实际的WebSocket客户端依赖，这里返回模拟结果
            IseResponse mockResponse = createMockResponse();
            future.complete(mockResponse);
            
        } catch (Exception e) {
            log.error("WebSocket评测失败", e);
            future.completeExceptionally(e);
        }
        
        return future;
    }
    
    /**
     * 生成鉴权URL
     */
    private String generateAuthUrl() throws Exception {
        String host = "ise-api.xfyun.cn";
        String path = "/v2/open-ise";
        String method = "GET";
        
        // 生成RFC1123格式的时间戳
        SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        format.setTimeZone(TimeZone.getTimeZone("GMT"));
        String date = format.format(new Date());
        
        // 拼接字符串
        String preStr = "host: " + host + "\n" +
                       "date: " + date + "\n" +
                       method + " " + path + " HTTP/1.1";
        
        // 进行hmac-sha256加密
        Mac mac = Mac.getInstance("hmacsha256");
        SecretKeySpec spec = new SecretKeySpec(xunfeiConfig.getApiSecret().getBytes(StandardCharsets.UTF_8), "hmacsha256");
        mac.init(spec);
        byte[] hexDigits = mac.doFinal(preStr.getBytes(StandardCharsets.UTF_8));
        String sha = Base64.getEncoder().encodeToString(hexDigits);
        
        // 拼接authorization
        String authorization = String.format("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
                xunfeiConfig.getApiKey(), "hmac-sha256", "host date request-line", sha);
        
        // 生成URL
        String authUrl = "wss://" + host + path + "?" +
                "authorization=" + URLEncoder.encode(Base64.getEncoder().encodeToString(authorization.getBytes(StandardCharsets.UTF_8)), "UTF-8") +
                "&date=" + URLEncoder.encode(date, "UTF-8") +
                "&host=" + URLEncoder.encode(host, "UTF-8");
        
        return authUrl;
    }
    
    /**
     * 构建评测参数
     */
    private JSONObject buildEvaluationParams(IseRequest request) {
        JSONObject params = new JSONObject();
        
        // 通用参数
        JSONObject common = new JSONObject();
        common.put("app_id", xunfeiConfig.getAppId());
        params.put("common", common);
        
        // 业务参数
        JSONObject business = new JSONObject();
        business.put("category", request.getCategory());
        business.put("group", request.getGroup());
        business.put("language", request.getLanguage());
        business.put("check_type", request.getCheckType());
        business.put("result_encoding", request.getResultEncoding());
        business.put("extra_ability", request.getExtraAbility());
        business.put("text", Base64.getEncoder().encodeToString(request.getText().getBytes(StandardCharsets.UTF_8)));
        params.put("business", business);
        
        return params;
    }
    
    /**
     * 创建模拟响应
     */
    private IseResponse createMockResponse() {
        IseResponse response = new IseResponse();
        response.setCode(0);
        response.setMessage("success");
        response.setSid("ise" + System.currentTimeMillis());
        
        IseResponse.IseResultData data = new IseResponse.IseResultData();
        data.setStatus(2);
        
        // 生成模拟的评测结果XML
        Random random = new Random();
        int totalScore = 75 + random.nextInt(20); // 75-94分
        int pronunciationScore = totalScore + random.nextInt(6) - 3;
        int fluencyScore = totalScore + random.nextInt(6) - 3;
        int toneScore = totalScore + random.nextInt(6) - 3;
        
        String mockXml = String.format(
            "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
            "<FinalResult>" +
            "<ret value=\"0\"/>" +
            "<total_score>%.1f</total_score>" +
            "<phone_score>%.1f</phone_score>" +
            "<fluency_score>%.1f</fluency_score>" +
            "<tone_score>%.1f</tone_score>" +
            "<accuracy_score>%.1f</accuracy_score>" +
            "<integrity_score>%.1f</integrity_score>" +
            "<standard_score>%.1f</standard_score>" +
            "<is_rejected>false</is_rejected>" +
            "<except_info></except_info>" +
            "</FinalResult>",
            (double) totalScore,
            (double) pronunciationScore,
            (double) fluencyScore,
            (double) toneScore,
            (double) totalScore,
            (double) totalScore,
            (double) totalScore
        );
        
        data.setData(Base64.getEncoder().encodeToString(mockXml.getBytes(StandardCharsets.UTF_8)));
        response.setData(data);
        
        return response;
    }
}
