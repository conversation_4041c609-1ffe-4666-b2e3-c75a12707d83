{"version": 3, "file": "vendor.js", "sources": ["../../../develop/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-i18n/dist/uni-i18n.es.js", "../../../develop/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-mp-vue/dist/vue.runtime.esm.js", "../../../develop/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-mp-weixin/dist/uni.api.esm.js", "../../../develop/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/dist/index.esm.js", "../../../develop/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-mp-weixin/dist/uni.mp.esm.js"], "sourcesContent": ["const isObject = (val) => val !== null && typeof val === 'object';\nconst defaultDelimiters = ['{', '}'];\nclass BaseFormatter {\n    constructor() {\n        this._caches = Object.create(null);\n    }\n    interpolate(message, values, delimiters = defaultDelimiters) {\n        if (!values) {\n            return [message];\n        }\n        let tokens = this._caches[message];\n        if (!tokens) {\n            tokens = parse(message, delimiters);\n            this._caches[message] = tokens;\n        }\n        return compile(tokens, values);\n    }\n}\nconst RE_TOKEN_LIST_VALUE = /^(?:\\d)+/;\nconst RE_TOKEN_NAMED_VALUE = /^(?:\\w)+/;\nfunction parse(format, [startDelimiter, endDelimiter]) {\n    const tokens = [];\n    let position = 0;\n    let text = '';\n    while (position < format.length) {\n        let char = format[position++];\n        if (char === startDelimiter) {\n            if (text) {\n                tokens.push({ type: 'text', value: text });\n            }\n            text = '';\n            let sub = '';\n            char = format[position++];\n            while (char !== undefined && char !== endDelimiter) {\n                sub += char;\n                char = format[position++];\n            }\n            const isClosed = char === endDelimiter;\n            const type = RE_TOKEN_LIST_VALUE.test(sub)\n                ? 'list'\n                : isClosed && RE_TOKEN_NAMED_VALUE.test(sub)\n                    ? 'named'\n                    : 'unknown';\n            tokens.push({ value: sub, type });\n        }\n        //  else if (char === '%') {\n        //   // when found rails i18n syntax, skip text capture\n        //   if (format[position] !== '{') {\n        //     text += char\n        //   }\n        // }\n        else {\n            text += char;\n        }\n    }\n    text && tokens.push({ type: 'text', value: text });\n    return tokens;\n}\nfunction compile(tokens, values) {\n    const compiled = [];\n    let index = 0;\n    const mode = Array.isArray(values)\n        ? 'list'\n        : isObject(values)\n            ? 'named'\n            : 'unknown';\n    if (mode === 'unknown') {\n        return compiled;\n    }\n    while (index < tokens.length) {\n        const token = tokens[index];\n        switch (token.type) {\n            case 'text':\n                compiled.push(token.value);\n                break;\n            case 'list':\n                compiled.push(values[parseInt(token.value, 10)]);\n                break;\n            case 'named':\n                if (mode === 'named') {\n                    compiled.push(values[token.value]);\n                }\n                else {\n                    if (process.env.NODE_ENV !== 'production') {\n                        console.warn(`Type of token '${token.type}' and format of value '${mode}' don't match!`);\n                    }\n                }\n                break;\n            case 'unknown':\n                if (process.env.NODE_ENV !== 'production') {\n                    console.warn(`Detect 'unknown' type of token!`);\n                }\n                break;\n        }\n        index++;\n    }\n    return compiled;\n}\n\nconst LOCALE_ZH_HANS = 'zh-Hans';\nconst LOCALE_ZH_HANT = 'zh-Hant';\nconst LOCALE_EN = 'en';\nconst LOCALE_FR = 'fr';\nconst LOCALE_ES = 'es';\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nconst hasOwn = (val, key) => hasOwnProperty.call(val, key);\nconst defaultFormatter = new BaseFormatter();\nfunction include(str, parts) {\n    return !!parts.find((part) => str.indexOf(part) !== -1);\n}\nfunction startsWith(str, parts) {\n    return parts.find((part) => str.indexOf(part) === 0);\n}\nfunction normalizeLocale(locale, messages) {\n    if (!locale) {\n        return;\n    }\n    locale = locale.trim().replace(/_/g, '-');\n    if (messages && messages[locale]) {\n        return locale;\n    }\n    locale = locale.toLowerCase();\n    if (locale === 'chinese') {\n        // 支付宝\n        return LOCALE_ZH_HANS;\n    }\n    if (locale.indexOf('zh') === 0) {\n        if (locale.indexOf('-hans') > -1) {\n            return LOCALE_ZH_HANS;\n        }\n        if (locale.indexOf('-hant') > -1) {\n            return LOCALE_ZH_HANT;\n        }\n        if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\n            return LOCALE_ZH_HANT;\n        }\n        return LOCALE_ZH_HANS;\n    }\n    let locales = [LOCALE_EN, LOCALE_FR, LOCALE_ES];\n    if (messages && Object.keys(messages).length > 0) {\n        locales = Object.keys(messages);\n    }\n    const lang = startsWith(locale, locales);\n    if (lang) {\n        return lang;\n    }\n}\nclass I18n {\n    constructor({ locale, fallbackLocale, messages, watcher, formater, }) {\n        this.locale = LOCALE_EN;\n        this.fallbackLocale = LOCALE_EN;\n        this.message = {};\n        this.messages = {};\n        this.watchers = [];\n        if (fallbackLocale) {\n            this.fallbackLocale = fallbackLocale;\n        }\n        this.formater = formater || defaultFormatter;\n        this.messages = messages || {};\n        this.setLocale(locale || LOCALE_EN);\n        if (watcher) {\n            this.watchLocale(watcher);\n        }\n    }\n    setLocale(locale) {\n        const oldLocale = this.locale;\n        this.locale = normalizeLocale(locale, this.messages) || this.fallbackLocale;\n        if (!this.messages[this.locale]) {\n            // 可能初始化时不存在\n            this.messages[this.locale] = {};\n        }\n        this.message = this.messages[this.locale];\n        // 仅发生变化时，通知\n        if (oldLocale !== this.locale) {\n            this.watchers.forEach((watcher) => {\n                watcher(this.locale, oldLocale);\n            });\n        }\n    }\n    getLocale() {\n        return this.locale;\n    }\n    watchLocale(fn) {\n        const index = this.watchers.push(fn) - 1;\n        return () => {\n            this.watchers.splice(index, 1);\n        };\n    }\n    add(locale, message, override = true) {\n        const curMessages = this.messages[locale];\n        if (curMessages) {\n            if (override) {\n                Object.assign(curMessages, message);\n            }\n            else {\n                Object.keys(message).forEach((key) => {\n                    if (!hasOwn(curMessages, key)) {\n                        curMessages[key] = message[key];\n                    }\n                });\n            }\n        }\n        else {\n            this.messages[locale] = message;\n        }\n    }\n    f(message, values, delimiters) {\n        return this.formater.interpolate(message, values, delimiters).join('');\n    }\n    t(key, locale, values) {\n        let message = this.message;\n        if (typeof locale === 'string') {\n            locale = normalizeLocale(locale, this.messages);\n            locale && (message = this.messages[locale]);\n        }\n        else {\n            values = locale;\n        }\n        if (!hasOwn(message, key)) {\n            console.warn(`Cannot translate the value of keypath ${key}. Use the value of keypath as default.`);\n            return key;\n        }\n        return this.formater.interpolate(message[key], values).join('');\n    }\n}\n\nfunction watchAppLocale(appVm, i18n) {\n    // 需要保证 watch 的触发在组件渲染之前\n    if (appVm.$watchLocale) {\n        // vue2\n        appVm.$watchLocale((newLocale) => {\n            i18n.setLocale(newLocale);\n        });\n    }\n    else {\n        appVm.$watch(() => appVm.$locale, (newLocale) => {\n            i18n.setLocale(newLocale);\n        });\n    }\n}\nfunction getDefaultLocale() {\n    if (typeof uni !== 'undefined' && uni.getLocale) {\n        return uni.getLocale();\n    }\n    // 小程序平台，uni 和 uni-i18n 互相引用，导致访问不到 uni，故在 global 上挂了 getLocale\n    if (typeof global !== 'undefined' && global.getLocale) {\n        return global.getLocale();\n    }\n    return LOCALE_EN;\n}\nfunction initVueI18n(locale, messages = {}, fallbackLocale, watcher) {\n    // 兼容旧版本入参\n    if (typeof locale !== 'string') {\n        // ;[locale, messages] = [\n        //   messages as unknown as string,\n        //   locale as unknown as LocaleMessages,\n        // ]\n        // 暂不使用数组解构，uts编译器暂未支持。\n        const options = [\n            messages,\n            locale,\n        ];\n        locale = options[0];\n        messages = options[1];\n    }\n    if (typeof locale !== 'string') {\n        // 因为小程序平台，uni-i18n 和 uni 互相引用，导致此时访问 uni 时，为 undefined\n        locale = getDefaultLocale();\n    }\n    if (typeof fallbackLocale !== 'string') {\n        fallbackLocale =\n            (typeof __uniConfig !== 'undefined' && __uniConfig.fallbackLocale) ||\n                LOCALE_EN;\n    }\n    const i18n = new I18n({\n        locale,\n        fallbackLocale,\n        messages,\n        watcher,\n    });\n    let t = (key, values) => {\n        if (typeof getApp !== 'function') {\n            // app view\n            /* eslint-disable no-func-assign */\n            t = function (key, values) {\n                return i18n.t(key, values);\n            };\n        }\n        else {\n            let isWatchedAppLocale = false;\n            t = function (key, values) {\n                const appVm = getApp().$vm;\n                // 可能$vm还不存在，比如在支付宝小程序中，组件定义较早，在props的default里使用了t()函数（如uni-goods-nav），此时app还未初始化\n                // options: {\n                // \ttype: Array,\n                // \tdefault () {\n                // \t\treturn [{\n                // \t\t\ticon: 'shop',\n                // \t\t\ttext: t(\"uni-goods-nav.options.shop\"),\n                // \t\t}, {\n                // \t\t\ticon: 'cart',\n                // \t\t\ttext: t(\"uni-goods-nav.options.cart\")\n                // \t\t}]\n                // \t}\n                // },\n                if (appVm) {\n                    // 触发响应式\n                    appVm.$locale;\n                    if (!isWatchedAppLocale) {\n                        isWatchedAppLocale = true;\n                        watchAppLocale(appVm, i18n);\n                    }\n                }\n                return i18n.t(key, values);\n            };\n        }\n        return t(key, values);\n    };\n    return {\n        i18n,\n        f(message, values, delimiters) {\n            return i18n.f(message, values, delimiters);\n        },\n        t(key, values) {\n            return t(key, values);\n        },\n        add(locale, message, override = true) {\n            return i18n.add(locale, message, override);\n        },\n        watch(fn) {\n            return i18n.watchLocale(fn);\n        },\n        getLocale() {\n            return i18n.getLocale();\n        },\n        setLocale(newLocale) {\n            return i18n.setLocale(newLocale);\n        },\n    };\n}\n\nconst isString = (val) => typeof val === 'string';\nlet formater;\nfunction hasI18nJson(jsonObj, delimiters) {\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    return walkJsonObj(jsonObj, (jsonObj, key) => {\n        const value = jsonObj[key];\n        if (isString(value)) {\n            if (isI18nStr(value, delimiters)) {\n                return true;\n            }\n        }\n        else {\n            return hasI18nJson(value, delimiters);\n        }\n    });\n}\nfunction parseI18nJson(jsonObj, values, delimiters) {\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    walkJsonObj(jsonObj, (jsonObj, key) => {\n        const value = jsonObj[key];\n        if (isString(value)) {\n            if (isI18nStr(value, delimiters)) {\n                jsonObj[key] = compileStr(value, values, delimiters);\n            }\n        }\n        else {\n            parseI18nJson(value, values, delimiters);\n        }\n    });\n    return jsonObj;\n}\nfunction compileI18nJsonStr(jsonStr, { locale, locales, delimiters, }) {\n    if (!isI18nStr(jsonStr, delimiters)) {\n        return jsonStr;\n    }\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    const localeValues = [];\n    Object.keys(locales).forEach((name) => {\n        if (name !== locale) {\n            localeValues.push({\n                locale: name,\n                values: locales[name],\n            });\n        }\n    });\n    localeValues.unshift({ locale, values: locales[locale] });\n    try {\n        return JSON.stringify(compileJsonObj(JSON.parse(jsonStr), localeValues, delimiters), null, 2);\n    }\n    catch (e) { }\n    return jsonStr;\n}\nfunction isI18nStr(value, delimiters) {\n    return value.indexOf(delimiters[0]) > -1;\n}\nfunction compileStr(value, values, delimiters) {\n    return formater.interpolate(value, values, delimiters).join('');\n}\nfunction compileValue(jsonObj, key, localeValues, delimiters) {\n    const value = jsonObj[key];\n    if (isString(value)) {\n        // 存在国际化\n        if (isI18nStr(value, delimiters)) {\n            jsonObj[key] = compileStr(value, localeValues[0].values, delimiters);\n            if (localeValues.length > 1) {\n                // 格式化国际化语言\n                const valueLocales = (jsonObj[key + 'Locales'] = {});\n                localeValues.forEach((localValue) => {\n                    valueLocales[localValue.locale] = compileStr(value, localValue.values, delimiters);\n                });\n            }\n        }\n    }\n    else {\n        compileJsonObj(value, localeValues, delimiters);\n    }\n}\nfunction compileJsonObj(jsonObj, localeValues, delimiters) {\n    walkJsonObj(jsonObj, (jsonObj, key) => {\n        compileValue(jsonObj, key, localeValues, delimiters);\n    });\n    return jsonObj;\n}\nfunction walkJsonObj(jsonObj, walk) {\n    if (Array.isArray(jsonObj)) {\n        for (let i = 0; i < jsonObj.length; i++) {\n            if (walk(jsonObj, i)) {\n                return true;\n            }\n        }\n    }\n    else if (isObject(jsonObj)) {\n        for (const key in jsonObj) {\n            if (walk(jsonObj, key)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\nfunction resolveLocale(locales) {\n    return (locale) => {\n        if (!locale) {\n            return locale;\n        }\n        locale = normalizeLocale(locale) || locale;\n        return resolveLocaleChain(locale).find((locale) => locales.indexOf(locale) > -1);\n    };\n}\nfunction resolveLocaleChain(locale) {\n    const chain = [];\n    const tokens = locale.split('-');\n    while (tokens.length) {\n        chain.push(tokens.join('-'));\n        tokens.pop();\n    }\n    return chain;\n}\n\nexport { BaseFormatter as Formatter, I18n, LOCALE_EN, LOCALE_ES, LOCALE_FR, LOCALE_ZH_HANS, LOCALE_ZH_HANT, compileI18nJsonStr, hasI18nJson, initVueI18n, isI18nStr, isString, normalizeLocale, parseI18nJson, resolveLocale };\n", "import { isRootHook, getValueByDataPath, isUniLifecycleHook, ON_ERROR, UniLifecycleHooks, invokeCreateErrorHandler, dynamicSlotName } from '@dcloudio/uni-shared';\nimport { NOOP, extend, isSymbol, isObject, def, hasChanged, isFunction, isArray, isPromise, camelize, capitalize, EMPTY_OBJ, remove, toHandlerKey, hasOwn, hyphenate, isReservedProp, toRawType, isString, normalizeClass, normalizeStyle, isOn, toTypeString, isMap, isIntegerKey, isSet, isPlainObject, makeMap, invokeArrayFns, isBuiltInDirective, looseToNumber, NO, EMPTY_ARR, isModelListener, toNumber, toDisplayString } from '@vue/shared';\nexport { EMPTY_OBJ, camelize, normalizeClass, normalizeProps, normalizeStyle, toDisplayString, toHandlerKey } from '@vue/shared';\n\n/**\n* @dcloudio/uni-mp-vue v3.4.21\n* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors\n* @license MIT\n**/\n\nfunction warn$2(msg, ...args) {\n  console.warn(`[Vue warn] ${msg}`, ...args);\n}\n\nlet activeEffectScope;\nclass EffectScope {\n  constructor(detached = false) {\n    this.detached = detached;\n    /**\n     * @internal\n     */\n    this._active = true;\n    /**\n     * @internal\n     */\n    this.effects = [];\n    /**\n     * @internal\n     */\n    this.cleanups = [];\n    this.parent = activeEffectScope;\n    if (!detached && activeEffectScope) {\n      this.index = (activeEffectScope.scopes || (activeEffectScope.scopes = [])).push(\n        this\n      ) - 1;\n    }\n  }\n  get active() {\n    return this._active;\n  }\n  run(fn) {\n    if (this._active) {\n      const currentEffectScope = activeEffectScope;\n      try {\n        activeEffectScope = this;\n        return fn();\n      } finally {\n        activeEffectScope = currentEffectScope;\n      }\n    } else if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$2(`cannot run an inactive effect scope.`);\n    }\n  }\n  /**\n   * This should only be called on non-detached scopes\n   * @internal\n   */\n  on() {\n    activeEffectScope = this;\n  }\n  /**\n   * This should only be called on non-detached scopes\n   * @internal\n   */\n  off() {\n    activeEffectScope = this.parent;\n  }\n  stop(fromParent) {\n    if (this._active) {\n      let i, l;\n      for (i = 0, l = this.effects.length; i < l; i++) {\n        this.effects[i].stop();\n      }\n      for (i = 0, l = this.cleanups.length; i < l; i++) {\n        this.cleanups[i]();\n      }\n      if (this.scopes) {\n        for (i = 0, l = this.scopes.length; i < l; i++) {\n          this.scopes[i].stop(true);\n        }\n      }\n      if (!this.detached && this.parent && !fromParent) {\n        const last = this.parent.scopes.pop();\n        if (last && last !== this) {\n          this.parent.scopes[this.index] = last;\n          last.index = this.index;\n        }\n      }\n      this.parent = void 0;\n      this._active = false;\n    }\n  }\n}\nfunction effectScope(detached) {\n  return new EffectScope(detached);\n}\nfunction recordEffectScope(effect, scope = activeEffectScope) {\n  if (scope && scope.active) {\n    scope.effects.push(effect);\n  }\n}\nfunction getCurrentScope() {\n  return activeEffectScope;\n}\nfunction onScopeDispose(fn) {\n  if (activeEffectScope) {\n    activeEffectScope.cleanups.push(fn);\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$2(\n      `onScopeDispose() is called when there is no active effect scope to be associated with.`\n    );\n  }\n}\n\nlet activeEffect;\nclass ReactiveEffect {\n  constructor(fn, trigger, scheduler, scope) {\n    this.fn = fn;\n    this.trigger = trigger;\n    this.scheduler = scheduler;\n    this.active = true;\n    this.deps = [];\n    /**\n     * @internal\n     */\n    this._dirtyLevel = 4;\n    /**\n     * @internal\n     */\n    this._trackId = 0;\n    /**\n     * @internal\n     */\n    this._runnings = 0;\n    /**\n     * @internal\n     */\n    this._shouldSchedule = false;\n    /**\n     * @internal\n     */\n    this._depsLength = 0;\n    recordEffectScope(this, scope);\n  }\n  get dirty() {\n    if (this._dirtyLevel === 2 || this._dirtyLevel === 3) {\n      this._dirtyLevel = 1;\n      pauseTracking();\n      for (let i = 0; i < this._depsLength; i++) {\n        const dep = this.deps[i];\n        if (dep.computed) {\n          triggerComputed(dep.computed);\n          if (this._dirtyLevel >= 4) {\n            break;\n          }\n        }\n      }\n      if (this._dirtyLevel === 1) {\n        this._dirtyLevel = 0;\n      }\n      resetTracking();\n    }\n    return this._dirtyLevel >= 4;\n  }\n  set dirty(v) {\n    this._dirtyLevel = v ? 4 : 0;\n  }\n  run() {\n    this._dirtyLevel = 0;\n    if (!this.active) {\n      return this.fn();\n    }\n    let lastShouldTrack = shouldTrack;\n    let lastEffect = activeEffect;\n    try {\n      shouldTrack = true;\n      activeEffect = this;\n      this._runnings++;\n      preCleanupEffect(this);\n      return this.fn();\n    } finally {\n      postCleanupEffect(this);\n      this._runnings--;\n      activeEffect = lastEffect;\n      shouldTrack = lastShouldTrack;\n    }\n  }\n  stop() {\n    var _a;\n    if (this.active) {\n      preCleanupEffect(this);\n      postCleanupEffect(this);\n      (_a = this.onStop) == null ? void 0 : _a.call(this);\n      this.active = false;\n    }\n  }\n}\nfunction triggerComputed(computed) {\n  return computed.value;\n}\nfunction preCleanupEffect(effect2) {\n  effect2._trackId++;\n  effect2._depsLength = 0;\n}\nfunction postCleanupEffect(effect2) {\n  if (effect2.deps.length > effect2._depsLength) {\n    for (let i = effect2._depsLength; i < effect2.deps.length; i++) {\n      cleanupDepEffect(effect2.deps[i], effect2);\n    }\n    effect2.deps.length = effect2._depsLength;\n  }\n}\nfunction cleanupDepEffect(dep, effect2) {\n  const trackId = dep.get(effect2);\n  if (trackId !== void 0 && effect2._trackId !== trackId) {\n    dep.delete(effect2);\n    if (dep.size === 0) {\n      dep.cleanup();\n    }\n  }\n}\nfunction effect(fn, options) {\n  if (fn.effect instanceof ReactiveEffect) {\n    fn = fn.effect.fn;\n  }\n  const _effect = new ReactiveEffect(fn, NOOP, () => {\n    if (_effect.dirty) {\n      _effect.run();\n    }\n  });\n  if (options) {\n    extend(_effect, options);\n    if (options.scope)\n      recordEffectScope(_effect, options.scope);\n  }\n  if (!options || !options.lazy) {\n    _effect.run();\n  }\n  const runner = _effect.run.bind(_effect);\n  runner.effect = _effect;\n  return runner;\n}\nfunction stop(runner) {\n  runner.effect.stop();\n}\nlet shouldTrack = true;\nlet pauseScheduleStack = 0;\nconst trackStack = [];\nfunction pauseTracking() {\n  trackStack.push(shouldTrack);\n  shouldTrack = false;\n}\nfunction resetTracking() {\n  const last = trackStack.pop();\n  shouldTrack = last === void 0 ? true : last;\n}\nfunction pauseScheduling() {\n  pauseScheduleStack++;\n}\nfunction resetScheduling() {\n  pauseScheduleStack--;\n  while (!pauseScheduleStack && queueEffectSchedulers.length) {\n    queueEffectSchedulers.shift()();\n  }\n}\nfunction trackEffect(effect2, dep, debuggerEventExtraInfo) {\n  var _a;\n  if (dep.get(effect2) !== effect2._trackId) {\n    dep.set(effect2, effect2._trackId);\n    const oldDep = effect2.deps[effect2._depsLength];\n    if (oldDep !== dep) {\n      if (oldDep) {\n        cleanupDepEffect(oldDep, effect2);\n      }\n      effect2.deps[effect2._depsLength++] = dep;\n    } else {\n      effect2._depsLength++;\n    }\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      (_a = effect2.onTrack) == null ? void 0 : _a.call(effect2, extend({ effect: effect2 }, debuggerEventExtraInfo));\n    }\n  }\n}\nconst queueEffectSchedulers = [];\nfunction triggerEffects(dep, dirtyLevel, debuggerEventExtraInfo) {\n  var _a;\n  pauseScheduling();\n  for (const effect2 of dep.keys()) {\n    let tracking;\n    if (effect2._dirtyLevel < dirtyLevel && (tracking != null ? tracking : tracking = dep.get(effect2) === effect2._trackId)) {\n      effect2._shouldSchedule || (effect2._shouldSchedule = effect2._dirtyLevel === 0);\n      effect2._dirtyLevel = dirtyLevel;\n    }\n    if (effect2._shouldSchedule && (tracking != null ? tracking : tracking = dep.get(effect2) === effect2._trackId)) {\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        (_a = effect2.onTrigger) == null ? void 0 : _a.call(effect2, extend({ effect: effect2 }, debuggerEventExtraInfo));\n      }\n      effect2.trigger();\n      if ((!effect2._runnings || effect2.allowRecurse) && effect2._dirtyLevel !== 2) {\n        effect2._shouldSchedule = false;\n        if (effect2.scheduler) {\n          queueEffectSchedulers.push(effect2.scheduler);\n        }\n      }\n    }\n  }\n  resetScheduling();\n}\n\nconst createDep = (cleanup, computed) => {\n  const dep = /* @__PURE__ */ new Map();\n  dep.cleanup = cleanup;\n  dep.computed = computed;\n  return dep;\n};\n\nconst targetMap = /* @__PURE__ */ new WeakMap();\nconst ITERATE_KEY = Symbol(!!(process.env.NODE_ENV !== \"production\") ? \"iterate\" : \"\");\nconst MAP_KEY_ITERATE_KEY = Symbol(!!(process.env.NODE_ENV !== \"production\") ? \"Map key iterate\" : \"\");\nfunction track(target, type, key) {\n  if (shouldTrack && activeEffect) {\n    let depsMap = targetMap.get(target);\n    if (!depsMap) {\n      targetMap.set(target, depsMap = /* @__PURE__ */ new Map());\n    }\n    let dep = depsMap.get(key);\n    if (!dep) {\n      depsMap.set(key, dep = createDep(() => depsMap.delete(key)));\n    }\n    trackEffect(\n      activeEffect,\n      dep,\n      !!(process.env.NODE_ENV !== \"production\") ? {\n        target,\n        type,\n        key\n      } : void 0\n    );\n  }\n}\nfunction trigger(target, type, key, newValue, oldValue, oldTarget) {\n  const depsMap = targetMap.get(target);\n  if (!depsMap) {\n    return;\n  }\n  let deps = [];\n  if (type === \"clear\") {\n    deps = [...depsMap.values()];\n  } else if (key === \"length\" && isArray(target)) {\n    const newLength = Number(newValue);\n    depsMap.forEach((dep, key2) => {\n      if (key2 === \"length\" || !isSymbol(key2) && key2 >= newLength) {\n        deps.push(dep);\n      }\n    });\n  } else {\n    if (key !== void 0) {\n      deps.push(depsMap.get(key));\n    }\n    switch (type) {\n      case \"add\":\n        if (!isArray(target)) {\n          deps.push(depsMap.get(ITERATE_KEY));\n          if (isMap(target)) {\n            deps.push(depsMap.get(MAP_KEY_ITERATE_KEY));\n          }\n        } else if (isIntegerKey(key)) {\n          deps.push(depsMap.get(\"length\"));\n        }\n        break;\n      case \"delete\":\n        if (!isArray(target)) {\n          deps.push(depsMap.get(ITERATE_KEY));\n          if (isMap(target)) {\n            deps.push(depsMap.get(MAP_KEY_ITERATE_KEY));\n          }\n        }\n        break;\n      case \"set\":\n        if (isMap(target)) {\n          deps.push(depsMap.get(ITERATE_KEY));\n        }\n        break;\n    }\n  }\n  pauseScheduling();\n  for (const dep of deps) {\n    if (dep) {\n      triggerEffects(\n        dep,\n        4,\n        !!(process.env.NODE_ENV !== \"production\") ? {\n          target,\n          type,\n          key,\n          newValue,\n          oldValue,\n          oldTarget\n        } : void 0\n      );\n    }\n  }\n  resetScheduling();\n}\nfunction getDepFromReactive(object, key) {\n  var _a;\n  return (_a = targetMap.get(object)) == null ? void 0 : _a.get(key);\n}\n\nconst isNonTrackableKeys = /* @__PURE__ */ makeMap(`__proto__,__v_isRef,__isVue`);\nconst builtInSymbols = new Set(\n  /* @__PURE__ */ Object.getOwnPropertyNames(Symbol).filter((key) => key !== \"arguments\" && key !== \"caller\").map((key) => Symbol[key]).filter(isSymbol)\n);\nconst arrayInstrumentations = /* @__PURE__ */ createArrayInstrumentations();\nfunction createArrayInstrumentations() {\n  const instrumentations = {};\n  [\"includes\", \"indexOf\", \"lastIndexOf\"].forEach((key) => {\n    instrumentations[key] = function(...args) {\n      const arr = toRaw(this);\n      for (let i = 0, l = this.length; i < l; i++) {\n        track(arr, \"get\", i + \"\");\n      }\n      const res = arr[key](...args);\n      if (res === -1 || res === false) {\n        return arr[key](...args.map(toRaw));\n      } else {\n        return res;\n      }\n    };\n  });\n  [\"push\", \"pop\", \"shift\", \"unshift\", \"splice\"].forEach((key) => {\n    instrumentations[key] = function(...args) {\n      pauseTracking();\n      pauseScheduling();\n      const res = toRaw(this)[key].apply(this, args);\n      resetScheduling();\n      resetTracking();\n      return res;\n    };\n  });\n  return instrumentations;\n}\nfunction hasOwnProperty(key) {\n  const obj = toRaw(this);\n  track(obj, \"has\", key);\n  return obj.hasOwnProperty(key);\n}\nclass BaseReactiveHandler {\n  constructor(_isReadonly = false, _isShallow = false) {\n    this._isReadonly = _isReadonly;\n    this._isShallow = _isShallow;\n  }\n  get(target, key, receiver) {\n    const isReadonly2 = this._isReadonly, isShallow2 = this._isShallow;\n    if (key === \"__v_isReactive\") {\n      return !isReadonly2;\n    } else if (key === \"__v_isReadonly\") {\n      return isReadonly2;\n    } else if (key === \"__v_isShallow\") {\n      return isShallow2;\n    } else if (key === \"__v_raw\") {\n      if (receiver === (isReadonly2 ? isShallow2 ? shallowReadonlyMap : readonlyMap : isShallow2 ? shallowReactiveMap : reactiveMap).get(target) || // receiver is not the reactive proxy, but has the same prototype\n      // this means the reciever is a user proxy of the reactive proxy\n      Object.getPrototypeOf(target) === Object.getPrototypeOf(receiver)) {\n        return target;\n      }\n      return;\n    }\n    const targetIsArray = isArray(target);\n    if (!isReadonly2) {\n      if (targetIsArray && hasOwn(arrayInstrumentations, key)) {\n        return Reflect.get(arrayInstrumentations, key, receiver);\n      }\n      if (key === \"hasOwnProperty\") {\n        return hasOwnProperty;\n      }\n    }\n    const res = Reflect.get(target, key, receiver);\n    if (isSymbol(key) ? builtInSymbols.has(key) : isNonTrackableKeys(key)) {\n      return res;\n    }\n    if (!isReadonly2) {\n      track(target, \"get\", key);\n    }\n    if (isShallow2) {\n      return res;\n    }\n    if (isRef(res)) {\n      return targetIsArray && isIntegerKey(key) ? res : res.value;\n    }\n    if (isObject(res)) {\n      return isReadonly2 ? readonly(res) : reactive(res);\n    }\n    return res;\n  }\n}\nclass MutableReactiveHandler extends BaseReactiveHandler {\n  constructor(isShallow2 = false) {\n    super(false, isShallow2);\n  }\n  set(target, key, value, receiver) {\n    let oldValue = target[key];\n    if (!this._isShallow) {\n      const isOldValueReadonly = isReadonly(oldValue);\n      if (!isShallow(value) && !isReadonly(value)) {\n        oldValue = toRaw(oldValue);\n        value = toRaw(value);\n      }\n      if (!isArray(target) && isRef(oldValue) && !isRef(value)) {\n        if (isOldValueReadonly) {\n          return false;\n        } else {\n          oldValue.value = value;\n          return true;\n        }\n      }\n    }\n    const hadKey = isArray(target) && isIntegerKey(key) ? Number(key) < target.length : hasOwn(target, key);\n    const result = Reflect.set(target, key, value, receiver);\n    if (target === toRaw(receiver)) {\n      if (!hadKey) {\n        trigger(target, \"add\", key, value);\n      } else if (hasChanged(value, oldValue)) {\n        trigger(target, \"set\", key, value, oldValue);\n      }\n    }\n    return result;\n  }\n  deleteProperty(target, key) {\n    const hadKey = hasOwn(target, key);\n    const oldValue = target[key];\n    const result = Reflect.deleteProperty(target, key);\n    if (result && hadKey) {\n      trigger(target, \"delete\", key, void 0, oldValue);\n    }\n    return result;\n  }\n  has(target, key) {\n    const result = Reflect.has(target, key);\n    if (!isSymbol(key) || !builtInSymbols.has(key)) {\n      track(target, \"has\", key);\n    }\n    return result;\n  }\n  ownKeys(target) {\n    track(\n      target,\n      \"iterate\",\n      isArray(target) ? \"length\" : ITERATE_KEY\n    );\n    return Reflect.ownKeys(target);\n  }\n}\nclass ReadonlyReactiveHandler extends BaseReactiveHandler {\n  constructor(isShallow2 = false) {\n    super(true, isShallow2);\n  }\n  set(target, key) {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$2(\n        `Set operation on key \"${String(key)}\" failed: target is readonly.`,\n        target\n      );\n    }\n    return true;\n  }\n  deleteProperty(target, key) {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$2(\n        `Delete operation on key \"${String(key)}\" failed: target is readonly.`,\n        target\n      );\n    }\n    return true;\n  }\n}\nconst mutableHandlers = /* @__PURE__ */ new MutableReactiveHandler();\nconst readonlyHandlers = /* @__PURE__ */ new ReadonlyReactiveHandler();\nconst shallowReactiveHandlers = /* @__PURE__ */ new MutableReactiveHandler(\n  true\n);\nconst shallowReadonlyHandlers = /* @__PURE__ */ new ReadonlyReactiveHandler(true);\n\nconst toShallow = (value) => value;\nconst getProto = (v) => Reflect.getPrototypeOf(v);\nfunction get(target, key, isReadonly = false, isShallow = false) {\n  target = target[\"__v_raw\"];\n  const rawTarget = toRaw(target);\n  const rawKey = toRaw(key);\n  if (!isReadonly) {\n    if (hasChanged(key, rawKey)) {\n      track(rawTarget, \"get\", key);\n    }\n    track(rawTarget, \"get\", rawKey);\n  }\n  const { has: has2 } = getProto(rawTarget);\n  const wrap = isShallow ? toShallow : isReadonly ? toReadonly : toReactive;\n  if (has2.call(rawTarget, key)) {\n    return wrap(target.get(key));\n  } else if (has2.call(rawTarget, rawKey)) {\n    return wrap(target.get(rawKey));\n  } else if (target !== rawTarget) {\n    target.get(key);\n  }\n}\nfunction has(key, isReadonly = false) {\n  const target = this[\"__v_raw\"];\n  const rawTarget = toRaw(target);\n  const rawKey = toRaw(key);\n  if (!isReadonly) {\n    if (hasChanged(key, rawKey)) {\n      track(rawTarget, \"has\", key);\n    }\n    track(rawTarget, \"has\", rawKey);\n  }\n  return key === rawKey ? target.has(key) : target.has(key) || target.has(rawKey);\n}\nfunction size(target, isReadonly = false) {\n  target = target[\"__v_raw\"];\n  !isReadonly && track(toRaw(target), \"iterate\", ITERATE_KEY);\n  return Reflect.get(target, \"size\", target);\n}\nfunction add(value) {\n  value = toRaw(value);\n  const target = toRaw(this);\n  const proto = getProto(target);\n  const hadKey = proto.has.call(target, value);\n  if (!hadKey) {\n    target.add(value);\n    trigger(target, \"add\", value, value);\n  }\n  return this;\n}\nfunction set$1(key, value) {\n  value = toRaw(value);\n  const target = toRaw(this);\n  const { has: has2, get: get2 } = getProto(target);\n  let hadKey = has2.call(target, key);\n  if (!hadKey) {\n    key = toRaw(key);\n    hadKey = has2.call(target, key);\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    checkIdentityKeys(target, has2, key);\n  }\n  const oldValue = get2.call(target, key);\n  target.set(key, value);\n  if (!hadKey) {\n    trigger(target, \"add\", key, value);\n  } else if (hasChanged(value, oldValue)) {\n    trigger(target, \"set\", key, value, oldValue);\n  }\n  return this;\n}\nfunction deleteEntry(key) {\n  const target = toRaw(this);\n  const { has: has2, get: get2 } = getProto(target);\n  let hadKey = has2.call(target, key);\n  if (!hadKey) {\n    key = toRaw(key);\n    hadKey = has2.call(target, key);\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    checkIdentityKeys(target, has2, key);\n  }\n  const oldValue = get2 ? get2.call(target, key) : void 0;\n  const result = target.delete(key);\n  if (hadKey) {\n    trigger(target, \"delete\", key, void 0, oldValue);\n  }\n  return result;\n}\nfunction clear() {\n  const target = toRaw(this);\n  const hadItems = target.size !== 0;\n  const oldTarget = !!(process.env.NODE_ENV !== \"production\") ? isMap(target) ? new Map(target) : new Set(target) : void 0;\n  const result = target.clear();\n  if (hadItems) {\n    trigger(target, \"clear\", void 0, void 0, oldTarget);\n  }\n  return result;\n}\nfunction createForEach(isReadonly, isShallow) {\n  return function forEach(callback, thisArg) {\n    const observed = this;\n    const target = observed[\"__v_raw\"];\n    const rawTarget = toRaw(target);\n    const wrap = isShallow ? toShallow : isReadonly ? toReadonly : toReactive;\n    !isReadonly && track(rawTarget, \"iterate\", ITERATE_KEY);\n    return target.forEach((value, key) => {\n      return callback.call(thisArg, wrap(value), wrap(key), observed);\n    });\n  };\n}\nfunction createIterableMethod(method, isReadonly, isShallow) {\n  return function(...args) {\n    const target = this[\"__v_raw\"];\n    const rawTarget = toRaw(target);\n    const targetIsMap = isMap(rawTarget);\n    const isPair = method === \"entries\" || method === Symbol.iterator && targetIsMap;\n    const isKeyOnly = method === \"keys\" && targetIsMap;\n    const innerIterator = target[method](...args);\n    const wrap = isShallow ? toShallow : isReadonly ? toReadonly : toReactive;\n    !isReadonly && track(\n      rawTarget,\n      \"iterate\",\n      isKeyOnly ? MAP_KEY_ITERATE_KEY : ITERATE_KEY\n    );\n    return {\n      // iterator protocol\n      next() {\n        const { value, done } = innerIterator.next();\n        return done ? { value, done } : {\n          value: isPair ? [wrap(value[0]), wrap(value[1])] : wrap(value),\n          done\n        };\n      },\n      // iterable protocol\n      [Symbol.iterator]() {\n        return this;\n      }\n    };\n  };\n}\nfunction createReadonlyMethod(type) {\n  return function(...args) {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      const key = args[0] ? `on key \"${args[0]}\" ` : ``;\n      warn$2(\n        `${capitalize(type)} operation ${key}failed: target is readonly.`,\n        toRaw(this)\n      );\n    }\n    return type === \"delete\" ? false : type === \"clear\" ? void 0 : this;\n  };\n}\nfunction createInstrumentations() {\n  const mutableInstrumentations2 = {\n    get(key) {\n      return get(this, key);\n    },\n    get size() {\n      return size(this);\n    },\n    has,\n    add,\n    set: set$1,\n    delete: deleteEntry,\n    clear,\n    forEach: createForEach(false, false)\n  };\n  const shallowInstrumentations2 = {\n    get(key) {\n      return get(this, key, false, true);\n    },\n    get size() {\n      return size(this);\n    },\n    has,\n    add,\n    set: set$1,\n    delete: deleteEntry,\n    clear,\n    forEach: createForEach(false, true)\n  };\n  const readonlyInstrumentations2 = {\n    get(key) {\n      return get(this, key, true);\n    },\n    get size() {\n      return size(this, true);\n    },\n    has(key) {\n      return has.call(this, key, true);\n    },\n    add: createReadonlyMethod(\"add\"),\n    set: createReadonlyMethod(\"set\"),\n    delete: createReadonlyMethod(\"delete\"),\n    clear: createReadonlyMethod(\"clear\"),\n    forEach: createForEach(true, false)\n  };\n  const shallowReadonlyInstrumentations2 = {\n    get(key) {\n      return get(this, key, true, true);\n    },\n    get size() {\n      return size(this, true);\n    },\n    has(key) {\n      return has.call(this, key, true);\n    },\n    add: createReadonlyMethod(\"add\"),\n    set: createReadonlyMethod(\"set\"),\n    delete: createReadonlyMethod(\"delete\"),\n    clear: createReadonlyMethod(\"clear\"),\n    forEach: createForEach(true, true)\n  };\n  const iteratorMethods = [\n    \"keys\",\n    \"values\",\n    \"entries\",\n    Symbol.iterator\n  ];\n  iteratorMethods.forEach((method) => {\n    mutableInstrumentations2[method] = createIterableMethod(method, false, false);\n    readonlyInstrumentations2[method] = createIterableMethod(method, true, false);\n    shallowInstrumentations2[method] = createIterableMethod(method, false, true);\n    shallowReadonlyInstrumentations2[method] = createIterableMethod(\n      method,\n      true,\n      true\n    );\n  });\n  return [\n    mutableInstrumentations2,\n    readonlyInstrumentations2,\n    shallowInstrumentations2,\n    shallowReadonlyInstrumentations2\n  ];\n}\nconst [\n  mutableInstrumentations,\n  readonlyInstrumentations,\n  shallowInstrumentations,\n  shallowReadonlyInstrumentations\n] = /* @__PURE__ */ createInstrumentations();\nfunction createInstrumentationGetter(isReadonly, shallow) {\n  const instrumentations = shallow ? isReadonly ? shallowReadonlyInstrumentations : shallowInstrumentations : isReadonly ? readonlyInstrumentations : mutableInstrumentations;\n  return (target, key, receiver) => {\n    if (key === \"__v_isReactive\") {\n      return !isReadonly;\n    } else if (key === \"__v_isReadonly\") {\n      return isReadonly;\n    } else if (key === \"__v_raw\") {\n      return target;\n    }\n    return Reflect.get(\n      hasOwn(instrumentations, key) && key in target ? instrumentations : target,\n      key,\n      receiver\n    );\n  };\n}\nconst mutableCollectionHandlers = {\n  get: /* @__PURE__ */ createInstrumentationGetter(false, false)\n};\nconst shallowCollectionHandlers = {\n  get: /* @__PURE__ */ createInstrumentationGetter(false, true)\n};\nconst readonlyCollectionHandlers = {\n  get: /* @__PURE__ */ createInstrumentationGetter(true, false)\n};\nconst shallowReadonlyCollectionHandlers = {\n  get: /* @__PURE__ */ createInstrumentationGetter(true, true)\n};\nfunction checkIdentityKeys(target, has2, key) {\n  const rawKey = toRaw(key);\n  if (rawKey !== key && has2.call(target, rawKey)) {\n    const type = toRawType(target);\n    warn$2(\n      `Reactive ${type} contains both the raw and reactive versions of the same object${type === `Map` ? ` as keys` : ``}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`\n    );\n  }\n}\n\nconst reactiveMap = /* @__PURE__ */ new WeakMap();\nconst shallowReactiveMap = /* @__PURE__ */ new WeakMap();\nconst readonlyMap = /* @__PURE__ */ new WeakMap();\nconst shallowReadonlyMap = /* @__PURE__ */ new WeakMap();\nfunction targetTypeMap(rawType) {\n  switch (rawType) {\n    case \"Object\":\n    case \"Array\":\n      return 1 /* COMMON */;\n    case \"Map\":\n    case \"Set\":\n    case \"WeakMap\":\n    case \"WeakSet\":\n      return 2 /* COLLECTION */;\n    default:\n      return 0 /* INVALID */;\n  }\n}\nfunction getTargetType(value) {\n  return value[\"__v_skip\"] || !Object.isExtensible(value) ? 0 /* INVALID */ : targetTypeMap(toRawType(value));\n}\nfunction reactive(target) {\n  if (isReadonly(target)) {\n    return target;\n  }\n  return createReactiveObject(\n    target,\n    false,\n    mutableHandlers,\n    mutableCollectionHandlers,\n    reactiveMap\n  );\n}\nfunction shallowReactive(target) {\n  return createReactiveObject(\n    target,\n    false,\n    shallowReactiveHandlers,\n    shallowCollectionHandlers,\n    shallowReactiveMap\n  );\n}\nfunction readonly(target) {\n  return createReactiveObject(\n    target,\n    true,\n    readonlyHandlers,\n    readonlyCollectionHandlers,\n    readonlyMap\n  );\n}\nfunction shallowReadonly(target) {\n  return createReactiveObject(\n    target,\n    true,\n    shallowReadonlyHandlers,\n    shallowReadonlyCollectionHandlers,\n    shallowReadonlyMap\n  );\n}\nfunction createReactiveObject(target, isReadonly2, baseHandlers, collectionHandlers, proxyMap) {\n  if (!isObject(target)) {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$2(`value cannot be made reactive: ${String(target)}`);\n    }\n    return target;\n  }\n  if (target[\"__v_raw\"] && !(isReadonly2 && target[\"__v_isReactive\"])) {\n    return target;\n  }\n  const existingProxy = proxyMap.get(target);\n  if (existingProxy) {\n    return existingProxy;\n  }\n  const targetType = getTargetType(target);\n  if (targetType === 0 /* INVALID */) {\n    return target;\n  }\n  const proxy = new Proxy(\n    target,\n    targetType === 2 /* COLLECTION */ ? collectionHandlers : baseHandlers\n  );\n  proxyMap.set(target, proxy);\n  return proxy;\n}\nfunction isReactive(value) {\n  if (isReadonly(value)) {\n    return isReactive(value[\"__v_raw\"]);\n  }\n  return !!(value && value[\"__v_isReactive\"]);\n}\nfunction isReadonly(value) {\n  return !!(value && value[\"__v_isReadonly\"]);\n}\nfunction isShallow(value) {\n  return !!(value && value[\"__v_isShallow\"]);\n}\nfunction isProxy(value) {\n  return isReactive(value) || isReadonly(value);\n}\nfunction toRaw(observed) {\n  const raw = observed && observed[\"__v_raw\"];\n  return raw ? toRaw(raw) : observed;\n}\nfunction markRaw(value) {\n  if (Object.isExtensible(value)) {\n    def(value, \"__v_skip\", true);\n  }\n  return value;\n}\nconst toReactive = (value) => isObject(value) ? reactive(value) : value;\nconst toReadonly = (value) => isObject(value) ? readonly(value) : value;\n\nconst COMPUTED_SIDE_EFFECT_WARN = `Computed is still dirty after getter evaluation, likely because a computed is mutating its own dependency in its getter. State mutations in computed getters should be avoided.  Check the docs for more details: https://vuejs.org/guide/essentials/computed.html#getters-should-be-side-effect-free`;\nclass ComputedRefImpl {\n  constructor(getter, _setter, isReadonly, isSSR) {\n    this.getter = getter;\n    this._setter = _setter;\n    this.dep = void 0;\n    this.__v_isRef = true;\n    this[\"__v_isReadonly\"] = false;\n    this.effect = new ReactiveEffect(\n      () => getter(this._value),\n      () => triggerRefValue(\n        this,\n        this.effect._dirtyLevel === 2 ? 2 : 3\n      )\n    );\n    this.effect.computed = this;\n    this.effect.active = this._cacheable = !isSSR;\n    this[\"__v_isReadonly\"] = isReadonly;\n  }\n  get value() {\n    const self = toRaw(this);\n    if ((!self._cacheable || self.effect.dirty) && hasChanged(self._value, self._value = self.effect.run())) {\n      triggerRefValue(self, 4);\n    }\n    trackRefValue(self);\n    if (self.effect._dirtyLevel >= 2) {\n      if (!!(process.env.NODE_ENV !== \"production\") && this._warnRecursive) {\n        warn$2(COMPUTED_SIDE_EFFECT_WARN, `\n\ngetter: `, this.getter);\n      }\n      triggerRefValue(self, 2);\n    }\n    return self._value;\n  }\n  set value(newValue) {\n    this._setter(newValue);\n  }\n  // #region polyfill _dirty for backward compatibility third party code for Vue <= 3.3.x\n  get _dirty() {\n    return this.effect.dirty;\n  }\n  set _dirty(v) {\n    this.effect.dirty = v;\n  }\n  // #endregion\n}\nfunction computed$1(getterOrOptions, debugOptions, isSSR = false) {\n  let getter;\n  let setter;\n  const onlyGetter = isFunction(getterOrOptions);\n  if (onlyGetter) {\n    getter = getterOrOptions;\n    setter = !!(process.env.NODE_ENV !== \"production\") ? () => {\n      warn$2(\"Write operation failed: computed value is readonly\");\n    } : NOOP;\n  } else {\n    getter = getterOrOptions.get;\n    setter = getterOrOptions.set;\n  }\n  const cRef = new ComputedRefImpl(getter, setter, onlyGetter || !setter, isSSR);\n  if (!!(process.env.NODE_ENV !== \"production\") && debugOptions && !isSSR) {\n    cRef.effect.onTrack = debugOptions.onTrack;\n    cRef.effect.onTrigger = debugOptions.onTrigger;\n  }\n  return cRef;\n}\n\nfunction trackRefValue(ref2) {\n  var _a;\n  if (shouldTrack && activeEffect) {\n    ref2 = toRaw(ref2);\n    trackEffect(\n      activeEffect,\n      (_a = ref2.dep) != null ? _a : ref2.dep = createDep(\n        () => ref2.dep = void 0,\n        ref2 instanceof ComputedRefImpl ? ref2 : void 0\n      ),\n      !!(process.env.NODE_ENV !== \"production\") ? {\n        target: ref2,\n        type: \"get\",\n        key: \"value\"\n      } : void 0\n    );\n  }\n}\nfunction triggerRefValue(ref2, dirtyLevel = 4, newVal) {\n  ref2 = toRaw(ref2);\n  const dep = ref2.dep;\n  if (dep) {\n    triggerEffects(\n      dep,\n      dirtyLevel,\n      !!(process.env.NODE_ENV !== \"production\") ? {\n        target: ref2,\n        type: \"set\",\n        key: \"value\",\n        newValue: newVal\n      } : void 0\n    );\n  }\n}\nfunction isRef(r) {\n  return !!(r && r.__v_isRef === true);\n}\nfunction ref(value) {\n  return createRef(value, false);\n}\nfunction shallowRef(value) {\n  return createRef(value, true);\n}\nfunction createRef(rawValue, shallow) {\n  if (isRef(rawValue)) {\n    return rawValue;\n  }\n  return new RefImpl(rawValue, shallow);\n}\nclass RefImpl {\n  constructor(value, __v_isShallow) {\n    this.__v_isShallow = __v_isShallow;\n    this.dep = void 0;\n    this.__v_isRef = true;\n    this._rawValue = __v_isShallow ? value : toRaw(value);\n    this._value = __v_isShallow ? value : toReactive(value);\n  }\n  get value() {\n    trackRefValue(this);\n    return this._value;\n  }\n  set value(newVal) {\n    const useDirectValue = this.__v_isShallow || isShallow(newVal) || isReadonly(newVal);\n    newVal = useDirectValue ? newVal : toRaw(newVal);\n    if (hasChanged(newVal, this._rawValue)) {\n      this._rawValue = newVal;\n      this._value = useDirectValue ? newVal : toReactive(newVal);\n      triggerRefValue(this, 4, newVal);\n    }\n  }\n}\nfunction triggerRef(ref2) {\n  triggerRefValue(ref2, 4, !!(process.env.NODE_ENV !== \"production\") ? ref2.value : void 0);\n}\nfunction unref(ref2) {\n  return isRef(ref2) ? ref2.value : ref2;\n}\nfunction toValue(source) {\n  return isFunction(source) ? source() : unref(source);\n}\nconst shallowUnwrapHandlers = {\n  get: (target, key, receiver) => unref(Reflect.get(target, key, receiver)),\n  set: (target, key, value, receiver) => {\n    const oldValue = target[key];\n    if (isRef(oldValue) && !isRef(value)) {\n      oldValue.value = value;\n      return true;\n    } else {\n      return Reflect.set(target, key, value, receiver);\n    }\n  }\n};\nfunction proxyRefs(objectWithRefs) {\n  return isReactive(objectWithRefs) ? objectWithRefs : new Proxy(objectWithRefs, shallowUnwrapHandlers);\n}\nclass CustomRefImpl {\n  constructor(factory) {\n    this.dep = void 0;\n    this.__v_isRef = true;\n    const { get, set } = factory(\n      () => trackRefValue(this),\n      () => triggerRefValue(this)\n    );\n    this._get = get;\n    this._set = set;\n  }\n  get value() {\n    return this._get();\n  }\n  set value(newVal) {\n    this._set(newVal);\n  }\n}\nfunction customRef(factory) {\n  return new CustomRefImpl(factory);\n}\nfunction toRefs(object) {\n  if (!!(process.env.NODE_ENV !== \"production\") && !isProxy(object)) {\n    warn$2(`toRefs() expects a reactive object but received a plain one.`);\n  }\n  const ret = isArray(object) ? new Array(object.length) : {};\n  for (const key in object) {\n    ret[key] = propertyToRef(object, key);\n  }\n  return ret;\n}\nclass ObjectRefImpl {\n  constructor(_object, _key, _defaultValue) {\n    this._object = _object;\n    this._key = _key;\n    this._defaultValue = _defaultValue;\n    this.__v_isRef = true;\n  }\n  get value() {\n    const val = this._object[this._key];\n    return val === void 0 ? this._defaultValue : val;\n  }\n  set value(newVal) {\n    this._object[this._key] = newVal;\n  }\n  get dep() {\n    return getDepFromReactive(toRaw(this._object), this._key);\n  }\n}\nclass GetterRefImpl {\n  constructor(_getter) {\n    this._getter = _getter;\n    this.__v_isRef = true;\n    this.__v_isReadonly = true;\n  }\n  get value() {\n    return this._getter();\n  }\n}\nfunction toRef(source, key, defaultValue) {\n  if (isRef(source)) {\n    return source;\n  } else if (isFunction(source)) {\n    return new GetterRefImpl(source);\n  } else if (isObject(source) && arguments.length > 1) {\n    return propertyToRef(source, key, defaultValue);\n  } else {\n    return ref(source);\n  }\n}\nfunction propertyToRef(source, key, defaultValue) {\n  const val = source[key];\n  return isRef(val) ? val : new ObjectRefImpl(source, key, defaultValue);\n}\n\nconst stack = [];\nfunction pushWarningContext(vnode) {\n  stack.push(vnode);\n}\nfunction popWarningContext() {\n  stack.pop();\n}\nfunction warn$1(msg, ...args) {\n  pauseTracking();\n  const instance = stack.length ? stack[stack.length - 1].component : null;\n  const appWarnHandler = instance && instance.appContext.config.warnHandler;\n  const trace = getComponentTrace();\n  if (appWarnHandler) {\n    callWithErrorHandling(\n      appWarnHandler,\n      instance,\n      11,\n      [\n        msg + args.map((a) => {\n          var _a, _b;\n          return (_b = (_a = a.toString) == null ? void 0 : _a.call(a)) != null ? _b : JSON.stringify(a);\n        }).join(\"\"),\n        instance && instance.proxy,\n        trace.map(\n          ({ vnode }) => `at <${formatComponentName(instance, vnode.type)}>`\n        ).join(\"\\n\"),\n        trace\n      ]\n    );\n  } else {\n    const warnArgs = [`[Vue warn]: ${msg}`, ...args];\n    if (trace.length && // avoid spamming console during tests\n    true) {\n      warnArgs.push(`\n`, ...formatTrace(trace));\n    }\n    console.warn(...warnArgs);\n  }\n  resetTracking();\n}\nfunction getComponentTrace() {\n  let currentVNode = stack[stack.length - 1];\n  if (!currentVNode) {\n    return [];\n  }\n  const normalizedStack = [];\n  while (currentVNode) {\n    const last = normalizedStack[0];\n    if (last && last.vnode === currentVNode) {\n      last.recurseCount++;\n    } else {\n      normalizedStack.push({\n        vnode: currentVNode,\n        recurseCount: 0\n      });\n    }\n    const parentInstance = currentVNode.component && currentVNode.component.parent;\n    currentVNode = parentInstance && parentInstance.vnode;\n  }\n  return normalizedStack;\n}\nfunction formatTrace(trace) {\n  const logs = [];\n  trace.forEach((entry, i) => {\n    logs.push(...i === 0 ? [] : [`\n`], ...formatTraceEntry(entry));\n  });\n  return logs;\n}\nfunction formatTraceEntry({ vnode, recurseCount }) {\n  const postfix = recurseCount > 0 ? `... (${recurseCount} recursive calls)` : ``;\n  const isRoot = vnode.component ? vnode.component.parent == null : false;\n  const open = ` at <${formatComponentName(\n    vnode.component,\n    vnode.type,\n    isRoot\n  )}`;\n  const close = `>` + postfix;\n  return vnode.props ? [open, ...formatProps(vnode.props), close] : [open + close];\n}\nfunction formatProps(props) {\n  const res = [];\n  const keys = Object.keys(props);\n  keys.slice(0, 3).forEach((key) => {\n    res.push(...formatProp(key, props[key]));\n  });\n  if (keys.length > 3) {\n    res.push(` ...`);\n  }\n  return res;\n}\nfunction formatProp(key, value, raw) {\n  if (isString(value)) {\n    value = JSON.stringify(value);\n    return raw ? value : [`${key}=${value}`];\n  } else if (typeof value === \"number\" || typeof value === \"boolean\" || value == null) {\n    return raw ? value : [`${key}=${value}`];\n  } else if (isRef(value)) {\n    value = formatProp(key, toRaw(value.value), true);\n    return raw ? value : [`${key}=Ref<`, value, `>`];\n  } else if (isFunction(value)) {\n    return [`${key}=fn${value.name ? `<${value.name}>` : ``}`];\n  } else {\n    value = toRaw(value);\n    return raw ? value : [`${key}=`, value];\n  }\n}\n\nconst ErrorTypeStrings = {\n  [\"sp\"]: \"serverPrefetch hook\",\n  [\"bc\"]: \"beforeCreate hook\",\n  [\"c\"]: \"created hook\",\n  [\"bm\"]: \"beforeMount hook\",\n  [\"m\"]: \"mounted hook\",\n  [\"bu\"]: \"beforeUpdate hook\",\n  [\"u\"]: \"updated\",\n  [\"bum\"]: \"beforeUnmount hook\",\n  [\"um\"]: \"unmounted hook\",\n  [\"a\"]: \"activated hook\",\n  [\"da\"]: \"deactivated hook\",\n  [\"ec\"]: \"errorCaptured hook\",\n  [\"rtc\"]: \"renderTracked hook\",\n  [\"rtg\"]: \"renderTriggered hook\",\n  [0]: \"setup function\",\n  [1]: \"render function\",\n  [2]: \"watcher getter\",\n  [3]: \"watcher callback\",\n  [4]: \"watcher cleanup function\",\n  [5]: \"native event handler\",\n  [6]: \"component event handler\",\n  [7]: \"vnode hook\",\n  [8]: \"directive hook\",\n  [9]: \"transition hook\",\n  [10]: \"app errorHandler\",\n  [11]: \"app warnHandler\",\n  [12]: \"ref function\",\n  [13]: \"async component loader\",\n  [14]: \"scheduler flush. This is likely a Vue internals bug. Please open an issue at https://github.com/vuejs/core .\"\n};\nfunction callWithErrorHandling(fn, instance, type, args) {\n  try {\n    return args ? fn(...args) : fn();\n  } catch (err) {\n    handleError(err, instance, type);\n  }\n}\nfunction callWithAsyncErrorHandling(fn, instance, type, args) {\n  if (isFunction(fn)) {\n    const res = callWithErrorHandling(fn, instance, type, args);\n    if (res && isPromise(res)) {\n      res.catch((err) => {\n        handleError(err, instance, type);\n      });\n    }\n    return res;\n  }\n  const values = [];\n  for (let i = 0; i < fn.length; i++) {\n    values.push(callWithAsyncErrorHandling(fn[i], instance, type, args));\n  }\n  return values;\n}\nfunction handleError(err, instance, type, throwInDev = true) {\n  const contextVNode = instance ? instance.vnode : null;\n  if (instance) {\n    let cur = instance.parent;\n    const exposedInstance = instance.proxy;\n    const errorInfo = !!(process.env.NODE_ENV !== \"production\") ? ErrorTypeStrings[type] || type : `https://vuejs.org/error-reference/#runtime-${type}`;\n    while (cur) {\n      const errorCapturedHooks = cur.ec;\n      if (errorCapturedHooks) {\n        for (let i = 0; i < errorCapturedHooks.length; i++) {\n          if (errorCapturedHooks[i](err, exposedInstance, errorInfo) === false) {\n            return;\n          }\n        }\n      }\n      cur = cur.parent;\n    }\n    const appErrorHandler = instance.appContext.config.errorHandler;\n    if (appErrorHandler) {\n      callWithErrorHandling(\n        appErrorHandler,\n        null,\n        10,\n        [err, exposedInstance, errorInfo]\n      );\n      return;\n    }\n  }\n  logError(err, type, contextVNode, throwInDev);\n}\nfunction logError(err, type, contextVNode, throwInDev = true) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const info = ErrorTypeStrings[type] || type;\n    if (contextVNode) {\n      pushWarningContext(contextVNode);\n    }\n    warn$1(`Unhandled error${info ? ` during execution of ${info}` : ``}`);\n    if (contextVNode) {\n      popWarningContext();\n    }\n    if (throwInDev) {\n      console.error(err);\n    } else {\n      console.error(err);\n    }\n  } else {\n    console.error(err);\n  }\n}\n\nlet isFlushing = false;\nlet isFlushPending = false;\nconst queue = [];\nlet flushIndex = 0;\nconst pendingPostFlushCbs = [];\nlet activePostFlushCbs = null;\nlet postFlushIndex = 0;\nconst resolvedPromise = /* @__PURE__ */ Promise.resolve();\nlet currentFlushPromise = null;\nconst RECURSION_LIMIT = 100;\nfunction nextTick$1(fn) {\n  const p = currentFlushPromise || resolvedPromise;\n  return fn ? p.then(this ? fn.bind(this) : fn) : p;\n}\nfunction findInsertionIndex(id) {\n  let start = flushIndex + 1;\n  let end = queue.length;\n  while (start < end) {\n    const middle = start + end >>> 1;\n    const middleJob = queue[middle];\n    const middleJobId = getId(middleJob);\n    if (middleJobId < id || middleJobId === id && middleJob.pre) {\n      start = middle + 1;\n    } else {\n      end = middle;\n    }\n  }\n  return start;\n}\nfunction queueJob(job) {\n  if (!queue.length || !queue.includes(\n    job,\n    isFlushing && job.allowRecurse ? flushIndex + 1 : flushIndex\n  )) {\n    if (job.id == null) {\n      queue.push(job);\n    } else {\n      queue.splice(findInsertionIndex(job.id), 0, job);\n    }\n    queueFlush();\n  }\n}\nfunction queueFlush() {\n  if (!isFlushing && !isFlushPending) {\n    isFlushPending = true;\n    currentFlushPromise = resolvedPromise.then(flushJobs);\n  }\n}\nfunction hasQueueJob(job) {\n  return queue.indexOf(job) > -1;\n}\nfunction invalidateJob(job) {\n  const i = queue.indexOf(job);\n  if (i > flushIndex) {\n    queue.splice(i, 1);\n  }\n}\nfunction queuePostFlushCb(cb) {\n  if (!isArray(cb)) {\n    if (!activePostFlushCbs || !activePostFlushCbs.includes(\n      cb,\n      cb.allowRecurse ? postFlushIndex + 1 : postFlushIndex\n    )) {\n      pendingPostFlushCbs.push(cb);\n    }\n  } else {\n    pendingPostFlushCbs.push(...cb);\n  }\n  queueFlush();\n}\nfunction flushPreFlushCbs(instance, seen, i = isFlushing ? flushIndex + 1 : 0) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    seen = seen || /* @__PURE__ */ new Map();\n  }\n  for (; i < queue.length; i++) {\n    const cb = queue[i];\n    if (cb && cb.pre) {\n      if (!!(process.env.NODE_ENV !== \"production\") && checkRecursiveUpdates(seen, cb)) {\n        continue;\n      }\n      queue.splice(i, 1);\n      i--;\n      cb();\n    }\n  }\n}\nfunction flushPostFlushCbs(seen) {\n  if (pendingPostFlushCbs.length) {\n    const deduped = [...new Set(pendingPostFlushCbs)].sort(\n      (a, b) => getId(a) - getId(b)\n    );\n    pendingPostFlushCbs.length = 0;\n    if (activePostFlushCbs) {\n      activePostFlushCbs.push(...deduped);\n      return;\n    }\n    activePostFlushCbs = deduped;\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      seen = seen || /* @__PURE__ */ new Map();\n    }\n    for (postFlushIndex = 0; postFlushIndex < activePostFlushCbs.length; postFlushIndex++) {\n      if (!!(process.env.NODE_ENV !== \"production\") && checkRecursiveUpdates(seen, activePostFlushCbs[postFlushIndex])) {\n        continue;\n      }\n      activePostFlushCbs[postFlushIndex]();\n    }\n    activePostFlushCbs = null;\n    postFlushIndex = 0;\n  }\n}\nconst getId = (job) => job.id == null ? Infinity : job.id;\nconst comparator = (a, b) => {\n  const diff = getId(a) - getId(b);\n  if (diff === 0) {\n    if (a.pre && !b.pre)\n      return -1;\n    if (b.pre && !a.pre)\n      return 1;\n  }\n  return diff;\n};\nfunction flushJobs(seen) {\n  isFlushPending = false;\n  isFlushing = true;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    seen = seen || /* @__PURE__ */ new Map();\n  }\n  queue.sort(comparator);\n  const check = !!(process.env.NODE_ENV !== \"production\") ? (job) => checkRecursiveUpdates(seen, job) : NOOP;\n  try {\n    for (flushIndex = 0; flushIndex < queue.length; flushIndex++) {\n      const job = queue[flushIndex];\n      if (job && job.active !== false) {\n        if (!!(process.env.NODE_ENV !== \"production\") && check(job)) {\n          continue;\n        }\n        callWithErrorHandling(job, null, 14);\n      }\n    }\n  } finally {\n    flushIndex = 0;\n    queue.length = 0;\n    flushPostFlushCbs(seen);\n    isFlushing = false;\n    currentFlushPromise = null;\n    if (queue.length || pendingPostFlushCbs.length) {\n      flushJobs(seen);\n    }\n  }\n}\nfunction checkRecursiveUpdates(seen, fn) {\n  if (!seen.has(fn)) {\n    seen.set(fn, 1);\n  } else {\n    const count = seen.get(fn);\n    if (count > RECURSION_LIMIT) {\n      const instance = fn.ownerInstance;\n      const componentName = instance && getComponentName(instance.type);\n      handleError(\n        `Maximum recursive updates exceeded${componentName ? ` in component <${componentName}>` : ``}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,\n        null,\n        10\n      );\n      return true;\n    } else {\n      seen.set(fn, count + 1);\n    }\n  }\n}\n\nlet devtools;\nlet buffer = [];\nlet devtoolsNotInstalled = false;\nfunction emit$1(event, ...args) {\n  if (devtools) {\n    devtools.emit(event, ...args);\n  } else if (!devtoolsNotInstalled) {\n    buffer.push({ event, args });\n  }\n}\nfunction setDevtoolsHook(hook, target) {\n  var _a, _b;\n  devtools = hook;\n  if (devtools) {\n    devtools.enabled = true;\n    buffer.forEach(({ event, args }) => devtools.emit(event, ...args));\n    buffer = [];\n  } else if (\n    // handle late devtools injection - only do this if we are in an actual\n    // browser environment to avoid the timer handle stalling test runner exit\n    // (#4815)\n    typeof window !== \"undefined\" && // some envs mock window but not fully\n    window.HTMLElement && // also exclude jsdom\n    !((_b = (_a = window.navigator) == null ? void 0 : _a.userAgent) == null ? void 0 : _b.includes(\"jsdom\"))\n  ) {\n    const replay = target.__VUE_DEVTOOLS_HOOK_REPLAY__ = target.__VUE_DEVTOOLS_HOOK_REPLAY__ || [];\n    replay.push((newHook) => {\n      setDevtoolsHook(newHook, target);\n    });\n    setTimeout(() => {\n      if (!devtools) {\n        target.__VUE_DEVTOOLS_HOOK_REPLAY__ = null;\n        devtoolsNotInstalled = true;\n        buffer = [];\n      }\n    }, 3e3);\n  } else {\n    devtoolsNotInstalled = true;\n    buffer = [];\n  }\n}\nfunction devtoolsInitApp(app, version) {\n  emit$1(\"app:init\" /* APP_INIT */, app, version, {\n    Fragment,\n    Text,\n    Comment,\n    Static\n  });\n}\nconst devtoolsComponentAdded = /* @__PURE__ */ createDevtoolsComponentHook(\n  \"component:added\" /* COMPONENT_ADDED */\n);\nconst devtoolsComponentUpdated = /* @__PURE__ */ createDevtoolsComponentHook(\"component:updated\" /* COMPONENT_UPDATED */);\nconst _devtoolsComponentRemoved = /* @__PURE__ */ createDevtoolsComponentHook(\n  \"component:removed\" /* COMPONENT_REMOVED */\n);\nconst devtoolsComponentRemoved = (component) => {\n  if (devtools && typeof devtools.cleanupBuffer === \"function\" && // remove the component if it wasn't buffered\n  !devtools.cleanupBuffer(component)) {\n    _devtoolsComponentRemoved(component);\n  }\n};\n/*! #__NO_SIDE_EFFECTS__ */\n// @__NO_SIDE_EFFECTS__\nfunction createDevtoolsComponentHook(hook) {\n  return (component) => {\n    emit$1(\n      hook,\n      component.appContext.app,\n      component.uid,\n      // fixed by xxxxxx\n      // 为 0 是 App，无 parent 是 Page 指向 App\n      component.uid === 0 ? void 0 : component.parent ? component.parent.uid : 0,\n      component\n    );\n  };\n}\nconst devtoolsPerfStart = /* @__PURE__ */ createDevtoolsPerformanceHook(\n  \"perf:start\" /* PERFORMANCE_START */\n);\nconst devtoolsPerfEnd = /* @__PURE__ */ createDevtoolsPerformanceHook(\n  \"perf:end\" /* PERFORMANCE_END */\n);\nfunction createDevtoolsPerformanceHook(hook) {\n  return (component, type, time) => {\n    emit$1(hook, component.appContext.app, component.uid, component, type, time);\n  };\n}\nfunction devtoolsComponentEmit(component, event, params) {\n  emit$1(\n    \"component:emit\" /* COMPONENT_EMIT */,\n    component.appContext.app,\n    component,\n    event,\n    params\n  );\n}\n\nfunction emit(instance, event, ...rawArgs) {\n  if (instance.isUnmounted)\n    return;\n  const props = instance.vnode.props || EMPTY_OBJ;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const {\n      emitsOptions,\n      propsOptions: [propsOptions]\n    } = instance;\n    if (emitsOptions) {\n      if (!(event in emitsOptions) && true) {\n        if (!propsOptions || !(toHandlerKey(event) in propsOptions)) {\n          warn$1(\n            `Component emitted event \"${event}\" but it is neither declared in the emits option nor as an \"${toHandlerKey(event)}\" prop.`\n          );\n        }\n      } else {\n        const validator = emitsOptions[event];\n        if (isFunction(validator)) {\n          const isValid = validator(...rawArgs);\n          if (!isValid) {\n            warn$1(\n              `Invalid event arguments: event validation failed for event \"${event}\".`\n            );\n          }\n        }\n      }\n    }\n  }\n  let args = rawArgs;\n  const isModelListener = event.startsWith(\"update:\");\n  const modelArg = isModelListener && event.slice(7);\n  if (modelArg && modelArg in props) {\n    const modifiersKey = `${modelArg === \"modelValue\" ? \"model\" : modelArg}Modifiers`;\n    const { number, trim } = props[modifiersKey] || EMPTY_OBJ;\n    if (trim) {\n      args = rawArgs.map((a) => isString(a) ? a.trim() : a);\n    }\n    if (number) {\n      args = rawArgs.map(looseToNumber);\n    }\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n    devtoolsComponentEmit(instance, event, args);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const lowerCaseEvent = event.toLowerCase();\n    if (lowerCaseEvent !== event && props[toHandlerKey(lowerCaseEvent)]) {\n      warn$1(\n        `Event \"${lowerCaseEvent}\" is emitted in component ${formatComponentName(\n          instance,\n          instance.type\n        )} but the handler is registered for \"${event}\". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use \"${hyphenate(\n          event\n        )}\" instead of \"${event}\".`\n      );\n    }\n  }\n  let handlerName;\n  let handler = props[handlerName = toHandlerKey(event)] || // also try camelCase event handler (#2249)\n  props[handlerName = toHandlerKey(camelize(event))];\n  if (!handler && isModelListener) {\n    handler = props[handlerName = toHandlerKey(hyphenate(event))];\n  }\n  if (handler) {\n    callWithAsyncErrorHandling(\n      handler,\n      instance,\n      6,\n      args\n    );\n  }\n  const onceHandler = props[handlerName + `Once`];\n  if (onceHandler) {\n    if (!instance.emitted) {\n      instance.emitted = {};\n    } else if (instance.emitted[handlerName]) {\n      return;\n    }\n    instance.emitted[handlerName] = true;\n    callWithAsyncErrorHandling(\n      onceHandler,\n      instance,\n      6,\n      args\n    );\n  }\n}\nfunction normalizeEmitsOptions(comp, appContext, asMixin = false) {\n  const cache = appContext.emitsCache;\n  const cached = cache.get(comp);\n  if (cached !== void 0) {\n    return cached;\n  }\n  const raw = comp.emits;\n  let normalized = {};\n  let hasExtends = false;\n  if (__VUE_OPTIONS_API__ && !isFunction(comp)) {\n    const extendEmits = (raw2) => {\n      const normalizedFromExtend = normalizeEmitsOptions(raw2, appContext, true);\n      if (normalizedFromExtend) {\n        hasExtends = true;\n        extend(normalized, normalizedFromExtend);\n      }\n    };\n    if (!asMixin && appContext.mixins.length) {\n      appContext.mixins.forEach(extendEmits);\n    }\n    if (comp.extends) {\n      extendEmits(comp.extends);\n    }\n    if (comp.mixins) {\n      comp.mixins.forEach(extendEmits);\n    }\n  }\n  if (!raw && !hasExtends) {\n    if (isObject(comp)) {\n      cache.set(comp, null);\n    }\n    return null;\n  }\n  if (isArray(raw)) {\n    raw.forEach((key) => normalized[key] = null);\n  } else {\n    extend(normalized, raw);\n  }\n  if (isObject(comp)) {\n    cache.set(comp, normalized);\n  }\n  return normalized;\n}\nfunction isEmitListener(options, key) {\n  if (!options || !isOn(key)) {\n    return false;\n  }\n  key = key.slice(2).replace(/Once$/, \"\");\n  return hasOwn(options, key[0].toLowerCase() + key.slice(1)) || hasOwn(options, hyphenate(key)) || hasOwn(options, key);\n}\n\nlet currentRenderingInstance = null;\nlet currentScopeId = null;\nfunction setCurrentRenderingInstance(instance) {\n  const prev = currentRenderingInstance;\n  currentRenderingInstance = instance;\n  currentScopeId = instance && instance.type.__scopeId || null;\n  return prev;\n}\nconst withScopeId = (_id) => withCtx;\nfunction withCtx(fn, ctx = currentRenderingInstance, isNonScopedSlot) {\n  if (!ctx)\n    return fn;\n  if (fn._n) {\n    return fn;\n  }\n  const renderFnWithContext = (...args) => {\n    if (renderFnWithContext._d) {\n      setBlockTracking(-1);\n    }\n    const prevInstance = setCurrentRenderingInstance(ctx);\n    let res;\n    try {\n      res = fn(...args);\n    } finally {\n      setCurrentRenderingInstance(prevInstance);\n      if (renderFnWithContext._d) {\n        setBlockTracking(1);\n      }\n    }\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      devtoolsComponentUpdated(ctx);\n    }\n    return res;\n  };\n  renderFnWithContext._n = true;\n  renderFnWithContext._c = true;\n  renderFnWithContext._d = true;\n  return renderFnWithContext;\n}\n\nfunction markAttrsAccessed() {\n}\n\nconst COMPONENTS = \"components\";\nconst DIRECTIVES = \"directives\";\nfunction resolveComponent(name, maybeSelfReference) {\n  return resolveAsset(COMPONENTS, name, true, maybeSelfReference) || name;\n}\nconst NULL_DYNAMIC_COMPONENT = Symbol.for(\"v-ndc\");\nfunction resolveDirective(name) {\n  return resolveAsset(DIRECTIVES, name);\n}\nfunction resolveAsset(type, name, warnMissing = true, maybeSelfReference = false) {\n  const instance = currentRenderingInstance || currentInstance;\n  if (instance) {\n    const Component = instance.type;\n    if (type === COMPONENTS) {\n      const selfName = getComponentName(\n        Component,\n        false\n      );\n      if (selfName && (selfName === name || selfName === camelize(name) || selfName === capitalize(camelize(name)))) {\n        return Component;\n      }\n    }\n    const res = (\n      // local registration\n      // check instance[type] first which is resolved for options API\n      resolve(instance[type] || Component[type], name) || // global registration\n      resolve(instance.appContext[type], name)\n    );\n    if (!res && maybeSelfReference) {\n      return Component;\n    }\n    if (!!(process.env.NODE_ENV !== \"production\") && warnMissing && !res) {\n      const extra = type === COMPONENTS ? `\nIf this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.` : ``;\n      warn$1(`Failed to resolve ${type.slice(0, -1)}: ${name}${extra}`);\n    }\n    return res;\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(\n      `resolve${capitalize(type.slice(0, -1))} can only be used in render() or setup().`\n    );\n  }\n}\nfunction resolve(registry, name) {\n  return registry && (registry[name] || registry[camelize(name)] || registry[capitalize(camelize(name))]);\n}\n\nconst ssrContextKey = Symbol.for(\"v-scx\");\nconst useSSRContext = () => {\n  {\n    const ctx = inject(ssrContextKey);\n    if (!ctx) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(\n        `Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build.`\n      );\n    }\n    return ctx;\n  }\n};\n\nfunction watchEffect(effect, options) {\n  return doWatch(effect, null, options);\n}\nfunction watchPostEffect(effect, options) {\n  return doWatch(\n    effect,\n    null,\n    !!(process.env.NODE_ENV !== \"production\") ? extend({}, options, { flush: \"post\" }) : { flush: \"post\" }\n  );\n}\nfunction watchSyncEffect(effect, options) {\n  return doWatch(\n    effect,\n    null,\n    !!(process.env.NODE_ENV !== \"production\") ? extend({}, options, { flush: \"sync\" }) : { flush: \"sync\" }\n  );\n}\nconst INITIAL_WATCHER_VALUE = {};\nfunction watch(source, cb, options) {\n  if (!!(process.env.NODE_ENV !== \"production\") && !isFunction(cb)) {\n    warn$1(\n      `\\`watch(fn, options?)\\` signature has been moved to a separate API. Use \\`watchEffect(fn, options?)\\` instead. \\`watch\\` now only supports \\`watch(source, cb, options?) signature.`\n    );\n  }\n  return doWatch(source, cb, options);\n}\nfunction doWatch(source, cb, {\n  immediate,\n  deep,\n  flush,\n  once,\n  onTrack,\n  onTrigger\n} = EMPTY_OBJ) {\n  if (cb && once) {\n    const _cb = cb;\n    cb = (...args) => {\n      _cb(...args);\n      unwatch();\n    };\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && deep !== void 0 && typeof deep === \"number\") {\n    warn$1(\n      `watch() \"deep\" option with number value will be used as watch depth in future versions. Please use a boolean instead to avoid potential breakage.`\n    );\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && !cb) {\n    if (immediate !== void 0) {\n      warn$1(\n        `watch() \"immediate\" option is only respected when using the watch(source, callback, options?) signature.`\n      );\n    }\n    if (deep !== void 0) {\n      warn$1(\n        `watch() \"deep\" option is only respected when using the watch(source, callback, options?) signature.`\n      );\n    }\n    if (once !== void 0) {\n      warn$1(\n        `watch() \"once\" option is only respected when using the watch(source, callback, options?) signature.`\n      );\n    }\n  }\n  const warnInvalidSource = (s) => {\n    warn$1(\n      `Invalid watch source: `,\n      s,\n      `A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.`\n    );\n  };\n  const instance = currentInstance;\n  const reactiveGetter = (source2) => deep === true ? source2 : (\n    // for deep: false, only traverse root-level properties\n    traverse(source2, deep === false ? 1 : void 0)\n  );\n  let getter;\n  let forceTrigger = false;\n  let isMultiSource = false;\n  if (isRef(source)) {\n    getter = () => source.value;\n    forceTrigger = isShallow(source);\n  } else if (isReactive(source)) {\n    getter = () => reactiveGetter(source);\n    forceTrigger = true;\n  } else if (isArray(source)) {\n    isMultiSource = true;\n    forceTrigger = source.some((s) => isReactive(s) || isShallow(s));\n    getter = () => source.map((s) => {\n      if (isRef(s)) {\n        return s.value;\n      } else if (isReactive(s)) {\n        return reactiveGetter(s);\n      } else if (isFunction(s)) {\n        return callWithErrorHandling(s, instance, 2);\n      } else {\n        !!(process.env.NODE_ENV !== \"production\") && warnInvalidSource(s);\n      }\n    });\n  } else if (isFunction(source)) {\n    if (cb) {\n      getter = () => callWithErrorHandling(source, instance, 2);\n    } else {\n      getter = () => {\n        if (cleanup) {\n          cleanup();\n        }\n        return callWithAsyncErrorHandling(\n          source,\n          instance,\n          3,\n          [onCleanup]\n        );\n      };\n    }\n  } else {\n    getter = NOOP;\n    !!(process.env.NODE_ENV !== \"production\") && warnInvalidSource(source);\n  }\n  if (cb && deep) {\n    const baseGetter = getter;\n    getter = () => traverse(baseGetter());\n  }\n  let cleanup;\n  let onCleanup = (fn) => {\n    cleanup = effect.onStop = () => {\n      callWithErrorHandling(fn, instance, 4);\n      cleanup = effect.onStop = void 0;\n    };\n  };\n  let oldValue = isMultiSource ? new Array(source.length).fill(INITIAL_WATCHER_VALUE) : INITIAL_WATCHER_VALUE;\n  const job = () => {\n    if (!effect.active || !effect.dirty) {\n      return;\n    }\n    if (cb) {\n      const newValue = effect.run();\n      if (deep || forceTrigger || (isMultiSource ? newValue.some((v, i) => hasChanged(v, oldValue[i])) : hasChanged(newValue, oldValue)) || false) {\n        if (cleanup) {\n          cleanup();\n        }\n        callWithAsyncErrorHandling(cb, instance, 3, [\n          newValue,\n          // pass undefined as the old value when it's changed for the first time\n          oldValue === INITIAL_WATCHER_VALUE ? void 0 : isMultiSource && oldValue[0] === INITIAL_WATCHER_VALUE ? [] : oldValue,\n          onCleanup\n        ]);\n        oldValue = newValue;\n      }\n    } else {\n      effect.run();\n    }\n  };\n  job.allowRecurse = !!cb;\n  let scheduler;\n  if (flush === \"sync\") {\n    scheduler = job;\n  } else if (flush === \"post\") {\n    scheduler = () => queuePostRenderEffect$1(job, instance && instance.suspense);\n  } else {\n    job.pre = true;\n    if (instance)\n      job.id = instance.uid;\n    scheduler = () => queueJob(job);\n  }\n  const effect = new ReactiveEffect(getter, NOOP, scheduler);\n  const scope = getCurrentScope();\n  const unwatch = () => {\n    effect.stop();\n    if (scope) {\n      remove(scope.effects, effect);\n    }\n  };\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    effect.onTrack = onTrack;\n    effect.onTrigger = onTrigger;\n  }\n  if (cb) {\n    if (immediate) {\n      job();\n    } else {\n      oldValue = effect.run();\n    }\n  } else if (flush === \"post\") {\n    queuePostRenderEffect$1(\n      effect.run.bind(effect),\n      instance && instance.suspense\n    );\n  } else {\n    effect.run();\n  }\n  return unwatch;\n}\nfunction instanceWatch(source, value, options) {\n  const publicThis = this.proxy;\n  const getter = isString(source) ? source.includes(\".\") ? createPathGetter(publicThis, source) : () => publicThis[source] : source.bind(publicThis, publicThis);\n  let cb;\n  if (isFunction(value)) {\n    cb = value;\n  } else {\n    cb = value.handler;\n    options = value;\n  }\n  const reset = setCurrentInstance(this);\n  const res = doWatch(getter, cb.bind(publicThis), options);\n  reset();\n  return res;\n}\nfunction createPathGetter(ctx, path) {\n  const segments = path.split(\".\");\n  return () => {\n    let cur = ctx;\n    for (let i = 0; i < segments.length && cur; i++) {\n      cur = cur[segments[i]];\n    }\n    return cur;\n  };\n}\nfunction traverse(value, depth, currentDepth = 0, seen) {\n  if (!isObject(value) || value[\"__v_skip\"]) {\n    return value;\n  }\n  if (depth && depth > 0) {\n    if (currentDepth >= depth) {\n      return value;\n    }\n    currentDepth++;\n  }\n  seen = seen || /* @__PURE__ */ new Set();\n  if (seen.has(value)) {\n    return value;\n  }\n  seen.add(value);\n  if (isRef(value)) {\n    traverse(value.value, depth, currentDepth, seen);\n  } else if (isArray(value)) {\n    for (let i = 0; i < value.length; i++) {\n      traverse(value[i], depth, currentDepth, seen);\n    }\n  } else if (isSet(value) || isMap(value)) {\n    value.forEach((v) => {\n      traverse(v, depth, currentDepth, seen);\n    });\n  } else if (isPlainObject(value)) {\n    for (const key in value) {\n      traverse(value[key], depth, currentDepth, seen);\n    }\n  }\n  return value;\n}\n\nfunction validateDirectiveName(name) {\n  if (isBuiltInDirective(name)) {\n    warn$1(\"Do not use built-in directive ids as custom directive id: \" + name);\n  }\n}\nfunction withDirectives(vnode, directives) {\n  if (currentRenderingInstance === null) {\n    !!(process.env.NODE_ENV !== \"production\") && warn$1(`withDirectives can only be used inside render functions.`);\n    return vnode;\n  }\n  const instance = getExposeProxy(currentRenderingInstance) || currentRenderingInstance.proxy;\n  const bindings = vnode.dirs || (vnode.dirs = []);\n  for (let i = 0; i < directives.length; i++) {\n    let [dir, value, arg, modifiers = EMPTY_OBJ] = directives[i];\n    if (dir) {\n      if (isFunction(dir)) {\n        dir = {\n          mounted: dir,\n          updated: dir\n        };\n      }\n      if (dir.deep) {\n        traverse(value);\n      }\n      bindings.push({\n        dir,\n        instance,\n        value,\n        oldValue: void 0,\n        arg,\n        modifiers\n      });\n    }\n  }\n  return vnode;\n}\n\nfunction createAppContext() {\n  return {\n    app: null,\n    config: {\n      isNativeTag: NO,\n      performance: false,\n      globalProperties: {},\n      optionMergeStrategies: {},\n      errorHandler: void 0,\n      warnHandler: void 0,\n      compilerOptions: {}\n    },\n    mixins: [],\n    components: {},\n    directives: {},\n    provides: /* @__PURE__ */ Object.create(null),\n    optionsCache: /* @__PURE__ */ new WeakMap(),\n    propsCache: /* @__PURE__ */ new WeakMap(),\n    emitsCache: /* @__PURE__ */ new WeakMap()\n  };\n}\nlet uid$1 = 0;\nfunction createAppAPI(render, hydrate) {\n  return function createApp(rootComponent, rootProps = null) {\n    if (!isFunction(rootComponent)) {\n      rootComponent = extend({}, rootComponent);\n    }\n    if (rootProps != null && !isObject(rootProps)) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`root props passed to app.mount() must be an object.`);\n      rootProps = null;\n    }\n    const context = createAppContext();\n    const installedPlugins = /* @__PURE__ */ new WeakSet();\n    const app = context.app = {\n      _uid: uid$1++,\n      _component: rootComponent,\n      _props: rootProps,\n      _container: null,\n      _context: context,\n      _instance: null,\n      version,\n      get config() {\n        return context.config;\n      },\n      set config(v) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(\n            `app.config cannot be replaced. Modify individual options instead.`\n          );\n        }\n      },\n      use(plugin, ...options) {\n        if (installedPlugins.has(plugin)) {\n          !!(process.env.NODE_ENV !== \"production\") && warn$1(`Plugin has already been applied to target app.`);\n        } else if (plugin && isFunction(plugin.install)) {\n          installedPlugins.add(plugin);\n          plugin.install(app, ...options);\n        } else if (isFunction(plugin)) {\n          installedPlugins.add(plugin);\n          plugin(app, ...options);\n        } else if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(\n            `A plugin must either be a function or an object with an \"install\" function.`\n          );\n        }\n        return app;\n      },\n      mixin(mixin) {\n        if (__VUE_OPTIONS_API__) {\n          if (!context.mixins.includes(mixin)) {\n            context.mixins.push(mixin);\n          } else if (!!(process.env.NODE_ENV !== \"production\")) {\n            warn$1(\n              \"Mixin has already been applied to target app\" + (mixin.name ? `: ${mixin.name}` : \"\")\n            );\n          }\n        } else if (!!(process.env.NODE_ENV !== \"production\")) {\n          warn$1(\"Mixins are only available in builds supporting Options API\");\n        }\n        return app;\n      },\n      component(name, component) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          validateComponentName(name, context.config);\n        }\n        if (!component) {\n          return context.components[name];\n        }\n        if (!!(process.env.NODE_ENV !== \"production\") && context.components[name]) {\n          warn$1(`Component \"${name}\" has already been registered in target app.`);\n        }\n        context.components[name] = component;\n        return app;\n      },\n      directive(name, directive) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          validateDirectiveName(name);\n        }\n        if (!directive) {\n          return context.directives[name];\n        }\n        if (!!(process.env.NODE_ENV !== \"production\") && context.directives[name]) {\n          warn$1(`Directive \"${name}\" has already been registered in target app.`);\n        }\n        context.directives[name] = directive;\n        return app;\n      },\n      // fixed by xxxxxx\n      mount() {\n      },\n      // fixed by xxxxxx\n      unmount() {\n      },\n      provide(key, value) {\n        if (!!(process.env.NODE_ENV !== \"production\") && key in context.provides) {\n          warn$1(\n            `App already provides property with key \"${String(key)}\". It will be overwritten with the new value.`\n          );\n        }\n        context.provides[key] = value;\n        return app;\n      },\n      runWithContext(fn) {\n        const lastApp = currentApp;\n        currentApp = app;\n        try {\n          return fn();\n        } finally {\n          currentApp = lastApp;\n        }\n      }\n    };\n    return app;\n  };\n}\nlet currentApp = null;\n\nfunction provide(key, value) {\n  if (!currentInstance) {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$1(`provide() can only be used inside setup().`);\n    }\n  } else {\n    let provides = currentInstance.provides;\n    const parentProvides = currentInstance.parent && currentInstance.parent.provides;\n    if (parentProvides === provides) {\n      provides = currentInstance.provides = Object.create(parentProvides);\n    }\n    provides[key] = value;\n    if (currentInstance.type.mpType === \"app\") {\n      currentInstance.appContext.app.provide(key, value);\n    }\n  }\n}\nfunction inject(key, defaultValue, treatDefaultAsFactory = false) {\n  const instance = currentInstance || currentRenderingInstance;\n  if (instance || currentApp) {\n    const provides = instance ? instance.parent == null ? instance.vnode.appContext && instance.vnode.appContext.provides : instance.parent.provides : currentApp._context.provides;\n    if (provides && key in provides) {\n      return provides[key];\n    } else if (arguments.length > 1) {\n      return treatDefaultAsFactory && isFunction(defaultValue) ? defaultValue.call(instance && instance.proxy) : defaultValue;\n    } else if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$1(`injection \"${String(key)}\" not found.`);\n    }\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`inject() can only be used inside setup() or functional components.`);\n  }\n}\nfunction hasInjectionContext() {\n  return !!(currentInstance || currentRenderingInstance || currentApp);\n}\n\n/*! #__NO_SIDE_EFFECTS__ */\n// @__NO_SIDE_EFFECTS__\nfunction defineComponent(options, extraOptions) {\n  return isFunction(options) ? (\n    // #8326: extend call and options.name access are considered side-effects\n    // by Rollup, so we have to wrap it in a pure-annotated IIFE.\n    /* @__PURE__ */ (() => extend({ name: options.name }, extraOptions, { setup: options }))()\n  ) : options;\n}\n\nconst isKeepAlive = (vnode) => vnode.type.__isKeepAlive;\nfunction onActivated(hook, target) {\n  registerKeepAliveHook(hook, \"a\", target);\n}\nfunction onDeactivated(hook, target) {\n  registerKeepAliveHook(hook, \"da\", target);\n}\nfunction registerKeepAliveHook(hook, type, target = currentInstance) {\n  const wrappedHook = hook.__wdc || (hook.__wdc = () => {\n    let current = target;\n    while (current) {\n      if (current.isDeactivated) {\n        return;\n      }\n      current = current.parent;\n    }\n    return hook();\n  });\n  injectHook(type, wrappedHook, target);\n  if (target) {\n    let current = target.parent;\n    while (current && current.parent) {\n      if (isKeepAlive(current.parent.vnode)) {\n        injectToKeepAliveRoot(wrappedHook, type, target, current);\n      }\n      current = current.parent;\n    }\n  }\n}\nfunction injectToKeepAliveRoot(hook, type, target, keepAliveRoot) {\n  const injected = injectHook(\n    type,\n    hook,\n    keepAliveRoot,\n    true\n    /* prepend */\n  );\n  onUnmounted(() => {\n    remove(keepAliveRoot[type], injected);\n  }, target);\n}\n\nfunction injectHook(type, hook, target = currentInstance, prepend = false) {\n  if (target) {\n    if (isRootHook(type)) {\n      target = target.root;\n    }\n    const hooks = target[type] || (target[type] = []);\n    const wrappedHook = hook.__weh || (hook.__weh = (...args) => {\n      if (target.isUnmounted) {\n        return;\n      }\n      pauseTracking();\n      const reset = setCurrentInstance(target);\n      const res = callWithAsyncErrorHandling(hook, target, type, args);\n      reset();\n      resetTracking();\n      return res;\n    });\n    if (prepend) {\n      hooks.unshift(wrappedHook);\n    } else {\n      hooks.push(wrappedHook);\n    }\n    return wrappedHook;\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    const apiName = toHandlerKey(\n      (ErrorTypeStrings[type] || type.replace(/^on/, \"\")).replace(/ hook$/, \"\")\n    );\n    warn$1(\n      `${apiName} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup().` + (``)\n    );\n  }\n}\nconst createHook = (lifecycle) => (hook, target = currentInstance) => (\n  // post-create lifecycle registrations are noops during SSR (except for serverPrefetch)\n  (!isInSSRComponentSetup || lifecycle === \"sp\") && injectHook(lifecycle, (...args) => hook(...args), target)\n);\nconst onBeforeMount = createHook(\"bm\");\nconst onMounted = createHook(\"m\");\nconst onBeforeUpdate = createHook(\"bu\");\nconst onUpdated = createHook(\"u\");\nconst onBeforeUnmount = createHook(\"bum\");\nconst onUnmounted = createHook(\"um\");\nconst onServerPrefetch = createHook(\"sp\");\nconst onRenderTriggered = createHook(\n  \"rtg\"\n);\nconst onRenderTracked = createHook(\n  \"rtc\"\n);\nfunction onErrorCaptured(hook, target = currentInstance) {\n  injectHook(\"ec\", hook, target);\n}\n\nfunction toHandlers(obj, preserveCaseIfNecessary) {\n  const ret = {};\n  if (!!(process.env.NODE_ENV !== \"production\") && !isObject(obj)) {\n    warn$1(`v-on with no argument expects an object value.`);\n    return ret;\n  }\n  for (const key in obj) {\n    ret[preserveCaseIfNecessary && /[A-Z]/.test(key) ? `on:${key}` : toHandlerKey(key)] = obj[key];\n  }\n  return ret;\n}\n\nconst getPublicInstance = (i) => {\n  if (!i)\n    return null;\n  if (isStatefulComponent(i))\n    return getExposeProxy(i) || i.proxy;\n  return getPublicInstance(i.parent);\n};\nconst publicPropertiesMap = (\n  // Move PURE marker to new line to workaround compiler discarding it\n  // due to type annotation\n  /* @__PURE__ */ extend(/* @__PURE__ */ Object.create(null), {\n    $: (i) => i,\n    // fixed by xxxxxx vue-i18n 在 dev 模式，访问了 $el，故模拟一个假的\n    // $el: i => i.vnode.el,\n    $el: (i) => i.__$el || (i.__$el = {}),\n    $data: (i) => i.data,\n    $props: (i) => !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(i.props) : i.props,\n    $attrs: (i) => !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(i.attrs) : i.attrs,\n    $slots: (i) => !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(i.slots) : i.slots,\n    $refs: (i) => !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(i.refs) : i.refs,\n    $parent: (i) => getPublicInstance(i.parent),\n    $root: (i) => getPublicInstance(i.root),\n    $emit: (i) => i.emit,\n    $options: (i) => __VUE_OPTIONS_API__ ? resolveMergedOptions(i) : i.type,\n    $forceUpdate: (i) => i.f || (i.f = () => {\n      i.effect.dirty = true;\n      queueJob(i.update);\n    }),\n    // $nextTick: i => i.n || (i.n = nextTick.bind(i.proxy!)),// fixed by xxxxxx\n    $watch: (i) => __VUE_OPTIONS_API__ ? instanceWatch.bind(i) : NOOP\n  })\n);\nconst isReservedPrefix = (key) => key === \"_\" || key === \"$\";\nconst hasSetupBinding = (state, key) => state !== EMPTY_OBJ && !state.__isScriptSetup && hasOwn(state, key);\nconst PublicInstanceProxyHandlers = {\n  get({ _: instance }, key) {\n    const { ctx, setupState, data, props, accessCache, type, appContext } = instance;\n    if (!!(process.env.NODE_ENV !== \"production\") && key === \"__isVue\") {\n      return true;\n    }\n    let normalizedProps;\n    if (key[0] !== \"$\") {\n      const n = accessCache[key];\n      if (n !== void 0) {\n        switch (n) {\n          case 1 /* SETUP */:\n            return setupState[key];\n          case 2 /* DATA */:\n            return data[key];\n          case 4 /* CONTEXT */:\n            return ctx[key];\n          case 3 /* PROPS */:\n            return props[key];\n        }\n      } else if (hasSetupBinding(setupState, key)) {\n        accessCache[key] = 1 /* SETUP */;\n        return setupState[key];\n      } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {\n        accessCache[key] = 2 /* DATA */;\n        return data[key];\n      } else if (\n        // only cache other properties when instance has declared (thus stable)\n        // props\n        (normalizedProps = instance.propsOptions[0]) && hasOwn(normalizedProps, key)\n      ) {\n        accessCache[key] = 3 /* PROPS */;\n        return props[key];\n      } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {\n        accessCache[key] = 4 /* CONTEXT */;\n        return ctx[key];\n      } else if (!__VUE_OPTIONS_API__ || shouldCacheAccess) {\n        accessCache[key] = 0 /* OTHER */;\n      }\n    }\n    const publicGetter = publicPropertiesMap[key];\n    let cssModule, globalProperties;\n    if (publicGetter) {\n      if (key === \"$attrs\") {\n        track(instance, \"get\", key);\n        !!(process.env.NODE_ENV !== \"production\") && markAttrsAccessed();\n      } else if (!!(process.env.NODE_ENV !== \"production\") && key === \"$slots\") {\n        track(instance, \"get\", key);\n      }\n      return publicGetter(instance);\n    } else if (\n      // css module (injected by vue-loader)\n      (cssModule = type.__cssModules) && (cssModule = cssModule[key])\n    ) {\n      return cssModule;\n    } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {\n      accessCache[key] = 4 /* CONTEXT */;\n      return ctx[key];\n    } else if (\n      // global properties\n      globalProperties = appContext.config.globalProperties, hasOwn(globalProperties, key)\n    ) {\n      {\n        return globalProperties[key];\n      }\n    } else if (!!(process.env.NODE_ENV !== \"production\") && currentRenderingInstance && (!isString(key) || // #1091 avoid internal isRef/isVNode checks on component instance leading\n    // to infinite warning loop\n    key.indexOf(\"__v\") !== 0)) {\n      if (data !== EMPTY_OBJ && isReservedPrefix(key[0]) && hasOwn(data, key)) {\n        warn$1(\n          `Property ${JSON.stringify(\n            key\n          )} must be accessed via $data because it starts with a reserved character (\"$\" or \"_\") and is not proxied on the render context.`\n        );\n      } else if (instance === currentRenderingInstance) {\n        warn$1(\n          `Property ${JSON.stringify(key)} was accessed during render but is not defined on instance.`\n        );\n      }\n    }\n  },\n  set({ _: instance }, key, value) {\n    const { data, setupState, ctx } = instance;\n    if (hasSetupBinding(setupState, key)) {\n      setupState[key] = value;\n      return true;\n    } else if (!!(process.env.NODE_ENV !== \"production\") && setupState.__isScriptSetup && hasOwn(setupState, key)) {\n      warn$1(`Cannot mutate <script setup> binding \"${key}\" from Options API.`);\n      return false;\n    } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {\n      data[key] = value;\n      return true;\n    } else if (hasOwn(instance.props, key)) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`Attempting to mutate prop \"${key}\". Props are readonly.`);\n      return false;\n    }\n    if (key[0] === \"$\" && key.slice(1) in instance) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(\n        `Attempting to mutate public property \"${key}\". Properties starting with $ are reserved and readonly.`\n      );\n      return false;\n    } else {\n      if (!!(process.env.NODE_ENV !== \"production\") && key in instance.appContext.config.globalProperties) {\n        Object.defineProperty(ctx, key, {\n          enumerable: true,\n          configurable: true,\n          value\n        });\n      } else {\n        ctx[key] = value;\n      }\n    }\n    return true;\n  },\n  has({\n    _: { data, setupState, accessCache, ctx, appContext, propsOptions }\n  }, key) {\n    let normalizedProps;\n    return !!accessCache[key] || data !== EMPTY_OBJ && hasOwn(data, key) || hasSetupBinding(setupState, key) || (normalizedProps = propsOptions[0]) && hasOwn(normalizedProps, key) || hasOwn(ctx, key) || hasOwn(publicPropertiesMap, key) || hasOwn(appContext.config.globalProperties, key);\n  },\n  defineProperty(target, key, descriptor) {\n    if (descriptor.get != null) {\n      target._.accessCache[key] = 0;\n    } else if (hasOwn(descriptor, \"value\")) {\n      this.set(target, key, descriptor.value, null);\n    }\n    return Reflect.defineProperty(target, key, descriptor);\n  }\n};\nif (!!(process.env.NODE_ENV !== \"production\") && true) {\n  PublicInstanceProxyHandlers.ownKeys = (target) => {\n    warn$1(\n      `Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead.`\n    );\n    return Reflect.ownKeys(target);\n  };\n}\nfunction createDevRenderContext(instance) {\n  const target = {};\n  Object.defineProperty(target, `_`, {\n    configurable: true,\n    enumerable: false,\n    get: () => instance\n  });\n  Object.keys(publicPropertiesMap).forEach((key) => {\n    Object.defineProperty(target, key, {\n      configurable: true,\n      enumerable: false,\n      get: () => publicPropertiesMap[key](instance),\n      // intercepted by the proxy so no need for implementation,\n      // but needed to prevent set errors\n      set: NOOP\n    });\n  });\n  return target;\n}\nfunction exposePropsOnRenderContext(instance) {\n  const {\n    ctx,\n    propsOptions: [propsOptions]\n  } = instance;\n  if (propsOptions) {\n    Object.keys(propsOptions).forEach((key) => {\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: () => instance.props[key],\n        set: NOOP\n      });\n    });\n  }\n}\nfunction exposeSetupStateOnRenderContext(instance) {\n  const { ctx, setupState } = instance;\n  Object.keys(toRaw(setupState)).forEach((key) => {\n    if (!setupState.__isScriptSetup) {\n      if (isReservedPrefix(key[0])) {\n        warn$1(\n          `setup() return property ${JSON.stringify(\n            key\n          )} should not start with \"$\" or \"_\" which are reserved prefixes for Vue internals.`\n        );\n        return;\n      }\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: () => setupState[key],\n        set: NOOP\n      });\n    }\n  });\n}\n\nconst warnRuntimeUsage = (method) => warn$1(\n  `${method}() is a compiler-hint helper that is only usable inside <script setup> of a single file component. Its arguments should be compiled away and passing it at runtime has no effect.`\n);\nfunction defineProps() {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`defineProps`);\n  }\n  return null;\n}\nfunction defineEmits() {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`defineEmits`);\n  }\n  return null;\n}\nfunction defineExpose(exposed) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`defineExpose`);\n  }\n}\nfunction withDefaults(props, defaults) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    warnRuntimeUsage(`withDefaults`);\n  }\n  return null;\n}\nfunction useSlots() {\n  return getContext().slots;\n}\nfunction useAttrs() {\n  return getContext().attrs;\n}\nfunction getContext() {\n  const i = getCurrentInstance();\n  if (!!(process.env.NODE_ENV !== \"production\") && !i) {\n    warn$1(`useContext() called without active instance.`);\n  }\n  return i.setupContext || (i.setupContext = createSetupContext(i));\n}\nfunction normalizePropsOrEmits(props) {\n  return isArray(props) ? props.reduce(\n    (normalized, p) => (normalized[p] = null, normalized),\n    {}\n  ) : props;\n}\nfunction mergeDefaults(raw, defaults) {\n  const props = normalizePropsOrEmits(raw);\n  for (const key in defaults) {\n    if (key.startsWith(\"__skip\"))\n      continue;\n    let opt = props[key];\n    if (opt) {\n      if (isArray(opt) || isFunction(opt)) {\n        opt = props[key] = { type: opt, default: defaults[key] };\n      } else {\n        opt.default = defaults[key];\n      }\n    } else if (opt === null) {\n      opt = props[key] = { default: defaults[key] };\n    } else if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$1(`props default key \"${key}\" has no corresponding declaration.`);\n    }\n    if (opt && defaults[`__skip_${key}`]) {\n      opt.skipFactory = true;\n    }\n  }\n  return props;\n}\nfunction mergeModels(a, b) {\n  if (!a || !b)\n    return a || b;\n  if (isArray(a) && isArray(b))\n    return a.concat(b);\n  return extend({}, normalizePropsOrEmits(a), normalizePropsOrEmits(b));\n}\nfunction createPropsRestProxy(props, excludedKeys) {\n  const ret = {};\n  for (const key in props) {\n    if (!excludedKeys.includes(key)) {\n      Object.defineProperty(ret, key, {\n        enumerable: true,\n        get: () => props[key]\n      });\n    }\n  }\n  return ret;\n}\nfunction withAsyncContext(getAwaitable) {\n  const ctx = getCurrentInstance();\n  if (!!(process.env.NODE_ENV !== \"production\") && !ctx) {\n    warn$1(\n      `withAsyncContext called without active current instance. This is likely a bug.`\n    );\n  }\n  let awaitable = getAwaitable();\n  unsetCurrentInstance();\n  if (isPromise(awaitable)) {\n    awaitable = awaitable.catch((e) => {\n      setCurrentInstance(ctx);\n      throw e;\n    });\n  }\n  return [awaitable, () => setCurrentInstance(ctx)];\n}\n\nfunction createDuplicateChecker() {\n  const cache = /* @__PURE__ */ Object.create(null);\n  return (type, key) => {\n    if (cache[key]) {\n      warn$1(`${type} property \"${key}\" is already defined in ${cache[key]}.`);\n    } else {\n      cache[key] = type;\n    }\n  };\n}\nlet shouldCacheAccess = true;\nfunction applyOptions$1(instance) {\n  const options = resolveMergedOptions(instance);\n  const publicThis = instance.proxy;\n  const ctx = instance.ctx;\n  shouldCacheAccess = false;\n  if (options.beforeCreate) {\n    callHook(options.beforeCreate, instance, \"bc\");\n  }\n  const {\n    // state\n    data: dataOptions,\n    computed: computedOptions,\n    methods,\n    watch: watchOptions,\n    provide: provideOptions,\n    inject: injectOptions,\n    // lifecycle\n    created,\n    beforeMount,\n    mounted,\n    beforeUpdate,\n    updated,\n    activated,\n    deactivated,\n    beforeDestroy,\n    beforeUnmount,\n    destroyed,\n    unmounted,\n    render,\n    renderTracked,\n    renderTriggered,\n    errorCaptured,\n    serverPrefetch,\n    // public API\n    expose,\n    inheritAttrs,\n    // assets\n    components,\n    directives,\n    filters\n  } = options;\n  const checkDuplicateProperties = !!(process.env.NODE_ENV !== \"production\") ? createDuplicateChecker() : null;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const [propsOptions] = instance.propsOptions;\n    if (propsOptions) {\n      for (const key in propsOptions) {\n        checkDuplicateProperties(\"Props\" /* PROPS */, key);\n      }\n    }\n  }\n  function initInjections() {\n    if (injectOptions) {\n      resolveInjections(injectOptions, ctx, checkDuplicateProperties);\n    }\n  }\n  if (!__VUE_CREATED_DEFERRED__) {\n    initInjections();\n  }\n  if (methods) {\n    for (const key in methods) {\n      const methodHandler = methods[key];\n      if (isFunction(methodHandler)) {\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          Object.defineProperty(ctx, key, {\n            value: methodHandler.bind(publicThis),\n            configurable: true,\n            enumerable: true,\n            writable: true\n          });\n        } else {\n          ctx[key] = methodHandler.bind(publicThis);\n        }\n        if (!!(process.env.NODE_ENV !== \"production\")) {\n          checkDuplicateProperties(\"Methods\" /* METHODS */, key);\n        }\n      } else if (!!(process.env.NODE_ENV !== \"production\")) {\n        warn$1(\n          `Method \"${key}\" has type \"${typeof methodHandler}\" in the component definition. Did you reference the function correctly?`\n        );\n      }\n    }\n  }\n  if (dataOptions) {\n    if (!!(process.env.NODE_ENV !== \"production\") && !isFunction(dataOptions)) {\n      warn$1(\n        `The data option must be a function. Plain object usage is no longer supported.`\n      );\n    }\n    const data = dataOptions.call(publicThis, publicThis);\n    if (!!(process.env.NODE_ENV !== \"production\") && isPromise(data)) {\n      warn$1(\n        `data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>.`\n      );\n    }\n    if (!isObject(data)) {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(`data() should return an object.`);\n    } else {\n      instance.data = reactive(data);\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        for (const key in data) {\n          checkDuplicateProperties(\"Data\" /* DATA */, key);\n          if (!isReservedPrefix(key[0])) {\n            Object.defineProperty(ctx, key, {\n              configurable: true,\n              enumerable: true,\n              get: () => data[key],\n              set: NOOP\n            });\n          }\n        }\n      }\n    }\n  }\n  shouldCacheAccess = true;\n  if (computedOptions) {\n    for (const key in computedOptions) {\n      const opt = computedOptions[key];\n      const get = isFunction(opt) ? opt.bind(publicThis, publicThis) : isFunction(opt.get) ? opt.get.bind(publicThis, publicThis) : NOOP;\n      if (!!(process.env.NODE_ENV !== \"production\") && get === NOOP) {\n        warn$1(`Computed property \"${key}\" has no getter.`);\n      }\n      const set = !isFunction(opt) && isFunction(opt.set) ? opt.set.bind(publicThis) : !!(process.env.NODE_ENV !== \"production\") ? () => {\n        warn$1(\n          `Write operation failed: computed property \"${key}\" is readonly.`\n        );\n      } : NOOP;\n      const c = computed({\n        get,\n        set\n      });\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: () => c.value,\n        set: (v) => c.value = v\n      });\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        checkDuplicateProperties(\"Computed\" /* COMPUTED */, key);\n      }\n    }\n  }\n  if (watchOptions) {\n    for (const key in watchOptions) {\n      createWatcher(watchOptions[key], ctx, publicThis, key);\n    }\n  }\n  function initProvides() {\n    if (provideOptions) {\n      const provides = isFunction(provideOptions) ? provideOptions.call(publicThis) : provideOptions;\n      Reflect.ownKeys(provides).forEach((key) => {\n        provide(key, provides[key]);\n      });\n    }\n  }\n  if (!__VUE_CREATED_DEFERRED__) {\n    initProvides();\n  }\n  if (__VUE_CREATED_DEFERRED__) {\n    let callCreatedHook2 = function() {\n      initInjections();\n      initProvides();\n      if (created) {\n        callHook(created, instance, \"c\");\n      }\n      instance.update();\n    };\n    ctx.$callCreatedHook = function(name) {\n      const reset = setCurrentInstance(instance);\n      pauseTracking();\n      try {\n        callCreatedHook2();\n      } finally {\n        resetTracking();\n        reset();\n      }\n    };\n  } else {\n    if (created) {\n      callHook(created, instance, \"c\");\n    }\n  }\n  function registerLifecycleHook(register, hook) {\n    if (isArray(hook)) {\n      hook.forEach((_hook) => register(_hook.bind(publicThis)));\n    } else if (hook) {\n      register(hook.bind(publicThis));\n    }\n  }\n  registerLifecycleHook(onBeforeMount, beforeMount);\n  registerLifecycleHook(onMounted, mounted);\n  registerLifecycleHook(onBeforeUpdate, beforeUpdate);\n  registerLifecycleHook(onUpdated, updated);\n  registerLifecycleHook(onActivated, activated);\n  registerLifecycleHook(onDeactivated, deactivated);\n  registerLifecycleHook(onErrorCaptured, errorCaptured);\n  registerLifecycleHook(onRenderTracked, renderTracked);\n  registerLifecycleHook(onRenderTriggered, renderTriggered);\n  registerLifecycleHook(onBeforeUnmount, beforeUnmount);\n  registerLifecycleHook(onUnmounted, unmounted);\n  registerLifecycleHook(onServerPrefetch, serverPrefetch);\n  if (isArray(expose)) {\n    if (expose.length) {\n      const exposed = instance.exposed || (instance.exposed = {});\n      expose.forEach((key) => {\n        Object.defineProperty(exposed, key, {\n          get: () => publicThis[key],\n          set: (val) => publicThis[key] = val\n        });\n      });\n    } else if (!instance.exposed) {\n      instance.exposed = {};\n    }\n  }\n  if (render && instance.render === NOOP) {\n    instance.render = render;\n  }\n  if (inheritAttrs != null) {\n    instance.inheritAttrs = inheritAttrs;\n  }\n  if (components)\n    instance.components = components;\n  if (directives)\n    instance.directives = directives;\n  if (instance.ctx.$onApplyOptions) {\n    instance.ctx.$onApplyOptions(options, instance, publicThis);\n  }\n}\nfunction resolveInjections(injectOptions, ctx, checkDuplicateProperties = NOOP) {\n  if (isArray(injectOptions)) {\n    injectOptions = normalizeInject(injectOptions);\n  }\n  for (const key in injectOptions) {\n    const opt = injectOptions[key];\n    let injected;\n    if (isObject(opt)) {\n      if (\"default\" in opt) {\n        injected = inject(\n          opt.from || key,\n          opt.default,\n          true\n        );\n      } else {\n        injected = inject(opt.from || key);\n      }\n    } else {\n      injected = inject(opt);\n    }\n    if (isRef(injected)) {\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: () => injected.value,\n        set: (v) => injected.value = v\n      });\n    } else {\n      ctx[key] = injected;\n    }\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      checkDuplicateProperties(\"Inject\" /* INJECT */, key);\n    }\n  }\n}\nfunction callHook(hook, instance, type) {\n  callWithAsyncErrorHandling(\n    isArray(hook) ? hook.map((h) => h.bind(instance.proxy)) : hook.bind(instance.proxy),\n    instance,\n    type\n  );\n}\nfunction createWatcher(raw, ctx, publicThis, key) {\n  const getter = key.includes(\".\") ? createPathGetter(publicThis, key) : () => publicThis[key];\n  if (isString(raw)) {\n    const handler = ctx[raw];\n    if (isFunction(handler)) {\n      watch(getter, handler);\n    } else if (!!(process.env.NODE_ENV !== \"production\")) {\n      warn$1(`Invalid watch handler specified by key \"${raw}\"`, handler);\n    }\n  } else if (isFunction(raw)) {\n    watch(getter, raw.bind(publicThis));\n  } else if (isObject(raw)) {\n    if (isArray(raw)) {\n      raw.forEach((r) => createWatcher(r, ctx, publicThis, key));\n    } else {\n      const handler = isFunction(raw.handler) ? raw.handler.bind(publicThis) : ctx[raw.handler];\n      if (isFunction(handler)) {\n        watch(getter, handler, raw);\n      } else if (!!(process.env.NODE_ENV !== \"production\")) {\n        warn$1(`Invalid watch handler specified by key \"${raw.handler}\"`, handler);\n      }\n    }\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`Invalid watch option: \"${key}\"`, raw);\n  }\n}\nfunction resolveMergedOptions(instance) {\n  const base = instance.type;\n  const { mixins, extends: extendsOptions } = base;\n  const {\n    mixins: globalMixins,\n    optionsCache: cache,\n    config: { optionMergeStrategies }\n  } = instance.appContext;\n  const cached = cache.get(base);\n  let resolved;\n  if (cached) {\n    resolved = cached;\n  } else if (!globalMixins.length && !mixins && !extendsOptions) {\n    {\n      resolved = base;\n    }\n  } else {\n    resolved = {};\n    if (globalMixins.length) {\n      globalMixins.forEach(\n        (m) => mergeOptions(resolved, m, optionMergeStrategies, true)\n      );\n    }\n    mergeOptions(resolved, base, optionMergeStrategies);\n  }\n  if (isObject(base)) {\n    cache.set(base, resolved);\n  }\n  return resolved;\n}\nfunction mergeOptions(to, from, strats, asMixin = false) {\n  const { mixins, extends: extendsOptions } = from;\n  if (extendsOptions) {\n    mergeOptions(to, extendsOptions, strats, true);\n  }\n  if (mixins) {\n    mixins.forEach(\n      (m) => mergeOptions(to, m, strats, true)\n    );\n  }\n  for (const key in from) {\n    if (asMixin && key === \"expose\") {\n      !!(process.env.NODE_ENV !== \"production\") && warn$1(\n        `\"expose\" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.`\n      );\n    } else {\n      const strat = internalOptionMergeStrats[key] || strats && strats[key];\n      to[key] = strat ? strat(to[key], from[key]) : from[key];\n    }\n  }\n  return to;\n}\nconst internalOptionMergeStrats = {\n  data: mergeDataFn,\n  props: mergeEmitsOrPropsOptions,\n  emits: mergeEmitsOrPropsOptions,\n  // objects\n  methods: mergeObjectOptions,\n  computed: mergeObjectOptions,\n  // lifecycle\n  beforeCreate: mergeAsArray$1,\n  created: mergeAsArray$1,\n  beforeMount: mergeAsArray$1,\n  mounted: mergeAsArray$1,\n  beforeUpdate: mergeAsArray$1,\n  updated: mergeAsArray$1,\n  beforeDestroy: mergeAsArray$1,\n  beforeUnmount: mergeAsArray$1,\n  destroyed: mergeAsArray$1,\n  unmounted: mergeAsArray$1,\n  activated: mergeAsArray$1,\n  deactivated: mergeAsArray$1,\n  errorCaptured: mergeAsArray$1,\n  serverPrefetch: mergeAsArray$1,\n  // assets\n  components: mergeObjectOptions,\n  directives: mergeObjectOptions,\n  // watch\n  watch: mergeWatchOptions,\n  // provide / inject\n  provide: mergeDataFn,\n  inject: mergeInject\n};\nfunction mergeDataFn(to, from) {\n  if (!from) {\n    return to;\n  }\n  if (!to) {\n    return from;\n  }\n  return function mergedDataFn() {\n    return (extend)(\n      isFunction(to) ? to.call(this, this) : to,\n      isFunction(from) ? from.call(this, this) : from\n    );\n  };\n}\nfunction mergeInject(to, from) {\n  return mergeObjectOptions(normalizeInject(to), normalizeInject(from));\n}\nfunction normalizeInject(raw) {\n  if (isArray(raw)) {\n    const res = {};\n    for (let i = 0; i < raw.length; i++) {\n      res[raw[i]] = raw[i];\n    }\n    return res;\n  }\n  return raw;\n}\nfunction mergeAsArray$1(to, from) {\n  return to ? [...new Set([].concat(to, from))] : from;\n}\nfunction mergeObjectOptions(to, from) {\n  return to ? extend(/* @__PURE__ */ Object.create(null), to, from) : from;\n}\nfunction mergeEmitsOrPropsOptions(to, from) {\n  if (to) {\n    if (isArray(to) && isArray(from)) {\n      return [.../* @__PURE__ */ new Set([...to, ...from])];\n    }\n    return extend(\n      /* @__PURE__ */ Object.create(null),\n      normalizePropsOrEmits(to),\n      normalizePropsOrEmits(from != null ? from : {})\n    );\n  } else {\n    return from;\n  }\n}\nfunction mergeWatchOptions(to, from) {\n  if (!to)\n    return from;\n  if (!from)\n    return to;\n  const merged = extend(/* @__PURE__ */ Object.create(null), to);\n  for (const key in from) {\n    merged[key] = mergeAsArray$1(to[key], from[key]);\n  }\n  return merged;\n}\n\nfunction initProps(instance, rawProps, isStateful, isSSR = false) {\n  const props = {};\n  const attrs = {};\n  instance.propsDefaults = /* @__PURE__ */ Object.create(null);\n  setFullProps(instance, rawProps, props, attrs);\n  for (const key in instance.propsOptions[0]) {\n    if (!(key in props)) {\n      props[key] = void 0;\n    }\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    validateProps(rawProps || {}, props, instance);\n  }\n  if (isStateful) {\n    instance.props = isSSR ? props : shallowReactive(props);\n  } else {\n    if (!instance.type.props) {\n      instance.props = attrs;\n    } else {\n      instance.props = props;\n    }\n  }\n  instance.attrs = attrs;\n}\nfunction isInHmrContext(instance) {\n}\nfunction updateProps(instance, rawProps, rawPrevProps, optimized) {\n  const {\n    props,\n    attrs,\n    vnode: { patchFlag }\n  } = instance;\n  const rawCurrentProps = toRaw(props);\n  const [options] = instance.propsOptions;\n  let hasAttrsChanged = false;\n  if (\n    // always force full diff in dev\n    // - #1942 if hmr is enabled with sfc component\n    // - vite#872 non-sfc component used by sfc component\n    !(!!(process.env.NODE_ENV !== \"production\") && isInHmrContext()) && (optimized || patchFlag > 0) && !(patchFlag & 16)\n  ) {\n    if (patchFlag & 8) {\n      const propsToUpdate = instance.vnode.dynamicProps;\n      for (let i = 0; i < propsToUpdate.length; i++) {\n        let key = propsToUpdate[i];\n        if (isEmitListener(instance.emitsOptions, key)) {\n          continue;\n        }\n        const value = rawProps[key];\n        if (options) {\n          if (hasOwn(attrs, key)) {\n            if (value !== attrs[key]) {\n              attrs[key] = value;\n              hasAttrsChanged = true;\n            }\n          } else {\n            const camelizedKey = camelize(key);\n            props[camelizedKey] = resolvePropValue(\n              options,\n              rawCurrentProps,\n              camelizedKey,\n              value,\n              instance,\n              false\n            );\n          }\n        } else {\n          if (value !== attrs[key]) {\n            attrs[key] = value;\n            hasAttrsChanged = true;\n          }\n        }\n      }\n    }\n  } else {\n    if (setFullProps(instance, rawProps, props, attrs)) {\n      hasAttrsChanged = true;\n    }\n    let kebabKey;\n    for (const key in rawCurrentProps) {\n      if (!rawProps || // for camelCase\n      !hasOwn(rawProps, key) && // it's possible the original props was passed in as kebab-case\n      // and converted to camelCase (#955)\n      ((kebabKey = hyphenate(key)) === key || !hasOwn(rawProps, kebabKey))) {\n        if (options) {\n          if (rawPrevProps && // for camelCase\n          (rawPrevProps[key] !== void 0 || // for kebab-case\n          rawPrevProps[kebabKey] !== void 0)) {\n            props[key] = resolvePropValue(\n              options,\n              rawCurrentProps,\n              key,\n              void 0,\n              instance,\n              true\n            );\n          }\n        } else {\n          delete props[key];\n        }\n      }\n    }\n    if (attrs !== rawCurrentProps) {\n      for (const key in attrs) {\n        if (!rawProps || !hasOwn(rawProps, key) && true) {\n          delete attrs[key];\n          hasAttrsChanged = true;\n        }\n      }\n    }\n  }\n  if (hasAttrsChanged) {\n    trigger(instance, \"set\", \"$attrs\");\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    validateProps(rawProps || {}, props, instance);\n  }\n}\nfunction setFullProps(instance, rawProps, props, attrs) {\n  const [options, needCastKeys] = instance.propsOptions;\n  let hasAttrsChanged = false;\n  let rawCastValues;\n  if (rawProps) {\n    for (let key in rawProps) {\n      if (isReservedProp(key)) {\n        continue;\n      }\n      const value = rawProps[key];\n      let camelKey;\n      if (options && hasOwn(options, camelKey = camelize(key))) {\n        if (!needCastKeys || !needCastKeys.includes(camelKey)) {\n          props[camelKey] = value;\n        } else {\n          (rawCastValues || (rawCastValues = {}))[camelKey] = value;\n        }\n      } else if (!isEmitListener(instance.emitsOptions, key)) {\n        if (!(key in attrs) || value !== attrs[key]) {\n          attrs[key] = value;\n          hasAttrsChanged = true;\n        }\n      }\n    }\n  }\n  if (needCastKeys) {\n    const rawCurrentProps = toRaw(props);\n    const castValues = rawCastValues || EMPTY_OBJ;\n    for (let i = 0; i < needCastKeys.length; i++) {\n      const key = needCastKeys[i];\n      props[key] = resolvePropValue(\n        options,\n        rawCurrentProps,\n        key,\n        castValues[key],\n        instance,\n        !hasOwn(castValues, key)\n      );\n    }\n  }\n  return hasAttrsChanged;\n}\nfunction resolvePropValue(options, props, key, value, instance, isAbsent) {\n  const opt = options[key];\n  if (opt != null) {\n    const hasDefault = hasOwn(opt, \"default\");\n    if (hasDefault && value === void 0) {\n      const defaultValue = opt.default;\n      if (opt.type !== Function && !opt.skipFactory && isFunction(defaultValue)) {\n        const { propsDefaults } = instance;\n        if (key in propsDefaults) {\n          value = propsDefaults[key];\n        } else {\n          const reset = setCurrentInstance(instance);\n          value = propsDefaults[key] = defaultValue.call(\n            null,\n            props\n          );\n          reset();\n        }\n      } else {\n        value = defaultValue;\n      }\n    }\n    if (opt[0 /* shouldCast */]) {\n      if (isAbsent && !hasDefault) {\n        value = false;\n      } else if (opt[1 /* shouldCastTrue */] && (value === \"\" || value === hyphenate(key))) {\n        value = true;\n      }\n    }\n  }\n  return value;\n}\nfunction normalizePropsOptions(comp, appContext, asMixin = false) {\n  const cache = appContext.propsCache;\n  const cached = cache.get(comp);\n  if (cached) {\n    return cached;\n  }\n  const raw = comp.props;\n  const normalized = {};\n  const needCastKeys = [];\n  let hasExtends = false;\n  if (__VUE_OPTIONS_API__ && !isFunction(comp)) {\n    const extendProps = (raw2) => {\n      hasExtends = true;\n      const [props, keys] = normalizePropsOptions(raw2, appContext, true);\n      extend(normalized, props);\n      if (keys)\n        needCastKeys.push(...keys);\n    };\n    if (!asMixin && appContext.mixins.length) {\n      appContext.mixins.forEach(extendProps);\n    }\n    if (comp.extends) {\n      extendProps(comp.extends);\n    }\n    if (comp.mixins) {\n      comp.mixins.forEach(extendProps);\n    }\n  }\n  if (!raw && !hasExtends) {\n    if (isObject(comp)) {\n      cache.set(comp, EMPTY_ARR);\n    }\n    return EMPTY_ARR;\n  }\n  if (isArray(raw)) {\n    for (let i = 0; i < raw.length; i++) {\n      if (!!(process.env.NODE_ENV !== \"production\") && !isString(raw[i])) {\n        warn$1(`props must be strings when using array syntax.`, raw[i]);\n      }\n      const normalizedKey = camelize(raw[i]);\n      if (validatePropName(normalizedKey)) {\n        normalized[normalizedKey] = EMPTY_OBJ;\n      }\n    }\n  } else if (raw) {\n    if (!!(process.env.NODE_ENV !== \"production\") && !isObject(raw)) {\n      warn$1(`invalid props options`, raw);\n    }\n    for (const key in raw) {\n      const normalizedKey = camelize(key);\n      if (validatePropName(normalizedKey)) {\n        const opt = raw[key];\n        const prop = normalized[normalizedKey] = isArray(opt) || isFunction(opt) ? { type: opt } : extend({}, opt);\n        if (prop) {\n          const booleanIndex = getTypeIndex(Boolean, prop.type);\n          const stringIndex = getTypeIndex(String, prop.type);\n          prop[0 /* shouldCast */] = booleanIndex > -1;\n          prop[1 /* shouldCastTrue */] = stringIndex < 0 || booleanIndex < stringIndex;\n          if (booleanIndex > -1 || hasOwn(prop, \"default\")) {\n            needCastKeys.push(normalizedKey);\n          }\n        }\n      }\n    }\n  }\n  const res = [normalized, needCastKeys];\n  if (isObject(comp)) {\n    cache.set(comp, res);\n  }\n  return res;\n}\nfunction validatePropName(key) {\n  if (key[0] !== \"$\" && !isReservedProp(key)) {\n    return true;\n  } else if (!!(process.env.NODE_ENV !== \"production\")) {\n    warn$1(`Invalid prop name: \"${key}\" is a reserved property.`);\n  }\n  return false;\n}\nfunction getType(ctor) {\n  if (ctor === null) {\n    return \"null\";\n  }\n  if (typeof ctor === \"function\") {\n    return ctor.name || \"\";\n  } else if (typeof ctor === \"object\") {\n    const name = ctor.constructor && ctor.constructor.name;\n    return name || \"\";\n  }\n  return \"\";\n}\nfunction isSameType(a, b) {\n  return getType(a) === getType(b);\n}\nfunction getTypeIndex(type, expectedTypes) {\n  if (isArray(expectedTypes)) {\n    return expectedTypes.findIndex((t) => isSameType(t, type));\n  } else if (isFunction(expectedTypes)) {\n    return isSameType(expectedTypes, type) ? 0 : -1;\n  }\n  return -1;\n}\nfunction validateProps(rawProps, props, instance) {\n  const resolvedValues = toRaw(props);\n  const options = instance.propsOptions[0];\n  for (const key in options) {\n    let opt = options[key];\n    if (opt == null)\n      continue;\n    validateProp(\n      key,\n      resolvedValues[key],\n      opt,\n      !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(resolvedValues) : resolvedValues,\n      !hasOwn(rawProps, key) && !hasOwn(rawProps, hyphenate(key))\n    );\n  }\n}\nfunction validateProp(name, value, prop, props, isAbsent) {\n  const { type, required, validator, skipCheck } = prop;\n  if (required && isAbsent) {\n    warn$1('Missing required prop: \"' + name + '\"');\n    return;\n  }\n  if (value == null && !required) {\n    return;\n  }\n  if (type != null && type !== true && !skipCheck) {\n    let isValid = false;\n    const types = isArray(type) ? type : [type];\n    const expectedTypes = [];\n    for (let i = 0; i < types.length && !isValid; i++) {\n      const { valid, expectedType } = assertType(value, types[i]);\n      expectedTypes.push(expectedType || \"\");\n      isValid = valid;\n    }\n    if (!isValid) {\n      warn$1(getInvalidTypeMessage(name, value, expectedTypes));\n      return;\n    }\n  }\n  if (validator && !validator(value, props)) {\n    warn$1('Invalid prop: custom validator check failed for prop \"' + name + '\".');\n  }\n}\nconst isSimpleType = /* @__PURE__ */ makeMap(\n  \"String,Number,Boolean,Function,Symbol,BigInt\"\n);\nfunction assertType(value, type) {\n  let valid;\n  const expectedType = getType(type);\n  if (isSimpleType(expectedType)) {\n    const t = typeof value;\n    valid = t === expectedType.toLowerCase();\n    if (!valid && t === \"object\") {\n      valid = value instanceof type;\n    }\n  } else if (expectedType === \"Object\") {\n    valid = isObject(value);\n  } else if (expectedType === \"Array\") {\n    valid = isArray(value);\n  } else if (expectedType === \"null\") {\n    valid = value === null;\n  } else {\n    valid = value instanceof type;\n  }\n  return {\n    valid,\n    expectedType\n  };\n}\nfunction getInvalidTypeMessage(name, value, expectedTypes) {\n  if (expectedTypes.length === 0) {\n    return `Prop type [] for prop \"${name}\" won't match anything. Did you mean to use type Array instead?`;\n  }\n  let message = `Invalid prop: type check failed for prop \"${name}\". Expected ${expectedTypes.map(capitalize).join(\" | \")}`;\n  const expectedType = expectedTypes[0];\n  const receivedType = toRawType(value);\n  const expectedValue = styleValue(value, expectedType);\n  const receivedValue = styleValue(value, receivedType);\n  if (expectedTypes.length === 1 && isExplicable(expectedType) && !isBoolean(expectedType, receivedType)) {\n    message += ` with value ${expectedValue}`;\n  }\n  message += `, got ${receivedType} `;\n  if (isExplicable(receivedType)) {\n    message += `with value ${receivedValue}.`;\n  }\n  return message;\n}\nfunction styleValue(value, type) {\n  if (type === \"String\") {\n    return `\"${value}\"`;\n  } else if (type === \"Number\") {\n    return `${Number(value)}`;\n  } else {\n    return `${value}`;\n  }\n}\nfunction isExplicable(type) {\n  const explicitTypes = [\"string\", \"number\", \"boolean\"];\n  return explicitTypes.some((elem) => type.toLowerCase() === elem);\n}\nfunction isBoolean(...args) {\n  return args.some((elem) => elem.toLowerCase() === \"boolean\");\n}\n\nlet supported;\nlet perf;\nfunction startMeasure(instance, type) {\n  if (instance.appContext.config.performance && isSupported()) {\n    perf.mark(`vue-${type}-${instance.uid}`);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n    devtoolsPerfStart(instance, type, isSupported() ? perf.now() : Date.now());\n  }\n}\nfunction endMeasure(instance, type) {\n  if (instance.appContext.config.performance && isSupported()) {\n    const startTag = `vue-${type}-${instance.uid}`;\n    const endTag = startTag + `:end`;\n    perf.mark(endTag);\n    perf.measure(\n      `<${formatComponentName(instance, instance.type)}> ${type}`,\n      startTag,\n      endTag\n    );\n    perf.clearMarks(startTag);\n    perf.clearMarks(endTag);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n    devtoolsPerfEnd(instance, type, isSupported() ? perf.now() : Date.now());\n  }\n}\nfunction isSupported() {\n  if (supported !== void 0) {\n    return supported;\n  }\n  if (typeof window !== \"undefined\" && window.performance) {\n    supported = true;\n    perf = window.performance;\n  } else {\n    supported = false;\n  }\n  return supported;\n}\n\nconst queuePostRenderEffect$1 = queuePostFlushCb;\n\nconst isTeleport = (type) => type.__isTeleport;\n\nconst Fragment = Symbol.for(\"v-fgt\");\nconst Text = Symbol.for(\"v-txt\");\nconst Comment = Symbol.for(\"v-cmt\");\nconst Static = Symbol.for(\"v-stc\");\nlet currentBlock = null;\nlet isBlockTreeEnabled = 1;\nfunction setBlockTracking(value) {\n  isBlockTreeEnabled += value;\n}\nfunction isVNode(value) {\n  return value ? value.__v_isVNode === true : false;\n}\nconst createVNodeWithArgsTransform = (...args) => {\n  return _createVNode(\n    ...args\n  );\n};\nconst InternalObjectKey = `__vInternal`;\nconst normalizeKey = ({ key }) => key != null ? key : null;\nconst normalizeRef = ({\n  ref,\n  ref_key,\n  ref_for\n}) => {\n  if (typeof ref === \"number\") {\n    ref = \"\" + ref;\n  }\n  return ref != null ? isString(ref) || isRef(ref) || isFunction(ref) ? { i: currentRenderingInstance, r: ref, k: ref_key, f: !!ref_for } : ref : null;\n};\nfunction createBaseVNode(type, props = null, children = null, patchFlag = 0, dynamicProps = null, shapeFlag = type === Fragment ? 0 : 1, isBlockNode = false, needFullChildrenNormalization = false) {\n  const vnode = {\n    __v_isVNode: true,\n    __v_skip: true,\n    type,\n    props,\n    key: props && normalizeKey(props),\n    ref: props && normalizeRef(props),\n    scopeId: currentScopeId,\n    slotScopeIds: null,\n    children,\n    component: null,\n    suspense: null,\n    ssContent: null,\n    ssFallback: null,\n    dirs: null,\n    transition: null,\n    el: null,\n    anchor: null,\n    target: null,\n    targetAnchor: null,\n    staticCount: 0,\n    shapeFlag,\n    patchFlag,\n    dynamicProps,\n    dynamicChildren: null,\n    appContext: null,\n    ctx: currentRenderingInstance\n  };\n  if (needFullChildrenNormalization) {\n    normalizeChildren(vnode, children);\n  } else if (children) {\n    vnode.shapeFlag |= isString(children) ? 8 : 16;\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && vnode.key !== vnode.key) {\n    warn$1(`VNode created with invalid key (NaN). VNode type:`, vnode.type);\n  }\n  if (isBlockTreeEnabled > 0 && // avoid a block node from tracking itself\n  !isBlockNode && // has current parent block\n  currentBlock && // presence of a patch flag indicates this node needs patching on updates.\n  // component nodes also should always be patched, because even if the\n  // component doesn't need to update, it needs to persist the instance on to\n  // the next vnode so that it can be properly unmounted later.\n  (vnode.patchFlag > 0 || shapeFlag & 6) && // the EVENTS flag is only for hydration and if it is the only flag, the\n  // vnode should not be considered dynamic due to handler caching.\n  vnode.patchFlag !== 32) {\n    currentBlock.push(vnode);\n  }\n  return vnode;\n}\nconst createVNode$1 = !!(process.env.NODE_ENV !== \"production\") ? createVNodeWithArgsTransform : _createVNode;\nfunction _createVNode(type, props = null, children = null, patchFlag = 0, dynamicProps = null, isBlockNode = false) {\n  if (!type || type === NULL_DYNAMIC_COMPONENT) {\n    if (!!(process.env.NODE_ENV !== \"production\") && !type) {\n      warn$1(`Invalid vnode type when creating vnode: ${type}.`);\n    }\n    type = Comment;\n  }\n  if (isVNode(type)) {\n    const cloned = cloneVNode(\n      type,\n      props,\n      true\n      /* mergeRef: true */\n    );\n    if (children) {\n      normalizeChildren(cloned, children);\n    }\n    if (isBlockTreeEnabled > 0 && !isBlockNode && currentBlock) {\n      if (cloned.shapeFlag & 6) {\n        currentBlock[currentBlock.indexOf(type)] = cloned;\n      } else {\n        currentBlock.push(cloned);\n      }\n    }\n    cloned.patchFlag |= -2;\n    return cloned;\n  }\n  if (isClassComponent(type)) {\n    type = type.__vccOpts;\n  }\n  if (props) {\n    props = guardReactiveProps(props);\n    let { class: klass, style } = props;\n    if (klass && !isString(klass)) {\n      props.class = normalizeClass(klass);\n    }\n    if (isObject(style)) {\n      if (isProxy(style) && !isArray(style)) {\n        style = extend({}, style);\n      }\n      props.style = normalizeStyle(style);\n    }\n  }\n  const shapeFlag = isString(type) ? 1 : isTeleport(type) ? 64 : isObject(type) ? 4 : isFunction(type) ? 2 : 0;\n  if (!!(process.env.NODE_ENV !== \"production\") && shapeFlag & 4 && isProxy(type)) {\n    type = toRaw(type);\n    warn$1(\n      `Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with \\`markRaw\\` or using \\`shallowRef\\` instead of \\`ref\\`.`,\n      `\nComponent that was made reactive: `,\n      type\n    );\n  }\n  return createBaseVNode(\n    type,\n    props,\n    children,\n    patchFlag,\n    dynamicProps,\n    shapeFlag,\n    isBlockNode,\n    true\n  );\n}\nfunction guardReactiveProps(props) {\n  if (!props)\n    return null;\n  return isProxy(props) || InternalObjectKey in props ? extend({}, props) : props;\n}\nfunction cloneVNode(vnode, extraProps, mergeRef = false) {\n  const { props, ref, patchFlag, children } = vnode;\n  const mergedProps = extraProps ? mergeProps(props || {}, extraProps) : props;\n  const cloned = {\n    __v_isVNode: true,\n    __v_skip: true,\n    type: vnode.type,\n    props: mergedProps,\n    key: mergedProps && normalizeKey(mergedProps),\n    ref: extraProps && extraProps.ref ? (\n      // #2078 in the case of <component :is=\"vnode\" ref=\"extra\"/>\n      // if the vnode itself already has a ref, cloneVNode will need to merge\n      // the refs so the single vnode can be set on multiple refs\n      mergeRef && ref ? isArray(ref) ? ref.concat(normalizeRef(extraProps)) : [ref, normalizeRef(extraProps)] : normalizeRef(extraProps)\n    ) : ref,\n    scopeId: vnode.scopeId,\n    slotScopeIds: vnode.slotScopeIds,\n    children: !!(process.env.NODE_ENV !== \"production\") && patchFlag === -1 && isArray(children) ? children.map(deepCloneVNode) : children,\n    target: vnode.target,\n    targetAnchor: vnode.targetAnchor,\n    staticCount: vnode.staticCount,\n    shapeFlag: vnode.shapeFlag,\n    // if the vnode is cloned with extra props, we can no longer assume its\n    // existing patch flag to be reliable and need to add the FULL_PROPS flag.\n    // note: preserve flag for fragments since they use the flag for children\n    // fast paths only.\n    patchFlag: extraProps && vnode.type !== Fragment ? patchFlag === -1 ? 16 : patchFlag | 16 : patchFlag,\n    dynamicProps: vnode.dynamicProps,\n    dynamicChildren: vnode.dynamicChildren,\n    appContext: vnode.appContext,\n    dirs: vnode.dirs,\n    transition: vnode.transition,\n    // These should technically only be non-null on mounted VNodes. However,\n    // they *should* be copied for kept-alive vnodes. So we just always copy\n    // them since them being non-null during a mount doesn't affect the logic as\n    // they will simply be overwritten.\n    component: vnode.component,\n    suspense: vnode.suspense,\n    ssContent: vnode.ssContent && cloneVNode(vnode.ssContent),\n    ssFallback: vnode.ssFallback && cloneVNode(vnode.ssFallback),\n    el: vnode.el,\n    anchor: vnode.anchor,\n    ctx: vnode.ctx,\n    ce: vnode.ce\n  };\n  return cloned;\n}\nfunction deepCloneVNode(vnode) {\n  const cloned = cloneVNode(vnode);\n  if (isArray(vnode.children)) {\n    cloned.children = vnode.children.map(deepCloneVNode);\n  }\n  return cloned;\n}\nfunction createTextVNode(text = \" \", flag = 0) {\n  return createVNode$1(Text, null, text, flag);\n}\nfunction normalizeChildren(vnode, children) {\n  let type = 0;\n  const { shapeFlag } = vnode;\n  if (children == null) {\n    children = null;\n  } else if (isArray(children)) {\n    type = 16;\n  } else if (typeof children === \"object\") {\n    if (shapeFlag & (1 | 64)) {\n      const slot = children.default;\n      if (slot) {\n        slot._c && (slot._d = false);\n        normalizeChildren(vnode, slot());\n        slot._c && (slot._d = true);\n      }\n      return;\n    } else {\n      type = 32;\n      const slotFlag = children._;\n      if (!slotFlag && !(InternalObjectKey in children)) {\n        children._ctx = currentRenderingInstance;\n      } else if (slotFlag === 3 && currentRenderingInstance) {\n        if (currentRenderingInstance.slots._ === 1) {\n          children._ = 1;\n        } else {\n          children._ = 2;\n          vnode.patchFlag |= 1024;\n        }\n      }\n    }\n  } else if (isFunction(children)) {\n    children = { default: children, _ctx: currentRenderingInstance };\n    type = 32;\n  } else {\n    children = String(children);\n    if (shapeFlag & 64) {\n      type = 16;\n      children = [createTextVNode(children)];\n    } else {\n      type = 8;\n    }\n  }\n  vnode.children = children;\n  vnode.shapeFlag |= type;\n}\nfunction mergeProps(...args) {\n  const ret = {};\n  for (let i = 0; i < args.length; i++) {\n    const toMerge = args[i];\n    for (const key in toMerge) {\n      if (key === \"class\") {\n        if (ret.class !== toMerge.class) {\n          ret.class = normalizeClass([ret.class, toMerge.class]);\n        }\n      } else if (key === \"style\") {\n        ret.style = normalizeStyle([ret.style, toMerge.style]);\n      } else if (isOn(key)) {\n        const existing = ret[key];\n        const incoming = toMerge[key];\n        if (incoming && existing !== incoming && !(isArray(existing) && existing.includes(incoming))) {\n          ret[key] = existing ? [].concat(existing, incoming) : incoming;\n        }\n      } else if (key !== \"\") {\n        ret[key] = toMerge[key];\n      }\n    }\n  }\n  return ret;\n}\n\nconst emptyAppContext = createAppContext();\nlet uid = 0;\nfunction createComponentInstance(vnode, parent, suspense) {\n  const type = vnode.type;\n  const appContext = (parent ? parent.appContext : vnode.appContext) || emptyAppContext;\n  const instance = {\n    uid: uid++,\n    vnode,\n    type,\n    parent,\n    appContext,\n    root: null,\n    // to be immediately set\n    next: null,\n    subTree: null,\n    // will be set synchronously right after creation\n    effect: null,\n    update: null,\n    // will be set synchronously right after creation\n    scope: new EffectScope(\n      true\n      /* detached */\n    ),\n    render: null,\n    proxy: null,\n    exposed: null,\n    exposeProxy: null,\n    withProxy: null,\n    provides: parent ? parent.provides : Object.create(appContext.provides),\n    accessCache: null,\n    renderCache: [],\n    // local resolved assets\n    components: null,\n    directives: null,\n    // resolved props and emits options\n    propsOptions: normalizePropsOptions(type, appContext),\n    emitsOptions: normalizeEmitsOptions(type, appContext),\n    // emit\n    emit: null,\n    // to be set immediately\n    emitted: null,\n    // props default value\n    propsDefaults: EMPTY_OBJ,\n    // inheritAttrs\n    inheritAttrs: type.inheritAttrs,\n    // state\n    ctx: EMPTY_OBJ,\n    data: EMPTY_OBJ,\n    props: EMPTY_OBJ,\n    attrs: EMPTY_OBJ,\n    slots: EMPTY_OBJ,\n    refs: EMPTY_OBJ,\n    setupState: EMPTY_OBJ,\n    setupContext: null,\n    attrsProxy: null,\n    slotsProxy: null,\n    // suspense related\n    suspense,\n    suspenseId: suspense ? suspense.pendingId : 0,\n    asyncDep: null,\n    asyncResolved: false,\n    // lifecycle hooks\n    // not using enums here because it results in computed properties\n    isMounted: false,\n    isUnmounted: false,\n    isDeactivated: false,\n    bc: null,\n    c: null,\n    bm: null,\n    m: null,\n    bu: null,\n    u: null,\n    um: null,\n    bum: null,\n    da: null,\n    a: null,\n    rtg: null,\n    rtc: null,\n    ec: null,\n    sp: null,\n    // fixed by xxxxxx 用于存储uni-app的元素缓存\n    $uniElements: /* @__PURE__ */ new Map(),\n    $templateUniElementRefs: [],\n    $templateUniElementStyles: {},\n    $eS: {},\n    $eA: {}\n  };\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    instance.ctx = createDevRenderContext(instance);\n  } else {\n    instance.ctx = { _: instance };\n  }\n  instance.root = parent ? parent.root : instance;\n  instance.emit = emit.bind(null, instance);\n  if (vnode.ce) {\n    vnode.ce(instance);\n  }\n  return instance;\n}\nlet currentInstance = null;\nconst getCurrentInstance = () => currentInstance || currentRenderingInstance;\nlet internalSetCurrentInstance;\nlet setInSSRSetupState;\n{\n  internalSetCurrentInstance = (i) => {\n    currentInstance = i;\n  };\n  setInSSRSetupState = (v) => {\n    isInSSRComponentSetup = v;\n  };\n}\nconst setCurrentInstance = (instance) => {\n  const prev = currentInstance;\n  internalSetCurrentInstance(instance);\n  instance.scope.on();\n  return () => {\n    instance.scope.off();\n    internalSetCurrentInstance(prev);\n  };\n};\nconst unsetCurrentInstance = () => {\n  currentInstance && currentInstance.scope.off();\n  internalSetCurrentInstance(null);\n};\nconst isBuiltInTag = /* @__PURE__ */ makeMap(\"slot,component\");\nfunction validateComponentName(name, { isNativeTag }) {\n  if (isBuiltInTag(name) || isNativeTag(name)) {\n    warn$1(\n      \"Do not use built-in or reserved HTML elements as component id: \" + name\n    );\n  }\n}\nfunction isStatefulComponent(instance) {\n  return instance.vnode.shapeFlag & 4;\n}\nlet isInSSRComponentSetup = false;\nfunction setupComponent(instance, isSSR = false) {\n  isSSR && setInSSRSetupState(isSSR);\n  const {\n    props\n    /*, children*/\n  } = instance.vnode;\n  const isStateful = isStatefulComponent(instance);\n  initProps(instance, props, isStateful, isSSR);\n  const setupResult = isStateful ? setupStatefulComponent(instance, isSSR) : void 0;\n  isSSR && setInSSRSetupState(false);\n  return setupResult;\n}\nfunction setupStatefulComponent(instance, isSSR) {\n  const Component = instance.type;\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    if (Component.name) {\n      validateComponentName(Component.name, instance.appContext.config);\n    }\n    if (Component.components) {\n      const names = Object.keys(Component.components);\n      for (let i = 0; i < names.length; i++) {\n        validateComponentName(names[i], instance.appContext.config);\n      }\n    }\n    if (Component.directives) {\n      const names = Object.keys(Component.directives);\n      for (let i = 0; i < names.length; i++) {\n        validateDirectiveName(names[i]);\n      }\n    }\n    if (Component.compilerOptions && isRuntimeOnly()) {\n      warn$1(\n        `\"compilerOptions\" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.`\n      );\n    }\n  }\n  instance.accessCache = /* @__PURE__ */ Object.create(null);\n  instance.proxy = markRaw(new Proxy(instance.ctx, PublicInstanceProxyHandlers));\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    exposePropsOnRenderContext(instance);\n  }\n  const { setup } = Component;\n  if (setup) {\n    const setupContext = instance.setupContext = setup.length > 1 ? createSetupContext(instance) : null;\n    const reset = setCurrentInstance(instance);\n    pauseTracking();\n    const setupResult = callWithErrorHandling(\n      setup,\n      instance,\n      0,\n      [\n        !!(process.env.NODE_ENV !== \"production\") ? shallowReadonly(instance.props) : instance.props,\n        setupContext\n      ]\n    );\n    resetTracking();\n    reset();\n    if (isPromise(setupResult)) {\n      setupResult.then(unsetCurrentInstance, unsetCurrentInstance);\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        warn$1(\n          `setup() returned a Promise, but the version of Vue you are using does not support it yet.`\n        );\n      }\n    } else {\n      handleSetupResult(instance, setupResult, isSSR);\n    }\n  } else {\n    finishComponentSetup(instance, isSSR);\n  }\n}\nfunction handleSetupResult(instance, setupResult, isSSR) {\n  if (isFunction(setupResult)) {\n    {\n      instance.render = setupResult;\n    }\n  } else if (isObject(setupResult)) {\n    if (!!(process.env.NODE_ENV !== \"production\") && isVNode(setupResult)) {\n      warn$1(\n        `setup() should not return VNodes directly - return a render function instead.`\n      );\n    }\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      instance.devtoolsRawSetupState = setupResult;\n    }\n    instance.setupState = proxyRefs(setupResult);\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      exposeSetupStateOnRenderContext(instance);\n    }\n  } else if (!!(process.env.NODE_ENV !== \"production\") && setupResult !== void 0) {\n    warn$1(\n      `setup() should return an object. Received: ${setupResult === null ? \"null\" : typeof setupResult}`\n    );\n  }\n  finishComponentSetup(instance, isSSR);\n}\nlet compile;\nconst isRuntimeOnly = () => !compile;\nfunction finishComponentSetup(instance, isSSR, skipOptions) {\n  const Component = instance.type;\n  if (!instance.render) {\n    instance.render = Component.render || NOOP;\n  }\n  if (__VUE_OPTIONS_API__ && true) {\n    const reset = setCurrentInstance(instance);\n    pauseTracking();\n    try {\n      applyOptions$1(instance);\n    } finally {\n      resetTracking();\n      reset();\n    }\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && !Component.render && instance.render === NOOP && !isSSR) {\n    if (Component.template) {\n      warn$1(\n        `Component provided template option but runtime compilation is not supported in this build of Vue.` + (` Configure your bundler to alias \"vue\" to \"vue/dist/vue.esm-bundler.js\".` )\n      );\n    } else {\n      warn$1(`Component is missing template or render function.`);\n    }\n  }\n}\nfunction getAttrsProxy(instance) {\n  return instance.attrsProxy || (instance.attrsProxy = new Proxy(\n    instance.attrs,\n    !!(process.env.NODE_ENV !== \"production\") ? {\n      get(target, key) {\n        track(instance, \"get\", \"$attrs\");\n        return target[key];\n      },\n      set() {\n        warn$1(`setupContext.attrs is readonly.`);\n        return false;\n      },\n      deleteProperty() {\n        warn$1(`setupContext.attrs is readonly.`);\n        return false;\n      }\n    } : {\n      get(target, key) {\n        track(instance, \"get\", \"$attrs\");\n        return target[key];\n      }\n    }\n  ));\n}\nfunction getSlotsProxy(instance) {\n  return instance.slotsProxy || (instance.slotsProxy = new Proxy(instance.slots, {\n    get(target, key) {\n      track(instance, \"get\", \"$slots\");\n      return target[key];\n    }\n  }));\n}\nfunction createSetupContext(instance) {\n  const expose = (exposed) => {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      if (instance.exposed) {\n        warn$1(`expose() should be called only once per setup().`);\n      }\n      if (exposed != null) {\n        let exposedType = typeof exposed;\n        if (exposedType === \"object\") {\n          if (isArray(exposed)) {\n            exposedType = \"array\";\n          } else if (isRef(exposed)) {\n            exposedType = \"ref\";\n          }\n        }\n        if (exposedType !== \"object\") {\n          warn$1(\n            `expose() should be passed a plain object, received ${exposedType}.`\n          );\n        }\n      }\n    }\n    instance.exposed = exposed || {};\n  };\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    return Object.freeze({\n      get attrs() {\n        return getAttrsProxy(instance);\n      },\n      get slots() {\n        return getSlotsProxy(instance);\n      },\n      get emit() {\n        return (event, ...args) => instance.emit(event, ...args);\n      },\n      expose\n    });\n  } else {\n    return {\n      get attrs() {\n        return getAttrsProxy(instance);\n      },\n      slots: instance.slots,\n      emit: instance.emit,\n      expose\n    };\n  }\n}\nfunction getExposeProxy(instance) {\n  if (instance.exposed) {\n    return instance.exposeProxy || (instance.exposeProxy = new Proxy(proxyRefs(markRaw(instance.exposed)), {\n      get(target, key) {\n        if (key in target) {\n          return target[key];\n        }\n        return instance.proxy[key];\n      },\n      has(target, key) {\n        return key in target || key in publicPropertiesMap;\n      }\n    }));\n  }\n}\nconst classifyRE = /(?:^|[-_])(\\w)/g;\nconst classify = (str) => str.replace(classifyRE, (c) => c.toUpperCase()).replace(/[-_]/g, \"\");\nfunction getComponentName(Component, includeInferred = true) {\n  return isFunction(Component) ? Component.displayName || Component.name : Component.name || includeInferred && Component.__name;\n}\nfunction formatComponentName(instance, Component, isRoot = false) {\n  let name = getComponentName(Component);\n  if (!name && Component.__file) {\n    const match = Component.__file.match(/([^/\\\\]+)\\.\\w+$/);\n    if (match) {\n      name = match[1];\n    }\n  }\n  if (!name && instance && instance.parent) {\n    const inferFromRegistry = (registry) => {\n      for (const key in registry) {\n        if (registry[key] === Component) {\n          return key;\n        }\n      }\n    };\n    name = inferFromRegistry(\n      instance.components || instance.parent.type.components\n    ) || inferFromRegistry(instance.appContext.components);\n  }\n  return name ? classify(name) : isRoot ? `App` : `Anonymous`;\n}\nfunction isClassComponent(value) {\n  return isFunction(value) && \"__vccOpts\" in value;\n}\n\nconst computed = (getterOrOptions, debugOptions) => {\n  const c = computed$1(getterOrOptions, debugOptions, isInSSRComponentSetup);\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const i = getCurrentInstance();\n    if (i && i.appContext.config.warnRecursiveComputed) {\n      c._warnRecursive = true;\n    }\n  }\n  return c;\n};\n\nfunction useModel(props, name, options = EMPTY_OBJ) {\n  const i = getCurrentInstance();\n  if (!!(process.env.NODE_ENV !== \"production\") && !i) {\n    warn$1(`useModel() called without active instance.`);\n    return ref();\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && !i.propsOptions[0][name]) {\n    warn$1(`useModel() called with prop \"${name}\" which is not declared.`);\n    return ref();\n  }\n  const camelizedName = camelize(name);\n  const hyphenatedName = hyphenate(name);\n  const res = customRef((track, trigger) => {\n    let localValue;\n    watchSyncEffect(() => {\n      const propValue = props[name];\n      if (hasChanged(localValue, propValue)) {\n        localValue = propValue;\n        trigger();\n      }\n    });\n    return {\n      get() {\n        track();\n        return options.get ? options.get(localValue) : localValue;\n      },\n      set(value) {\n        const rawProps = i.vnode.props;\n        if (!(rawProps && // check if parent has passed v-model\n        (name in rawProps || camelizedName in rawProps || hyphenatedName in rawProps) && (`onUpdate:${name}` in rawProps || `onUpdate:${camelizedName}` in rawProps || `onUpdate:${hyphenatedName}` in rawProps)) && hasChanged(value, localValue)) {\n          localValue = value;\n          trigger();\n        }\n        i.emit(`update:${name}`, options.set ? options.set(value) : value);\n      }\n    };\n  });\n  const modifierKey = name === \"modelValue\" ? \"modelModifiers\" : `${name}Modifiers`;\n  res[Symbol.iterator] = () => {\n    let i2 = 0;\n    return {\n      next() {\n        if (i2 < 2) {\n          return { value: i2++ ? props[modifierKey] || {} : res, done: false };\n        } else {\n          return { done: true };\n        }\n      }\n    };\n  };\n  return res;\n}\n\nconst version = \"3.4.21\";\nconst warn = !!(process.env.NODE_ENV !== \"production\") ? warn$1 : NOOP;\nconst resolveFilter = null;\n\nfunction unwrapper(target) {\n  return unref(target);\n}\nfunction defineAsyncComponent(source) {\n  console.error(\"defineAsyncComponent is unsupported\");\n}\n\nconst ARRAYTYPE = \"[object Array]\";\nconst OBJECTTYPE = \"[object Object]\";\nfunction diff(current, pre) {\n  const result = {};\n  syncKeys(current, pre);\n  _diff(current, pre, \"\", result);\n  return result;\n}\nfunction syncKeys(current, pre) {\n  current = unwrapper(current);\n  if (current === pre)\n    return;\n  const rootCurrentType = toTypeString(current);\n  const rootPreType = toTypeString(pre);\n  if (rootCurrentType == OBJECTTYPE && rootPreType == OBJECTTYPE) {\n    for (let key in pre) {\n      const currentValue = current[key];\n      if (currentValue === void 0) {\n        current[key] = null;\n      } else {\n        syncKeys(currentValue, pre[key]);\n      }\n    }\n  } else if (rootCurrentType == ARRAYTYPE && rootPreType == ARRAYTYPE) {\n    if (current.length >= pre.length) {\n      pre.forEach((item, index) => {\n        syncKeys(current[index], item);\n      });\n    }\n  }\n}\nfunction _diff(current, pre, path, result) {\n  current = unwrapper(current);\n  if (current === pre)\n    return;\n  const rootCurrentType = toTypeString(current);\n  const rootPreType = toTypeString(pre);\n  if (rootCurrentType == OBJECTTYPE) {\n    if (rootPreType != OBJECTTYPE || Object.keys(current).length < Object.keys(pre).length) {\n      setResult(result, path, current);\n    } else {\n      for (let key in current) {\n        const currentValue = unwrapper(current[key]);\n        const preValue = pre[key];\n        const currentType = toTypeString(currentValue);\n        const preType = toTypeString(preValue);\n        if (currentType != ARRAYTYPE && currentType != OBJECTTYPE) {\n          if (currentValue != preValue) {\n            setResult(\n              result,\n              (path == \"\" ? \"\" : path + \".\") + key,\n              currentValue\n            );\n          }\n        } else if (currentType == ARRAYTYPE) {\n          if (preType != ARRAYTYPE) {\n            setResult(\n              result,\n              (path == \"\" ? \"\" : path + \".\") + key,\n              currentValue\n            );\n          } else {\n            if (currentValue.length < preValue.length) {\n              setResult(\n                result,\n                (path == \"\" ? \"\" : path + \".\") + key,\n                currentValue\n              );\n            } else {\n              currentValue.forEach((item, index) => {\n                _diff(\n                  item,\n                  preValue[index],\n                  (path == \"\" ? \"\" : path + \".\") + key + \"[\" + index + \"]\",\n                  result\n                );\n              });\n            }\n          }\n        } else if (currentType == OBJECTTYPE) {\n          if (preType != OBJECTTYPE || Object.keys(currentValue).length < Object.keys(preValue).length) {\n            setResult(\n              result,\n              (path == \"\" ? \"\" : path + \".\") + key,\n              currentValue\n            );\n          } else {\n            for (let subKey in currentValue) {\n              _diff(\n                currentValue[subKey],\n                preValue[subKey],\n                (path == \"\" ? \"\" : path + \".\") + key + \".\" + subKey,\n                result\n              );\n            }\n          }\n        }\n      }\n    }\n  } else if (rootCurrentType == ARRAYTYPE) {\n    if (rootPreType != ARRAYTYPE) {\n      setResult(result, path, current);\n    } else {\n      if (current.length < pre.length) {\n        setResult(result, path, current);\n      } else {\n        current.forEach((item, index) => {\n          _diff(item, pre[index], path + \"[\" + index + \"]\", result);\n        });\n      }\n    }\n  } else {\n    setResult(result, path, current);\n  }\n}\nfunction setResult(result, k, v) {\n  result[k] = v;\n}\n\nfunction hasComponentEffect(instance) {\n  return queue.includes(instance.update);\n}\nfunction flushCallbacks(instance) {\n  const ctx = instance.ctx;\n  const callbacks = ctx.__next_tick_callbacks;\n  if (callbacks && callbacks.length) {\n    if (process.env.UNI_DEBUG) {\n      const mpInstance = ctx.$scope;\n      console.log(\n        \"uni-app:[\" + +/* @__PURE__ */ new Date() + \"][\" + (mpInstance.is || mpInstance.route) + \"][\" + instance.uid + \"]:flushCallbacks[\" + callbacks.length + \"]\"\n      );\n    }\n    const copies = callbacks.slice(0);\n    callbacks.length = 0;\n    for (let i = 0; i < copies.length; i++) {\n      copies[i]();\n    }\n  }\n}\nfunction nextTick(instance, fn) {\n  const ctx = instance.ctx;\n  if (!ctx.__next_tick_pending && !hasComponentEffect(instance)) {\n    if (process.env.UNI_DEBUG) {\n      const mpInstance = ctx.$scope;\n      console.log(\n        \"uni-app:[\" + +/* @__PURE__ */ new Date() + \"][\" + (mpInstance.is || mpInstance.route) + \"][\" + instance.uid + \"]:nextVueTick\"\n      );\n    }\n    return nextTick$1(fn && fn.bind(instance.proxy));\n  }\n  if (process.env.UNI_DEBUG) {\n    const mpInstance = ctx.$scope;\n    console.log(\n      \"uni-app:[\" + +/* @__PURE__ */ new Date() + \"][\" + (mpInstance.is || mpInstance.route) + \"][\" + instance.uid + \"]:nextMPTick\"\n    );\n  }\n  let _resolve;\n  if (!ctx.__next_tick_callbacks) {\n    ctx.__next_tick_callbacks = [];\n  }\n  ctx.__next_tick_callbacks.push(() => {\n    if (fn) {\n      callWithErrorHandling(\n        fn.bind(instance.proxy),\n        instance,\n        14\n      );\n    } else if (_resolve) {\n      _resolve(instance.proxy);\n    }\n  });\n  return new Promise((resolve) => {\n    _resolve = resolve;\n  });\n}\n\nfunction clone(src, seen) {\n  src = unwrapper(src);\n  const type = typeof src;\n  if (type === \"object\" && src !== null) {\n    let copy = seen.get(src);\n    if (typeof copy !== \"undefined\") {\n      return copy;\n    }\n    if (isArray(src)) {\n      const len = src.length;\n      copy = new Array(len);\n      seen.set(src, copy);\n      for (let i = 0; i < len; i++) {\n        copy[i] = clone(src[i], seen);\n      }\n    } else {\n      copy = {};\n      seen.set(src, copy);\n      for (const name in src) {\n        if (hasOwn(src, name)) {\n          copy[name] = clone(src[name], seen);\n        }\n      }\n    }\n    return copy;\n  }\n  if (type !== \"symbol\") {\n    return src;\n  }\n}\nfunction deepCopy(src) {\n  return clone(src, typeof WeakMap !== \"undefined\" ? /* @__PURE__ */ new WeakMap() : /* @__PURE__ */ new Map());\n}\n\nfunction getMPInstanceData(instance, keys) {\n  const data = instance.data;\n  const ret = /* @__PURE__ */ Object.create(null);\n  keys.forEach((key) => {\n    ret[key] = data[key];\n  });\n  return ret;\n}\nfunction patch(instance, data, oldData) {\n  if (!data) {\n    return;\n  }\n  data = deepCopy(data);\n  data.$eS = instance.$eS || {};\n  data.$eA = instance.$eA || {};\n  const ctx = instance.ctx;\n  const mpType = ctx.mpType;\n  if (mpType === \"page\" || mpType === \"component\") {\n    data.r0 = 1;\n    const start = Date.now();\n    const mpInstance = ctx.$scope;\n    const keys = Object.keys(data);\n    const diffData = diff(data, oldData || getMPInstanceData(mpInstance, keys));\n    if (Object.keys(diffData).length) {\n      if (process.env.UNI_DEBUG) {\n        console.log(\n          \"uni-app:[\" + +/* @__PURE__ */ new Date() + \"][\" + (mpInstance.is || mpInstance.route) + \"][\" + instance.uid + \"][\\u8017\\u65F6\" + (Date.now() - start) + \"]\\u5DEE\\u91CF\\u66F4\\u65B0\",\n          JSON.stringify(diffData)\n        );\n      }\n      ctx.__next_tick_pending = true;\n      mpInstance.setData(diffData, () => {\n        ctx.__next_tick_pending = false;\n        flushCallbacks(instance);\n      });\n      flushPreFlushCbs();\n    } else {\n      flushCallbacks(instance);\n    }\n  }\n}\n\nfunction initAppConfig(appConfig) {\n  appConfig.globalProperties.$nextTick = function $nextTick(fn) {\n    return nextTick(this.$, fn);\n  };\n}\n\nfunction onApplyOptions(options, instance, publicThis) {\n  instance.appContext.config.globalProperties.$applyOptions(\n    options,\n    instance,\n    publicThis\n  );\n  const computedOptions = options.computed;\n  if (computedOptions) {\n    const keys = Object.keys(computedOptions);\n    if (keys.length) {\n      const ctx = instance.ctx;\n      if (!ctx.$computedKeys) {\n        ctx.$computedKeys = [];\n      }\n      ctx.$computedKeys.push(...keys);\n    }\n  }\n  delete instance.ctx.$onApplyOptions;\n}\n\nfunction setRef$1(instance, isUnmount = false) {\n  const {\n    setupState,\n    $templateRefs,\n    $templateUniElementRefs,\n    ctx: { $scope, $mpPlatform }\n  } = instance;\n  if ($mpPlatform === \"mp-alipay\") {\n    return;\n  }\n  if (!$scope || !$templateRefs && !$templateUniElementRefs) {\n    return;\n  }\n  if (isUnmount) {\n    $templateRefs && $templateRefs.forEach(\n      (templateRef) => setTemplateRef(templateRef, null, setupState)\n    );\n    $templateUniElementRefs && $templateUniElementRefs.forEach(\n      (templateRef) => setTemplateRef(templateRef, null, setupState)\n    );\n    return;\n  }\n  const check = $mpPlatform === \"mp-baidu\" || $mpPlatform === \"mp-toutiao\";\n  const doSetByRefs = (refs) => {\n    if (refs.length === 0) {\n      return [];\n    }\n    const mpComponents = (\n      // 字节小程序 selectAllComponents 可能返回 null\n      // https://github.com/dcloudio/uni-app/issues/3954\n      ($scope.selectAllComponents(\".r\") || []).concat(\n        $scope.selectAllComponents(\".r-i-f\") || []\n      )\n    );\n    return refs.filter((templateRef) => {\n      const refValue = findComponentPublicInstance(mpComponents, templateRef.i);\n      if (check && refValue === null) {\n        return true;\n      }\n      setTemplateRef(templateRef, refValue, setupState);\n      return false;\n    });\n  };\n  const doSet = () => {\n    if ($templateRefs) {\n      const refs = doSetByRefs($templateRefs);\n      if (refs.length && instance.proxy && instance.proxy.$scope) {\n        instance.proxy.$scope.setData({ r1: 1 }, () => {\n          doSetByRefs(refs);\n        });\n      }\n    }\n  };\n  if ($templateUniElementRefs && $templateUniElementRefs.length) {\n    nextTick(instance, () => {\n      $templateUniElementRefs.forEach((templateRef) => {\n        if (isArray(templateRef.v)) {\n          templateRef.v.forEach((v) => {\n            setTemplateRef(templateRef, v, setupState);\n          });\n        } else {\n          setTemplateRef(templateRef, templateRef.v, setupState);\n        }\n      });\n    });\n  }\n  if ($scope._$setRef) {\n    $scope._$setRef(doSet);\n  } else {\n    nextTick(instance, doSet);\n  }\n}\nfunction toSkip(value) {\n  if (isObject(value)) {\n    markRaw(value);\n  }\n  return value;\n}\nfunction findComponentPublicInstance(mpComponents, id) {\n  const mpInstance = mpComponents.find(\n    (com) => com && (com.properties || com.props).uI === id\n  );\n  if (mpInstance) {\n    const vm = mpInstance.$vm;\n    if (vm) {\n      return getExposeProxy(vm.$) || vm;\n    }\n    return toSkip(mpInstance);\n  }\n  return null;\n}\nfunction setTemplateRef({ r, f }, refValue, setupState) {\n  if (isFunction(r)) {\n    r(refValue, {});\n  } else {\n    const _isString = isString(r);\n    const _isRef = isRef(r);\n    if (_isString || _isRef) {\n      if (f) {\n        if (!_isRef) {\n          return;\n        }\n        if (!isArray(r.value)) {\n          r.value = [];\n        }\n        const existing = r.value;\n        if (existing.indexOf(refValue) === -1) {\n          existing.push(refValue);\n          if (!refValue) {\n            return;\n          }\n          if (refValue.$) {\n            onBeforeUnmount(() => remove(existing, refValue), refValue.$);\n          }\n        }\n      } else if (_isString) {\n        if (hasOwn(setupState, r)) {\n          setupState[r] = refValue;\n        }\n      } else if (isRef(r)) {\n        r.value = refValue;\n      } else if (!!(process.env.NODE_ENV !== \"production\")) {\n        warnRef(r);\n      }\n    } else if (!!(process.env.NODE_ENV !== \"production\")) {\n      warnRef(r);\n    }\n  }\n}\nfunction warnRef(ref) {\n  warn(\"Invalid template ref type:\", ref, `(${typeof ref})`);\n}\n\nconst queuePostRenderEffect = queuePostFlushCb;\nfunction mountComponent(initialVNode, options) {\n  const instance = initialVNode.component = createComponentInstance(initialVNode, options.parentComponent, null);\n  if (__VUE_OPTIONS_API__) {\n    instance.ctx.$onApplyOptions = onApplyOptions;\n    instance.ctx.$children = [];\n  }\n  if (options.mpType === \"app\") {\n    instance.render = NOOP;\n  }\n  if (options.onBeforeSetup) {\n    options.onBeforeSetup(instance, options);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    pushWarningContext(initialVNode);\n    startMeasure(instance, `mount`);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    startMeasure(instance, `init`);\n  }\n  setupComponent(instance);\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    endMeasure(instance, `init`);\n  }\n  if (__VUE_OPTIONS_API__) {\n    if (options.parentComponent && instance.proxy) {\n      options.parentComponent.ctx.$children.push(getExposeProxy(instance) || instance.proxy);\n    }\n  }\n  setupRenderEffect(instance);\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    popWarningContext();\n    endMeasure(instance, `mount`);\n  }\n  return instance.proxy;\n}\nconst getFunctionalFallthrough = (attrs) => {\n  let res;\n  for (const key in attrs) {\n    if (key === \"class\" || key === \"style\" || isOn(key)) {\n      (res || (res = {}))[key] = attrs[key];\n    }\n  }\n  return res;\n};\nfunction renderComponentRoot(instance) {\n  const {\n    type: Component,\n    vnode,\n    proxy,\n    withProxy,\n    props,\n    propsOptions: [propsOptions],\n    slots,\n    attrs,\n    emit,\n    render,\n    renderCache,\n    data,\n    setupState,\n    ctx,\n    uid,\n    appContext: {\n      app: {\n        config: {\n          globalProperties: { pruneComponentPropsCache }\n        }\n      }\n    },\n    inheritAttrs\n  } = instance;\n  instance.$uniElementIds = /* @__PURE__ */ new Map();\n  instance.$templateRefs = [];\n  instance.$templateUniElementRefs = [];\n  instance.$templateUniElementStyles = {};\n  instance.$ei = 0;\n  pruneComponentPropsCache(uid);\n  instance.__counter = instance.__counter === 0 ? 1 : 0;\n  let result;\n  const prev = setCurrentRenderingInstance(instance);\n  try {\n    if (vnode.shapeFlag & 4) {\n      fallthroughAttrs(inheritAttrs, props, propsOptions, attrs);\n      const proxyToUse = withProxy || proxy;\n      result = render.call(\n        proxyToUse,\n        proxyToUse,\n        renderCache,\n        props,\n        setupState,\n        data,\n        ctx\n      );\n    } else {\n      fallthroughAttrs(\n        inheritAttrs,\n        props,\n        propsOptions,\n        Component.props ? attrs : getFunctionalFallthrough(attrs)\n      );\n      const render2 = Component;\n      result = render2.length > 1 ? render2(props, { attrs, slots, emit }) : render2(\n        props,\n        null\n        /* we know it doesn't need it */\n      );\n    }\n  } catch (err) {\n    handleError(err, instance, 1);\n    result = false;\n  }\n  setRef$1(instance);\n  setCurrentRenderingInstance(prev);\n  return result;\n}\nfunction fallthroughAttrs(inheritAttrs, props, propsOptions, fallthroughAttrs2) {\n  if (props && fallthroughAttrs2 && inheritAttrs !== false) {\n    const keys = Object.keys(fallthroughAttrs2).filter(\n      (key) => key !== \"class\" && key !== \"style\"\n    );\n    if (!keys.length) {\n      return;\n    }\n    if (propsOptions && keys.some(isModelListener)) {\n      keys.forEach((key) => {\n        if (!isModelListener(key) || !(key.slice(9) in propsOptions)) {\n          props[key] = fallthroughAttrs2[key];\n        }\n      });\n    } else {\n      keys.forEach((key) => props[key] = fallthroughAttrs2[key]);\n    }\n  }\n}\nconst updateComponentPreRender = (instance) => {\n  pauseTracking();\n  flushPreFlushCbs();\n  resetTracking();\n};\nfunction componentUpdateScopedSlotsFn() {\n  const scopedSlotsData = this.$scopedSlotsData;\n  if (!scopedSlotsData || scopedSlotsData.length === 0) {\n    return;\n  }\n  const start = Date.now();\n  const mpInstance = this.ctx.$scope;\n  const oldData = mpInstance.data;\n  const diffData = /* @__PURE__ */ Object.create(null);\n  scopedSlotsData.forEach(({ path, index, data }) => {\n    const oldScopedSlotData = getValueByDataPath(oldData, path);\n    const diffPath = isString(index) ? `${path}.${index}` : `${path}[${index}]`;\n    if (typeof oldScopedSlotData === \"undefined\" || typeof oldScopedSlotData[index] === \"undefined\") {\n      diffData[diffPath] = data;\n    } else {\n      const diffScopedSlotData = diff(\n        data,\n        oldScopedSlotData[index]\n      );\n      Object.keys(diffScopedSlotData).forEach((name) => {\n        diffData[diffPath + \".\" + name] = diffScopedSlotData[name];\n      });\n    }\n  });\n  scopedSlotsData.length = 0;\n  if (Object.keys(diffData).length) {\n    if (process.env.UNI_DEBUG) {\n      console.log(\n        \"uni-app:[\" + +/* @__PURE__ */ new Date() + \"][\" + (mpInstance.is || mpInstance.route) + \"][\" + this.uid + \"][\\u8017\\u65F6\" + (Date.now() - start) + \"]\\u4F5C\\u7528\\u57DF\\u63D2\\u69FD\\u5DEE\\u91CF\\u66F4\\u65B0\",\n        JSON.stringify(diffData)\n      );\n    }\n    mpInstance.setData(diffData);\n  }\n}\nfunction toggleRecurse({ effect, update }, allowed) {\n  effect.allowRecurse = update.allowRecurse = allowed;\n}\nfunction setupRenderEffect(instance) {\n  const updateScopedSlots = componentUpdateScopedSlotsFn.bind(\n    instance\n  );\n  instance.$updateScopedSlots = () => nextTick$1(() => queueJob(updateScopedSlots));\n  const componentUpdateFn = () => {\n    if (!instance.isMounted) {\n      onBeforeUnmount(() => {\n        setRef$1(instance, true);\n      }, instance);\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        startMeasure(instance, `patch`);\n      }\n      patch(instance, renderComponentRoot(instance));\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        endMeasure(instance, `patch`);\n      }\n      if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n        devtoolsComponentAdded(instance);\n      }\n    } else {\n      const { next, bu, u } = instance;\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        pushWarningContext(next || instance.vnode);\n      }\n      toggleRecurse(instance, false);\n      updateComponentPreRender();\n      if (bu) {\n        invokeArrayFns(bu);\n      }\n      toggleRecurse(instance, true);\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        startMeasure(instance, `patch`);\n      }\n      patch(instance, renderComponentRoot(instance));\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        endMeasure(instance, `patch`);\n      }\n      if (u) {\n        queuePostRenderEffect(u);\n      }\n      if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n        devtoolsComponentUpdated(instance);\n      }\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        popWarningContext();\n      }\n    }\n  };\n  const effect = instance.effect = new ReactiveEffect(\n    componentUpdateFn,\n    NOOP,\n    () => queueJob(update),\n    instance.scope\n    // track it in component's effect scope\n  );\n  const update = instance.update = () => {\n    if (effect.dirty) {\n      effect.run();\n    }\n  };\n  update.id = instance.uid;\n  toggleRecurse(instance, true);\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    effect.onTrack = instance.rtc ? (e) => invokeArrayFns(instance.rtc, e) : void 0;\n    effect.onTrigger = instance.rtg ? (e) => invokeArrayFns(instance.rtg, e) : void 0;\n    update.ownerInstance = instance;\n  }\n  if (!__VUE_CREATED_DEFERRED__) {\n    update();\n  }\n}\nfunction unmountComponent(instance) {\n  const { bum, scope, update, um } = instance;\n  if (bum) {\n    invokeArrayFns(bum);\n  }\n  if (__VUE_OPTIONS_API__) {\n    const parentInstance = instance.parent;\n    if (parentInstance) {\n      const $children = parentInstance.ctx.$children;\n      const target = getExposeProxy(instance) || instance.proxy;\n      const index = $children.indexOf(target);\n      if (index > -1) {\n        $children.splice(index, 1);\n      }\n    }\n  }\n  scope.stop();\n  if (update) {\n    update.active = false;\n  }\n  if (um) {\n    queuePostRenderEffect(um);\n  }\n  queuePostRenderEffect(() => {\n    instance.isUnmounted = true;\n  });\n  if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n    devtoolsComponentRemoved(instance);\n  }\n}\nconst oldCreateApp = createAppAPI();\nfunction getTarget() {\n  if (typeof window !== \"undefined\") {\n    return window;\n  }\n  if (typeof globalThis !== \"undefined\") {\n    return globalThis;\n  }\n  if (typeof global !== \"undefined\") {\n    return global;\n  }\n  if (typeof my !== \"undefined\") {\n    return my;\n  }\n}\nfunction createVueApp(rootComponent, rootProps = null) {\n  const target = getTarget();\n  target.__VUE__ = true;\n  if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n    setDevtoolsHook(target.__VUE_DEVTOOLS_GLOBAL_HOOK__, target);\n  }\n  const app = oldCreateApp(rootComponent, rootProps);\n  const appContext = app._context;\n  initAppConfig(appContext.config);\n  const createVNode = (initialVNode) => {\n    initialVNode.appContext = appContext;\n    initialVNode.shapeFlag = 6;\n    return initialVNode;\n  };\n  const createComponent = function createComponent2(initialVNode, options) {\n    return mountComponent(createVNode(initialVNode), options);\n  };\n  const destroyComponent = function destroyComponent2(component) {\n    return component && unmountComponent(component.$);\n  };\n  app.mount = function mount() {\n    rootComponent.render = NOOP;\n    const instance = mountComponent(\n      createVNode({ type: rootComponent }),\n      {\n        mpType: \"app\",\n        mpInstance: null,\n        parentComponent: null,\n        slots: [],\n        props: null\n      }\n    );\n    app._instance = instance.$;\n    if (!!(process.env.NODE_ENV !== \"production\") || __VUE_PROD_DEVTOOLS__) {\n      devtoolsInitApp(app, version);\n    }\n    instance.$app = app;\n    instance.$createComponent = createComponent;\n    instance.$destroyComponent = destroyComponent;\n    appContext.$appInstance = instance;\n    return instance;\n  };\n  app.unmount = function unmount() {\n    warn(`Cannot unmount an app.`);\n  };\n  return app;\n}\n\nfunction useCssModule(name = \"$style\") {\n  {\n    const instance = getCurrentInstance();\n    if (!instance) {\n      !!(process.env.NODE_ENV !== \"production\") && warn(`useCssModule must be called inside setup()`);\n      return EMPTY_OBJ;\n    }\n    const modules = instance.type.__cssModules;\n    if (!modules) {\n      !!(process.env.NODE_ENV !== \"production\") && warn(`Current instance does not have CSS modules injected.`);\n      return EMPTY_OBJ;\n    }\n    const mod = modules[name];\n    if (!mod) {\n      !!(process.env.NODE_ENV !== \"production\") && warn(`Current instance does not have CSS module named \"${name}\".`);\n      return EMPTY_OBJ;\n    }\n    return mod;\n  }\n}\n\nfunction useCssVars(getter) {\n  const instance = getCurrentInstance();\n  if (!instance) {\n    !!(process.env.NODE_ENV !== \"production\") && warn(`useCssVars is called without current active component instance.`);\n    return;\n  }\n  initCssVarsRender(instance, getter);\n}\nfunction initCssVarsRender(instance, getter) {\n  instance.ctx.__cssVars = () => {\n    const vars = getter(instance.proxy);\n    const cssVars = {};\n    for (const key in vars) {\n      cssVars[`--${key}`] = vars[key];\n    }\n    return cssVars;\n  };\n}\n\nfunction withModifiers() {\n}\nfunction createVNode() {\n}\n\nfunction injectLifecycleHook(name, hook, publicThis, instance) {\n    if (isFunction(hook)) {\n        injectHook(name, hook.bind(publicThis), instance);\n    }\n}\nfunction initHooks(options, instance, publicThis) {\n    const mpType = options.mpType || publicThis.$mpType;\n    if (!mpType || mpType === 'component') {\n        // 仅 App,Page 类型支持在 options 中配置 on 生命周期，组件可以使用组合式 API 定义页面生命周期\n        return;\n    }\n    Object.keys(options).forEach((name) => {\n        if (isUniLifecycleHook(name, options[name], false)) {\n            const hooks = options[name];\n            if (isArray(hooks)) {\n                hooks.forEach((hook) => injectLifecycleHook(name, hook, publicThis, instance));\n            }\n            else {\n                injectLifecycleHook(name, hooks, publicThis, instance);\n            }\n        }\n    });\n}\n\nfunction applyOptions(options, instance, publicThis) {\n    initHooks(options, instance, publicThis);\n}\n\nfunction set(target, key, val) {\n    return (target[key] = val);\n}\nfunction $callMethod(method, ...args) {\n    const fn = this[method];\n    if (fn) {\n        return fn(...args);\n    }\n    console.error(`method ${method} not found`);\n    return null;\n}\n\nfunction createErrorHandler(app) {\n    const userErrorHandler = app.config.errorHandler;\n    return function errorHandler(err, instance, info) {\n        if (userErrorHandler) {\n            userErrorHandler(err, instance, info);\n        }\n        const appInstance = app._instance;\n        if (!appInstance || !appInstance.proxy) {\n            throw err;\n        }\n        if (appInstance[ON_ERROR]) {\n            {\n                appInstance.proxy.$callHook(ON_ERROR, err);\n            }\n        }\n        else {\n            logError(err, info, instance ? instance.$.vnode : null, false);\n        }\n    };\n}\nfunction mergeAsArray(to, from) {\n    return to ? [...new Set([].concat(to, from))] : from;\n}\nfunction initOptionMergeStrategies(optionMergeStrategies) {\n    UniLifecycleHooks.forEach((name) => {\n        optionMergeStrategies[name] = mergeAsArray;\n    });\n}\n\nlet realAtob;\nconst b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\nconst b64re = /^(?:[A-Za-z\\d+/]{4})*?(?:[A-Za-z\\d+/]{2}(?:==)?|[A-Za-z\\d+/]{3}=?)?$/;\nif (typeof atob !== 'function') {\n    realAtob = function (str) {\n        str = String(str).replace(/[\\t\\n\\f\\r ]+/g, '');\n        if (!b64re.test(str)) {\n            throw new Error(\"Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.\");\n        }\n        // Adding the padding if missing, for semplicity\n        str += '=='.slice(2 - (str.length & 3));\n        var bitmap;\n        var result = '';\n        var r1;\n        var r2;\n        var i = 0;\n        for (; i < str.length;) {\n            bitmap =\n                (b64.indexOf(str.charAt(i++)) << 18) |\n                    (b64.indexOf(str.charAt(i++)) << 12) |\n                    ((r1 = b64.indexOf(str.charAt(i++))) << 6) |\n                    (r2 = b64.indexOf(str.charAt(i++)));\n            result +=\n                r1 === 64\n                    ? String.fromCharCode((bitmap >> 16) & 255)\n                    : r2 === 64\n                        ? String.fromCharCode((bitmap >> 16) & 255, (bitmap >> 8) & 255)\n                        : String.fromCharCode((bitmap >> 16) & 255, (bitmap >> 8) & 255, bitmap & 255);\n        }\n        return result;\n    };\n}\nelse {\n    // 注意atob只能在全局对象上调用，例如：`const Base64 = {atob};Base64.atob('xxxx')`是错误的用法\n    realAtob = atob;\n}\nfunction b64DecodeUnicode(str) {\n    return decodeURIComponent(realAtob(str)\n        .split('')\n        .map(function (c) {\n        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n    })\n        .join(''));\n}\nfunction getCurrentUserInfo() {\n    const token = uni.getStorageSync('uni_id_token') || '';\n    const tokenArr = token.split('.');\n    if (!token || tokenArr.length !== 3) {\n        return {\n            uid: null,\n            role: [],\n            permission: [],\n            tokenExpired: 0,\n        };\n    }\n    let userInfo;\n    try {\n        userInfo = JSON.parse(b64DecodeUnicode(tokenArr[1]));\n    }\n    catch (error) {\n        throw new Error('获取当前用户信息出错，详细错误信息为：' + error.message);\n    }\n    userInfo.tokenExpired = userInfo.exp * 1000;\n    delete userInfo.exp;\n    delete userInfo.iat;\n    return userInfo;\n}\nfunction uniIdMixin(globalProperties) {\n    globalProperties.uniIDHasRole = function (roleId) {\n        const { role } = getCurrentUserInfo();\n        return role.indexOf(roleId) > -1;\n    };\n    globalProperties.uniIDHasPermission = function (permissionId) {\n        const { permission } = getCurrentUserInfo();\n        return this.uniIDHasRole('admin') || permission.indexOf(permissionId) > -1;\n    };\n    globalProperties.uniIDTokenValid = function () {\n        const { tokenExpired } = getCurrentUserInfo();\n        return tokenExpired > Date.now();\n    };\n}\n\nfunction initApp(app) {\n    const appConfig = app.config;\n    // 该逻辑全平台会调用\n    // - 需要兼容支持开发者自定义的 errorHandler\n    // - nvue、vue 需要使用同一个（once） errorHandler\n    // - 需要支持 uni.onError 注册监听\n    //   * 目前仅部分小程序平台支持，调用uni.onError时，如果app已存在，则添加到instance的hooks中，如果不存在，则临时存储，初始化instance时添加到hooks中\n    //   * 目前在 errorHandler 中，会调用 app.$callHook(ON_ERROR, err)，所以上一步需要将 uni.onError 存储到 app 的 hooks 中\n    // - 部分平台（目前主要是小程序）开发阶段 uni-console 会调用 uni.onError 注册监听\n    appConfig.errorHandler = invokeCreateErrorHandler(app, createErrorHandler);\n    initOptionMergeStrategies(appConfig.optionMergeStrategies);\n    const globalProperties = appConfig.globalProperties;\n    {\n        uniIdMixin(globalProperties);\n    }\n    if (__VUE_OPTIONS_API__) {\n        globalProperties.$set = set;\n        globalProperties.$applyOptions = applyOptions;\n        globalProperties.$callMethod = $callMethod;\n    }\n    {\n        uni.invokeCreateVueAppHook(app);\n    }\n}\n\nconst propsCaches = Object.create(null);\nfunction renderProps(props) {\n    const { uid, __counter } = getCurrentInstance();\n    const propsId = (propsCaches[uid] || (propsCaches[uid] = [])).push(guardReactiveProps(props)) - 1;\n    // 强制每次更新\n    return uid + ',' + propsId + ',' + __counter;\n}\nfunction pruneComponentPropsCache(uid) {\n    delete propsCaches[uid];\n}\nfunction findComponentPropsData(up) {\n    if (!up) {\n        return;\n    }\n    const [uid, propsId] = up.split(',');\n    if (!propsCaches[uid]) {\n        return;\n    }\n    return propsCaches[uid][parseInt(propsId)];\n}\n\nvar plugin = {\n    install(app) {\n        initApp(app);\n        app.config.globalProperties.pruneComponentPropsCache =\n            pruneComponentPropsCache;\n        const oldMount = app.mount;\n        app.mount = function mount(rootContainer) {\n            const instance = oldMount.call(app, rootContainer);\n            const createApp = getCreateApp();\n            if (createApp) {\n                createApp(instance);\n            }\n            else {\n                // @ts-expect-error 旧编译器\n                if (typeof createMiniProgramApp !== 'undefined') {\n                    // @ts-expect-error\n                    createMiniProgramApp(instance);\n                }\n            }\n            return instance;\n        };\n    },\n};\nfunction getCreateApp() {\n    const method = process.env.UNI_MP_PLUGIN\n        ? 'createPluginApp'\n        : process.env.UNI_SUBPACKAGE\n            ? 'createSubpackageApp'\n            : 'createApp';\n    if (typeof global !== 'undefined' &&\n        typeof global[method] !== 'undefined') {\n        return global[method];\n        // @ts-expect-error\n    }\n    else if (typeof my !== 'undefined') {\n        // 支付宝小程序开启globalObjectMode配置后才会有global\n        // @ts-expect-error\n        return my[method];\n    }\n}\n\nfunction stringifyStyle(value) {\n    if (isString(value)) {\n        return value;\n    }\n    return stringify(normalizeStyle(value));\n}\n// 不使用 @vue/shared 中的 stringifyStyle (#3456)\nfunction stringify(styles) {\n    let ret = '';\n    if (!styles || isString(styles)) {\n        return ret;\n    }\n    for (const key in styles) {\n        ret += `${key.startsWith(`--`) ? key : hyphenate(key)}:${styles[key]};`;\n    }\n    return ret;\n}\n\nfunction vOn(value, key) {\n    const instance = getCurrentInstance();\n    const ctx = instance.ctx;\n    // 微信小程序，QQ小程序，当 setData diff 的时候，若事件不主动同步过去，会导致事件绑定不更新，（question/137217）\n    const extraKey = typeof key !== 'undefined' &&\n        (ctx.$mpPlatform === 'mp-weixin' ||\n            ctx.$mpPlatform === 'mp-qq' ||\n            ctx.$mpPlatform === 'mp-xhs') &&\n        (isString(key) || typeof key === 'number')\n        ? '_' + key\n        : '';\n    const name = 'e' + instance.$ei++ + extraKey;\n    const mpInstance = ctx.$scope;\n    if (!value) {\n        // remove\n        delete mpInstance[name];\n        return name;\n    }\n    const existingInvoker = mpInstance[name];\n    if (existingInvoker) {\n        // patch\n        existingInvoker.value = value;\n    }\n    else {\n        // add\n        mpInstance[name] = createInvoker(value, instance);\n    }\n    return name;\n}\nfunction createInvoker(initialValue, instance) {\n    const invoker = (e) => {\n        patchMPEvent(e);\n        let args = [e];\n        if (instance && instance.ctx.$getTriggerEventDetail) {\n            if (typeof e.detail === 'number') {\n                e.detail = instance.ctx.$getTriggerEventDetail(e.detail);\n            }\n        }\n        if (e.detail && e.detail.__args__) {\n            args = e.detail.__args__;\n        }\n        const eventValue = invoker.value;\n        const invoke = () => callWithAsyncErrorHandling(patchStopImmediatePropagation(e, eventValue), instance, 5 /* ErrorCodes.NATIVE_EVENT_HANDLER */, args);\n        // 冒泡事件触发时，启用延迟策略，避免同一批次的事件执行时机不正确，对性能可能有略微影响 https://github.com/dcloudio/uni-app/issues/3228\n        const eventTarget = e.target;\n        const eventSync = eventTarget\n            ? eventTarget.dataset\n                ? String(eventTarget.dataset.eventsync) === 'true'\n                : false\n            : false;\n        if (bubbles.includes(e.type) && !eventSync) {\n            setTimeout(invoke);\n        }\n        else {\n            const res = invoke();\n            if (e.type === 'input' && (isArray(res) || isPromise(res))) {\n                return;\n            }\n            return res;\n        }\n    };\n    invoker.value = initialValue;\n    return invoker;\n}\n// 冒泡事件列表\nconst bubbles = [\n    // touch事件暂不做延迟，否则在 Android 上会影响性能，比如一些拖拽跟手手势等\n    // 'touchstart',\n    // 'touchmove',\n    // 'touchcancel',\n    // 'touchend',\n    'tap',\n    'longpress',\n    'longtap',\n    'transitionend',\n    'animationstart',\n    'animationiteration',\n    'animationend',\n    'touchforcechange',\n];\nfunction patchMPEvent(event, instance) {\n    if (event.type && event.target) {\n        event.preventDefault = NOOP;\n        event.stopPropagation = NOOP;\n        event.stopImmediatePropagation = NOOP;\n        if (!hasOwn(event, 'detail')) {\n            event.detail = {};\n        }\n        if (hasOwn(event, 'markerId')) {\n            event.detail = typeof event.detail === 'object' ? event.detail : {};\n            event.detail.markerId = event.markerId;\n        }\n        // mp-baidu，checked=>value\n        if (isPlainObject(event.detail) &&\n            hasOwn(event.detail, 'checked') &&\n            !hasOwn(event.detail, 'value')) {\n            event.detail.value = event.detail.checked;\n        }\n        if (isPlainObject(event.detail)) {\n            event.target = extend({}, event.target, event.detail);\n        }\n    }\n}\nfunction patchStopImmediatePropagation(e, value) {\n    if (isArray(value)) {\n        const originalStop = e.stopImmediatePropagation;\n        e.stopImmediatePropagation = () => {\n            originalStop && originalStop.call(e);\n            e._stopped = true;\n        };\n        return value.map((fn) => (e) => !e._stopped && fn(e));\n    }\n    else {\n        return value;\n    }\n}\n\n/**\n * Actual implementation\n */\nfunction vFor(source, renderItem) {\n    let ret;\n    if (isArray(source) || isString(source)) {\n        ret = new Array(source.length);\n        for (let i = 0, l = source.length; i < l; i++) {\n            ret[i] = renderItem(source[i], i, i);\n        }\n    }\n    else if (typeof source === 'number') {\n        if ((process.env.NODE_ENV !== 'production') && !Number.isInteger(source)) {\n            warn(`The v-for range expect an integer value but got ${source}.`);\n            return [];\n        }\n        ret = new Array(source);\n        for (let i = 0; i < source; i++) {\n            ret[i] = renderItem(i + 1, i, i);\n        }\n    }\n    else if (isObject(source)) {\n        if (source[Symbol.iterator]) {\n            ret = Array.from(source, (item, i) => renderItem(item, i, i));\n        }\n        else {\n            const keys = Object.keys(source);\n            ret = new Array(keys.length);\n            for (let i = 0, l = keys.length; i < l; i++) {\n                const key = keys[i];\n                ret[i] = renderItem(source[key], key, i);\n            }\n        }\n    }\n    else {\n        ret = [];\n    }\n    return ret;\n}\n\nfunction renderSlot(name, props = {}, key) {\n    const instance = getCurrentInstance();\n    const { parent, isMounted, ctx: { $scope }, } = instance;\n    // mp-alipay 为 props\n    const vueIds = ($scope.properties || $scope.props).uI;\n    if (!vueIds) {\n        return;\n    }\n    if (!parent && !isMounted) {\n        // 头条小程序首次 render 时，还没有 parent\n        onMounted(() => {\n            renderSlot(name, props, key);\n        }, instance);\n        return;\n    }\n    const invoker = findScopedSlotInvoker(vueIds, instance);\n    // 可能不存在，因为插槽不是必需的\n    if (invoker) {\n        invoker(name, props, key);\n    }\n}\nfunction findScopedSlotInvoker(vueId, instance) {\n    let parent = instance.parent;\n    while (parent) {\n        const invokers = parent.$ssi;\n        if (invokers && invokers[vueId]) {\n            return invokers[vueId];\n        }\n        parent = parent.parent;\n    }\n}\n\nfunction withScopedSlot(fn, { name, path, vueId, }) {\n    const instance = getCurrentInstance();\n    fn.path = path;\n    const scopedSlots = (instance.$ssi ||\n        (instance.$ssi = {}));\n    const invoker = scopedSlots[vueId] ||\n        (scopedSlots[vueId] = createScopedSlotInvoker(instance));\n    if (!invoker.slots[name]) {\n        invoker.slots[name] = {\n            fn,\n        };\n    }\n    else {\n        invoker.slots[name].fn = fn;\n    }\n    return getValueByDataPath(instance.ctx.$scope.data, path);\n}\nfunction createScopedSlotInvoker(instance) {\n    const invoker = (slotName, args, index) => {\n        const slot = invoker.slots[slotName];\n        if (!slot) {\n            // slot 可能不存在 https://github.com/dcloudio/uni-app/issues/3346\n            return;\n        }\n        const hasIndex = typeof index !== 'undefined';\n        index = index || 0;\n        // 确保当前 slot 的上下文，类似 withCtx\n        const prevInstance = setCurrentRenderingInstance(instance);\n        const data = slot.fn(args, slotName + (hasIndex ? '-' + index : ''), index);\n        const path = slot.fn.path;\n        setCurrentRenderingInstance(prevInstance);\n        (instance.$scopedSlotsData || (instance.$scopedSlotsData = [])).push({\n            path,\n            index,\n            data,\n        });\n        instance.$updateScopedSlots();\n    };\n    invoker.slots = {};\n    return invoker;\n}\n\n/**\n * quickapp-webview 不能使用 default 作为插槽名称，故统一转换 default 为 d\n * @param names\n * @returns\n */\nfunction dynamicSlot(names) {\n    if (isString(names)) {\n        return dynamicSlotName(names);\n    }\n    return names.map((name) => dynamicSlotName(name));\n}\n\nfunction setRef(ref, id, opts = {}) {\n    const { $templateRefs } = getCurrentInstance();\n    $templateRefs.push({ i: id, r: ref, k: opts.k, f: opts.f });\n}\n\nfunction withModelModifiers(fn, { number, trim }, isComponent = false) {\n    if (isComponent) {\n        return (...args) => {\n            if (trim) {\n                args = args.map((a) => a.trim());\n            }\n            else if (number) {\n                args = args.map(toNumber);\n            }\n            return fn(...args);\n        };\n    }\n    return (event) => {\n        const value = event.detail.value;\n        if (trim) {\n            event.detail.value = value.trim();\n        }\n        else if (number) {\n            event.detail.value = toNumber(value);\n        }\n        return fn(event);\n    };\n}\n\nfunction hasIdProp(_ctx) {\n    return (_ctx.$.propsOptions &&\n        _ctx.$.propsOptions[0] &&\n        'id' in _ctx.$.propsOptions[0]);\n}\nfunction hasVirtualHostId(_ctx) {\n    return _ctx.$scope.virtualHostId !== '';\n}\nfunction genIdWithVirtualHost(_ctx, idBinding) {\n    if (!hasVirtualHostId(_ctx) || hasIdProp(_ctx)) {\n        return idBinding;\n    }\n    return _ctx.virtualHostId;\n}\nfunction genUniElementId(_ctx, idBinding, genId) {\n    return genIdWithVirtualHost(_ctx, idBinding) || genId || '';\n}\n\nfunction setupDevtoolsPlugin() {\n    // noop\n}\n\nconst o = (value, key) => vOn(value, key);\nconst f = (source, renderItem) => vFor(source, renderItem);\nconst d = (names) => dynamicSlot(names);\nconst r = (name, props, key) => renderSlot(name, props, key);\nconst w = (fn, options) => withScopedSlot(fn, options);\nconst s = (value) => stringifyStyle(value);\nconst c = (str) => camelize(str);\nconst e = (target, ...sources) => extend(target, ...sources);\nconst h = (str) => hyphenate(str);\nconst n = (value) => normalizeClass(value);\nconst t = (val) => toDisplayString(val);\nconst p = (props) => renderProps(props);\nconst sr = (ref, id, opts) => setRef(ref, id, opts);\nconst m = (fn, modifiers, isComponent = false) => withModelModifiers(fn, modifiers, isComponent);\nconst j = (obj) => JSON.stringify(obj);\nconst gei = genUniElementId;\n\nfunction createApp(rootComponent, rootProps = null) {\n    rootComponent && (rootComponent.mpType = 'app');\n    return createVueApp(rootComponent, rootProps).use(plugin);\n}\nconst createSSRApp = createApp;\n\nexport { EffectScope, Fragment, ReactiveEffect, Text, c, callWithAsyncErrorHandling, callWithErrorHandling, computed, createApp, createPropsRestProxy, createSSRApp, createVNode, createVueApp, customRef, d, defineAsyncComponent, defineComponent, defineEmits, defineExpose, defineProps, devtoolsComponentAdded, devtoolsComponentRemoved, devtoolsComponentUpdated, diff, e, effect, effectScope, f, findComponentPropsData, gei, getCurrentInstance, getCurrentScope, getExposeProxy, guardReactiveProps, h, hasInjectionContext, hasQueueJob, inject, injectHook, invalidateJob, isInSSRComponentSetup, isProxy, isReactive, isReadonly, isRef, isShallow, j, logError, m, markRaw, mergeDefaults, mergeModels, mergeProps, n, nextTick$1 as nextTick, o, onActivated, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onDeactivated, onErrorCaptured, onMounted, onRenderTracked, onRenderTriggered, onScopeDispose, onServerPrefetch, onUnmounted, onUpdated, p, patch, provide, proxyRefs, pruneComponentPropsCache, queuePostFlushCb, r, reactive, readonly, ref, resolveComponent, resolveDirective, resolveFilter, s, setCurrentRenderingInstance, setTemplateRef, setupDevtoolsPlugin, shallowReactive, shallowReadonly, shallowRef, sr, stop, t, toHandlers, toRaw, toRef, toRefs, toValue, triggerRef, unref, updateProps, useAttrs, useCssModule, useCssVars, useModel, useSSRContext, useSlots, version, w, warn, watch, watchEffect, watchPostEffect, watchSyncEffect, withAsyncContext, withCtx, withDefaults, withDirectives, withModifiers, withScopeId };\n", "import { isArray, hasOwn, isString, isPlainObject, isObject, capitalize, toRawType, makeMap, isFunction, isPromise, extend, remove } from '@vue/shared';\nimport { LOCALE_EN, normalizeLocale } from '@dcloudio/uni-i18n';\nimport { Emitter, sortObject, ON_ERROR, onCreateVueApp, invokeCreateVueAppHook } from '@dcloudio/uni-shared';\nimport { injectHook } from 'vue';\n\nfunction getLocaleLanguage() {\n    let localeLanguage = '';\n    {\n        const appBaseInfo = wx.getAppBaseInfo();\n        const language = appBaseInfo && appBaseInfo.language ? appBaseInfo.language : LOCALE_EN;\n        localeLanguage = normalizeLocale(language) || LOCALE_EN;\n    }\n    return localeLanguage;\n}\n\nfunction validateProtocolFail(name, msg) {\n    console.warn(`${name}: ${msg}`);\n}\nfunction validateProtocol(name, data, protocol, onFail) {\n    if (!onFail) {\n        onFail = validateProtocolFail;\n    }\n    for (const key in protocol) {\n        const errMsg = validateProp(key, data[key], protocol[key], !hasOwn(data, key));\n        if (isString(errMsg)) {\n            onFail(name, errMsg);\n        }\n    }\n}\nfunction validateProtocols(name, args, protocol, onFail) {\n    if (!protocol) {\n        return;\n    }\n    if (!isArray(protocol)) {\n        return validateProtocol(name, args[0] || Object.create(null), protocol, onFail);\n    }\n    const len = protocol.length;\n    const argsLen = args.length;\n    for (let i = 0; i < len; i++) {\n        const opts = protocol[i];\n        const data = Object.create(null);\n        if (argsLen > i) {\n            data[opts.name] = args[i];\n        }\n        validateProtocol(name, data, { [opts.name]: opts }, onFail);\n    }\n}\nfunction validateProp(name, value, prop, isAbsent) {\n    if (!isPlainObject(prop)) {\n        prop = { type: prop };\n    }\n    const { type, required, validator } = prop;\n    // required!\n    if (required && isAbsent) {\n        return 'Missing required args: \"' + name + '\"';\n    }\n    // missing but optional\n    if (value == null && !required) {\n        return;\n    }\n    // type check\n    if (type != null) {\n        let isValid = false;\n        const types = isArray(type) ? type : [type];\n        const expectedTypes = [];\n        // value is valid as long as one of the specified types match\n        for (let i = 0; i < types.length && !isValid; i++) {\n            const { valid, expectedType } = assertType(value, types[i]);\n            expectedTypes.push(expectedType || '');\n            isValid = valid;\n        }\n        if (!isValid) {\n            return getInvalidTypeMessage(name, value, expectedTypes);\n        }\n    }\n    // custom validator\n    if (validator) {\n        return validator(value);\n    }\n}\nconst isSimpleType = /*#__PURE__*/ makeMap('String,Number,Boolean,Function,Symbol');\nfunction assertType(value, type) {\n    let valid;\n    const expectedType = getType(type);\n    if (isSimpleType(expectedType)) {\n        const t = typeof value;\n        valid = t === expectedType.toLowerCase();\n        // for primitive wrapper objects\n        if (!valid && t === 'object') {\n            valid = value instanceof type;\n        }\n    }\n    else if (expectedType === 'Object') {\n        valid = isObject(value);\n    }\n    else if (expectedType === 'Array') {\n        valid = isArray(value);\n    }\n    else {\n        {\n            valid = value instanceof type;\n        }\n    }\n    return {\n        valid,\n        expectedType,\n    };\n}\nfunction getInvalidTypeMessage(name, value, expectedTypes) {\n    let message = `Invalid args: type check failed for args \"${name}\".` +\n        ` Expected ${expectedTypes.map(capitalize).join(', ')}`;\n    const expectedType = expectedTypes[0];\n    const receivedType = toRawType(value);\n    const expectedValue = styleValue(value, expectedType);\n    const receivedValue = styleValue(value, receivedType);\n    // check if we need to specify expected value\n    if (expectedTypes.length === 1 &&\n        isExplicable(expectedType) &&\n        !isBoolean(expectedType, receivedType)) {\n        message += ` with value ${expectedValue}`;\n    }\n    message += `, got ${receivedType} `;\n    // check if we need to specify received value\n    if (isExplicable(receivedType)) {\n        message += `with value ${receivedValue}.`;\n    }\n    return message;\n}\nfunction getType(ctor) {\n    const match = ctor && ctor.toString().match(/^\\s*function (\\w+)/);\n    return match ? match[1] : '';\n}\nfunction styleValue(value, type) {\n    if (type === 'String') {\n        return `\"${value}\"`;\n    }\n    else if (type === 'Number') {\n        return `${Number(value)}`;\n    }\n    else {\n        return `${value}`;\n    }\n}\nfunction isExplicable(type) {\n    const explicitTypes = ['string', 'number', 'boolean'];\n    return explicitTypes.some((elem) => type.toLowerCase() === elem);\n}\nfunction isBoolean(...args) {\n    return args.some((elem) => elem.toLowerCase() === 'boolean');\n}\n\nfunction tryCatch(fn) {\n    return function () {\n        try {\n            return fn.apply(fn, arguments);\n        }\n        catch (e) {\n            // TODO\n            console.error(e);\n        }\n    };\n}\n\nlet invokeCallbackId = 1;\nconst invokeCallbacks = {};\nfunction addInvokeCallback(id, name, callback, keepAlive = false) {\n    invokeCallbacks[id] = {\n        name,\n        keepAlive,\n        callback,\n    };\n    return id;\n}\n// onNativeEventReceive((event,data)=>{}) 需要两个参数，目前写死最多两个参数\nfunction invokeCallback(id, res, extras) {\n    if (typeof id === 'number') {\n        const opts = invokeCallbacks[id];\n        if (opts) {\n            if (!opts.keepAlive) {\n                delete invokeCallbacks[id];\n            }\n            return opts.callback(res, extras);\n        }\n    }\n    return res;\n}\nconst API_SUCCESS = 'success';\nconst API_FAIL = 'fail';\nconst API_COMPLETE = 'complete';\nfunction getApiCallbacks(args) {\n    const apiCallbacks = {};\n    for (const name in args) {\n        const fn = args[name];\n        if (isFunction(fn)) {\n            apiCallbacks[name] = tryCatch(fn);\n            delete args[name];\n        }\n    }\n    return apiCallbacks;\n}\nfunction normalizeErrMsg(errMsg, name) {\n    if (!errMsg || errMsg.indexOf(':fail') === -1) {\n        return name + ':ok';\n    }\n    return name + errMsg.substring(errMsg.indexOf(':fail'));\n}\nfunction createAsyncApiCallback(name, args = {}, { beforeAll, beforeSuccess } = {}) {\n    if (!isPlainObject(args)) {\n        args = {};\n    }\n    const { success, fail, complete } = getApiCallbacks(args);\n    const hasSuccess = isFunction(success);\n    const hasFail = isFunction(fail);\n    const hasComplete = isFunction(complete);\n    const callbackId = invokeCallbackId++;\n    addInvokeCallback(callbackId, name, (res) => {\n        res = res || {};\n        res.errMsg = normalizeErrMsg(res.errMsg, name);\n        isFunction(beforeAll) && beforeAll(res);\n        if (res.errMsg === name + ':ok') {\n            isFunction(beforeSuccess) && beforeSuccess(res, args);\n            hasSuccess && success(res);\n        }\n        else {\n            hasFail && fail(res);\n        }\n        hasComplete && complete(res);\n    });\n    return callbackId;\n}\n\nconst HOOK_SUCCESS = 'success';\nconst HOOK_FAIL = 'fail';\nconst HOOK_COMPLETE = 'complete';\nconst globalInterceptors = {};\nconst scopedInterceptors = {};\nfunction wrapperHook(hook, params) {\n    return function (data) {\n        return hook(data, params) || data;\n    };\n}\nfunction queue(hooks, data, params) {\n    let promise = false;\n    for (let i = 0; i < hooks.length; i++) {\n        const hook = hooks[i];\n        if (promise) {\n            promise = Promise.resolve(wrapperHook(hook, params));\n        }\n        else {\n            const res = hook(data, params);\n            if (isPromise(res)) {\n                promise = Promise.resolve(res);\n            }\n            if (res === false) {\n                return {\n                    then() { },\n                    catch() { },\n                };\n            }\n        }\n    }\n    return (promise || {\n        then(callback) {\n            return callback(data);\n        },\n        catch() { },\n    });\n}\nfunction wrapperOptions(interceptors, options = {}) {\n    [HOOK_SUCCESS, HOOK_FAIL, HOOK_COMPLETE].forEach((name) => {\n        const hooks = interceptors[name];\n        if (!isArray(hooks)) {\n            return;\n        }\n        const oldCallback = options[name];\n        options[name] = function callbackInterceptor(res) {\n            queue(hooks, res, options).then((res) => {\n                return (isFunction(oldCallback) && oldCallback(res)) || res;\n            });\n        };\n    });\n    return options;\n}\nfunction wrapperReturnValue(method, returnValue) {\n    const returnValueHooks = [];\n    if (isArray(globalInterceptors.returnValue)) {\n        returnValueHooks.push(...globalInterceptors.returnValue);\n    }\n    const interceptor = scopedInterceptors[method];\n    if (interceptor && isArray(interceptor.returnValue)) {\n        returnValueHooks.push(...interceptor.returnValue);\n    }\n    returnValueHooks.forEach((hook) => {\n        returnValue = hook(returnValue) || returnValue;\n    });\n    return returnValue;\n}\nfunction getApiInterceptorHooks(method) {\n    const interceptor = Object.create(null);\n    Object.keys(globalInterceptors).forEach((hook) => {\n        if (hook !== 'returnValue') {\n            interceptor[hook] = globalInterceptors[hook].slice();\n        }\n    });\n    const scopedInterceptor = scopedInterceptors[method];\n    if (scopedInterceptor) {\n        Object.keys(scopedInterceptor).forEach((hook) => {\n            if (hook !== 'returnValue') {\n                interceptor[hook] = (interceptor[hook] || []).concat(scopedInterceptor[hook]);\n            }\n        });\n    }\n    return interceptor;\n}\nfunction invokeApi(method, api, options, params) {\n    const interceptor = getApiInterceptorHooks(method);\n    if (interceptor && Object.keys(interceptor).length) {\n        if (isArray(interceptor.invoke)) {\n            const res = queue(interceptor.invoke, options);\n            return res.then((options) => {\n                // 重新访问 getApiInterceptorHooks, 允许 invoke 中再次调用 addInterceptor,removeInterceptor\n                return api(wrapperOptions(getApiInterceptorHooks(method), options), ...params);\n            });\n        }\n        else {\n            return api(wrapperOptions(interceptor, options), ...params);\n        }\n    }\n    return api(options, ...params);\n}\n\nfunction hasCallback(args) {\n    if (isPlainObject(args) &&\n        [API_SUCCESS, API_FAIL, API_COMPLETE].find((cb) => isFunction(args[cb]))) {\n        return true;\n    }\n    return false;\n}\nfunction handlePromise(promise) {\n    // if (__UNI_FEATURE_PROMISE__) {\n    //   return promise\n    //     .then((data) => {\n    //       return [null, data]\n    //     })\n    //     .catch((err) => [err])\n    // }\n    return promise;\n}\nfunction promisify$1(name, fn) {\n    return (args = {}, ...rest) => {\n        if (hasCallback(args)) {\n            return wrapperReturnValue(name, invokeApi(name, fn, args, rest));\n        }\n        return wrapperReturnValue(name, handlePromise(new Promise((resolve, reject) => {\n            invokeApi(name, fn, extend(args, { success: resolve, fail: reject }), rest);\n        })));\n    };\n}\n\nfunction formatApiArgs(args, options) {\n    args[0];\n    {\n        return;\n    }\n}\nfunction invokeSuccess(id, name, res) {\n    const result = {\n        errMsg: name + ':ok',\n    };\n    return invokeCallback(id, extend((res || {}), result));\n}\nfunction invokeFail(id, name, errMsg, errRes = {}) {\n    const errMsgPrefix = name + ':fail';\n    let apiErrMsg = '';\n    if (!errMsg) {\n        apiErrMsg = errMsgPrefix;\n    }\n    else if (errMsg.indexOf(errMsgPrefix) === 0) {\n        apiErrMsg = errMsg;\n    }\n    else {\n        apiErrMsg = errMsgPrefix + ' ' + errMsg;\n    }\n    {\n        delete errRes.errCode;\n    }\n    let res = extend({ errMsg: apiErrMsg }, errRes);\n    return invokeCallback(id, res);\n}\nfunction beforeInvokeApi(name, args, protocol, options) {\n    if ((process.env.NODE_ENV !== 'production')) {\n        validateProtocols(name, args, protocol);\n    }\n    const errMsg = formatApiArgs(args);\n    if (errMsg) {\n        return errMsg;\n    }\n}\nfunction parseErrMsg(errMsg) {\n    if (!errMsg || isString(errMsg)) {\n        return errMsg;\n    }\n    if (errMsg.stack) {\n        // 此处同时被鸿蒙arkts和jsvm使用，暂时使用运行时判断鸿蒙jsvm环境，注意此用法仅内部使用\n        if ((typeof globalThis === 'undefined' || !globalThis.harmonyChannel)) {\n            console.error(errMsg.message + '\\n' + errMsg.stack);\n        }\n        return errMsg.message;\n    }\n    return errMsg;\n}\nfunction wrapperTaskApi(name, fn, protocol, options) {\n    return (args) => {\n        const id = createAsyncApiCallback(name, args, options);\n        const errMsg = beforeInvokeApi(name, [args], protocol);\n        if (errMsg) {\n            return invokeFail(id, name, errMsg);\n        }\n        return fn(args, {\n            resolve: (res) => invokeSuccess(id, name, res),\n            reject: (errMsg, errRes) => invokeFail(id, name, parseErrMsg(errMsg), errRes),\n        });\n    };\n}\nfunction wrapperSyncApi(name, fn, protocol, options) {\n    return (...args) => {\n        const errMsg = beforeInvokeApi(name, args, protocol);\n        if (errMsg) {\n            throw new Error(errMsg);\n        }\n        return fn.apply(null, args);\n    };\n}\nfunction wrapperAsyncApi(name, fn, protocol, options) {\n    return wrapperTaskApi(name, fn, protocol, options);\n}\nfunction defineSyncApi(name, fn, protocol, options) {\n    return wrapperSyncApi(name, fn, (process.env.NODE_ENV !== 'production') ? protocol : undefined);\n}\nfunction defineAsyncApi(name, fn, protocol, options) {\n    return promisify$1(name, wrapperAsyncApi(name, fn, (process.env.NODE_ENV !== 'production') ? protocol : undefined, options));\n}\n\nconst API_UPX2PX = 'upx2px';\nconst Upx2pxProtocol = [\n    {\n        name: 'upx',\n        type: [Number, String],\n        required: true,\n    },\n];\n\nconst EPS = 1e-4;\nconst BASE_DEVICE_WIDTH = 750;\nlet isIOS = false;\nlet deviceWidth = 0;\nlet deviceDPR = 0;\nfunction checkDeviceWidth() {\n    const { windowWidth, pixelRatio, platform } = Object.assign({}, wx.getWindowInfo(), {\n            platform: wx.getDeviceInfo().platform,\n        })\n        ;\n    deviceWidth = windowWidth;\n    deviceDPR = pixelRatio;\n    isIOS = platform === 'ios';\n}\nconst upx2px = defineSyncApi(API_UPX2PX, (number, newDeviceWidth) => {\n    if (deviceWidth === 0) {\n        checkDeviceWidth();\n    }\n    number = Number(number);\n    if (number === 0) {\n        return 0;\n    }\n    let width = newDeviceWidth || deviceWidth;\n    let result = (number / BASE_DEVICE_WIDTH) * width;\n    if (result < 0) {\n        result = -result;\n    }\n    result = Math.floor(result + EPS);\n    if (result === 0) {\n        if (deviceDPR === 1 || !isIOS) {\n            result = 1;\n        }\n        else {\n            result = 0.5;\n        }\n    }\n    return number < 0 ? -result : result;\n}, Upx2pxProtocol);\n\nfunction __f__(type, filename, ...args) {\n    if (filename) {\n        args.push(filename);\n    }\n    console[type].apply(console, args);\n}\n\nconst API_ADD_INTERCEPTOR = 'addInterceptor';\nconst API_REMOVE_INTERCEPTOR = 'removeInterceptor';\nconst AddInterceptorProtocol = [\n    {\n        name: 'method',\n        type: [String, Object],\n        required: true,\n    },\n];\nconst RemoveInterceptorProtocol = AddInterceptorProtocol;\n\nfunction mergeInterceptorHook(interceptors, interceptor) {\n    Object.keys(interceptor).forEach((hook) => {\n        if (isFunction(interceptor[hook])) {\n            interceptors[hook] = mergeHook(interceptors[hook], interceptor[hook]);\n        }\n    });\n}\nfunction removeInterceptorHook(interceptors, interceptor) {\n    if (!interceptors || !interceptor) {\n        return;\n    }\n    Object.keys(interceptor).forEach((name) => {\n        const hooks = interceptors[name];\n        const hook = interceptor[name];\n        if (isArray(hooks) && isFunction(hook)) {\n            remove(hooks, hook);\n        }\n    });\n}\nfunction mergeHook(parentVal, childVal) {\n    const res = childVal\n        ? parentVal\n            ? parentVal.concat(childVal)\n            : isArray(childVal)\n                ? childVal\n                : [childVal]\n        : parentVal;\n    return res ? dedupeHooks(res) : res;\n}\nfunction dedupeHooks(hooks) {\n    const res = [];\n    for (let i = 0; i < hooks.length; i++) {\n        if (res.indexOf(hooks[i]) === -1) {\n            res.push(hooks[i]);\n        }\n    }\n    return res;\n}\nconst addInterceptor = defineSyncApi(API_ADD_INTERCEPTOR, (method, interceptor) => {\n    if (isString(method) && isPlainObject(interceptor)) {\n        mergeInterceptorHook(scopedInterceptors[method] || (scopedInterceptors[method] = {}), interceptor);\n    }\n    else if (isPlainObject(method)) {\n        mergeInterceptorHook(globalInterceptors, method);\n    }\n}, AddInterceptorProtocol);\nconst removeInterceptor = defineSyncApi(API_REMOVE_INTERCEPTOR, (method, interceptor) => {\n    if (isString(method)) {\n        if (isPlainObject(interceptor)) {\n            removeInterceptorHook(scopedInterceptors[method], interceptor);\n        }\n        else {\n            delete scopedInterceptors[method];\n        }\n    }\n    else if (isPlainObject(method)) {\n        removeInterceptorHook(globalInterceptors, method);\n    }\n}, RemoveInterceptorProtocol);\nconst interceptors = {};\n\nconst API_ON = '$on';\nconst OnProtocol = [\n    {\n        name: 'event',\n        type: String,\n        required: true,\n    },\n    {\n        name: 'callback',\n        type: Function,\n        required: true,\n    },\n];\nconst API_ONCE = '$once';\nconst OnceProtocol = OnProtocol;\nconst API_OFF = '$off';\nconst OffProtocol = [\n    {\n        name: 'event',\n        type: [String, Array],\n    },\n    {\n        name: 'callback',\n        type: [Function, Number],\n    },\n];\nconst API_EMIT = '$emit';\nconst EmitProtocol = [\n    {\n        name: 'event',\n        type: String,\n        required: true,\n    },\n];\n\nclass EventBus {\n    constructor() {\n        this.$emitter = new Emitter();\n    }\n    on(name, callback) {\n        return this.$emitter.on(name, callback);\n    }\n    once(name, callback) {\n        return this.$emitter.once(name, callback);\n    }\n    off(name, callback) {\n        if (!name) {\n            this.$emitter.e = {};\n            return;\n        }\n        this.$emitter.off(name, callback);\n    }\n    emit(name, ...args) {\n        this.$emitter.emit(name, ...args);\n    }\n}\nconst eventBus = new EventBus();\nconst $on = defineSyncApi(API_ON, (name, callback) => {\n    eventBus.on(name, callback);\n    return () => eventBus.off(name, callback);\n}, OnProtocol);\nconst $once = defineSyncApi(API_ONCE, (name, callback) => {\n    eventBus.once(name, callback);\n    return () => eventBus.off(name, callback);\n}, OnceProtocol);\nconst $off = defineSyncApi(API_OFF, (name, callback) => {\n    // 类型中不再体现 name 支持 string[] 类型, 仅在 uni.$off 保留该逻辑向下兼容\n    if (!isArray(name))\n        name = name ? [name] : [];\n    name.forEach((n) => {\n        eventBus.off(n, callback);\n    });\n}, OffProtocol);\nconst $emit = defineSyncApi(API_EMIT, (name, ...args) => {\n    eventBus.emit(name, ...args);\n}, EmitProtocol);\n\nlet cid;\nlet cidErrMsg;\nlet enabled;\nfunction normalizePushMessage(message) {\n    try {\n        return JSON.parse(message);\n    }\n    catch (e) { }\n    return message;\n}\n/**\n * @private\n * @param args\n */\nfunction invokePushCallback(args) {\n    if (args.type === 'enabled') {\n        enabled = true;\n    }\n    else if (args.type === 'clientId') {\n        cid = args.cid;\n        cidErrMsg = args.errMsg;\n        invokeGetPushCidCallbacks(cid, args.errMsg);\n    }\n    else if (args.type === 'pushMsg') {\n        const message = {\n            type: 'receive',\n            data: normalizePushMessage(args.message),\n        };\n        for (let i = 0; i < onPushMessageCallbacks.length; i++) {\n            const callback = onPushMessageCallbacks[i];\n            callback(message);\n            // 该消息已被阻止\n            if (message.stopped) {\n                break;\n            }\n        }\n    }\n    else if (args.type === 'click') {\n        onPushMessageCallbacks.forEach((callback) => {\n            callback({\n                type: 'click',\n                data: normalizePushMessage(args.message),\n            });\n        });\n    }\n}\nconst getPushCidCallbacks = [];\nfunction invokeGetPushCidCallbacks(cid, errMsg) {\n    getPushCidCallbacks.forEach((callback) => {\n        callback(cid, errMsg);\n    });\n    getPushCidCallbacks.length = 0;\n}\nconst API_GET_PUSH_CLIENT_ID = 'getPushClientId';\nconst getPushClientId = defineAsyncApi(API_GET_PUSH_CLIENT_ID, (_, { resolve, reject }) => {\n    Promise.resolve().then(() => {\n        if (typeof enabled === 'undefined') {\n            enabled = false;\n            cid = '';\n            cidErrMsg = 'uniPush is not enabled';\n        }\n        getPushCidCallbacks.push((cid, errMsg) => {\n            if (cid) {\n                resolve({ cid });\n            }\n            else {\n                reject(errMsg);\n            }\n        });\n        if (typeof cid !== 'undefined') {\n            invokeGetPushCidCallbacks(cid, cidErrMsg);\n        }\n    });\n});\nconst onPushMessageCallbacks = [];\n// 不使用 defineOnApi 实现，是因为 defineOnApi 依赖 UniServiceJSBridge ，该对象目前在小程序上未提供，故简单实现\nconst onPushMessage = (fn) => {\n    if (onPushMessageCallbacks.indexOf(fn) === -1) {\n        onPushMessageCallbacks.push(fn);\n    }\n};\nconst offPushMessage = (fn) => {\n    if (!fn) {\n        onPushMessageCallbacks.length = 0;\n    }\n    else {\n        const index = onPushMessageCallbacks.indexOf(fn);\n        if (index > -1) {\n            onPushMessageCallbacks.splice(index, 1);\n        }\n    }\n};\n\nconst SYNC_API_RE = /^\\$|__f__|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|rpx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/;\nconst CONTEXT_API_RE = /^create|Manager$/;\n// Context例外情况\nconst CONTEXT_API_RE_EXC = ['createBLEConnection'];\nconst TASK_APIS = ['request', 'downloadFile', 'uploadFile', 'connectSocket'];\n// 同步例外情况\nconst ASYNC_API = ['createBLEConnection'];\nconst CALLBACK_API_RE = /^on|^off/;\nfunction isContextApi(name) {\n    return CONTEXT_API_RE.test(name) && CONTEXT_API_RE_EXC.indexOf(name) === -1;\n}\nfunction isSyncApi(name) {\n    return SYNC_API_RE.test(name) && ASYNC_API.indexOf(name) === -1;\n}\nfunction isCallbackApi(name) {\n    return CALLBACK_API_RE.test(name) && name !== 'onPush';\n}\nfunction isTaskApi(name) {\n    return TASK_APIS.indexOf(name) !== -1;\n}\nfunction shouldPromise(name) {\n    if (isContextApi(name) || isSyncApi(name) || isCallbackApi(name)) {\n        return false;\n    }\n    return true;\n}\n/* eslint-disable no-extend-native */\nif (!Promise.prototype.finally) {\n    Promise.prototype.finally = function (onfinally) {\n        const promise = this.constructor;\n        return this.then((value) => promise.resolve(onfinally && onfinally()).then(() => value), (reason) => promise.resolve(onfinally && onfinally()).then(() => {\n            throw reason;\n        }));\n    };\n}\nfunction promisify(name, api) {\n    if (!shouldPromise(name)) {\n        return api;\n    }\n    if (!isFunction(api)) {\n        return api;\n    }\n    return function promiseApi(options = {}, ...rest) {\n        if (isFunction(options.success) ||\n            isFunction(options.fail) ||\n            isFunction(options.complete)) {\n            return wrapperReturnValue(name, invokeApi(name, api, options, rest));\n        }\n        return wrapperReturnValue(name, handlePromise(new Promise((resolve, reject) => {\n            invokeApi(name, api, extend({}, options, {\n                success: resolve,\n                fail: reject,\n            }), rest);\n        })));\n    };\n}\n\nconst CALLBACKS = ['success', 'fail', 'cancel', 'complete'];\nfunction initWrapper(protocols) {\n    function processCallback(methodName, method, returnValue) {\n        return function (res) {\n            return method(processReturnValue(methodName, res, returnValue));\n        };\n    }\n    function processArgs(methodName, fromArgs, argsOption = {}, returnValue = {}, keepFromArgs = false) {\n        if (isPlainObject(fromArgs)) {\n            // 一般 api 的参数解析\n            const toArgs = (keepFromArgs === true ? fromArgs : {}); // returnValue 为 false 时，说明是格式化返回值，直接在返回值对象上修改赋值\n            if (isFunction(argsOption)) {\n                argsOption = argsOption(fromArgs, toArgs) || {};\n            }\n            for (const key in fromArgs) {\n                if (hasOwn(argsOption, key)) {\n                    let keyOption = argsOption[key];\n                    if (isFunction(keyOption)) {\n                        keyOption = keyOption(fromArgs[key], fromArgs, toArgs);\n                    }\n                    if (!keyOption) {\n                        // 不支持的参数\n                        console.warn(`微信小程序 ${methodName} 暂不支持 ${key}`);\n                    }\n                    else if (isString(keyOption)) {\n                        // 重写参数 key\n                        toArgs[keyOption] = fromArgs[key];\n                    }\n                    else if (isPlainObject(keyOption)) {\n                        // {name:newName,value:value}可重新指定参数 key:value\n                        toArgs[keyOption.name ? keyOption.name : key] = keyOption.value;\n                    }\n                }\n                else if (CALLBACKS.indexOf(key) !== -1) {\n                    const callback = fromArgs[key];\n                    if (isFunction(callback)) {\n                        toArgs[key] = processCallback(methodName, callback, returnValue);\n                    }\n                }\n                else {\n                    if (!keepFromArgs && !hasOwn(toArgs, key)) {\n                        toArgs[key] = fromArgs[key];\n                    }\n                }\n            }\n            return toArgs;\n        }\n        else if (isFunction(fromArgs)) {\n            if (isFunction(argsOption)) {\n                argsOption(fromArgs, {});\n            }\n            fromArgs = processCallback(methodName, fromArgs, returnValue);\n        }\n        return fromArgs;\n    }\n    function processReturnValue(methodName, res, returnValue, keepReturnValue = false) {\n        if (isFunction(protocols.returnValue)) {\n            // 处理通用 returnValue\n            res = protocols.returnValue(methodName, res);\n        }\n        const realKeepReturnValue = keepReturnValue || (false);\n        return processArgs(methodName, res, returnValue, {}, realKeepReturnValue);\n    }\n    return function wrapper(methodName, method) {\n        /**\n         * 注意：\n         * - 此处method为原始全局对象上的uni方法名对应的属性值，比如method值可能为my.login，即undefined\n         * - uni.env并非方法，但是也会被传入wrapper\n         * - 开发者自定义的方法属性也会进入此方法，此时method为undefined，应返回undefined\n         */\n        const hasProtocol = hasOwn(protocols, methodName);\n        if (!hasProtocol && typeof wx[methodName] !== 'function') {\n            return method;\n        }\n        const needWrapper = hasProtocol ||\n            isFunction(protocols.returnValue) ||\n            isContextApi(methodName) ||\n            isTaskApi(methodName);\n        const hasMethod = hasProtocol || isFunction(method);\n        if (!hasProtocol && !method) {\n            // 暂不支持的 api\n            return function () {\n                console.error(`微信小程序 暂不支持${methodName}`);\n            };\n        }\n        if (!needWrapper || !hasMethod) {\n            return method;\n        }\n        const protocol = protocols[methodName];\n        return function (arg1, arg2) {\n            // 目前 api 最多两个参数\n            let options = protocol || {};\n            if (isFunction(protocol)) {\n                options = protocol(arg1);\n            }\n            arg1 = processArgs(methodName, arg1, options.args, options.returnValue);\n            const args = [arg1];\n            if (typeof arg2 !== 'undefined') {\n                args.push(arg2);\n            }\n            const returnValue = wx[options.name || methodName].apply(wx, args);\n            if (isContextApi(methodName) || isTaskApi(methodName)) {\n                if (returnValue && !returnValue.__v_skip) {\n                    returnValue.__v_skip = true;\n                }\n            }\n            if (isSyncApi(methodName)) {\n                // 同步 api\n                return processReturnValue(methodName, returnValue, options.returnValue, isContextApi(methodName));\n            }\n            return returnValue;\n        };\n    };\n}\n\nconst getLocale = () => {\n    // 优先使用 $locale\n    const app = isFunction(getApp) && getApp({ allowDefault: true });\n    if (app && app.$vm) {\n        return app.$vm.$locale;\n    }\n    return getLocaleLanguage();\n};\nconst setLocale = (locale) => {\n    const app = isFunction(getApp) && getApp();\n    if (!app) {\n        return false;\n    }\n    const oldLocale = app.$vm.$locale;\n    if (oldLocale !== locale) {\n        app.$vm.$locale = locale;\n        onLocaleChangeCallbacks.forEach((fn) => fn({ locale }));\n        return true;\n    }\n    return false;\n};\nconst onLocaleChangeCallbacks = [];\nconst onLocaleChange = (fn) => {\n    if (onLocaleChangeCallbacks.indexOf(fn) === -1) {\n        onLocaleChangeCallbacks.push(fn);\n    }\n};\nif (typeof global !== 'undefined') {\n    global.getLocale = getLocale;\n}\n\nconst UUID_KEY = '__DC_STAT_UUID';\nlet deviceId;\nfunction useDeviceId(global = wx) {\n    return function addDeviceId(_, toRes) {\n        deviceId = deviceId || global.getStorageSync(UUID_KEY);\n        if (!deviceId) {\n            deviceId = Date.now() + '' + Math.floor(Math.random() * 1e7);\n            wx.setStorage({\n                key: UUID_KEY,\n                data: deviceId,\n            });\n        }\n        toRes.deviceId = deviceId;\n    };\n}\nfunction addSafeAreaInsets(fromRes, toRes) {\n    if (fromRes.safeArea) {\n        const safeArea = fromRes.safeArea;\n        toRes.safeAreaInsets = {\n            top: safeArea.top,\n            left: safeArea.left,\n            right: fromRes.windowWidth - safeArea.right,\n            bottom: fromRes.screenHeight - safeArea.bottom,\n        };\n    }\n}\nfunction getOSInfo(system, platform) {\n    let osName = '';\n    let osVersion = '';\n    if (platform &&\n        (\"mp-weixin\" === 'mp-baidu')) {\n        osName = platform;\n        osVersion = system;\n    }\n    else {\n        osName = system.split(' ')[0] || '';\n        osVersion = system.split(' ')[1] || '';\n    }\n    return {\n        osName: osName.toLocaleLowerCase(),\n        osVersion,\n    };\n}\nfunction populateParameters(fromRes, toRes) {\n    const { brand = '', model = '', system = '', language = '', theme, version, platform, fontSizeSetting, SDKVersion, pixelRatio, deviceOrientation, } = fromRes;\n    // const isQuickApp = \"mp-weixin\".indexOf('quickapp-webview') !== -1\n    // osName osVersion\n    const { osName, osVersion } = getOSInfo(system, platform);\n    let hostVersion = version;\n    // deviceType\n    let deviceType = getGetDeviceType(fromRes, model);\n    // deviceModel\n    let deviceBrand = getDeviceBrand(brand);\n    // hostName\n    let _hostName = getHostName(fromRes);\n    // deviceOrientation\n    let _deviceOrientation = deviceOrientation; // 仅 微信 百度 支持\n    // devicePixelRatio\n    let _devicePixelRatio = pixelRatio;\n    // SDKVersion\n    let _SDKVersion = SDKVersion;\n    // hostLanguage\n    const hostLanguage = (language || '').replace(/_/g, '-');\n    // wx.getAccountInfoSync\n    const parameters = {\n        appId: process.env.UNI_APP_ID,\n        appName: process.env.UNI_APP_NAME,\n        appVersion: process.env.UNI_APP_VERSION_NAME,\n        appVersionCode: process.env.UNI_APP_VERSION_CODE,\n        appLanguage: getAppLanguage(hostLanguage),\n        uniCompileVersion: process.env.UNI_COMPILER_VERSION,\n        uniCompilerVersion: process.env.UNI_COMPILER_VERSION,\n        uniRuntimeVersion: process.env.UNI_COMPILER_VERSION,\n        uniPlatform: process.env.UNI_SUB_PLATFORM || process.env.UNI_PLATFORM,\n        deviceBrand,\n        deviceModel: model,\n        deviceType,\n        devicePixelRatio: _devicePixelRatio,\n        deviceOrientation: _deviceOrientation,\n        osName,\n        osVersion,\n        hostTheme: theme,\n        hostVersion,\n        hostLanguage,\n        hostName: _hostName,\n        hostSDKVersion: _SDKVersion,\n        hostFontSizeSetting: fontSizeSetting,\n        windowTop: 0,\n        windowBottom: 0,\n        // TODO\n        osLanguage: undefined,\n        osTheme: undefined,\n        ua: undefined,\n        hostPackageName: undefined,\n        browserName: undefined,\n        browserVersion: undefined,\n        isUniAppX: false,\n    };\n    extend(toRes, parameters);\n}\nfunction getGetDeviceType(fromRes, model) {\n    // deviceType\n    let deviceType = fromRes.deviceType || 'phone';\n    {\n        const deviceTypeMaps = {\n            ipad: 'pad',\n            windows: 'pc',\n            mac: 'pc',\n        };\n        const deviceTypeMapsKeys = Object.keys(deviceTypeMaps);\n        const _model = model.toLocaleLowerCase();\n        for (let index = 0; index < deviceTypeMapsKeys.length; index++) {\n            const _m = deviceTypeMapsKeys[index];\n            if (_model.indexOf(_m) !== -1) {\n                deviceType = deviceTypeMaps[_m];\n                break;\n            }\n        }\n    }\n    return deviceType;\n}\nfunction getDeviceBrand(brand) {\n    // deviceModel\n    let deviceBrand = brand;\n    if (deviceBrand) {\n        deviceBrand = deviceBrand.toLocaleLowerCase();\n    }\n    return deviceBrand;\n}\nfunction getAppLanguage(defaultLanguage) {\n    return getLocale ? getLocale() : defaultLanguage;\n}\nfunction getHostName(fromRes) {\n    const _platform = 'WeChat'\n        ;\n    let _hostName = fromRes.hostName || _platform; // mp-jd\n    {\n        if (fromRes.environment) {\n            _hostName = fromRes.environment;\n        }\n        else if (fromRes.host && fromRes.host.env) {\n            _hostName = fromRes.host.env;\n        }\n    }\n    return _hostName;\n}\n\nconst getSystemInfo = {\n    returnValue: (fromRes, toRes) => {\n        addSafeAreaInsets(fromRes, toRes);\n        useDeviceId()(fromRes, toRes);\n        populateParameters(fromRes, toRes);\n    },\n};\n\nconst getSystemInfoSync = getSystemInfo;\n\nconst redirectTo = {};\n\nconst previewImage = {\n    args(fromArgs, toArgs) {\n        let currentIndex = parseInt(fromArgs.current);\n        if (isNaN(currentIndex)) {\n            return;\n        }\n        const urls = fromArgs.urls;\n        if (!isArray(urls)) {\n            return;\n        }\n        const len = urls.length;\n        if (!len) {\n            return;\n        }\n        if (currentIndex < 0) {\n            currentIndex = 0;\n        }\n        else if (currentIndex >= len) {\n            currentIndex = len - 1;\n        }\n        if (currentIndex > 0) {\n            toArgs.current = urls[currentIndex];\n            toArgs.urls = urls.filter((item, index) => index < currentIndex ? item !== urls[currentIndex] : true);\n        }\n        else {\n            toArgs.current = urls[0];\n        }\n        return {\n            indicator: false,\n            loop: false,\n        };\n    },\n};\n\nconst showActionSheet = {\n    args(fromArgs, toArgs) {\n        toArgs.alertText = fromArgs.title;\n    },\n};\n\nconst getDeviceInfo = {\n    returnValue: (fromRes, toRes) => {\n        const { brand, model, system = '', platform = '' } = fromRes;\n        let deviceType = getGetDeviceType(fromRes, model);\n        let deviceBrand = getDeviceBrand(brand);\n        useDeviceId()(fromRes, toRes);\n        const { osName, osVersion } = getOSInfo(system, platform);\n        toRes = sortObject(extend(toRes, {\n            deviceType,\n            deviceBrand,\n            deviceModel: model,\n            osName,\n            osVersion,\n        }));\n    },\n};\n\nconst getAppBaseInfo = {\n    returnValue: (fromRes, toRes) => {\n        const { version, language, SDKVersion, theme } = fromRes;\n        let _hostName = getHostName(fromRes);\n        let hostLanguage = (language || '').replace(/_/g, '-');\n        const parameters = {\n            hostVersion: version,\n            hostLanguage,\n            hostName: _hostName,\n            hostSDKVersion: SDKVersion,\n            hostTheme: theme,\n            appId: process.env.UNI_APP_ID,\n            appName: process.env.UNI_APP_NAME,\n            appVersion: process.env.UNI_APP_VERSION_NAME,\n            appVersionCode: process.env.UNI_APP_VERSION_CODE,\n            appLanguage: getAppLanguage(hostLanguage),\n            isUniAppX: false,\n            uniPlatform: process.env.UNI_SUB_PLATFORM || process.env.UNI_PLATFORM,\n            uniCompileVersion: process.env.UNI_COMPILER_VERSION,\n            uniCompilerVersion: process.env.UNI_COMPILER_VERSION,\n            uniRuntimeVersion: process.env.UNI_COMPILER_VERSION,\n        };\n        extend(toRes, parameters);\n    },\n};\n\nconst getWindowInfo = {\n    returnValue: (fromRes, toRes) => {\n        addSafeAreaInsets(fromRes, toRes);\n        toRes = sortObject(extend(toRes, {\n            windowTop: 0,\n            windowBottom: 0,\n        }));\n    },\n};\n\nconst getAppAuthorizeSetting = {\n    returnValue: function (fromRes, toRes) {\n        const { locationReducedAccuracy } = fromRes;\n        toRes.locationAccuracy = 'unsupported';\n        if (locationReducedAccuracy === true) {\n            toRes.locationAccuracy = 'reduced';\n        }\n        else if (locationReducedAccuracy === false) {\n            toRes.locationAccuracy = 'full';\n        }\n    },\n};\n\nconst onError = {\n    args(fromArgs) {\n        const app = getApp({ allowDefault: true }) || {};\n        if (!app.$vm) {\n            if (!wx.$onErrorHandlers) {\n                wx.$onErrorHandlers = [];\n            }\n            wx.$onErrorHandlers.push(fromArgs);\n        }\n        else {\n            injectHook(ON_ERROR, fromArgs, app.$vm.$);\n        }\n    },\n};\nconst offError = {\n    args(fromArgs) {\n        const app = getApp({ allowDefault: true }) || {};\n        if (!app.$vm) {\n            if (!wx.$onErrorHandlers) {\n                return;\n            }\n            const index = wx.$onErrorHandlers.findIndex((fn) => fn === fromArgs);\n            if (index !== -1) {\n                wx.$onErrorHandlers.splice(index, 1);\n            }\n        }\n        else if (fromArgs.__weh) {\n            const onErrors = app.$vm.$[ON_ERROR];\n            if (onErrors) {\n                const index = onErrors.indexOf(fromArgs.__weh);\n                if (index > -1) {\n                    onErrors.splice(index, 1);\n                }\n            }\n        }\n    },\n};\n\nconst onSocketOpen = {\n    args() {\n        if (wx.__uni_console__) {\n            if (wx.__uni_console_warned__) {\n                return;\n            }\n            wx.__uni_console_warned__ = true;\n            console.warn(`开发模式下小程序日志回显会使用 socket 连接，为了避免冲突，建议使用 SocketTask 的方式去管理 WebSocket 或手动关闭日志回显功能。[详情](https://uniapp.dcloud.net.cn/tutorial/run/mp-log.html)`);\n        }\n    },\n};\nconst onSocketMessage = onSocketOpen;\n\nconst baseApis = {\n    $on,\n    $off,\n    $once,\n    $emit,\n    upx2px,\n    rpx2px: upx2px,\n    interceptors,\n    addInterceptor,\n    removeInterceptor,\n    onCreateVueApp,\n    invokeCreateVueAppHook,\n    getLocale,\n    setLocale,\n    onLocaleChange,\n    getPushClientId,\n    onPushMessage,\n    offPushMessage,\n    invokePushCallback,\n    __f__,\n};\nfunction initUni(api, protocols, platform = wx) {\n    const wrapper = initWrapper(protocols);\n    const UniProxyHandlers = {\n        get(target, key) {\n            if (hasOwn(target, key)) {\n                return target[key];\n            }\n            if (hasOwn(api, key)) {\n                return promisify(key, api[key]);\n            }\n            if (hasOwn(baseApis, key)) {\n                return promisify(key, baseApis[key]);\n            }\n            // event-api\n            // provider-api?\n            return promisify(key, wrapper(key, platform[key]));\n        },\n    };\n    return new Proxy({}, UniProxyHandlers);\n}\n\nfunction initGetProvider(providers) {\n    return function getProvider({ service, success, fail, complete, }) {\n        let res;\n        if (providers[service]) {\n            res = {\n                errMsg: 'getProvider:ok',\n                service,\n                provider: providers[service],\n            };\n            isFunction(success) && success(res);\n        }\n        else {\n            res = {\n                errMsg: 'getProvider:fail:服务[' + service + ']不存在',\n            };\n            isFunction(fail) && fail(res);\n        }\n        isFunction(complete) && complete(res);\n    };\n}\n\nconst objectKeys = [\n    'qy',\n    'env',\n    'error',\n    'version',\n    'lanDebug',\n    'cloud',\n    'serviceMarket',\n    'router',\n    'worklet',\n    '__webpack_require_UNI_MP_PLUGIN__',\n];\nconst singlePageDisableKey = ['lanDebug', 'router', 'worklet'];\nconst launchOption = wx.getLaunchOptionsSync\n    ? wx.getLaunchOptionsSync()\n    : null;\nfunction isWxKey(key) {\n    if (launchOption &&\n        launchOption.scene === 1154 &&\n        singlePageDisableKey.includes(key)) {\n        return false;\n    }\n    return objectKeys.indexOf(key) > -1 || typeof wx[key] === 'function';\n}\nfunction initWx() {\n    const newWx = {};\n    for (const key in wx) {\n        if (isWxKey(key)) {\n            // TODO wrapper function\n            newWx[key] = wx[key];\n        }\n    }\n    if (typeof globalThis !== 'undefined' &&\n        typeof requireMiniProgram === 'undefined') {\n        globalThis.wx = newWx;\n    }\n    return newWx;\n}\n\nconst mocks = ['__route__', '__wxExparserNodeId__', '__wxWebviewId__'];\n\nconst getProvider = initGetProvider({\n    oauth: ['weixin'],\n    share: ['weixin'],\n    payment: ['wxpay'],\n    push: ['weixin'],\n});\nfunction initComponentMocks(component) {\n    const res = Object.create(null);\n    mocks.forEach((name) => {\n        res[name] = component[name];\n    });\n    return res;\n}\n/**\n * 微信小程序内部会 Object.keys(vm)，导致告警\n * Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead.\n * @returns\n */\nfunction createSelectorQuery() {\n    const query = wx$2.createSelectorQuery();\n    const oldIn = query.in;\n    query.in = function newIn(component) {\n        if (component.$scope) {\n            // fix skyline 微信小程序内部无法读取component导致报错\n            return oldIn.call(this, component.$scope);\n        }\n        return oldIn.call(this, initComponentMocks(component));\n    };\n    return query;\n}\nconst wx$2 = initWx();\nif (!wx$2.canIUse('getAppBaseInfo')) {\n    wx$2.getAppBaseInfo = wx$2.getSystemInfoSync;\n}\nif (!wx$2.canIUse('getWindowInfo')) {\n    wx$2.getWindowInfo = wx$2.getSystemInfoSync;\n}\nif (!wx$2.canIUse('getDeviceInfo')) {\n    wx$2.getDeviceInfo = wx$2.getSystemInfoSync;\n}\nlet baseInfo = wx$2.getAppBaseInfo && wx$2.getAppBaseInfo();\nif (!baseInfo) {\n    baseInfo = wx$2.getSystemInfoSync();\n}\nconst host = baseInfo ? baseInfo.host : null;\nconst shareVideoMessage = host && host.env === 'SAAASDK'\n    ? wx$2.miniapp.shareVideoMessage\n    : wx$2.shareVideoMessage;\n\nvar shims = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  createSelectorQuery: createSelectorQuery,\n  getProvider: getProvider,\n  shareVideoMessage: shareVideoMessage\n});\n\nconst compressImage = {\n    args(fromArgs, toArgs) {\n        // https://developers.weixin.qq.com/community/develop/doc/000c08940c865011298e0a43256800?highLine=compressHeight\n        // @ts-expect-error\n        if (fromArgs.compressedHeight && !toArgs.compressHeight) {\n            // @ts-expect-error\n            toArgs.compressHeight = fromArgs.compressedHeight;\n        }\n        // @ts-expect-error\n        if (fromArgs.compressedWidth && !toArgs.compressWidth) {\n            // @ts-expect-error\n            toArgs.compressWidth = fromArgs.compressedWidth;\n        }\n    },\n};\n\nvar protocols = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  compressImage: compressImage,\n  getAppAuthorizeSetting: getAppAuthorizeSetting,\n  getAppBaseInfo: getAppBaseInfo,\n  getDeviceInfo: getDeviceInfo,\n  getSystemInfo: getSystemInfo,\n  getSystemInfoSync: getSystemInfoSync,\n  getWindowInfo: getWindowInfo,\n  offError: offError,\n  onError: onError,\n  onSocketMessage: onSocketMessage,\n  onSocketOpen: onSocketOpen,\n  previewImage: previewImage,\n  redirectTo: redirectTo,\n  showActionSheet: showActionSheet\n});\n\nconst wx$1 = initWx();\nvar index = initUni(shims, protocols, wx$1);\n\nexport { index as default, wx$1 as wx };\n", "/// <reference types=\"@dcloudio/uni-app-x/types/uni/global\" />\nfunction initRuntimeSocket(hosts, port, id) {\n    if (hosts == '' || port == '' || id == '')\n        return Promise.resolve(null);\n    return hosts\n        .split(',')\n        .reduce((promise, host) => {\n        return promise.then((socket) => {\n            if (socket != null)\n                return Promise.resolve(socket);\n            return tryConnectSocket(host, port, id);\n        });\n    }, Promise.resolve(null));\n}\nconst SOCKET_TIMEOUT = 500;\nfunction tryConnectSocket(host, port, id) {\n    return new Promise((resolve, reject) => {\n        const socket = uni.connectSocket({\n            url: `ws://${host}:${port}/${id}`,\n            multiple: true, // 支付宝小程序 是否开启多实例\n            fail() {\n                resolve(null);\n            },\n        });\n        const timer = setTimeout(() => {\n            // @ts-expect-error\n            socket.close({\n                code: 1006,\n                reason: 'connect timeout',\n            });\n            resolve(null);\n        }, SOCKET_TIMEOUT);\n        socket.onOpen((e) => {\n            clearTimeout(timer);\n            resolve(socket);\n        });\n        socket.onClose((e) => {\n            clearTimeout(timer);\n            resolve(null);\n        });\n        socket.onError((e) => {\n            clearTimeout(timer);\n            resolve(null);\n        });\n    });\n}\n\nfunction formatMessage(type, args) {\n    try {\n        return {\n            type,\n            args: formatArgs(args),\n        };\n    }\n    catch (e) {\n        // originalConsole.error(e)\n    }\n    return {\n        type,\n        args: [],\n    };\n}\nfunction formatArgs(args) {\n    return args.map((arg) => formatArg(arg));\n}\nfunction formatArg(arg, depth = 0) {\n    if (depth >= 7) {\n        return {\n            type: 'object',\n            value: '[Maximum depth reached]',\n        };\n    }\n    const type = typeof arg;\n    switch (type) {\n        case 'string':\n            return formatString(arg);\n        case 'number':\n            return formatNumber(arg);\n        case 'boolean':\n            return formatBoolean(arg);\n        case 'object':\n            return formatObject(arg, depth);\n        case 'undefined':\n            return formatUndefined();\n        case 'function':\n            return formatFunction(arg);\n        case 'symbol':\n            {\n                return formatSymbol(arg);\n            }\n        case 'bigint':\n            return formatBigInt(arg);\n    }\n}\nfunction formatFunction(value) {\n    return {\n        type: 'function',\n        value: `function ${value.name}() {}`,\n    };\n}\nfunction formatUndefined() {\n    return {\n        type: 'undefined',\n    };\n}\nfunction formatBoolean(value) {\n    return {\n        type: 'boolean',\n        value: String(value),\n    };\n}\nfunction formatNumber(value) {\n    return {\n        type: 'number',\n        value: String(value),\n    };\n}\nfunction formatBigInt(value) {\n    return {\n        type: 'bigint',\n        value: String(value),\n    };\n}\nfunction formatString(value) {\n    return {\n        type: 'string',\n        value,\n    };\n}\nfunction formatSymbol(value) {\n    return {\n        type: 'symbol',\n        value: value.description,\n    };\n}\nfunction formatObject(value, depth) {\n    if (value === null) {\n        return {\n            type: 'null',\n        };\n    }\n    {\n        if (isComponentPublicInstance(value)) {\n            return formatComponentPublicInstance(value, depth);\n        }\n        if (isComponentInternalInstance(value)) {\n            return formatComponentInternalInstance(value, depth);\n        }\n        if (isUniElement(value)) {\n            return formatUniElement(value, depth);\n        }\n        if (isCSSStyleDeclaration(value)) {\n            return formatCSSStyleDeclaration(value, depth);\n        }\n    }\n    if (Array.isArray(value)) {\n        return {\n            type: 'object',\n            subType: 'array',\n            value: {\n                properties: value.map((v, i) => formatArrayElement(v, i, depth + 1)),\n            },\n        };\n    }\n    if (value instanceof Set) {\n        return {\n            type: 'object',\n            subType: 'set',\n            className: 'Set',\n            description: `Set(${value.size})`,\n            value: {\n                entries: Array.from(value).map((v) => formatSetEntry(v, depth + 1)),\n            },\n        };\n    }\n    if (value instanceof Map) {\n        return {\n            type: 'object',\n            subType: 'map',\n            className: 'Map',\n            description: `Map(${value.size})`,\n            value: {\n                entries: Array.from(value.entries()).map((v) => formatMapEntry(v, depth + 1)),\n            },\n        };\n    }\n    if (value instanceof Promise) {\n        return {\n            type: 'object',\n            subType: 'promise',\n            value: {\n                properties: [],\n            },\n        };\n    }\n    if (value instanceof RegExp) {\n        return {\n            type: 'object',\n            subType: 'regexp',\n            value: String(value),\n            className: 'Regexp',\n        };\n    }\n    if (value instanceof Date) {\n        return {\n            type: 'object',\n            subType: 'date',\n            value: String(value),\n            className: 'Date',\n        };\n    }\n    if (value instanceof Error) {\n        return {\n            type: 'object',\n            subType: 'error',\n            value: value.message || String(value),\n            className: value.name || 'Error',\n        };\n    }\n    let className = undefined;\n    {\n        const constructor = value.constructor;\n        if (constructor) {\n            // @ts-expect-error\n            if (constructor.get$UTSMetadata$) {\n                // @ts-expect-error\n                className = constructor.get$UTSMetadata$().name;\n            }\n        }\n    }\n    return {\n        type: 'object',\n        className,\n        value: {\n            properties: Object.entries(value).map((entry) => formatObjectProperty(entry[0], entry[1], depth + 1)),\n        },\n    };\n}\nfunction isComponentPublicInstance(value) {\n    return value.$ && isComponentInternalInstance(value.$);\n}\nfunction isComponentInternalInstance(value) {\n    return value.type && value.uid != null && value.appContext;\n}\nfunction formatComponentPublicInstance(value, depth) {\n    return {\n        type: 'object',\n        className: 'ComponentPublicInstance',\n        value: {\n            properties: Object.entries(value.$.type).map(([name, value]) => formatObjectProperty(name, value, depth + 1)),\n        },\n    };\n}\nfunction formatComponentInternalInstance(value, depth) {\n    return {\n        type: 'object',\n        className: 'ComponentInternalInstance',\n        value: {\n            properties: Object.entries(value.type).map(([name, value]) => formatObjectProperty(name, value, depth + 1)),\n        },\n    };\n}\nfunction isUniElement(value) {\n    return value.style && value.tagName != null && value.nodeName != null;\n}\nfunction formatUniElement(value, depth) {\n    return {\n        type: 'object',\n        // 非 x 没有 UniElement 的概念\n        // className: 'UniElement',\n        value: {\n            properties: Object.entries(value)\n                .filter(([name]) => [\n                'id',\n                'tagName',\n                'nodeName',\n                'dataset',\n                'offsetTop',\n                'offsetLeft',\n                'style',\n            ].includes(name))\n                .map(([name, value]) => formatObjectProperty(name, value, depth + 1)),\n        },\n    };\n}\nfunction isCSSStyleDeclaration(value) {\n    return (typeof value.getPropertyValue === 'function' &&\n        typeof value.setProperty === 'function' &&\n        value.$styles);\n}\nfunction formatCSSStyleDeclaration(style, depth) {\n    return {\n        type: 'object',\n        value: {\n            properties: Object.entries(style.$styles).map(([name, value]) => formatObjectProperty(name, value, depth + 1)),\n        },\n    };\n}\nfunction formatObjectProperty(name, value, depth) {\n    const result = formatArg(value, depth);\n    result.name = name;\n    return result;\n}\nfunction formatArrayElement(value, index, depth) {\n    const result = formatArg(value, depth);\n    result.name = `${index}`;\n    return result;\n}\nfunction formatSetEntry(value, depth) {\n    return {\n        value: formatArg(value, depth),\n    };\n}\nfunction formatMapEntry(value, depth) {\n    return {\n        key: formatArg(value[0], depth),\n        value: formatArg(value[1], depth),\n    };\n}\n\nconst CONSOLE_TYPES = ['log', 'warn', 'error', 'info', 'debug'];\nlet sendConsole = null;\nconst messageQueue = [];\nconst messageExtra = {};\nfunction sendConsoleMessages(messages) {\n    if (sendConsole == null) {\n        messageQueue.push(...messages);\n        return;\n    }\n    sendConsole(JSON.stringify(Object.assign({\n        type: 'console',\n        data: messages,\n    }, messageExtra)));\n}\nfunction setSendConsole(value, extra = {}) {\n    sendConsole = value;\n    Object.assign(messageExtra, extra);\n    if (value != null && messageQueue.length > 0) {\n        const messages = messageQueue.slice();\n        messageQueue.length = 0;\n        sendConsoleMessages(messages);\n    }\n}\nconst originalConsole = /*@__PURE__*/ CONSOLE_TYPES.reduce((methods, type) => {\n    methods[type] = console[type].bind(console);\n    return methods;\n}, {});\nconst atFileRegex = /^\\s*at\\s+[\\w/./-]+:\\d+$/;\nfunction rewriteConsole() {\n    function wrapConsole(type) {\n        return function (...args) {\n            const originalArgs = [...args];\n            if (originalArgs.length) {\n                const maybeAtFile = originalArgs[originalArgs.length - 1];\n                // 移除最后的 at pages/index/index.uvue:6\n                if (typeof maybeAtFile === 'string' && atFileRegex.test(maybeAtFile)) {\n                    originalArgs.pop();\n                }\n            }\n            if (process.env.UNI_CONSOLE_KEEP_ORIGINAL) {\n                originalConsole[type](...originalArgs);\n            }\n            sendConsoleMessages([formatMessage(type, args)]);\n        };\n    }\n    // 百度小程序不允许赋值，所以需要判断是否可写\n    if (isConsoleWritable()) {\n        CONSOLE_TYPES.forEach((type) => {\n            console[type] = wrapConsole(type);\n        });\n        return function restoreConsole() {\n            CONSOLE_TYPES.forEach((type) => {\n                console[type] = originalConsole[type];\n            });\n        };\n    }\n    else {\n        {\n            if (typeof uni !== 'undefined' && uni.__f__) {\n                const oldLog = uni.__f__;\n                if (oldLog) {\n                    // 重写 uni.__f__ 方法，这样的话，仅能打印开发者代码里的日志，其他没有被重写为__f__的日志将无法打印（比如uni-app框架、小程序框架等）\n                    uni.__f__ = function (...args) {\n                        const [type, filename, ...rest] = args;\n                        // 原始日志移除 filename\n                        oldLog(type, '', ...rest);\n                        sendConsoleMessages([formatMessage(type, [...rest, filename])]);\n                    };\n                    return function restoreConsole() {\n                        uni.__f__ = oldLog;\n                    };\n                }\n            }\n        }\n    }\n    return function restoreConsole() { };\n}\nfunction isConsoleWritable() {\n    const value = console.log;\n    const sym = Symbol();\n    try {\n        // @ts-expect-error\n        console.log = sym;\n    }\n    catch (ex) {\n        return false;\n    }\n    // @ts-expect-error\n    const isWritable = console.log === sym;\n    console.log = value;\n    return isWritable;\n}\n\nlet sendError = null;\n// App.onError会监听到两类错误，一类是小程序自身抛出的，一类是 vue 的 errorHandler 触发的\n// uni.onError 和 App.onError 会同时监听到错误(主要是App.onError监听之前的错误)，所以需要用 Set 来去重\n// uni.onError 会在 App.onError 上边同时增加监听，因为要监听 vue 的errorHandler\n// 目前 vue 的 errorHandler 仅会callHook('onError')，所以需要把uni.onError的也挂在 App.onError 上\nconst errorQueue = new Set();\nconst errorExtra = {};\nfunction sendErrorMessages(errors) {\n    if (sendError == null) {\n        errors.forEach((error) => {\n            errorQueue.add(error);\n        });\n        return;\n    }\n    const data = errors\n        .map((err) => {\n        const isPromiseRejection = err && 'promise' in err && 'reason' in err;\n        const prefix = isPromiseRejection ? 'UnhandledPromiseRejection: ' : '';\n        if (isPromiseRejection) {\n            err = err.reason;\n        }\n        if (err instanceof Error && err.stack) {\n            if (err.message && !err.stack.includes(err.message)) {\n                return `${prefix}${err.message}\n${err.stack}`;\n            }\n            return `${prefix}${err.stack}`;\n        }\n        if (typeof err === 'object' && err !== null) {\n            try {\n                return prefix + JSON.stringify(err);\n            }\n            catch (err) {\n                return prefix + String(err);\n            }\n        }\n        return prefix + String(err);\n    })\n        .filter(Boolean);\n    if (data.length > 0) {\n        sendError(JSON.stringify(Object.assign({\n            type: 'error',\n            data,\n        }, errorExtra)));\n    }\n}\nfunction setSendError(value, extra = {}) {\n    sendError = value;\n    Object.assign(errorExtra, extra);\n    if (value != null && errorQueue.size > 0) {\n        const errors = Array.from(errorQueue);\n        errorQueue.clear();\n        sendErrorMessages(errors);\n    }\n}\nfunction initOnError() {\n    function onError(error) {\n        try {\n            // 小红书小程序 socket.send 时，会报错，onError错误信息为：\n            // Cannot create property 'errMsg' on string 'taskId'\n            // 导致陷入死循环\n            if (typeof PromiseRejectionEvent !== 'undefined' &&\n                error instanceof PromiseRejectionEvent &&\n                error.reason instanceof Error &&\n                error.reason.message &&\n                error.reason.message.includes(`Cannot create property 'errMsg' on string 'taskId`)) {\n                return;\n            }\n            if (process.env.UNI_CONSOLE_KEEP_ORIGINAL) {\n                originalConsole.error(error);\n            }\n            sendErrorMessages([error]);\n        }\n        catch (err) {\n            originalConsole.error(err);\n        }\n    }\n    if (typeof uni.onError === 'function') {\n        uni.onError(onError);\n    }\n    if (typeof uni.onUnhandledRejection === 'function') {\n        uni.onUnhandledRejection(onError);\n    }\n    return function offError() {\n        if (typeof uni.offError === 'function') {\n            uni.offError(onError);\n        }\n        if (typeof uni.offUnhandledRejection === 'function') {\n            uni.offUnhandledRejection(onError);\n        }\n    };\n}\n\nfunction initRuntimeSocketService() {\n    const hosts = process.env.UNI_SOCKET_HOSTS;\n    const port = process.env.UNI_SOCKET_PORT;\n    const id = process.env.UNI_SOCKET_ID;\n    if (!hosts || !port || !id)\n        return Promise.resolve(false);\n    // 百度小程序需要延迟初始化，不然会存在循环引用问题vendor.js\n    const lazy = typeof swan !== 'undefined';\n    // 重写需要同步，避免丢失早期日志信息\n    let restoreError = lazy ? () => { } : initOnError();\n    let restoreConsole = lazy ? () => { } : rewriteConsole();\n    // 百度小程序需要异步初始化，不然调用 uni.connectSocket 会循环引入vendor.js\n    return Promise.resolve().then(() => {\n        if (lazy) {\n            restoreError = initOnError();\n            restoreConsole = rewriteConsole();\n        }\n        return initRuntimeSocket(hosts, port, id).then((socket) => {\n            if (!socket) {\n                restoreError();\n                restoreConsole();\n                originalConsole.error(wrapError('开发模式下日志通道建立 socket 连接失败。'));\n                originalConsole.error(wrapError('如果是小程序平台，请勾选不校验合法域名配置。'));\n                originalConsole.error(wrapError('如果是运行到真机，请确认手机与电脑处于同一网络。'));\n                return false;\n            }\n            initMiniProgramGlobalFlag();\n            socket.onClose(() => {\n                if (process.env.UNI_DEBUG) {\n                    originalConsole.log(`uni-app:[${Date.now()}][socket]`, 'connect close and restore');\n                }\n                originalConsole.error(wrapError('开发模式下日志通道 socket 连接关闭，请在 HBuilderX 中重新运行。'));\n                restoreError();\n                restoreConsole();\n            });\n            setSendConsole((data) => {\n                if (process.env.UNI_DEBUG) {\n                    originalConsole.log(`uni-app:[${Date.now()}][console]`, data);\n                }\n                socket.send({\n                    data,\n                });\n            });\n            setSendError((data) => {\n                if (process.env.UNI_DEBUG) {\n                    originalConsole.log(`uni-app:[${Date.now()}][error]`, data);\n                }\n                socket.send({\n                    data,\n                });\n            });\n            return true;\n        });\n    });\n}\nconst ERROR_CHAR = '\\u200C';\nfunction wrapError(error) {\n    return `${ERROR_CHAR}${error}${ERROR_CHAR}`;\n}\nfunction initMiniProgramGlobalFlag() {\n    if (typeof wx !== 'undefined') {\n        // @ts-expect-error\n        wx.__uni_console__ = true;\n        // @ts-expect-error\n    }\n    else if (typeof my !== 'undefined') {\n        // @ts-expect-error\n        my.__uni_console__ = true;\n    }\n    else if (typeof tt !== 'undefined') {\n        tt.__uni_console__ = true;\n    }\n    else if (typeof swan !== 'undefined') {\n        swan.__uni_console__ = true;\n    }\n    else if (typeof qq !== 'undefined') {\n        qq.__uni_console__ = true;\n    }\n    else if (typeof ks !== 'undefined') {\n        ks.__uni_console__ = true;\n    }\n    else if (typeof jd !== 'undefined') {\n        jd.__uni_console__ = true;\n    }\n    else if (typeof xhs !== 'undefined') {\n        xhs.__uni_console__ = true;\n    }\n    else if (typeof has !== 'undefined') {\n        has.__uni_console__ = true;\n    }\n    else if (typeof qa !== 'undefined') {\n        qa.__uni_console__ = true;\n    }\n}\ninitRuntimeSocketService();\n\nexport { initRuntimeSocketService };\n", "import { VIRTUAL_HOST_ID, SLOT_DEFAULT_NAME, invokeArrayFns, MINI_PROGRAM_PAGE_RUNTIME_HOOKS, ON_LOAD, ON_SHOW, ON_HIDE, ON_UNLOAD, ON_RESIZE, ON_TAB_ITEM_TAP, ON_REACH_BOTTOM, ON_PULL_DOWN_REFRESH, ON_ADD_TO_FAVORITES, isUniLifecycleHook, ON_READY, once, ON_LAUNCH, ON_ERROR, ON_THEME_CHANGE, ON_PAGE_NOT_FOUND, ON_UNHANDLE_REJECTION, VIRTUAL_HOST_STYLE, VIRTUAL_HOST_CLASS, VIRTUAL_HOST_HIDDEN, addLeadingSlash, stringifyQuery, customizeEvent } from '@dcloudio/uni-shared';\nimport { hasOwn, isArray, isFunction, extend, isPlainObject, isObject } from '@vue/shared';\nimport { injectHook, ref, findComponentPropsData, toRaw, update<PERSON>rops, hasQueue<PERSON><PERSON>, invalidate<PERSON><PERSON>, devto<PERSON>ComponentAdded, getExposeProxy, prune<PERSON>omponent<PERSON><PERSON>Cache } from 'vue';\nimport { LOCALE_EN, normalizeLocale } from '@dcloudio/uni-i18n';\n\nfunction initVueIds(vueIds, mpInstance) {\n    if (!vueIds) {\n        return;\n    }\n    const ids = vueIds.split(',');\n    const len = ids.length;\n    if (len === 1) {\n        mpInstance._$vueId = ids[0];\n    }\n    else if (len === 2) {\n        mpInstance._$vueId = ids[0];\n        mpInstance._$vuePid = ids[1];\n    }\n}\nconst EXTRAS = ['externalClasses'];\nfunction initExtraOptions(miniProgramComponentOptions, vueOptions) {\n    EXTRAS.forEach((name) => {\n        if (hasOwn(vueOptions, name)) {\n            miniProgramComponentOptions[name] = vueOptions[name];\n        }\n    });\n}\nconst WORKLET_RE = /_(.*)_worklet_factory_/;\nfunction initWorkletMethods(mpMethods, vueMethods) {\n    if (vueMethods) {\n        Object.keys(vueMethods).forEach((name) => {\n            const matches = name.match(WORKLET_RE);\n            if (matches) {\n                const workletName = matches[1];\n                mpMethods[name] = vueMethods[name];\n                mpMethods[workletName] = vueMethods[workletName];\n            }\n        });\n    }\n}\nfunction initWxsCallMethods(methods, wxsCallMethods) {\n    if (!isArray(wxsCallMethods)) {\n        return;\n    }\n    wxsCallMethods.forEach((callMethod) => {\n        methods[callMethod] = function (args) {\n            return this.$vm[callMethod](args);\n        };\n    });\n}\nfunction selectAllComponents(mpInstance, selector, $refs) {\n    const components = mpInstance.selectAllComponents(selector);\n    components.forEach((component) => {\n        const ref = component.properties.uR;\n        $refs[ref] = component.$vm || component;\n    });\n}\nfunction initRefs(instance, mpInstance) {\n    Object.defineProperty(instance, 'refs', {\n        get() {\n            const $refs = {};\n            selectAllComponents(mpInstance, '.r', $refs);\n            const forComponents = mpInstance.selectAllComponents('.r-i-f');\n            forComponents.forEach((component) => {\n                const ref = component.properties.uR;\n                if (!ref) {\n                    return;\n                }\n                if (!$refs[ref]) {\n                    $refs[ref] = [];\n                }\n                $refs[ref].push(component.$vm || component);\n            });\n            return $refs;\n        },\n    });\n}\nfunction findVmByVueId(instance, vuePid) {\n    // 标准 vue3 中 没有 $children，定制了内核\n    const $children = instance.$children;\n    // 优先查找直属(反向查找:https://github.com/dcloudio/uni-app/issues/1200)\n    for (let i = $children.length - 1; i >= 0; i--) {\n        const childVm = $children[i];\n        if (childVm.$scope._$vueId === vuePid) {\n            return childVm;\n        }\n    }\n    // 反向递归查找\n    let parentVm;\n    for (let i = $children.length - 1; i >= 0; i--) {\n        parentVm = findVmByVueId($children[i], vuePid);\n        if (parentVm) {\n            return parentVm;\n        }\n    }\n}\nfunction getLocaleLanguage() {\n    let localeLanguage = '';\n    {\n        const appBaseInfo = wx.getAppBaseInfo();\n        const language = appBaseInfo && appBaseInfo.language ? appBaseInfo.language : LOCALE_EN;\n        localeLanguage = normalizeLocale(language) || LOCALE_EN;\n    }\n    return localeLanguage;\n}\n\nconst MP_METHODS = [\n    'createSelectorQuery',\n    'createIntersectionObserver',\n    'selectAllComponents',\n    'selectComponent',\n];\nfunction createEmitFn(oldEmit, ctx) {\n    return function emit(event, ...args) {\n        const scope = ctx.$scope;\n        if (scope && event) {\n            const detail = { __args__: args };\n            {\n                scope.triggerEvent(event, detail);\n            }\n        }\n        return oldEmit.apply(this, [event, ...args]);\n    };\n}\nfunction initBaseInstance(instance, options) {\n    const ctx = instance.ctx;\n    // mp\n    ctx.mpType = options.mpType; // @deprecated\n    ctx.$mpType = options.mpType;\n    ctx.$mpPlatform = \"mp-weixin\";\n    ctx.$scope = options.mpInstance;\n    {\n        // mergeVirtualHostAttributes\n        Object.defineProperties(ctx, {\n            // only id\n            [VIRTUAL_HOST_ID]: {\n                get() {\n                    const id = this.$scope.data[VIRTUAL_HOST_ID];\n                    // props in page can be undefined\n                    return id === undefined ? '' : id;\n                },\n            },\n        });\n    }\n    // TODO @deprecated\n    ctx.$mp = {};\n    if (__VUE_OPTIONS_API__) {\n        ctx._self = {};\n    }\n    // slots\n    instance.slots = {};\n    if (isArray(options.slots) && options.slots.length) {\n        options.slots.forEach((name) => {\n            instance.slots[name] = true;\n        });\n        if (instance.slots[SLOT_DEFAULT_NAME]) {\n            instance.slots.default = true;\n        }\n    }\n    ctx.getOpenerEventChannel = function () {\n        // 微信小程序使用自身getOpenerEventChannel\n        {\n            return options.mpInstance.getOpenerEventChannel();\n        }\n    };\n    ctx.$hasHook = hasHook;\n    ctx.$callHook = callHook;\n    // $emit\n    instance.emit = createEmitFn(instance.emit, ctx);\n}\nfunction initComponentInstance(instance, options) {\n    initBaseInstance(instance, options);\n    const ctx = instance.ctx;\n    MP_METHODS.forEach((method) => {\n        ctx[method] = function (...args) {\n            const mpInstance = ctx.$scope;\n            if (mpInstance && mpInstance[method]) {\n                return mpInstance[method].apply(mpInstance, args);\n            }\n        };\n    });\n}\nfunction initMocks(instance, mpInstance, mocks) {\n    const ctx = instance.ctx;\n    mocks.forEach((mock) => {\n        if (hasOwn(mpInstance, mock)) {\n            instance[mock] = ctx[mock] = mpInstance[mock];\n        }\n    });\n}\nfunction hasHook(name) {\n    const hooks = this.$[name];\n    if (hooks && hooks.length) {\n        return true;\n    }\n    return false;\n}\nfunction callHook(name, args) {\n    if (name === 'mounted') {\n        callHook.call(this, 'bm'); // beforeMount\n        this.$.isMounted = true;\n        name = 'm';\n    }\n    const hooks = this.$[name];\n    return hooks && invokeArrayFns(hooks, args);\n}\n\nconst PAGE_INIT_HOOKS = [\n    ON_LOAD,\n    ON_SHOW,\n    ON_HIDE,\n    ON_UNLOAD,\n    ON_RESIZE,\n    ON_TAB_ITEM_TAP,\n    ON_REACH_BOTTOM,\n    ON_PULL_DOWN_REFRESH,\n    ON_ADD_TO_FAVORITES,\n    // 'onReady', // lifetimes.ready\n    // 'onPageScroll', // 影响性能，开发者手动注册\n    // 'onShareTimeline', // 右上角菜单，开发者手动注册\n    // 'onShareAppMessage' // 右上角菜单，开发者手动注册\n];\nfunction findHooks(vueOptions, hooks = new Set()) {\n    if (vueOptions) {\n        Object.keys(vueOptions).forEach((name) => {\n            if (isUniLifecycleHook(name, vueOptions[name])) {\n                hooks.add(name);\n            }\n        });\n        if (__VUE_OPTIONS_API__) {\n            const { extends: extendsOptions, mixins } = vueOptions;\n            if (mixins) {\n                mixins.forEach((mixin) => findHooks(mixin, hooks));\n            }\n            if (extendsOptions) {\n                findHooks(extendsOptions, hooks);\n            }\n        }\n    }\n    return hooks;\n}\nfunction initHook(mpOptions, hook, excludes) {\n    if (excludes.indexOf(hook) === -1 && !hasOwn(mpOptions, hook)) {\n        mpOptions[hook] = function (args) {\n            return this.$vm && this.$vm.$callHook(hook, args);\n        };\n    }\n}\nconst EXCLUDE_HOOKS = [ON_READY];\nfunction initHooks(mpOptions, hooks, excludes = EXCLUDE_HOOKS) {\n    hooks.forEach((hook) => initHook(mpOptions, hook, excludes));\n}\nfunction initUnknownHooks(mpOptions, vueOptions, excludes = EXCLUDE_HOOKS) {\n    findHooks(vueOptions).forEach((hook) => initHook(mpOptions, hook, excludes));\n}\nfunction initRuntimeHooks(mpOptions, runtimeHooks) {\n    if (!runtimeHooks) {\n        return;\n    }\n    const hooks = Object.keys(MINI_PROGRAM_PAGE_RUNTIME_HOOKS);\n    hooks.forEach((hook) => {\n        if (runtimeHooks & MINI_PROGRAM_PAGE_RUNTIME_HOOKS[hook]) {\n            initHook(mpOptions, hook, []);\n        }\n    });\n}\nconst findMixinRuntimeHooks = /*#__PURE__*/ once(() => {\n    const runtimeHooks = [];\n    const app = isFunction(getApp) && getApp({ allowDefault: true });\n    if (app && app.$vm && app.$vm.$) {\n        const mixins = app.$vm.$.appContext.mixins;\n        if (isArray(mixins)) {\n            const hooks = Object.keys(MINI_PROGRAM_PAGE_RUNTIME_HOOKS);\n            mixins.forEach((mixin) => {\n                hooks.forEach((hook) => {\n                    if (hasOwn(mixin, hook) && !runtimeHooks.includes(hook)) {\n                        runtimeHooks.push(hook);\n                    }\n                });\n            });\n        }\n    }\n    return runtimeHooks;\n});\nfunction initMixinRuntimeHooks(mpOptions) {\n    initHooks(mpOptions, findMixinRuntimeHooks());\n}\n\nconst HOOKS = [\n    ON_SHOW,\n    ON_HIDE,\n    ON_ERROR,\n    ON_THEME_CHANGE,\n    ON_PAGE_NOT_FOUND,\n    ON_UNHANDLE_REJECTION,\n];\nfunction parseApp(instance, parseAppOptions) {\n    const internalInstance = instance.$;\n    if (__VUE_PROD_DEVTOOLS__) {\n        // 定制 App 的 $children\n        Object.defineProperty(internalInstance.ctx, '$children', {\n            get() {\n                return getCurrentPages().map((page) => page.$vm);\n            },\n        });\n    }\n    const appOptions = {\n        globalData: (instance.$options && instance.$options.globalData) || {},\n        $vm: instance, // mp-alipay 组件 data 初始化比 onLaunch 早，提前挂载\n        onLaunch(options) {\n            this.$vm = instance; // 飞书小程序可能会把 AppOptions 序列化，导致 $vm 对象部分属性丢失\n            const ctx = internalInstance.ctx;\n            if (this.$vm && ctx.$scope && ctx.$callHook) {\n                // 已经初始化过了，主要是为了百度，百度 onShow 在 onLaunch 之前\n                // $scope值在微信小程序混合分包情况下存在，额外用$callHook兼容判断处理\n                return;\n            }\n            initBaseInstance(internalInstance, {\n                mpType: 'app',\n                mpInstance: this,\n                slots: [],\n            });\n            ctx.globalData = this.globalData;\n            instance.$callHook(ON_LAUNCH, options);\n        },\n    };\n    const onErrorHandlers = wx.$onErrorHandlers;\n    if (onErrorHandlers) {\n        onErrorHandlers.forEach((fn) => {\n            injectHook(ON_ERROR, fn, internalInstance);\n        });\n        onErrorHandlers.length = 0;\n    }\n    initLocale(instance);\n    const vueOptions = instance.$.type;\n    initHooks(appOptions, HOOKS);\n    initUnknownHooks(appOptions, vueOptions);\n    if (__VUE_OPTIONS_API__) {\n        const methods = vueOptions.methods;\n        methods && extend(appOptions, methods);\n    }\n    return appOptions;\n}\nfunction initCreateApp(parseAppOptions) {\n    return function createApp(vm) {\n        return App(parseApp(vm));\n    };\n}\nfunction initCreateSubpackageApp(parseAppOptions) {\n    return function createApp(vm) {\n        const appOptions = parseApp(vm);\n        const app = isFunction(getApp) &&\n            getApp({\n                allowDefault: true,\n            });\n        if (!app)\n            return;\n        vm.$.ctx.$scope = app;\n        const globalData = app.globalData;\n        if (globalData) {\n            Object.keys(appOptions.globalData).forEach((name) => {\n                if (!hasOwn(globalData, name)) {\n                    globalData[name] = appOptions.globalData[name];\n                }\n            });\n        }\n        Object.keys(appOptions).forEach((name) => {\n            if (!hasOwn(app, name)) {\n                app[name] = appOptions[name];\n            }\n        });\n        initAppLifecycle(appOptions, vm);\n        if (process.env.UNI_SUBPACKAGE) {\n            (wx.$subpackages || (wx.$subpackages = {}))[process.env.UNI_SUBPACKAGE] = {\n                $vm: vm,\n            };\n        }\n    };\n}\nfunction initAppLifecycle(appOptions, vm) {\n    if (isFunction(appOptions.onLaunch)) {\n        const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\n        appOptions.onLaunch(args);\n    }\n    if (isFunction(appOptions.onShow) && wx.onAppShow) {\n        wx.onAppShow((args) => {\n            vm.$callHook('onShow', args);\n        });\n    }\n    if (isFunction(appOptions.onHide) && wx.onAppHide) {\n        wx.onAppHide((args) => {\n            vm.$callHook('onHide', args);\n        });\n    }\n}\nfunction initLocale(appVm) {\n    const locale = ref(getLocaleLanguage());\n    Object.defineProperty(appVm, '$locale', {\n        get() {\n            return locale.value;\n        },\n        set(v) {\n            locale.value = v;\n        },\n    });\n}\n\nconst builtInProps = [\n    // 百度小程序,快手小程序自定义组件不支持绑定动态事件，动态dataset，故通过props传递事件信息\n    // event-opts\n    'eO',\n    // 组件 ref\n    'uR',\n    // 组件 ref-in-for\n    'uRIF',\n    // 组件 id\n    'uI',\n    // 组件类型 m: 小程序组件\n    'uT',\n    // 组件 props\n    'uP',\n    // 小程序不能直接定义 $slots 的 props，所以通过 vueSlots 转换到 $slots\n    'uS',\n];\nfunction initDefaultProps(options, isBehavior = false) {\n    const properties = {};\n    if (!isBehavior) {\n        // 均不指定类型，避免微信小程序 property received type-uncompatible value 警告\n        builtInProps.forEach((name) => {\n            properties[name] = {\n                type: null,\n                value: '',\n            };\n        });\n        // 小程序不能直接定义 $slots 的 props，所以通过 vueSlots 转换到 $slots\n        function observerSlots(newVal) {\n            const $slots = Object.create(null);\n            newVal &&\n                newVal.forEach((slotName) => {\n                    $slots[slotName] = true;\n                });\n            this.setData({\n                $slots,\n            });\n        }\n        properties.uS = {\n            type: null,\n            value: [],\n        };\n        {\n            properties.uS.observer = observerSlots;\n        }\n    }\n    if (options.behaviors) {\n        // wx://form-field\n        if (options.behaviors.includes('wx' + '://form-field')) {\n            if (!options.properties || !options.properties.name) {\n                properties.name = {\n                    type: null,\n                    value: '',\n                };\n            }\n            if (!options.properties || !options.properties.value) {\n                properties.value = {\n                    type: null,\n                    value: '',\n                };\n            }\n        }\n    }\n    return properties;\n}\nfunction initVirtualHostProps(options) {\n    const properties = {};\n    {\n        if ((options && options.virtualHost)) {\n            properties[VIRTUAL_HOST_STYLE] = {\n                type: null,\n                value: '',\n            };\n            properties[VIRTUAL_HOST_CLASS] = {\n                type: null,\n                value: '',\n            };\n            properties[VIRTUAL_HOST_HIDDEN] = {\n                type: null,\n                value: '',\n            };\n            properties[VIRTUAL_HOST_ID] = {\n                type: null,\n                value: '',\n            };\n        }\n    }\n    return properties;\n}\n/**\n *\n * @param mpComponentOptions\n * @param isBehavior\n */\nfunction initProps(mpComponentOptions) {\n    if (!mpComponentOptions.properties) {\n        mpComponentOptions.properties = {};\n    }\n    extend(mpComponentOptions.properties, initDefaultProps(mpComponentOptions), initVirtualHostProps(mpComponentOptions.options));\n}\nconst PROP_TYPES = [String, Number, Boolean, Object, Array, null];\nfunction parsePropType(type, defaultValue) {\n    // [String]=>String\n    if (isArray(type) && type.length === 1) {\n        return type[0];\n    }\n    return type;\n}\nfunction normalizePropType(type, defaultValue) {\n    const res = parsePropType(type);\n    return PROP_TYPES.indexOf(res) !== -1 ? res : null;\n}\n/**\n * 初始化页面 props，方便接收页面参数，类型均为String，默认值均为''\n * @param param\n * @param rawProps\n */\nfunction initPageProps({ properties }, rawProps) {\n    if (isArray(rawProps)) {\n        rawProps.forEach((key) => {\n            properties[key] = {\n                type: String,\n                value: '',\n            };\n        });\n    }\n    else if (isPlainObject(rawProps)) {\n        Object.keys(rawProps).forEach((key) => {\n            const opts = rawProps[key];\n            if (isPlainObject(opts)) {\n                // title:{type:String,default:''}\n                let value = opts.default;\n                if (isFunction(value)) {\n                    value = value();\n                }\n                const type = opts.type;\n                opts.type = normalizePropType(type);\n                properties[key] = {\n                    type: opts.type,\n                    value,\n                };\n            }\n            else {\n                // content:String\n                properties[key] = {\n                    type: normalizePropType(opts),\n                };\n            }\n        });\n    }\n}\nfunction findPropsData(properties, isPage) {\n    return ((isPage\n        ? findPagePropsData(properties)\n        : findComponentPropsData(resolvePropValue(properties.uP))) || {});\n}\nfunction findPagePropsData(properties) {\n    const propsData = {};\n    if (isPlainObject(properties)) {\n        Object.keys(properties).forEach((name) => {\n            if (builtInProps.indexOf(name) === -1) {\n                propsData[name] = resolvePropValue(properties[name]);\n            }\n        });\n    }\n    return propsData;\n}\nfunction initFormField(vm) {\n    // 同步 form-field 的 name,value 值\n    const vueOptions = vm.$options;\n    if (isArray(vueOptions.behaviors) &&\n        vueOptions.behaviors.includes('uni://form-field')) {\n        vm.$watch('modelValue', () => {\n            vm.$scope &&\n                vm.$scope.setData({\n                    name: vm.name,\n                    value: vm.modelValue,\n                });\n        }, {\n            immediate: true,\n        });\n    }\n}\nfunction resolvePropValue(prop) {\n    return prop;\n}\n\nfunction initData(_) {\n    return {};\n}\nfunction initPropsObserver(componentOptions) {\n    const observe = function observe() {\n        const up = this.properties.uP;\n        if (!up) {\n            return;\n        }\n        if (this.$vm) {\n            updateComponentProps(resolvePropValue(up), this.$vm.$);\n        }\n        else if (resolvePropValue(this.properties.uT) === 'm') {\n            // 小程序组件\n            updateMiniProgramComponentProperties(resolvePropValue(up), this);\n        }\n    };\n    {\n        if (!componentOptions.observers) {\n            componentOptions.observers = {};\n        }\n        componentOptions.observers.uP = observe;\n    }\n}\nfunction updateMiniProgramComponentProperties(up, mpInstance) {\n    const prevProps = mpInstance.properties;\n    const nextProps = findComponentPropsData(up) || {};\n    if (hasPropsChanged(prevProps, nextProps, false)) {\n        mpInstance.setData(nextProps);\n    }\n}\nfunction updateComponentProps(up, instance) {\n    const prevProps = toRaw(instance.props);\n    const nextProps = findComponentPropsData(up) || {};\n    if (hasPropsChanged(prevProps, nextProps)) {\n        updateProps(instance, nextProps, prevProps, false);\n        if (hasQueueJob(instance.update)) {\n            invalidateJob(instance.update);\n        }\n        {\n            instance.update();\n        }\n    }\n}\nfunction hasPropsChanged(prevProps, nextProps, checkLen = true) {\n    const nextKeys = Object.keys(nextProps);\n    if (checkLen && nextKeys.length !== Object.keys(prevProps).length) {\n        return true;\n    }\n    for (let i = 0; i < nextKeys.length; i++) {\n        const key = nextKeys[i];\n        if (nextProps[key] !== prevProps[key]) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction initBehaviors(vueOptions) {\n    const vueBehaviors = vueOptions.behaviors;\n    let vueProps = vueOptions.props;\n    if (!vueProps) {\n        vueOptions.props = vueProps = [];\n    }\n    const behaviors = [];\n    if (isArray(vueBehaviors)) {\n        vueBehaviors.forEach((behavior) => {\n            // 这里的 global 应该是个变量\n            behaviors.push(behavior.replace('uni://', 'wx' + '://'));\n            if (behavior === 'uni://form-field') {\n                if (isArray(vueProps)) {\n                    vueProps.push('name');\n                    vueProps.push('modelValue');\n                }\n                else {\n                    vueProps.name = {\n                        type: String,\n                        default: '',\n                    };\n                    vueProps.modelValue = {\n                        type: [String, Number, Boolean, Array, Object, Date],\n                        default: '',\n                    };\n                }\n            }\n        });\n    }\n    return behaviors;\n}\nfunction applyOptions(componentOptions, vueOptions) {\n    componentOptions.data = initData();\n    componentOptions.behaviors = initBehaviors(vueOptions);\n}\n\nfunction parseComponent(vueOptions, { parse, mocks, isPage, isPageInProject, initRelation, handleLink, initLifetimes, }) {\n    vueOptions = vueOptions.default || vueOptions;\n    const options = {\n        multipleSlots: true,\n        // styleIsolation: 'apply-shared',\n        addGlobalClass: true,\n        pureDataPattern: /^uP$/,\n    };\n    if (isArray(vueOptions.mixins)) {\n        vueOptions.mixins.forEach((item) => {\n            if (isObject(item.options)) {\n                extend(options, item.options);\n            }\n        });\n    }\n    if (vueOptions.options) {\n        extend(options, vueOptions.options);\n    }\n    const mpComponentOptions = {\n        options,\n        lifetimes: initLifetimes({ mocks, isPage, initRelation, vueOptions }),\n        pageLifetimes: {\n            show() {\n                if (__VUE_PROD_DEVTOOLS__) {\n                    devtoolsComponentAdded(this.$vm.$);\n                }\n                this.$vm && this.$vm.$callHook('onPageShow');\n            },\n            hide() {\n                this.$vm && this.$vm.$callHook('onPageHide');\n            },\n            resize(size) {\n                this.$vm && this.$vm.$callHook('onPageResize', size);\n            },\n        },\n        methods: {\n            __l: handleLink,\n        },\n    };\n    if (__VUE_OPTIONS_API__) {\n        applyOptions(mpComponentOptions, vueOptions);\n    }\n    initProps(mpComponentOptions);\n    initPropsObserver(mpComponentOptions);\n    initExtraOptions(mpComponentOptions, vueOptions);\n    initWxsCallMethods(mpComponentOptions.methods, vueOptions.wxsCallMethods);\n    {\n        initWorkletMethods(mpComponentOptions.methods, vueOptions.methods);\n    }\n    if (parse) {\n        parse(mpComponentOptions, { handleLink });\n    }\n    return mpComponentOptions;\n}\nfunction initCreateComponent(parseOptions) {\n    return function createComponent(vueComponentOptions) {\n        return Component(parseComponent(vueComponentOptions, parseOptions));\n    };\n}\nlet $createComponentFn;\nlet $destroyComponentFn;\nfunction getAppVm() {\n    if (process.env.UNI_MP_PLUGIN) {\n        return wx.$vm;\n    }\n    if (process.env.UNI_SUBPACKAGE) {\n        return wx.$subpackages[process.env.UNI_SUBPACKAGE].$vm;\n    }\n    return getApp().$vm;\n}\nfunction $createComponent(initialVNode, options) {\n    if (!$createComponentFn) {\n        $createComponentFn = getAppVm().$createComponent;\n    }\n    const proxy = $createComponentFn(initialVNode, options);\n    return getExposeProxy(proxy.$) || proxy;\n}\nfunction $destroyComponent(instance) {\n    if (!$destroyComponentFn) {\n        $destroyComponentFn = getAppVm().$destroyComponent;\n    }\n    return $destroyComponentFn(instance);\n}\n\nfunction parsePage(vueOptions, parseOptions) {\n    const { parse, mocks, isPage, initRelation, handleLink, initLifetimes } = parseOptions;\n    const miniProgramPageOptions = parseComponent(vueOptions, {\n        mocks,\n        isPage,\n        isPageInProject: true,\n        initRelation,\n        handleLink,\n        initLifetimes,\n    });\n    initPageProps(miniProgramPageOptions, (vueOptions.default || vueOptions).props);\n    const methods = miniProgramPageOptions.methods;\n    methods.onLoad = function (query) {\n        {\n            this.options = query;\n        }\n        this.$page = {\n            fullPath: addLeadingSlash(this.route + stringifyQuery(query)),\n        };\n        return this.$vm && this.$vm.$callHook(ON_LOAD, query);\n    };\n    initHooks(methods, PAGE_INIT_HOOKS);\n    {\n        initUnknownHooks(methods, vueOptions);\n    }\n    initRuntimeHooks(methods, vueOptions.__runtimeHooks);\n    initMixinRuntimeHooks(methods);\n    parse && parse(miniProgramPageOptions, { handleLink });\n    return miniProgramPageOptions;\n}\nfunction initCreatePage(parseOptions) {\n    return function createPage(vuePageOptions) {\n        return Component(parsePage(vuePageOptions, parseOptions));\n    };\n}\n\nfunction initCreatePluginApp(parseAppOptions) {\n    return function createApp(vm) {\n        initAppLifecycle(parseApp(vm), vm);\n        if (process.env.UNI_MP_PLUGIN) {\n            wx.$vm = vm;\n        }\n    };\n}\n\nconst MPPage = Page;\nconst MPComponent = Component;\nfunction initTriggerEvent(mpInstance) {\n    const oldTriggerEvent = mpInstance.triggerEvent;\n    const newTriggerEvent = function (event, ...args) {\n        return oldTriggerEvent.apply(mpInstance, [\n            customizeEvent(event),\n            ...args,\n        ]);\n    };\n    // 京东小程序triggerEvent为只读属性\n    try {\n        mpInstance.triggerEvent = newTriggerEvent;\n    }\n    catch (error) {\n        mpInstance._triggerEvent = newTriggerEvent;\n    }\n}\nfunction initMiniProgramHook(name, options, isComponent) {\n    const oldHook = options[name];\n    if (!oldHook) {\n        options[name] = function () {\n            initTriggerEvent(this);\n        };\n    }\n    else {\n        options[name] = function (...args) {\n            initTriggerEvent(this);\n            return oldHook.apply(this, args);\n        };\n    }\n}\nPage = function (options) {\n    initMiniProgramHook(ON_LOAD, options);\n    return MPPage(options);\n};\nComponent = function (options) {\n    initMiniProgramHook('created', options);\n    // 小程序组件\n    const isVueComponent = options.properties && options.properties.uP;\n    if (!isVueComponent) {\n        initProps(options);\n        initPropsObserver(options);\n    }\n    return MPComponent(options);\n};\n\n// @ts-expect-error\nfunction initLifetimes({ mocks, isPage, initRelation, vueOptions, }) {\n    return {\n        attached() {\n            let properties = this.properties;\n            initVueIds(properties.uI, this);\n            const relationOptions = {\n                vuePid: this._$vuePid,\n            };\n            // 处理父子关系\n            initRelation(this, relationOptions);\n            // 初始化 vue 实例\n            const mpInstance = this;\n            const isMiniProgramPage = isPage(mpInstance);\n            let propsData = properties;\n            this.$vm = $createComponent({\n                type: vueOptions,\n                props: findPropsData(propsData, isMiniProgramPage),\n            }, {\n                mpType: isMiniProgramPage ? 'page' : 'component',\n                mpInstance,\n                slots: properties.uS || {}, // vueSlots\n                parentComponent: relationOptions.parent && relationOptions.parent.$,\n                onBeforeSetup(instance, options) {\n                    initRefs(instance, mpInstance);\n                    initMocks(instance, mpInstance, mocks);\n                    initComponentInstance(instance, options);\n                },\n            });\n            if (process.env.UNI_DEBUG) {\n                console.log('uni-app:[' +\n                    Date.now() +\n                    '][' +\n                    (mpInstance.is || mpInstance.route) +\n                    '][' +\n                    this.$vm.$.uid +\n                    ']attached');\n            }\n            if (!isMiniProgramPage) {\n                initFormField(this.$vm);\n            }\n        },\n        ready() {\n            if (process.env.UNI_DEBUG) {\n                console.log('uni-app:[' + Date.now() + '][' + (this.is || this.route) + ']ready');\n            }\n            // 当组件 props 默认值为 true，初始化时传入 false 会导致 created,ready 触发, 但 attached 不触发\n            // https://developers.weixin.qq.com/community/develop/doc/00066ae2844cc0f8eb883e2a557800\n            if (this.$vm) {\n                {\n                    this.$vm.$callHook('mounted');\n                    this.$vm.$callHook(ON_READY);\n                }\n            }\n        },\n        detached() {\n            if (this.$vm) {\n                pruneComponentPropsCache(this.$vm.$.uid);\n                $destroyComponent(this.$vm);\n            }\n        },\n    };\n}\n\nconst mocks = ['__route__', '__wxExparserNodeId__', '__wxWebviewId__'];\nfunction isPage(mpInstance) {\n    return !!mpInstance.route;\n}\nfunction initRelation(mpInstance, detail) {\n    mpInstance.triggerEvent('__l', detail);\n}\nfunction handleLink(event) {\n    // detail 是微信,value 是百度(dipatch)\n    const detail = (event.detail ||\n        event.value);\n    const vuePid = detail.vuePid;\n    let parentVm;\n    if (vuePid) {\n        parentVm = findVmByVueId(this.$vm, vuePid);\n    }\n    if (!parentVm) {\n        parentVm = this.$vm;\n    }\n    detail.parent = parentVm;\n}\n\nvar parseOptions = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  handleLink: handleLink,\n  initLifetimes: initLifetimes,\n  initRelation: initRelation,\n  isPage: isPage,\n  mocks: mocks\n});\n\nconst createApp = initCreateApp();\nconst createPage = initCreatePage(parseOptions);\nconst createComponent = initCreateComponent(parseOptions);\nconst createPluginApp = initCreatePluginApp();\nconst createSubpackageApp = initCreateSubpackageApp();\n{\n    wx.createApp = global.createApp = createApp;\n    wx.createPage = createPage;\n    wx.createComponent = createComponent;\n    wx.createPluginApp = global.createPluginApp =\n        createPluginApp;\n    wx.createSubpackageApp = global.createSubpackageApp =\n        createSubpackageApp;\n}\n\nexport { createApp, createComponent, createPage, createPluginApp, createSubpackageApp };\n"], "names": ["effect", "trigger", "computed", "isObject", "is<PERSON><PERSON><PERSON>ly", "isShallow", "has", "get2", "r", "isPromise", "queue", "p", "diff", "version", "isModelListener", "once", "s", "createApp", "plugin", "n", "callHook", "get", "set", "c", "register", "h", "m", "initProps", "resolvePropValue", "getType", "t", "validateProp", "assertType", "getInvalidTypeMessage", "isSimpleType", "styleValue", "isExplicable", "isBoolean", "Component", "index", "resolve", "f", "ref", "emit", "uid", "pruneComponentPropsCache", "update", "invokeArrayFns", "e", "createVNode", "createComponent", "createComponent2", "initHooks", "applyOptions", "uni", "getLocaleLanguage", "interceptors", "res", "options", "errMsg", "Emitter", "cid", "protocols", "global", "get<PERSON><PERSON><PERSON>", "mocks", "host", "value", "err", "onError", "offError", "wx", "isPage", "observe", "initRelation", "handleLink", "initLifetimes", "size", "parseOptions", "createPage"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmGA,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,YAAY;AAClB,MAAM,YAAY;AAClB,MAAM,YAAY;AAIlB,SAAS,QAAQ,KAAK,OAAO;AAClB,SAAA,CAAC,CAAC,MAAM,KAAK,CAAC,SAAS,IAAI,QAAQ,IAAI,MAAM,EAAE;AAC1D;AACA,SAAS,WAAW,KAAK,OAAO;AACrB,SAAA,MAAM,KAAK,CAAC,SAAS,IAAI,QAAQ,IAAI,MAAM,CAAC;AACvD;AACA,SAAS,gBAAgB,QAAQ,UAAU;AACvC,MAAI,CAAC,QAAQ;AACT;AAAA,EACJ;AACA,WAAS,OAAO,KAAA,EAAO,QAAQ,MAAM,GAAG;AACpC,MAAA,YAAY,SAAS,MAAM,GAAG;AACvB,WAAA;AAAA,EACX;AACA,WAAS,OAAO;AAChB,MAAI,WAAW,WAAW;AAEf,WAAA;AAAA,EACX;AACA,MAAI,OAAO,QAAQ,IAAI,MAAM,GAAG;AAC5B,QAAI,OAAO,QAAQ,OAAO,IAAI,IAAI;AACvB,aAAA;AAAA,IACX;AACA,QAAI,OAAO,QAAQ,OAAO,IAAI,IAAI;AACvB,aAAA;AAAA,IACX;AACI,QAAA,QAAQ,QAAQ,CAAC,OAAO,OAAO,OAAO,MAAM,CAAC,GAAG;AACzC,aAAA;AAAA,IACX;AACO,WAAA;AAAA,EACX;AACA,MAAI,UAAU,CAAC,WAAW,WAAW,SAAS;AAC9C,MAAI,YAAY,OAAO,KAAK,QAAQ,EAAE,SAAS,GAAG;AACpC,cAAA,OAAO,KAAK,QAAQ;AAAA,EAClC;AACM,QAAA,OAAO,WAAW,QAAQ,OAAO;AACvC,MAAI,MAAM;AACC,WAAA;AAAA,EACX;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9IA;AAAA;AAAA;AAAA;AAAA;AAMA,SAAS,OAAO,QAAQ,MAAM;AAC5B,UAAQ,KAAK,cAAc,GAAG,IAAI,GAAG,IAAI;AAC3C;AAEA,IAAI;AACJ,MAAM,YAAY;AAAA,EAChB,YAAY,WAAW,OAAO;AAC5B,SAAK,WAAW;AAIhB,SAAK,UAAU;AAIf,SAAK,UAAU;AAIf,SAAK,WAAW;AAChB,SAAK,SAAS;AACV,QAAA,CAAC,YAAY,mBAAmB;AAClC,WAAK,SAAS,kBAAkB,WAAW,kBAAkB,SAAS,CAAK,IAAA;AAAA,QACzE;AAAA,MACE,IAAA;AAAA,IACN;AAAA,EACF;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,IAAI;AACN,QAAI,KAAK,SAAS;AAChB,YAAM,qBAAqB;AACvB,UAAA;AACkB,4BAAA;AACpB,eAAO,GAAG;AAAA,MAAA,UACV;AACoB,4BAAA;AAAA,MACtB;AAAA,IAAA,OACoD;AACpD,aAAO,sCAAsC;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK;AACiB,wBAAA;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM;AACJ,wBAAoB,KAAK;AAAA,EAC3B;AAAA,EACA,KAAK,YAAY;AACf,QAAI,KAAK,SAAS;AAChB,UAAI,GAAG;AACF,WAAA,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC1C,aAAA,QAAQ,CAAC,EAAE,KAAK;AAAA,MACvB;AACK,WAAA,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC3C,aAAA,SAAS,CAAC;MACjB;AACA,UAAI,KAAK,QAAQ;AACV,aAAA,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC9C,eAAK,OAAO,CAAC,EAAE,KAAK,IAAI;AAAA,QAC1B;AAAA,MACF;AACA,UAAI,CAAC,KAAK,YAAY,KAAK,UAAU,CAAC,YAAY;AAChD,cAAM,OAAO,KAAK,OAAO,OAAO,IAAI;AAChC,YAAA,QAAQ,SAAS,MAAM;AACzB,eAAK,OAAO,OAAO,KAAK,KAAK,IAAI;AACjC,eAAK,QAAQ,KAAK;AAAA,QACpB;AAAA,MACF;AACA,WAAK,SAAS;AACd,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AACF;AACA,SAAS,YAAY,UAAU;AACtB,SAAA,IAAI,YAAY,QAAQ;AACjC;AACA,SAAS,kBAAkBA,SAAQ,QAAQ,mBAAmB;AACxD,MAAA,SAAS,MAAM,QAAQ;AACnB,UAAA,QAAQ,KAAKA,OAAM;AAAA,EAC3B;AACF;AACA,SAAS,kBAAkB;AAClB,SAAA;AACT;AAWA,IAAI;AACJ,MAAM,eAAe;AAAA,EACnB,YAAY,IAAIC,UAAS,WAAW,OAAO;AACzC,SAAK,KAAK;AACV,SAAK,UAAUA;AACf,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,OAAO;AAIZ,SAAK,cAAc;AAInB,SAAK,WAAW;AAIhB,SAAK,YAAY;AAIjB,SAAK,kBAAkB;AAIvB,SAAK,cAAc;AACnB,sBAAkB,MAAM,KAAK;AAAA,EAC/B;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,KAAK,gBAAgB,KAAK,KAAK,gBAAgB,GAAG;AACpD,WAAK,cAAc;AACL;AACd,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,KAAK;AACnC,cAAA,MAAM,KAAK,KAAK,CAAC;AACvB,YAAI,IAAI,UAAU;AAChB,0BAAgB,IAAI,QAAQ;AACxB,cAAA,KAAK,eAAe,GAAG;AACzB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACI,UAAA,KAAK,gBAAgB,GAAG;AAC1B,aAAK,cAAc;AAAA,MACrB;AACc;IAChB;AACA,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA,EACA,IAAI,MAAM,GAAG;AACN,SAAA,cAAc,IAAI,IAAI;AAAA,EAC7B;AAAA,EACA,MAAM;AACJ,SAAK,cAAc;AACf,QAAA,CAAC,KAAK,QAAQ;AAChB,aAAO,KAAK;IACd;AACA,QAAI,kBAAkB;AACtB,QAAI,aAAa;AACb,QAAA;AACY,oBAAA;AACC,qBAAA;AACV,WAAA;AACL,uBAAiB,IAAI;AACrB,aAAO,KAAK;IAAG,UACf;AACA,wBAAkB,IAAI;AACjB,WAAA;AACU,qBAAA;AACD,oBAAA;AAAA,IAChB;AAAA,EACF;AAAA,EACA,OAAO;AACD,QAAA;AACJ,QAAI,KAAK,QAAQ;AACf,uBAAiB,IAAI;AACrB,wBAAkB,IAAI;AACtB,OAAC,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG,KAAK,IAAI;AAClD,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AACF;AACA,SAAS,gBAAgBC,WAAU;AACjC,SAAOA,UAAS;AAClB;AACA,SAAS,iBAAiB,SAAS;AACzB,UAAA;AACR,UAAQ,cAAc;AACxB;AACA,SAAS,kBAAkB,SAAS;AAClC,MAAI,QAAQ,KAAK,SAAS,QAAQ,aAAa;AAC7C,aAAS,IAAI,QAAQ,aAAa,IAAI,QAAQ,KAAK,QAAQ,KAAK;AAC9D,uBAAiB,QAAQ,KAAK,CAAC,GAAG,OAAO;AAAA,IAC3C;AACQ,YAAA,KAAK,SAAS,QAAQ;AAAA,EAChC;AACF;AACA,SAAS,iBAAiB,KAAK,SAAS;AAChC,QAAA,UAAU,IAAI,IAAI,OAAO;AAC/B,MAAI,YAAY,UAAU,QAAQ,aAAa,SAAS;AACtD,QAAI,OAAO,OAAO;AACd,QAAA,IAAI,SAAS,GAAG;AAClB,UAAI,QAAQ;AAAA,IACd;AAAA,EACF;AACF;AAyBA,IAAI,cAAc;AAClB,IAAI,qBAAqB;AACzB,MAAM,aAAa,CAAA;AACnB,SAAS,gBAAgB;AACvB,aAAW,KAAK,WAAW;AACb,gBAAA;AAChB;AACA,SAAS,gBAAgB;AACjB,QAAA,OAAO,WAAW;AACV,gBAAA,SAAS,SAAS,OAAO;AACzC;AACA,SAAS,kBAAkB;AACzB;AACF;AACA,SAAS,kBAAkB;AACzB;AACO,SAAA,CAAC,sBAAsB,sBAAsB,QAAQ;AAC1D,0BAAsB;EACxB;AACF;AACA,SAAS,YAAY,SAAS,KAAK,wBAAwB;AACrD,MAAA;AACJ,MAAI,IAAI,IAAI,OAAO,MAAM,QAAQ,UAAU;AACrC,QAAA,IAAI,SAAS,QAAQ,QAAQ;AACjC,UAAM,SAAS,QAAQ,KAAK,QAAQ,WAAW;AAC/C,QAAI,WAAW,KAAK;AAClB,UAAI,QAAQ;AACV,yBAAiB,QAAQ,OAAO;AAAA,MAClC;AACQ,cAAA,KAAK,QAAQ,aAAa,IAAI;AAAA,IAAA,OACjC;AACG,cAAA;AAAA,IACV;AAC+C;AAC7C,OAAC,KAAK,QAAQ,YAAY,OAAO,SAAS,GAAG,KAAK,SAAS,OAAO,EAAE,QAAQ,QAAQ,GAAG,sBAAsB,CAAC;AAAA,IAChH;AAAA,EACF;AACF;AACA,MAAM,wBAAwB,CAAA;AAC9B,SAAS,eAAe,KAAK,YAAY,wBAAwB;AAC3D,MAAA;AACY;AACL,aAAA,WAAW,IAAI,QAAQ;AAC5B,QAAA;AACJ,QAAI,QAAQ,cAAc,eAAe,YAAY,OAAO,WAAW,WAAW,IAAI,IAAI,OAAO,MAAM,QAAQ,WAAW;AACxH,cAAQ,oBAAoB,QAAQ,kBAAkB,QAAQ,gBAAgB;AAC9E,cAAQ,cAAc;AAAA,IACxB;AACI,QAAA,QAAQ,oBAAoB,YAAY,OAAO,WAAW,WAAW,IAAI,IAAI,OAAO,MAAM,QAAQ,WAAW;AAChE;AAC7C,SAAC,KAAK,QAAQ,cAAc,OAAO,SAAS,GAAG,KAAK,SAAS,OAAO,EAAE,QAAQ,QAAQ,GAAG,sBAAsB,CAAC;AAAA,MAClH;AACA,cAAQ,QAAQ;AAChB,WAAK,CAAC,QAAQ,aAAa,QAAQ,iBAAiB,QAAQ,gBAAgB,GAAG;AAC7E,gBAAQ,kBAAkB;AAC1B,YAAI,QAAQ,WAAW;AACC,gCAAA,KAAK,QAAQ,SAAS;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACgB;AAClB;AAEA,MAAM,YAAY,CAAC,SAASA,cAAa;AACjC,QAAA,0BAA0B;AAChC,MAAI,UAAU;AACd,MAAI,WAAWA;AACR,SAAA;AACT;AAEA,MAAM,gCAAgC;AACtC,MAAM,cAAc,OAAmD,SAAc;AACrF,MAAM,sBAAsB,OAAmD,iBAAsB;AACrG,SAAS,MAAM,QAAQ,MAAM,KAAK;AAChC,MAAI,eAAe,cAAc;AAC3B,QAAA,UAAU,UAAU,IAAI,MAAM;AAClC,QAAI,CAAC,SAAS;AACZ,gBAAU,IAAI,QAAQ,UAA0B,oBAAI,IAAK,CAAA;AAAA,IAC3D;AACI,QAAA,MAAM,QAAQ,IAAI,GAAG;AACzB,QAAI,CAAC,KAAK;AACA,cAAA,IAAI,KAAK,MAAM,UAAU,MAAM,QAAQ,OAAO,GAAG,CAAC,CAAC;AAAA,IAC7D;AACA;AAAA,MACE;AAAA,MACA;AAAA,MAC4C;AAAA,QAC1C;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACE;AAAA,EAER;AACF;AACA,SAAS,QAAQ,QAAQ,MAAM,KAAK,UAAU,UAAU,WAAW;AAC3D,QAAA,UAAU,UAAU,IAAI,MAAM;AACpC,MAAI,CAAC,SAAS;AACZ;AAAA,EACF;AACA,MAAI,OAAO,CAAA;AACX,MAAI,SAAS,SAAS;AACpB,WAAO,CAAC,GAAG,QAAQ,OAAQ,CAAA;AAAA,EAClB,WAAA,QAAQ,YAAY,QAAQ,MAAM,GAAG;AACxC,UAAA,YAAY,OAAO,QAAQ;AACzB,YAAA,QAAQ,CAAC,KAAK,SAAS;AAC7B,UAAI,SAAS,YAAY,CAAC,SAAS,IAAI,KAAK,QAAQ,WAAW;AAC7D,aAAK,KAAK,GAAG;AAAA,MACf;AAAA,IAAA,CACD;AAAA,EAAA,OACI;AACL,QAAI,QAAQ,QAAQ;AAClB,WAAK,KAAK,QAAQ,IAAI,GAAG,CAAC;AAAA,IAC5B;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AACC,YAAA,CAAC,QAAQ,MAAM,GAAG;AACpB,eAAK,KAAK,QAAQ,IAAI,WAAW,CAAC;AAC9B,cAAA,MAAM,MAAM,GAAG;AACjB,iBAAK,KAAK,QAAQ,IAAI,mBAAmB,CAAC;AAAA,UAC5C;AAAA,QAAA,WACS,aAAa,GAAG,GAAG;AAC5B,eAAK,KAAK,QAAQ,IAAI,QAAQ,CAAC;AAAA,QACjC;AACA;AAAA,MACF,KAAK;AACC,YAAA,CAAC,QAAQ,MAAM,GAAG;AACpB,eAAK,KAAK,QAAQ,IAAI,WAAW,CAAC;AAC9B,cAAA,MAAM,MAAM,GAAG;AACjB,iBAAK,KAAK,QAAQ,IAAI,mBAAmB,CAAC;AAAA,UAC5C;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACC,YAAA,MAAM,MAAM,GAAG;AACjB,eAAK,KAAK,QAAQ,IAAI,WAAW,CAAC;AAAA,QACpC;AACA;AAAA,IACJ;AAAA,EACF;AACgB;AAChB,aAAW,OAAO,MAAM;AACtB,QAAI,KAAK;AACP;AAAA,QACE;AAAA,QACA;AAAA,QAC4C;AAAA,UAC1C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QAAA;AAAA,MACE;AAAA,IAER;AAAA,EACF;AACgB;AAClB;AAMA,MAAM,6CAA6C,6BAA6B;AAChF,MAAM,iBAAiB,IAAI;AAAA,EACT,uBAAO,oBAAoB,MAAM,EAAE,OAAO,CAAC,QAAQ,QAAQ,eAAe,QAAQ,QAAQ,EAAE,IAAI,CAAC,QAAQ,OAAO,GAAG,CAAC,EAAE,OAAO,QAAQ;AACvJ;AACA,MAAM,wBAAoE,4CAAA;AAC1E,SAAS,8BAA8B;AACrC,QAAM,mBAAmB,CAAA;AACzB,GAAC,YAAY,WAAW,aAAa,EAAE,QAAQ,CAAC,QAAQ;AACrC,qBAAA,GAAG,IAAI,YAAY,MAAM;AAClC,YAAA,MAAM,MAAM,IAAI;AACtB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AACrC,cAAA,KAAK,OAAO,IAAI,EAAE;AAAA,MAC1B;AACA,YAAM,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI;AACxB,UAAA,QAAQ,MAAM,QAAQ,OAAO;AAC/B,eAAO,IAAI,GAAG,EAAE,GAAG,KAAK,IAAI,KAAK,CAAC;AAAA,MAAA,OAC7B;AACE,eAAA;AAAA,MACT;AAAA,IAAA;AAAA,EACF,CACD;AACA,GAAA,QAAQ,OAAO,SAAS,WAAW,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AAC5C,qBAAA,GAAG,IAAI,YAAY,MAAM;AAC1B;AACE;AACV,YAAA,MAAM,MAAM,IAAI,EAAE,GAAG,EAAE,MAAM,MAAM,IAAI;AAC7B;AACF;AACP,aAAA;AAAA,IAAA;AAAA,EACT,CACD;AACM,SAAA;AACT;AACA,SAAS,eAAe,KAAK;AACrB,QAAA,MAAM,MAAM,IAAI;AAChB,QAAA,KAAK,OAAO,GAAG;AACd,SAAA,IAAI,eAAe,GAAG;AAC/B;AACA,MAAM,oBAAoB;AAAA,EACxB,YAAY,cAAc,OAAO,aAAa,OAAO;AACnD,SAAK,cAAc;AACnB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,QAAQ,KAAK,UAAU;AACzB,UAAM,cAAc,KAAK,aAAa,aAAa,KAAK;AACxD,QAAI,QAAQ,kBAAkB;AAC5B,aAAO,CAAC;AAAA,IAAA,WACC,QAAQ,kBAAkB;AAC5B,aAAA;AAAA,IAAA,WACE,QAAQ,iBAAiB;AAC3B,aAAA;AAAA,IAAA,WACE,QAAQ,WAAW;AACxB,UAAA,cAAc,cAAc,aAAa,qBAAqB,cAAc,aAAa,qBAAqB,aAAa,IAAI,MAAM;AAAA;AAAA,MAEzI,OAAO,eAAe,MAAM,MAAM,OAAO,eAAe,QAAQ,GAAG;AAC1D,eAAA;AAAA,MACT;AACA;AAAA,IACF;AACM,UAAA,gBAAgB,QAAQ,MAAM;AACpC,QAAI,CAAC,aAAa;AAChB,UAAI,iBAAiB,OAAO,uBAAuB,GAAG,GAAG;AACvD,eAAO,QAAQ,IAAI,uBAAuB,KAAK,QAAQ;AAAA,MACzD;AACA,UAAI,QAAQ,kBAAkB;AACrB,eAAA;AAAA,MACT;AAAA,IACF;AACA,UAAM,MAAM,QAAQ,IAAI,QAAQ,KAAK,QAAQ;AACzC,QAAA,SAAS,GAAG,IAAI,eAAe,IAAI,GAAG,IAAI,mBAAmB,GAAG,GAAG;AAC9D,aAAA;AAAA,IACT;AACA,QAAI,CAAC,aAAa;AACV,YAAA,QAAQ,OAAO,GAAG;AAAA,IAC1B;AACA,QAAI,YAAY;AACP,aAAA;AAAA,IACT;AACI,QAAA,MAAM,GAAG,GAAG;AACd,aAAO,iBAAiB,aAAa,GAAG,IAAI,MAAM,IAAI;AAAA,IACxD;AACI,QAAAC,WAAS,GAAG,GAAG;AACjB,aAAO,cAAc,SAAS,GAAG,IAAI,SAAS,GAAG;AAAA,IACnD;AACO,WAAA;AAAA,EACT;AACF;AACA,MAAM,+BAA+B,oBAAoB;AAAA,EACvD,YAAY,aAAa,OAAO;AAC9B,UAAM,OAAO,UAAU;AAAA,EACzB;AAAA,EACA,IAAI,QAAQ,KAAK,OAAO,UAAU;AAC5B,QAAA,WAAW,OAAO,GAAG;AACrB,QAAA,CAAC,KAAK,YAAY;AACd,YAAA,qBAAqB,WAAW,QAAQ;AAC9C,UAAI,CAAC,UAAU,KAAK,KAAK,CAAC,WAAW,KAAK,GAAG;AAC3C,mBAAW,MAAM,QAAQ;AACzB,gBAAQ,MAAM,KAAK;AAAA,MACrB;AACI,UAAA,CAAC,QAAQ,MAAM,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,KAAK,GAAG;AACxD,YAAI,oBAAoB;AACf,iBAAA;AAAA,QAAA,OACF;AACL,mBAAS,QAAQ;AACV,iBAAA;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,QAAQ,MAAM,KAAK,aAAa,GAAG,IAAI,OAAO,GAAG,IAAI,OAAO,SAAS,OAAO,QAAQ,GAAG;AACtG,UAAM,SAAS,QAAQ,IAAI,QAAQ,KAAK,OAAO,QAAQ;AACnD,QAAA,WAAW,MAAM,QAAQ,GAAG;AAC9B,UAAI,CAAC,QAAQ;AACH,gBAAA,QAAQ,OAAO,KAAK,KAAK;AAAA,MACxB,WAAA,WAAW,OAAO,QAAQ,GAAG;AACtC,gBAAQ,QAAQ,OAAO,KAAK,OAAO,QAAQ;AAAA,MAC7C;AAAA,IACF;AACO,WAAA;AAAA,EACT;AAAA,EACA,eAAe,QAAQ,KAAK;AACpB,UAAA,SAAS,OAAO,QAAQ,GAAG;AAC3B,UAAA,WAAW,OAAO,GAAG;AAC3B,UAAM,SAAS,QAAQ,eAAe,QAAQ,GAAG;AACjD,QAAI,UAAU,QAAQ;AACpB,cAAQ,QAAQ,UAAU,KAAK,QAAQ,QAAQ;AAAA,IACjD;AACO,WAAA;AAAA,EACT;AAAA,EACA,IAAI,QAAQ,KAAK;AACf,UAAM,SAAS,QAAQ,IAAI,QAAQ,GAAG;AAClC,QAAA,CAAC,SAAS,GAAG,KAAK,CAAC,eAAe,IAAI,GAAG,GAAG;AACxC,YAAA,QAAQ,OAAO,GAAG;AAAA,IAC1B;AACO,WAAA;AAAA,EACT;AAAA,EACA,QAAQ,QAAQ;AACd;AAAA,MACE;AAAA,MACA;AAAA,MACA,QAAQ,MAAM,IAAI,WAAW;AAAA,IAAA;AAExB,WAAA,QAAQ,QAAQ,MAAM;AAAA,EAC/B;AACF;AACA,MAAM,gCAAgC,oBAAoB;AAAA,EACxD,YAAY,aAAa,OAAO;AAC9B,UAAM,MAAM,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,QAAQ,KAAK;AACgC;AAC7C;AAAA,QACE,yBAAyB,OAAO,GAAG,CAAC;AAAA,QACpC;AAAA,MAAA;AAAA,IAEJ;AACO,WAAA;AAAA,EACT;AAAA,EACA,eAAe,QAAQ,KAAK;AACqB;AAC7C;AAAA,QACE,4BAA4B,OAAO,GAAG,CAAC;AAAA,QACvC;AAAA,MAAA;AAAA,IAEJ;AACO,WAAA;AAAA,EACT;AACF;AACA,MAAM,sCAAsC;AAC5C,MAAM,uCAAuC;AAC7C,MAAM,0BAA8C,oBAAA;AAAA,EAClD;AACF;AACA,MAAM,0BAA8C,oBAAA,wBAAwB,IAAI;AAEhF,MAAM,YAAY,CAAC,UAAU;AAC7B,MAAM,WAAW,CAAC,MAAM,QAAQ,eAAe,CAAC;AAChD,SAAS,IAAI,QAAQ,KAAKC,cAAa,OAAOC,aAAY,OAAO;AAC/D,WAAS,OAAO,SAAS;AACnB,QAAA,YAAY,MAAM,MAAM;AACxB,QAAA,SAAS,MAAM,GAAG;AACxB,MAAI,CAACD,aAAY;AACX,QAAA,WAAW,KAAK,MAAM,GAAG;AACrB,YAAA,WAAW,OAAO,GAAG;AAAA,IAC7B;AACM,UAAA,WAAW,OAAO,MAAM;AAAA,EAChC;AACA,QAAM,EAAE,KAAK,KAAK,IAAI,SAAS,SAAS;AACxC,QAAM,OAAOC,aAAY,YAAYD,cAAa,aAAa;AAC/D,MAAI,KAAK,KAAK,WAAW,GAAG,GAAG;AAC7B,WAAO,KAAK,OAAO,IAAI,GAAG,CAAC;AAAA,EAClB,WAAA,KAAK,KAAK,WAAW,MAAM,GAAG;AACvC,WAAO,KAAK,OAAO,IAAI,MAAM,CAAC;AAAA,EAAA,WACrB,WAAW,WAAW;AAC/B,WAAO,IAAI,GAAG;AAAA,EAChB;AACF;AACA,SAASE,MAAI,KAAKF,cAAa,OAAO;AAC9B,QAAA,SAAS,KAAK,SAAS;AACvB,QAAA,YAAY,MAAM,MAAM;AACxB,QAAA,SAAS,MAAM,GAAG;AACxB,MAAI,CAACA,aAAY;AACX,QAAA,WAAW,KAAK,MAAM,GAAG;AACrB,YAAA,WAAW,OAAO,GAAG;AAAA,IAC7B;AACM,UAAA,WAAW,OAAO,MAAM;AAAA,EAChC;AACA,SAAO,QAAQ,SAAS,OAAO,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,KAAK,OAAO,IAAI,MAAM;AAChF;AACA,SAAS,KAAK,QAAQA,cAAa,OAAO;AACxC,WAAS,OAAO,SAAS;AACzB,GAACA,eAAc,MAAM,MAAM,MAAM,GAAG,WAAW,WAAW;AAC1D,SAAO,QAAQ,IAAI,QAAQ,QAAQ,MAAM;AAC3C;AACA,SAAS,IAAI,OAAO;AAClB,UAAQ,MAAM,KAAK;AACb,QAAA,SAAS,MAAM,IAAI;AACnB,QAAA,QAAQ,SAAS,MAAM;AAC7B,QAAM,SAAS,MAAM,IAAI,KAAK,QAAQ,KAAK;AAC3C,MAAI,CAAC,QAAQ;AACX,WAAO,IAAI,KAAK;AACR,YAAA,QAAQ,OAAO,OAAO,KAAK;AAAA,EACrC;AACO,SAAA;AACT;AACA,SAAS,MAAM,KAAK,OAAO;AACzB,UAAQ,MAAM,KAAK;AACb,QAAA,SAAS,MAAM,IAAI;AACzB,QAAM,EAAE,KAAK,MAAM,KAAKG,UAAS,SAAS,MAAM;AAChD,MAAI,SAAS,KAAK,KAAK,QAAQ,GAAG;AAClC,MAAI,CAAC,QAAQ;AACX,UAAM,MAAM,GAAG;AACN,aAAA,KAAK,KAAK,QAAQ,GAAG;AAAA,EAAA,OACsB;AAClC,sBAAA,QAAQ,MAAM,GAAG;AAAA,EACrC;AACA,QAAM,WAAWA,MAAK,KAAK,QAAQ,GAAG;AAC/B,SAAA,IAAI,KAAK,KAAK;AACrB,MAAI,CAAC,QAAQ;AACH,YAAA,QAAQ,OAAO,KAAK,KAAK;AAAA,EACxB,WAAA,WAAW,OAAO,QAAQ,GAAG;AACtC,YAAQ,QAAQ,OAAO,KAAK,OAAO,QAAQ;AAAA,EAC7C;AACO,SAAA;AACT;AACA,SAAS,YAAY,KAAK;AAClB,QAAA,SAAS,MAAM,IAAI;AACzB,QAAM,EAAE,KAAK,MAAM,KAAKA,UAAS,SAAS,MAAM;AAChD,MAAI,SAAS,KAAK,KAAK,QAAQ,GAAG;AAClC,MAAI,CAAC,QAAQ;AACX,UAAM,MAAM,GAAG;AACN,aAAA,KAAK,KAAK,QAAQ,GAAG;AAAA,EAAA,OACsB;AAClC,sBAAA,QAAQ,MAAM,GAAG;AAAA,EACrC;AACA,QAAM,WAAWA,QAAOA,MAAK,KAAK,QAAQ,GAAG,IAAI;AAC3C,QAAA,SAAS,OAAO,OAAO,GAAG;AAChC,MAAI,QAAQ;AACV,YAAQ,QAAQ,UAAU,KAAK,QAAQ,QAAQ;AAAA,EACjD;AACO,SAAA;AACT;AACA,SAAS,QAAQ;AACT,QAAA,SAAS,MAAM,IAAI;AACnB,QAAA,WAAW,OAAO,SAAS;AACjC,QAAM,YAAwD,MAAM,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM;AACxG,QAAA,SAAS,OAAO;AACtB,MAAI,UAAU;AACZ,YAAQ,QAAQ,SAAS,QAAQ,QAAQ,SAAS;AAAA,EACpD;AACO,SAAA;AACT;AACA,SAAS,cAAcH,aAAYC,YAAW;AACrC,SAAA,SAAS,QAAQ,UAAU,SAAS;AACzC,UAAM,WAAW;AACX,UAAA,SAAS,SAAS,SAAS;AAC3B,UAAA,YAAY,MAAM,MAAM;AAC9B,UAAM,OAAOA,aAAY,YAAYD,cAAa,aAAa;AAC/D,KAACA,eAAc,MAAM,WAAW,WAAW,WAAW;AACtD,WAAO,OAAO,QAAQ,CAAC,OAAO,QAAQ;AAC7B,aAAA,SAAS,KAAK,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,GAAG,QAAQ;AAAA,IAAA,CAC/D;AAAA,EAAA;AAEL;AACA,SAAS,qBAAqB,QAAQA,aAAYC,YAAW;AAC3D,SAAO,YAAY,MAAM;AACjB,UAAA,SAAS,KAAK,SAAS;AACvB,UAAA,YAAY,MAAM,MAAM;AACxB,UAAA,cAAc,MAAM,SAAS;AACnC,UAAM,SAAS,WAAW,aAAa,WAAW,OAAO,YAAY;AAC/D,UAAA,YAAY,WAAW,UAAU;AACvC,UAAM,gBAAgB,OAAO,MAAM,EAAE,GAAG,IAAI;AAC5C,UAAM,OAAOA,aAAY,YAAYD,cAAa,aAAa;AAC/D,KAACA,eAAc;AAAA,MACb;AAAA,MACA;AAAA,MACA,YAAY,sBAAsB;AAAA,IAAA;AAE7B,WAAA;AAAA;AAAA,MAEL,OAAO;AACL,cAAM,EAAE,OAAO,KAAK,IAAI,cAAc,KAAK;AAC3C,eAAO,OAAO,EAAE,OAAO,SAAS;AAAA,UAC9B,OAAO,SAAS,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK;AAAA,UAC7D;AAAA,QAAA;AAAA,MAEJ;AAAA;AAAA,MAEA,CAAC,OAAO,QAAQ,IAAI;AACX,eAAA;AAAA,MACT;AAAA,IAAA;AAAA,EACF;AAEJ;AACA,SAAS,qBAAqB,MAAM;AAClC,SAAO,YAAY,MAAM;AACwB;AACvC,YAAA,MAAM,KAAK,CAAC,IAAI,WAAW,KAAK,CAAC,CAAC,OAAO;AAC/C;AAAA,QACE,GAAG,WAAW,IAAI,CAAC,cAAc,GAAG;AAAA,QACpC,MAAM,IAAI;AAAA,MAAA;AAAA,IAEd;AACA,WAAO,SAAS,WAAW,QAAQ,SAAS,UAAU,SAAS;AAAA,EAAA;AAEnE;AACA,SAAS,yBAAyB;AAChC,QAAM,2BAA2B;AAAA,IAC/B,IAAI,KAAK;AACA,aAAA,IAAI,MAAM,GAAG;AAAA,IACtB;AAAA,IACA,IAAI,OAAO;AACT,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,IAAA,KACAE;AAAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,IACA,SAAS,cAAc,OAAO,KAAK;AAAA,EAAA;AAErC,QAAM,2BAA2B;AAAA,IAC/B,IAAI,KAAK;AACP,aAAO,IAAI,MAAM,KAAK,OAAO,IAAI;AAAA,IACnC;AAAA,IACA,IAAI,OAAO;AACT,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,IAAA,KACAA;AAAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,IACA,SAAS,cAAc,OAAO,IAAI;AAAA,EAAA;AAEpC,QAAM,4BAA4B;AAAA,IAChC,IAAI,KAAK;AACA,aAAA,IAAI,MAAM,KAAK,IAAI;AAAA,IAC5B;AAAA,IACA,IAAI,OAAO;AACF,aAAA,KAAK,MAAM,IAAI;AAAA,IACxB;AAAA,IACA,IAAI,KAAK;AACP,aAAOA,MAAI,KAAK,MAAM,KAAK,IAAI;AAAA,IACjC;AAAA,IACA,KAAK,qBAAqB,KAAK;AAAA,IAC/B,KAAK,qBAAqB,KAAK;AAAA,IAC/B,QAAQ,qBAAqB,QAAQ;AAAA,IACrC,OAAO,qBAAqB,OAAO;AAAA,IACnC,SAAS,cAAc,MAAM,KAAK;AAAA,EAAA;AAEpC,QAAM,mCAAmC;AAAA,IACvC,IAAI,KAAK;AACP,aAAO,IAAI,MAAM,KAAK,MAAM,IAAI;AAAA,IAClC;AAAA,IACA,IAAI,OAAO;AACF,aAAA,KAAK,MAAM,IAAI;AAAA,IACxB;AAAA,IACA,IAAI,KAAK;AACP,aAAOA,MAAI,KAAK,MAAM,KAAK,IAAI;AAAA,IACjC;AAAA,IACA,KAAK,qBAAqB,KAAK;AAAA,IAC/B,KAAK,qBAAqB,KAAK;AAAA,IAC/B,QAAQ,qBAAqB,QAAQ;AAAA,IACrC,OAAO,qBAAqB,OAAO;AAAA,IACnC,SAAS,cAAc,MAAM,IAAI;AAAA,EAAA;AAEnC,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EAAA;AAEO,kBAAA,QAAQ,CAAC,WAAW;AAClC,6BAAyB,MAAM,IAAI,qBAAqB,QAAQ,OAAO,KAAK;AAC5E,8BAA0B,MAAM,IAAI,qBAAqB,QAAQ,MAAM,KAAK;AAC5E,6BAAyB,MAAM,IAAI,qBAAqB,QAAQ,OAAO,IAAI;AAC3E,qCAAiC,MAAM,IAAI;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAAA,EACF,CACD;AACM,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AACA,MAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAA2C,uCAAA;AAC3C,SAAS,4BAA4BF,aAAY,SAAS;AACxD,QAAM,mBAAmB,UAAUA,cAAa,kCAAkC,0BAA0BA,cAAa,2BAA2B;AAC7I,SAAA,CAAC,QAAQ,KAAK,aAAa;AAChC,QAAI,QAAQ,kBAAkB;AAC5B,aAAO,CAACA;AAAAA,IAAA,WACC,QAAQ,kBAAkB;AAC5BA,aAAAA;AAAAA,IAAA,WACE,QAAQ,WAAW;AACrB,aAAA;AAAA,IACT;AACA,WAAO,QAAQ;AAAA,MACb,OAAO,kBAAkB,GAAG,KAAK,OAAO,SAAS,mBAAmB;AAAA,MACpE;AAAA,MACA;AAAA,IAAA;AAAA,EACF;AAEJ;AACA,MAAM,4BAA4B;AAAA,EAChC,KAAiD,4CAAA,OAAO,KAAK;AAC/D;AACA,MAAM,4BAA4B;AAAA,EAChC,KAAiD,4CAAA,OAAO,IAAI;AAC9D;AACA,MAAM,6BAA6B;AAAA,EACjC,KAAiD,4CAAA,MAAM,KAAK;AAC9D;AACA,MAAM,oCAAoC;AAAA,EACxC,KAAiD,4CAAA,MAAM,IAAI;AAC7D;AACA,SAAS,kBAAkB,QAAQ,MAAM,KAAK;AACtC,QAAA,SAAS,MAAM,GAAG;AACxB,MAAI,WAAW,OAAO,KAAK,KAAK,QAAQ,MAAM,GAAG;AACzC,UAAA,OAAO,UAAU,MAAM;AAC7B;AAAA,MACE,YAAY,IAAI,kEAAkE,SAAS,QAAQ,aAAa,EAAE;AAAA,IAAA;AAAA,EAEtH;AACF;AAEA,MAAM,kCAAkC;AACxC,MAAM,yCAAyC;AAC/C,MAAM,kCAAkC;AACxC,MAAM,yCAAyC;AAC/C,SAAS,cAAc,SAAS;AAC9B,UAAQ,SAAS;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACI,aAAA;AAAA,IACT;AACS,aAAA;AAAA,EACX;AACF;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,MAAM,UAAU,KAAK,CAAC,OAAO,aAAa,KAAK,IAAI,IAAkB,cAAc,UAAU,KAAK,CAAC;AAC5G;AACA,SAAS,SAAS,QAAQ;AACpB,MAAA,WAAW,MAAM,GAAG;AACf,WAAA;AAAA,EACT;AACO,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AACA,SAAS,gBAAgB,QAAQ;AACxB,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AACA,SAAS,SAAS,QAAQ;AACjB,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AACA,SAAS,gBAAgB,QAAQ;AACxB,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AACA,SAAS,qBAAqB,QAAQ,aAAa,cAAc,oBAAoB,UAAU;AACzF,MAAA,CAACD,WAAS,MAAM,GAAG;AAC0B;AAC7C,aAAO,kCAAkC,OAAO,MAAM,CAAC,EAAE;AAAA,IAC3D;AACO,WAAA;AAAA,EACT;AACA,MAAI,OAAO,SAAS,KAAK,EAAE,eAAe,OAAO,gBAAgB,IAAI;AAC5D,WAAA;AAAA,EACT;AACM,QAAA,gBAAgB,SAAS,IAAI,MAAM;AACzC,MAAI,eAAe;AACV,WAAA;AAAA,EACT;AACM,QAAA,aAAa,cAAc,MAAM;AACvC,MAAI,eAAe,GAAiB;AAC3B,WAAA;AAAA,EACT;AACA,QAAM,QAAQ,IAAI;AAAA,IAChB;AAAA,IACA,eAAe,IAAqB,qBAAqB;AAAA,EAAA;AAElD,WAAA,IAAI,QAAQ,KAAK;AACnB,SAAA;AACT;AACA,SAAS,WAAW,OAAO;AACrB,MAAA,WAAW,KAAK,GAAG;AACd,WAAA,WAAW,MAAM,SAAS,CAAC;AAAA,EACpC;AACA,SAAO,CAAC,EAAE,SAAS,MAAM,gBAAgB;AAC3C;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,CAAC,EAAE,SAAS,MAAM,gBAAgB;AAC3C;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,CAAC,EAAE,SAAS,MAAM,eAAe;AAC1C;AAIA,SAAS,MAAM,UAAU;AACjB,QAAA,MAAM,YAAY,SAAS,SAAS;AACnC,SAAA,MAAM,MAAM,GAAG,IAAI;AAC5B;AACA,SAAS,QAAQ,OAAO;AAClB,MAAA,OAAO,aAAa,KAAK,GAAG;AAC1B,QAAA,OAAO,YAAY,IAAI;AAAA,EAC7B;AACO,SAAA;AACT;AACA,MAAM,aAAa,CAAC,UAAUA,WAAS,KAAK,IAAI,SAAS,KAAK,IAAI;AAClE,MAAM,aAAa,CAAC,UAAUA,WAAS,KAAK,IAAI,SAAS,KAAK,IAAI;AAElE,MAAM,4BAA4B;AAClC,MAAM,gBAAgB;AAAA,EACpB,YAAY,QAAQ,SAASC,aAAY,OAAO;AAC9C,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,gBAAgB,IAAI;AACzB,SAAK,SAAS,IAAI;AAAA,MAChB,MAAM,OAAO,KAAK,MAAM;AAAA,MACxB,MAAM;AAAA,QACJ;AAAA,QACA,KAAK,OAAO,gBAAgB,IAAI,IAAI;AAAA,MACtC;AAAA,IAAA;AAEF,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,SAAS,KAAK,aAAa,CAAC;AACxC,SAAK,gBAAgB,IAAIA;AAAAA,EAC3B;AAAA,EACA,IAAI,QAAQ;AACJ,UAAA,OAAO,MAAM,IAAI;AACvB,SAAK,CAAC,KAAK,cAAc,KAAK,OAAO,UAAU,WAAW,KAAK,QAAQ,KAAK,SAAS,KAAK,OAAO,IAAA,CAAK,GAAG;AACvG,sBAAgB,MAAM,CAAC;AAAA,IACzB;AACA,kBAAc,IAAI;AACd,QAAA,KAAK,OAAO,eAAe,GAAG;AAChC,UAAiD,KAAK,gBAAgB;AACpE,eAAO,2BAA2B;AAAA;AAAA,WAE/B,KAAK,MAAM;AAAA,MAChB;AACA,sBAAgB,MAAM,CAAC;AAAA,IACzB;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,SAAK,QAAQ,QAAQ;AAAA,EACvB;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,OAAO,GAAG;AACZ,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA;AAEF;AACA,SAAS,WAAW,iBAAiB,cAAc,QAAQ,OAAO;AAC5D,MAAA;AACA,MAAA;AACE,QAAA,aAAa,WAAW,eAAe;AAC7C,MAAI,YAAY;AACL,aAAA;AACT,aAAqD,MAAM;AACzD,aAAO,oDAAoD;AAAA,IAAA;AAAA,EACzD,OACC;AACL,aAAS,gBAAgB;AACzB,aAAS,gBAAgB;AAAA,EAC3B;AACM,QAAA,OAAO,IAAI,gBAAgB,QAAQ,QAAQ,cAAc,CAAC,QAAQ,KAAK;AAC5B,MAAA,gBAAgB,CAAC,OAAO;AAClE,SAAA,OAAO,UAAU,aAAa;AAC9B,SAAA,OAAO,YAAY,aAAa;AAAA,EACvC;AACO,SAAA;AACT;AAEA,SAAS,cAAc,MAAM;AACvB,MAAA;AACJ,MAAI,eAAe,cAAc;AAC/B,WAAO,MAAM,IAAI;AACjB;AAAA,MACE;AAAA,OACC,KAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,MAAM;AAAA,QACxC,MAAM,KAAK,MAAM;AAAA,QACjB,gBAAgB,kBAAkB,OAAO;AAAA,MAC3C;AAAA,MAC4C;AAAA,QAC1C,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,KAAK;AAAA,MAAA;AAAA,IACH;AAAA,EAER;AACF;AACA,SAAS,gBAAgB,MAAM,aAAa,GAAG,QAAQ;AACrD,SAAO,MAAM,IAAI;AACjB,QAAM,MAAM,KAAK;AACjB,MAAI,KAAK;AACP;AAAA,MACE;AAAA,MACA;AAAA,MAC4C;AAAA,QAC1C,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,MAAA;AAAA,IACR;AAAA,EAER;AACF;AACA,SAAS,MAAMI,IAAG;AAChB,SAAO,CAAC,EAAEA,MAAKA,GAAE,cAAc;AACjC;AACA,SAAS,IAAI,OAAO;AACX,SAAA,UAAU,OAAO,KAAK;AAC/B;AAIA,SAAS,UAAU,UAAU,SAAS;AAChC,MAAA,MAAM,QAAQ,GAAG;AACZ,WAAA;AAAA,EACT;AACO,SAAA,IAAI,QAAQ,UAAU,OAAO;AACtC;AACA,MAAM,QAAQ;AAAA,EACZ,YAAY,OAAO,eAAe;AAChC,SAAK,gBAAgB;AACrB,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,YAAY,gBAAgB,QAAQ,MAAM,KAAK;AACpD,SAAK,SAAS,gBAAgB,QAAQ,WAAW,KAAK;AAAA,EACxD;AAAA,EACA,IAAI,QAAQ;AACV,kBAAc,IAAI;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,QAAQ;AAChB,UAAM,iBAAiB,KAAK,iBAAiB,UAAU,MAAM,KAAK,WAAW,MAAM;AAC1E,aAAA,iBAAiB,SAAS,MAAM,MAAM;AAC/C,QAAI,WAAW,QAAQ,KAAK,SAAS,GAAG;AACtC,WAAK,YAAY;AACjB,WAAK,SAAS,iBAAiB,SAAS,WAAW,MAAM;AACzC,sBAAA,MAAM,GAAG,MAAM;AAAA,IACjC;AAAA,EACF;AACF;AAIA,SAAS,MAAM,MAAM;AACnB,SAAO,MAAM,IAAI,IAAI,KAAK,QAAQ;AACpC;AAIA,MAAM,wBAAwB;AAAA,EAC5B,KAAK,CAAC,QAAQ,KAAK,aAAa,MAAM,QAAQ,IAAI,QAAQ,KAAK,QAAQ,CAAC;AAAA,EACxE,KAAK,CAAC,QAAQ,KAAK,OAAO,aAAa;AAC/B,UAAA,WAAW,OAAO,GAAG;AAC3B,QAAI,MAAM,QAAQ,KAAK,CAAC,MAAM,KAAK,GAAG;AACpC,eAAS,QAAQ;AACV,aAAA;AAAA,IAAA,OACF;AACL,aAAO,QAAQ,IAAI,QAAQ,KAAK,OAAO,QAAQ;AAAA,IACjD;AAAA,EACF;AACF;AACA,SAAS,UAAU,gBAAgB;AACjC,SAAO,WAAW,cAAc,IAAI,iBAAiB,IAAI,MAAM,gBAAgB,qBAAqB;AACtG;AA4EA,MAAM,QAAQ,CAAA;AACd,SAAS,mBAAmB,OAAO;AACjC,QAAM,KAAK,KAAK;AAClB;AACA,SAAS,oBAAoB;AAC3B,QAAM,IAAI;AACZ;AACA,SAAS,OAAO,QAAQ,MAAM;AACd;AACR,QAAA,WAAW,MAAM,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,YAAY;AACpE,QAAM,iBAAiB,YAAY,SAAS,WAAW,OAAO;AAC9D,QAAM,QAAQ;AACd,MAAI,gBAAgB;AAClB;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,MAAM,KAAK,IAAI,CAAC,MAAM;AACpB,cAAI,IAAI;AACR,kBAAQ,MAAM,KAAK,EAAE,aAAa,OAAO,SAAS,GAAG,KAAK,CAAC,MAAM,OAAO,KAAK,KAAK,UAAU,CAAC;AAAA,QAAA,CAC9F,EAAE,KAAK,EAAE;AAAA,QACV,YAAY,SAAS;AAAA,QACrB,MAAM;AAAA,UACJ,CAAC,EAAE,YAAY,OAAO,oBAAoB,UAAU,MAAM,IAAI,CAAC;AAAA,QAAA,EAC/D,KAAK,IAAI;AAAA,QACX;AAAA,MACF;AAAA,IAAA;AAAA,EACF,OACK;AACL,UAAM,WAAW,CAAC,eAAe,GAAG,IAAI,GAAG,IAAI;AAC/C,QAAI,MAAM;AAAA,IACV,MAAM;AACJ,eAAS,KAAK;AAAA,GACjB,GAAG,YAAY,KAAK,CAAC;AAAA,IACpB;AACQ,YAAA,KAAK,GAAG,QAAQ;AAAA,EAC1B;AACc;AAChB;AACA,SAAS,oBAAoB;AAC3B,MAAI,eAAe,MAAM,MAAM,SAAS,CAAC;AACzC,MAAI,CAAC,cAAc;AACjB,WAAO;EACT;AACA,QAAM,kBAAkB,CAAA;AACxB,SAAO,cAAc;AACb,UAAA,OAAO,gBAAgB,CAAC;AAC1B,QAAA,QAAQ,KAAK,UAAU,cAAc;AAClC,WAAA;AAAA,IAAA,OACA;AACL,sBAAgB,KAAK;AAAA,QACnB,OAAO;AAAA,QACP,cAAc;AAAA,MAAA,CACf;AAAA,IACH;AACA,UAAM,iBAAiB,aAAa,aAAa,aAAa,UAAU;AACxE,mBAAe,kBAAkB,eAAe;AAAA,EAClD;AACO,SAAA;AACT;AACA,SAAS,YAAY,OAAO;AAC1B,QAAM,OAAO,CAAA;AACP,QAAA,QAAQ,CAAC,OAAO,MAAM;AAC1B,SAAK,KAAK,GAAG,MAAM,IAAI,CAAA,IAAK,CAAC;AAAA,CAChC,GAAG,GAAG,iBAAiB,KAAK,CAAC;AAAA,EAAA,CAC3B;AACM,SAAA;AACT;AACA,SAAS,iBAAiB,EAAE,OAAO,gBAAgB;AACjD,QAAM,UAAU,eAAe,IAAI,QAAQ,YAAY,sBAAsB;AAC7E,QAAM,SAAS,MAAM,YAAY,MAAM,UAAU,UAAU,OAAO;AAClE,QAAM,OAAO,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,MAAM;AAAA,IACN;AAAA,EACD,CAAA;AACD,QAAM,QAAQ,MAAM;AACpB,SAAO,MAAM,QAAQ,CAAC,MAAM,GAAG,YAAY,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,OAAO,KAAK;AACjF;AACA,SAAS,YAAY,OAAO;AAC1B,QAAM,MAAM,CAAA;AACN,QAAA,OAAO,OAAO,KAAK,KAAK;AAC9B,OAAK,MAAM,GAAG,CAAC,EAAE,QAAQ,CAAC,QAAQ;AAChC,QAAI,KAAK,GAAG,WAAW,KAAK,MAAM,GAAG,CAAC,CAAC;AAAA,EAAA,CACxC;AACG,MAAA,KAAK,SAAS,GAAG;AACnB,QAAI,KAAK,MAAM;AAAA,EACjB;AACO,SAAA;AACT;AACA,SAAS,WAAW,KAAK,OAAO,KAAK;AAC/B,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,KAAK,UAAU,KAAK;AAC5B,WAAO,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE;AAAA,EAAA,WAC9B,OAAO,UAAU,YAAY,OAAO,UAAU,aAAa,SAAS,MAAM;AACnF,WAAO,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE;AAAA,EAAA,WAC9B,MAAM,KAAK,GAAG;AACvB,YAAQ,WAAW,KAAK,MAAM,MAAM,KAAK,GAAG,IAAI;AAChD,WAAO,MAAM,QAAQ,CAAC,GAAG,GAAG,SAAS,OAAO,GAAG;AAAA,EAAA,WACtC,WAAW,KAAK,GAAG;AACrB,WAAA,CAAC,GAAG,GAAG,MAAM,MAAM,OAAO,IAAI,MAAM,IAAI,MAAM,EAAE,EAAE;AAAA,EAAA,OACpD;AACL,YAAQ,MAAM,KAAK;AACnB,WAAO,MAAM,QAAQ,CAAC,GAAG,GAAG,KAAK,KAAK;AAAA,EACxC;AACF;AAEA,MAAM,mBAAmB;AAAA,EACvB,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,GAAG,GAAG;AAAA,EACP,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,GAAG,GAAG;AAAA,EACP,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,GAAG,GAAG;AAAA,EACP,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,GAAG,GAAG;AAAA,EACP,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AACR;AACA,SAAS,sBAAsB,IAAI,UAAU,MAAM,MAAM;AACnD,MAAA;AACF,WAAO,OAAO,GAAG,GAAG,IAAI,IAAI,GAAG;AAAA,WACxB,KAAK;AACA,gBAAA,KAAK,UAAU,IAAI;AAAA,EACjC;AACF;AACA,SAAS,2BAA2B,IAAI,UAAU,MAAM,MAAM;AACxD,MAAA,WAAW,EAAE,GAAG;AAClB,UAAM,MAAM,sBAAsB,IAAI,UAAU,MAAM,IAAI;AACtD,QAAA,OAAOC,YAAU,GAAG,GAAG;AACrB,UAAA,MAAM,CAAC,QAAQ;AACL,oBAAA,KAAK,UAAU,IAAI;AAAA,MAAA,CAChC;AAAA,IACH;AACO,WAAA;AAAA,EACT;AACA,QAAM,SAAS,CAAA;AACf,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAC3B,WAAA,KAAK,2BAA2B,GAAG,CAAC,GAAG,UAAU,MAAM,IAAI,CAAC;AAAA,EACrE;AACO,SAAA;AACT;AACA,SAAS,YAAY,KAAK,UAAU,MAAM,aAAa,MAAM;AACrD,QAAA,eAAe,WAAW,SAAS,QAAQ;AACjD,MAAI,UAAU;AACZ,QAAI,MAAM,SAAS;AACnB,UAAM,kBAAkB,SAAS;AACjC,UAAM,YAAwD,iBAAiB,IAAI,KAAK;AACxF,WAAO,KAAK;AACV,YAAM,qBAAqB,IAAI;AAC/B,UAAI,oBAAoB;AACtB,iBAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,cAAI,mBAAmB,CAAC,EAAE,KAAK,iBAAiB,SAAS,MAAM,OAAO;AACpE;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,IAAI;AAAA,IACZ;AACM,UAAA,kBAAkB,SAAS,WAAW,OAAO;AACnD,QAAI,iBAAiB;AACnB;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC,KAAK,iBAAiB,SAAS;AAAA,MAAA;AAElC;AAAA,IACF;AAAA,EACF;AACS,WAAA,KAAK,MAAM,cAAc,UAAU;AAC9C;AACA,SAAS,SAAS,KAAK,MAAM,cAAc,aAAa,MAAM;AACb;AACvC,UAAA,OAAO,iBAAiB,IAAI,KAAK;AACvC,QAAI,cAAc;AAChB,yBAAmB,YAAY;AAAA,IACjC;AACA,WAAO,kBAAkB,OAAO,wBAAwB,IAAI,KAAK,EAAE,EAAE;AACrE,QAAI,cAAc;AACE;IACpB;AACA,QAAI,YAAY;AACd,cAAQ,MAAM,GAAG;AAAA,IAAA,OACZ;AACL,cAAQ,MAAM,GAAG;AAAA,IACnB;AAAA,EAGF;AACF;AAEA,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,MAAMC,UAAQ,CAAA;AACd,IAAI,aAAa;AACjB,MAAM,sBAAsB,CAAA;AAC5B,IAAI,qBAAqB;AACzB,IAAI,iBAAiB;AACrB,MAAM,0CAA0C;AAChD,IAAI,sBAAsB;AAC1B,MAAM,kBAAkB;AACxB,SAAS,WAAW,IAAI;AACtB,QAAMC,KAAI,uBAAuB;AAC1B,SAAA,KAAKA,GAAE,KAAK,OAAO,GAAG,KAAK,IAAI,IAAI,EAAE,IAAIA;AAClD;AACA,SAAS,mBAAmB,IAAI;AAC9B,MAAI,QAAQ,aAAa;AACzB,MAAI,MAAMD,QAAM;AAChB,SAAO,QAAQ,KAAK;AACZ,UAAA,SAAS,QAAQ,QAAQ;AACzB,UAAA,YAAYA,QAAM,MAAM;AACxB,UAAA,cAAc,MAAM,SAAS;AACnC,QAAI,cAAc,MAAM,gBAAgB,MAAM,UAAU,KAAK;AAC3D,cAAQ,SAAS;AAAA,IAAA,OACZ;AACC,YAAA;AAAA,IACR;AAAA,EACF;AACO,SAAA;AACT;AACA,SAAS,SAAS,KAAK;AACrB,MAAI,CAACA,QAAM,UAAU,CAACA,QAAM;AAAA,IAC1B;AAAA,IACA,cAAc,IAAI,eAAe,aAAa,IAAI;AAAA,EAAA,GACjD;AACG,QAAA,IAAI,MAAM,MAAM;AAClBA,cAAM,KAAK,GAAG;AAAA,IAAA,OACT;AACLA,cAAM,OAAO,mBAAmB,IAAI,EAAE,GAAG,GAAG,GAAG;AAAA,IACjD;AACW;EACb;AACF;AACA,SAAS,aAAa;AAChB,MAAA,CAAC,cAAc,CAAC,gBAAgB;AACjB,qBAAA;AACK,0BAAA,gBAAgB,KAAK,SAAS;AAAA,EACtD;AACF;AACA,SAAS,YAAY,KAAK;AACjB,SAAAA,QAAM,QAAQ,GAAG,IAAI;AAC9B;AACA,SAAS,cAAc,KAAK;AACpB,QAAA,IAAIA,QAAM,QAAQ,GAAG;AAC3B,MAAI,IAAI,YAAY;AACZA,YAAA,OAAO,GAAG,CAAC;AAAA,EACnB;AACF;AACA,SAAS,iBAAiB,IAAI;AACxB,MAAA,CAAC,QAAQ,EAAE,GAAG;AACZ,QAAA,CAAC,sBAAsB,CAAC,mBAAmB;AAAA,MAC7C;AAAA,MACA,GAAG,eAAe,iBAAiB,IAAI;AAAA,IAAA,GACtC;AACD,0BAAoB,KAAK,EAAE;AAAA,IAC7B;AAAA,EAAA,OACK;AACe,wBAAA,KAAK,GAAG,EAAE;AAAA,EAChC;AACW;AACb;AACA,SAAS,iBAAiB,UAAU,MAAM,IAAI,aAAa,aAAa,IAAI,GAAG;AAC9B;AACtC,WAAA,4BAA4B;EACrC;AACO,SAAA,IAAIA,QAAM,QAAQ,KAAK;AACtB,UAAA,KAAKA,QAAM,CAAC;AACd,QAAA,MAAM,GAAG,KAAK;AACiC,UAAA,sBAAsB,MAAM,EAAE,GAAG;AAChF;AAAA,MACF;AACMA,cAAA,OAAO,GAAG,CAAC;AACjB;AACG;IACL;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,oBAAoB,QAAQ;AAC9B,UAAM,UAAU,CAAC,GAAG,IAAI,IAAI,mBAAmB,CAAC,EAAE;AAAA,MAChD,CAAC,GAAG,MAAM,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,IAAA;AAE9B,wBAAoB,SAAS;AAC7B,QAAI,oBAAoB;AACH,yBAAA,KAAK,GAAG,OAAO;AAClC;AAAA,IACF;AACqB,yBAAA;AAC0B;AACtC,aAAA,4BAA4B;IACrC;AACA,SAAK,iBAAiB,GAAG,iBAAiB,mBAAmB,QAAQ,kBAAkB;AACrF,UAAiD,sBAAsB,MAAM,mBAAmB,cAAc,CAAC,GAAG;AAChH;AAAA,MACF;AACA,yBAAmB,cAAc;IACnC;AACqB,yBAAA;AACJ,qBAAA;AAAA,EACnB;AACF;AACA,MAAM,QAAQ,CAAC,QAAQ,IAAI,MAAM,OAAO,WAAW,IAAI;AACvD,MAAM,aAAa,CAAC,GAAG,MAAM;AAC3B,QAAME,QAAO,MAAM,CAAC,IAAI,MAAM,CAAC;AAC/B,MAAIA,UAAS,GAAG;AACV,QAAA,EAAE,OAAO,CAAC,EAAE;AACP,aAAA;AACL,QAAA,EAAE,OAAO,CAAC,EAAE;AACP,aAAA;AAAA,EACX;AACOA,SAAAA;AACT;AACA,SAAS,UAAU,MAAM;AACN,mBAAA;AACJ,eAAA;AACkC;AACtC,WAAA,4BAA4B;EACrC;AACAF,UAAM,KAAK,UAAU;AACrB,QAAM,QAAoD,CAAC,QAAQ,sBAAsB,MAAM,GAAG;AAC9F,MAAA;AACF,SAAK,aAAa,GAAG,aAAaA,QAAM,QAAQ,cAAc;AACtD,YAAA,MAAMA,QAAM,UAAU;AACxB,UAAA,OAAO,IAAI,WAAW,OAAO;AACkB,YAAA,MAAM,GAAG,GAAG;AAC3D;AAAA,QACF;AACsB,8BAAA,KAAK,MAAM,EAAE;AAAA,MACrC;AAAA,IACF;AAAA,EAAA,UACA;AACa,iBAAA;AACbA,YAAM,SAAS;AACf,sBAAkB,IAAI;AACT,iBAAA;AACS,0BAAA;AAClB,QAAAA,QAAM,UAAU,oBAAoB,QAAQ;AAC9C,gBAAU,IAAI;AAAA,IAChB;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,MAAM,IAAI;AACvC,MAAI,CAAC,KAAK,IAAI,EAAE,GAAG;AACZ,SAAA,IAAI,IAAI,CAAC;AAAA,EAAA,OACT;AACC,UAAA,QAAQ,KAAK,IAAI,EAAE;AACzB,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,WAAW,GAAG;AACpB,YAAM,gBAAgB,YAAY,iBAAiB,SAAS,IAAI;AAChE;AAAA,QACE,qCAAqC,gBAAgB,kBAAkB,aAAa,MAAM,EAAE;AAAA,QAC5F;AAAA,QACA;AAAA,MAAA;AAEK,aAAA;AAAA,IAAA,OACF;AACA,WAAA,IAAI,IAAI,QAAQ,CAAC;AAAA,IACxB;AAAA,EACF;AACF;AAEA,IAAI;AACJ,IAAI,SAAS,CAAA;AACb,IAAI,uBAAuB;AAC3B,SAAS,OAAO,UAAU,MAAM;AAC9B,MAAI,UAAU;AACH,aAAA,KAAK,OAAO,GAAG,IAAI;AAAA,EAAA,WACnB,CAAC,sBAAsB;AAChC,WAAO,KAAK,EAAE,OAAO,KAAM,CAAA;AAAA,EAC7B;AACF;AACA,SAAS,gBAAgB,MAAM,QAAQ;AACrC,MAAI,IAAI;AACG,aAAA;AACX,MAAI,UAAU;AACZ,aAAS,UAAU;AACZ,WAAA,QAAQ,CAAC,EAAE,OAAO,KAAA,MAAW,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC;AACjE,aAAS,CAAA;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,IAKV,OAAO,WAAW;AAAA,IAClB,OAAO;AAAA,IACP,GAAG,MAAM,KAAK,OAAO,cAAc,OAAO,SAAS,GAAG,cAAc,OAAO,SAAS,GAAG,SAAS,OAAO;AAAA,IACvG;AACA,UAAM,SAAS,OAAO,+BAA+B,OAAO,gCAAgC,CAAA;AACrF,WAAA,KAAK,CAAC,YAAY;AACvB,sBAAgB,SAAS,MAAM;AAAA,IAAA,CAChC;AACD,eAAW,MAAM;AACf,UAAI,CAAC,UAAU;AACb,eAAO,+BAA+B;AACf,+BAAA;AACvB,iBAAS,CAAA;AAAA,MACX;AAAA,OACC,GAAG;AAAA,EAAA,OACD;AACkB,2BAAA;AACvB,aAAS,CAAA;AAAA,EACX;AACF;AACA,SAAS,gBAAgB,KAAKG,UAAS;AAC9B,SAAA,YAA2B,KAAKA,UAAS;AAAA,IAC9C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA,CACD;AACH;AACA,MAAM,yBAAyC;AAAA,EAC7C;AAAA;AACF;AACA,MAAM,2BAA2C;AAAA,EAA4B;AAAA;AAA2C;AACxH,MAAM,4BAA4C;AAAA,EAChD;AAAA;AACF;AACA,MAAM,2BAA2B,CAAC,cAAc;AAC1C,MAAA,YAAY,OAAO,SAAS,kBAAkB;AAAA,EAClD,CAAC,SAAS,cAAc,SAAS,GAAG;AAClC,8BAA0B,SAAS;AAAA,EACrC;AACF;AACA;AAAA;AAEA,SAAS,4BAA4B,MAAM;AACzC,SAAO,CAAC,cAAc;AACpB;AAAA,MACE;AAAA,MACA,UAAU,WAAW;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA,MAGV,UAAU,QAAQ,IAAI,SAAS,UAAU,SAAS,UAAU,OAAO,MAAM;AAAA,MACzE;AAAA,IAAA;AAAA,EACF;AAEJ;AACA,MAAM,oBAAoC;AAAA,EACxC;AAAA;AACF;AACA,MAAM,kBAAkC;AAAA,EACtC;AAAA;AACF;AACA,SAAS,8BAA8B,MAAM;AACpC,SAAA,CAAC,WAAW,MAAM,SAAS;AACzB,WAAA,MAAM,UAAU,WAAW,KAAK,UAAU,KAAK,WAAW,MAAM,IAAI;AAAA,EAAA;AAE/E;AACA,SAAS,sBAAsB,WAAW,OAAO,QAAQ;AACvD;AAAA,IACE;AAAA,IACA,UAAU,WAAW;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AAEA,SAAS,KAAK,UAAU,UAAU,SAAS;AACzC,MAAI,SAAS;AACX;AACI,QAAA,QAAQ,SAAS,MAAM,SAAS;AACS;AACvC,UAAA;AAAA,MACJ;AAAA,MACA,cAAc,CAAC,YAAY;AAAA,IACzB,IAAA;AACJ,QAAI,cAAc;AACZ,UAAA,EAAE,SAAS,iBAAiB,MAAM;AACpC,YAAI,CAAC,gBAAgB,EAAE,aAAa,KAAK,KAAK,eAAe;AAC3D;AAAA,YACE,4BAA4B,KAAK,+DAA+D,aAAa,KAAK,CAAC;AAAA,UAAA;AAAA,QAEvH;AAAA,MAAA,OACK;AACC,cAAA,YAAY,aAAa,KAAK;AAChC,YAAA,WAAW,SAAS,GAAG;AACnB,gBAAA,UAAU,UAAU,GAAG,OAAO;AACpC,cAAI,CAAC,SAAS;AACZ;AAAA,cACE,+DAA+D,KAAK;AAAA,YAAA;AAAA,UAExE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO;AACLC,QAAAA,mBAAkB,MAAM,WAAW,SAAS;AAClD,QAAM,WAAWA,oBAAmB,MAAM,MAAM,CAAC;AAC7C,MAAA,YAAY,YAAY,OAAO;AACjC,UAAM,eAAe,GAAG,aAAa,eAAe,UAAU,QAAQ;AACtE,UAAM,EAAE,QAAQ,KAAA,IAAS,MAAM,YAAY,KAAK;AAChD,QAAI,MAAM;AACD,aAAA,QAAQ,IAAI,CAAC,MAAM,SAAS,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC;AAAA,IACtD;AACA,QAAI,QAAQ;AACH,aAAA,QAAQ,IAAI,aAAa;AAAA,IAClC;AAAA,EACF;AACwE;AAChD,0BAAA,UAAU,OAAO,IAAI;AAAA,EAC7C;AAC+C;AACvC,UAAA,iBAAiB,MAAM;AAC7B,QAAI,mBAAmB,SAAS,MAAM,aAAa,cAAc,CAAC,GAAG;AACnE;AAAA,QACE,UAAU,cAAc,6BAA6B;AAAA,UACnD;AAAA,UACA,SAAS;AAAA,QAAA,CACV,uCAAuC,KAAK,iKAAiK;AAAA,UAC5M;AAAA,QAAA,CACD,iBAAiB,KAAK;AAAA,MAAA;AAAA,IAE3B;AAAA,EACF;AACI,MAAA;AACJ,MAAI,UAAU,MAAM,cAAc,aAAa,KAAK,CAAC;AAAA,EACrD,MAAM,cAAc,aAAa,SAAS,KAAK,CAAC,CAAC;AAC7C,MAAA,CAAC,WAAWA,kBAAiB;AAC/B,cAAU,MAAM,cAAc,aAAa,UAAU,KAAK,CAAC,CAAC;AAAA,EAC9D;AACA,MAAI,SAAS;AACX;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAAA,EAEJ;AACM,QAAA,cAAc,MAAM,cAAc,MAAM;AAC9C,MAAI,aAAa;AACX,QAAA,CAAC,SAAS,SAAS;AACrB,eAAS,UAAU;IACV,WAAA,SAAS,QAAQ,WAAW,GAAG;AACxC;AAAA,IACF;AACS,aAAA,QAAQ,WAAW,IAAI;AAChC;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAAA,EAEJ;AACF;AACA,SAAS,sBAAsB,MAAM,YAAY,UAAU,OAAO;AAChE,QAAM,QAAQ,WAAW;AACnB,QAAA,SAAS,MAAM,IAAI,IAAI;AAC7B,MAAI,WAAW,QAAQ;AACd,WAAA;AAAA,EACT;AACA,QAAM,MAAM,KAAK;AACjB,MAAI,aAAa,CAAA;AACjB,MAAI,aAAa;AACU,MAAA,CAAC,WAAW,IAAI,GAAG;AACtC,UAAA,cAAc,CAAC,SAAS;AAC5B,YAAM,uBAAuB,sBAAsB,MAAM,YAAY,IAAI;AACzE,UAAI,sBAAsB;AACX,qBAAA;AACb,eAAO,YAAY,oBAAoB;AAAA,MACzC;AAAA,IAAA;AAEF,QAAI,CAAC,WAAW,WAAW,OAAO,QAAQ;AAC7B,iBAAA,OAAO,QAAQ,WAAW;AAAA,IACvC;AACA,QAAI,KAAK,SAAS;AAChB,kBAAY,KAAK,OAAO;AAAA,IAC1B;AACA,QAAI,KAAK,QAAQ;AACV,WAAA,OAAO,QAAQ,WAAW;AAAA,IACjC;AAAA,EACF;AACI,MAAA,CAAC,OAAO,CAAC,YAAY;AACnB,QAAAX,WAAS,IAAI,GAAG;AACZ,YAAA,IAAI,MAAM,IAAI;AAAA,IACtB;AACO,WAAA;AAAA,EACT;AACI,MAAA,QAAQ,GAAG,GAAG;AAChB,QAAI,QAAQ,CAAC,QAAQ,WAAW,GAAG,IAAI,IAAI;AAAA,EAAA,OACtC;AACL,WAAO,YAAY,GAAG;AAAA,EACxB;AACI,MAAAA,WAAS,IAAI,GAAG;AACZ,UAAA,IAAI,MAAM,UAAU;AAAA,EAC5B;AACO,SAAA;AACT;AACA,SAAS,eAAe,SAAS,KAAK;AACpC,MAAI,CAAC,WAAW,CAAC,KAAK,GAAG,GAAG;AACnB,WAAA;AAAA,EACT;AACA,QAAM,IAAI,MAAM,CAAC,EAAE,QAAQ,SAAS,EAAE;AAC/B,SAAA,OAAO,SAAS,IAAI,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC,CAAC,KAAK,OAAO,SAAS,UAAU,GAAG,CAAC,KAAK,OAAO,SAAS,GAAG;AACvH;AAEA,IAAI,2BAA2B;AAE/B,SAAS,4BAA4B,UAAU;AAC7C,QAAM,OAAO;AACc,6BAAA;AACV,cAAY,SAAS,KAAK,aAAa;AACjD,SAAA;AACT;AAiHA,MAAM,wBAAwB,CAAA;AAC9B,SAAS,MAAM,QAAQ,IAAI,SAAS;AACe,MAAA,CAAC,WAAW,EAAE,GAAG;AAChE;AAAA,MACE;AAAA,IAAA;AAAA,EAEJ;AACO,SAAA,QAAQ,QAAQ,IAAI,OAAO;AACpC;AACA,SAAS,QAAQ,QAAQ,IAAI;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAAY;AAAA,EACA;AAAA,EACA;AACF,IAAI,WAAW;AACb,MAAI,MAAMA,OAAM;AACd,UAAM,MAAM;AACZ,SAAK,IAAI,SAAS;AAChB,UAAI,GAAG,IAAI;AACH;IAAA;AAAA,EAEZ;AACA,MAAiD,SAAS,UAAU,OAAO,SAAS,UAAU;AAC5F;AAAA,MACE;AAAA,IAAA;AAAA,EAEJ;AACA,MAAiD,CAAC,IAAI;AACpD,QAAI,cAAc,QAAQ;AACxB;AAAA,QACE;AAAA,MAAA;AAAA,IAEJ;AACA,QAAI,SAAS,QAAQ;AACnB;AAAA,QACE;AAAA,MAAA;AAAA,IAEJ;AACA,QAAIA,UAAS,QAAQ;AACnB;AAAA,QACE;AAAA,MAAA;AAAA,IAEJ;AAAA,EACF;AACM,QAAA,oBAAoB,CAACC,OAAM;AAC/B;AAAA,MACE;AAAA,MACAA;AAAAA,MACA;AAAA,IAAA;AAAA,EACF;AAEF,QAAM,WAAW;AACjB,QAAM,iBAAiB,CAAC,YAAY,SAAS,OAAO;AAAA;AAAA,IAElD,SAAS,SAAS,SAAS,QAAQ,IAAI,MAAM;AAAA;AAE3C,MAAA;AACJ,MAAI,eAAe;AACnB,MAAI,gBAAgB;AAChB,MAAA,MAAM,MAAM,GAAG;AACjB,aAAS,MAAM,OAAO;AACtB,mBAAe,UAAU,MAAM;AAAA,EAAA,WACtB,WAAW,MAAM,GAAG;AACpB,aAAA,MAAM,eAAe,MAAM;AACrB,mBAAA;AAAA,EAAA,WACN,QAAQ,MAAM,GAAG;AACV,oBAAA;AACD,mBAAA,OAAO,KAAK,CAACA,OAAM,WAAWA,EAAC,KAAK,UAAUA,EAAC,CAAC;AAC/D,aAAS,MAAM,OAAO,IAAI,CAACA,OAAM;AAC3B,UAAA,MAAMA,EAAC,GAAG;AACZ,eAAOA,GAAE;AAAA,MAAA,WACA,WAAWA,EAAC,GAAG;AACxB,eAAO,eAAeA,EAAC;AAAA,MAAA,WACd,WAAWA,EAAC,GAAG;AACjB,eAAA,sBAAsBA,IAAG,UAAU,CAAC;AAAA,MAAA,OACtC;AACwC,0BAAkBA,EAAC;AAAA,MAClE;AAAA,IAAA,CACD;AAAA,EAAA,WACQ,WAAW,MAAM,GAAG;AAC7B,QAAI,IAAI;AACN,eAAS,MAAM,sBAAsB,QAAQ,UAAU,CAAC;AAAA,IAAA,OACnD;AACL,eAAS,MAAM;AACb,YAAI,SAAS;AACH;QACV;AACO,eAAA;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA,CAAC,SAAS;AAAA,QAAA;AAAA,MACZ;AAAA,IAEJ;AAAA,EAAA,OACK;AACI,aAAA;AACoC,sBAAkB,MAAM;AAAA,EACvE;AACA,MAAI,MAAM,MAAM;AACd,UAAM,aAAa;AACV,aAAA,MAAM,SAAS,WAAA,CAAY;AAAA,EACtC;AACI,MAAA;AACA,MAAA,YAAY,CAAC,OAAO;AACZhB,cAAAA,QAAO,SAAS,MAAM;AACR,4BAAA,IAAI,UAAU,CAAC;AACrC,gBAAUA,QAAO,SAAS;AAAA,IAAA;AAAA,EAC5B;AAEE,MAAA,WAAW,gBAAgB,IAAI,MAAM,OAAO,MAAM,EAAE,KAAK,qBAAqB,IAAI;AACtF,QAAM,MAAM,MAAM;AAChB,QAAI,CAACA,QAAO,UAAU,CAACA,QAAO,OAAO;AACnC;AAAA,IACF;AACA,QAAI,IAAI;AACA,YAAA,WAAWA,QAAO;AACxB,UAAI,QAAQ,iBAAiB,gBAAgB,SAAS,KAAK,CAAC,GAAG,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,WAAW,UAAU,QAAQ,MAAM,OAAO;AAC3I,YAAI,SAAS;AACH;QACV;AAC2B,mCAAA,IAAI,UAAU,GAAG;AAAA,UAC1C;AAAA;AAAA,UAEA,aAAa,wBAAwB,SAAS,iBAAiB,SAAS,CAAC,MAAM,wBAAwB,CAAA,IAAK;AAAA,UAC5G;AAAA,QAAA,CACD;AACU,mBAAA;AAAA,MACb;AAAA,IAAA,OACK;AACLA,cAAO,IAAI;AAAA,IACb;AAAA,EAAA;AAEE,MAAA,eAAe,CAAC,CAAC;AACjB,MAAA;AACJ,MAAI,UAAU,QAAQ;AACR,gBAAA;AAAA,EAAA,WACH,UAAU,QAAQ;AAC3B,gBAAY,MAAM,wBAAwB,KAAK,YAAY,SAAS,QAAQ;AAAA,EAAA,OACvE;AACL,QAAI,MAAM;AACN,QAAA;AACF,UAAI,KAAK,SAAS;AACR,gBAAA,MAAM,SAAS,GAAG;AAAA,EAChC;AACA,QAAMA,UAAS,IAAI,eAAe,QAAQ,MAAM,SAAS;AACzD,QAAM,QAAQ;AACd,QAAM,UAAU,MAAM;AACpBA,YAAO,KAAK;AACZ,QAAI,OAAO;AACF,aAAA,MAAM,SAASA,OAAM;AAAA,IAC9B;AAAA,EAAA;AAE6C;AAC7CA,YAAO,UAAU;AACjBA,YAAO,YAAY;AAAA,EACrB;AACA,MAAI,IAAI;AACN,QAAI,WAAW;AACT;IAAA,OACC;AACL,iBAAWA,QAAO;IACpB;AAAA,EAAA,WACS,UAAU,QAAQ;AAC3B;AAAA,MACEA,QAAO,IAAI,KAAKA,OAAM;AAAA,MACtB,YAAY,SAAS;AAAA,IAAA;AAAA,EACvB,OACK;AACLA,YAAO,IAAI;AAAA,EACb;AACO,SAAA;AACT;AACA,SAAS,cAAc,QAAQ,OAAO,SAAS;AAC7C,QAAM,aAAa,KAAK;AACxB,QAAM,SAAS,SAAS,MAAM,IAAI,OAAO,SAAS,GAAG,IAAI,iBAAiB,YAAY,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,OAAO,KAAK,YAAY,UAAU;AACzJ,MAAA;AACA,MAAA,WAAW,KAAK,GAAG;AAChB,SAAA;AAAA,EAAA,OACA;AACL,SAAK,MAAM;AACD,cAAA;AAAA,EACZ;AACM,QAAA,QAAQ,mBAAmB,IAAI;AACrC,QAAM,MAAM,QAAQ,QAAQ,GAAG,KAAK,UAAU,GAAG,OAAO;AAClD;AACC,SAAA;AACT;AACA,SAAS,iBAAiB,KAAK,MAAM;AAC7B,QAAA,WAAW,KAAK,MAAM,GAAG;AAC/B,SAAO,MAAM;AACX,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,SAAS,UAAU,KAAK,KAAK;AACzC,YAAA,IAAI,SAAS,CAAC,CAAC;AAAA,IACvB;AACO,WAAA;AAAA,EAAA;AAEX;AACA,SAAS,SAAS,OAAO,OAAO,eAAe,GAAG,MAAM;AACtD,MAAI,CAACG,WAAS,KAAK,KAAK,MAAM,UAAU,GAAG;AAClC,WAAA;AAAA,EACT;AACI,MAAA,SAAS,QAAQ,GAAG;AACtB,QAAI,gBAAgB,OAAO;AAClB,aAAA;AAAA,IACT;AACA;AAAA,EACF;AACO,SAAA,4BAA4B;AAC/B,MAAA,KAAK,IAAI,KAAK,GAAG;AACZ,WAAA;AAAA,EACT;AACA,OAAK,IAAI,KAAK;AACV,MAAA,MAAM,KAAK,GAAG;AAChB,aAAS,MAAM,OAAO,OAAO,cAAc,IAAI;AAAA,EAAA,WACtC,QAAQ,KAAK,GAAG;AACzB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,eAAS,MAAM,CAAC,GAAG,OAAO,cAAc,IAAI;AAAA,IAC9C;AAAA,aACS,MAAM,KAAK,KAAK,MAAM,KAAK,GAAG;AACjC,UAAA,QAAQ,CAAC,MAAM;AACV,eAAA,GAAG,OAAO,cAAc,IAAI;AAAA,IAAA,CACtC;AAAA,EAAA,WACQ,cAAc,KAAK,GAAG;AAC/B,eAAW,OAAO,OAAO;AACvB,eAAS,MAAM,GAAG,GAAG,OAAO,cAAc,IAAI;AAAA,IAChD;AAAA,EACF;AACO,SAAA;AACT;AAEA,SAAS,sBAAsB,MAAM;AAC/B,MAAA,mBAAmB,IAAI,GAAG;AAC5B,WAAO,+DAA+D,IAAI;AAAA,EAC5E;AACF;AAiCA,SAAS,mBAAmB;AACnB,SAAA;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,aAAa;AAAA,MACb,kBAAkB,CAAC;AAAA,MACnB,uBAAuB,CAAC;AAAA,MACxB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB,CAAC;AAAA,IACpB;AAAA,IACA,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,IACb,UAAiC,uBAAA,OAAO,IAAI;AAAA,IAC5C,kCAAkC,QAAQ;AAAA,IAC1C,gCAAgC,QAAQ;AAAA,IACxC,gCAAgC,QAAQ;AAAA,EAAA;AAE5C;AACA,IAAI,QAAQ;AACZ,SAAS,aAAa,QAAQ,SAAS;AACrC,SAAO,SAASc,WAAU,eAAe,YAAY,MAAM;AACrD,QAAA,CAAC,WAAW,aAAa,GAAG;AACd,sBAAA,OAAO,IAAI,aAAa;AAAA,IAC1C;AACA,QAAI,aAAa,QAAQ,CAACd,WAAS,SAAS,GAAG;AACA,aAAO,qDAAqD;AAC7F,kBAAA;AAAA,IACd;AACA,UAAM,UAAU;AACV,UAAA,uCAAuC;AACvC,UAAA,MAAM,QAAQ,MAAM;AAAA,MACxB,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,MACX;AAAA,MACA,IAAI,SAAS;AACX,eAAO,QAAQ;AAAA,MACjB;AAAA,MACA,IAAI,OAAO,GAAG;AACmC;AAC7C;AAAA,YACE;AAAA,UAAA;AAAA,QAEJ;AAAA,MACF;AAAA,MACA,IAAIe,YAAW,SAAS;AAClB,YAAA,iBAAiB,IAAIA,OAAM,GAAG;AACa,iBAAO,gDAAgD;AAAA,QAC3FA,WAAAA,WAAU,WAAWA,QAAO,OAAO,GAAG;AAC/C,2BAAiB,IAAIA,OAAM;AAC3BA,kBAAO,QAAQ,KAAK,GAAG,OAAO;AAAA,QAAA,WACrB,WAAWA,OAAM,GAAG;AAC7B,2BAAiB,IAAIA,OAAM;AAC3BA,kBAAO,KAAK,GAAG,OAAO;AAAA,QAAA,OAC8B;AACpD;AAAA,YACE;AAAA,UAAA;AAAA,QAEJ;AACO,eAAA;AAAA,MACT;AAAA,MACA,MAAM,OAAO;AACc;AACvB,cAAI,CAAC,QAAQ,OAAO,SAAS,KAAK,GAAG;AAC3B,oBAAA,OAAO,KAAK,KAAK;AAAA,UAAA,OAC2B;AACpD;AAAA,cACE,kDAAkD,MAAM,OAAO,KAAK,MAAM,IAAI,KAAK;AAAA,YAAA;AAAA,UAEvF;AAAA,QAGF;AACO,eAAA;AAAA,MACT;AAAA,MACA,UAAU,MAAM,WAAW;AACsB;AACvB,gCAAA,MAAM,QAAQ,MAAM;AAAA,QAC5C;AACA,YAAI,CAAC,WAAW;AACP,iBAAA,QAAQ,WAAW,IAAI;AAAA,QAChC;AACiD,YAAA,QAAQ,WAAW,IAAI,GAAG;AAClE,iBAAA,cAAc,IAAI,8CAA8C;AAAA,QACzE;AACQ,gBAAA,WAAW,IAAI,IAAI;AACpB,eAAA;AAAA,MACT;AAAA,MACA,UAAU,MAAM,WAAW;AACsB;AAC7C,gCAAsB,IAAI;AAAA,QAC5B;AACA,YAAI,CAAC,WAAW;AACP,iBAAA,QAAQ,WAAW,IAAI;AAAA,QAChC;AACiD,YAAA,QAAQ,WAAW,IAAI,GAAG;AAClE,iBAAA,cAAc,IAAI,8CAA8C;AAAA,QACzE;AACQ,gBAAA,WAAW,IAAI,IAAI;AACpB,eAAA;AAAA,MACT;AAAA;AAAA,MAEA,QAAQ;AAAA,MACR;AAAA;AAAA,MAEA,UAAU;AAAA,MACV;AAAA,MACA,QAAQ,KAAK,OAAO;AAC+B,YAAA,OAAO,QAAQ,UAAU;AACxE;AAAA,YACE,2CAA2C,OAAO,GAAG,CAAC;AAAA,UAAA;AAAA,QAE1D;AACQ,gBAAA,SAAS,GAAG,IAAI;AACjB,eAAA;AAAA,MACT;AAAA,MACA,eAAe,IAAI;AACjB,cAAM,UAAU;AACH,qBAAA;AACT,YAAA;AACF,iBAAO,GAAG;AAAA,QAAA,UACV;AACa,uBAAA;AAAA,QACf;AAAA,MACF;AAAA,IAAA;AAEK,WAAA;AAAA,EAAA;AAEX;AACA,IAAI,aAAa;AAEjB,SAAS,QAAQ,KAAK,OAAO;AAC3B,MAAI,CAAC,iBAAiB;AAC2B;AAC7C,aAAO,4CAA4C;AAAA,IACrD;AAAA,EAAA,OACK;AACL,QAAI,WAAW,gBAAgB;AAC/B,UAAM,iBAAiB,gBAAgB,UAAU,gBAAgB,OAAO;AACxE,QAAI,mBAAmB,UAAU;AAC/B,iBAAW,gBAAgB,WAAW,OAAO,OAAO,cAAc;AAAA,IACpE;AACA,aAAS,GAAG,IAAI;AACZ,QAAA,gBAAgB,KAAK,WAAW,OAAO;AACzC,sBAAgB,WAAW,IAAI,QAAQ,KAAK,KAAK;AAAA,IACnD;AAAA,EACF;AACF;AACA,SAAS,OAAO,KAAK,cAAc,wBAAwB,OAAO;AAChE,QAAM,WAAW,mBAAmB;AACpC,MAAI,YAAY,YAAY;AAC1B,UAAM,WAAW,WAAW,SAAS,UAAU,OAAO,SAAS,MAAM,cAAc,SAAS,MAAM,WAAW,WAAW,SAAS,OAAO,WAAW,WAAW,SAAS;AACnK,QAAA,YAAY,OAAO,UAAU;AAC/B,aAAO,SAAS,GAAG;AAAA,IAAA,WACV,UAAU,SAAS,GAAG;AACxB,aAAA,yBAAyB,WAAW,YAAY,IAAI,aAAa,KAAK,YAAY,SAAS,KAAK,IAAI;AAAA,IAAA,OACvD;AACpD,aAAO,cAAc,OAAO,GAAG,CAAC,cAAc;AAAA,IAChD;AAAA,EAAA,OACoD;AACpD,WAAO,oEAAoE;AAAA,EAC7E;AACF;AAeA,MAAM,cAAc,CAAC,UAAU,MAAM,KAAK;AAC1C,SAAS,YAAY,MAAM,QAAQ;AACX,wBAAA,MAAM,KAAK,MAAM;AACzC;AACA,SAAS,cAAc,MAAM,QAAQ;AACb,wBAAA,MAAM,MAAM,MAAM;AAC1C;AACA,SAAS,sBAAsB,MAAM,MAAM,SAAS,iBAAiB;AACnE,QAAM,cAAc,KAAK,UAAU,KAAK,QAAQ,MAAM;AACpD,QAAI,UAAU;AACd,WAAO,SAAS;AACd,UAAI,QAAQ,eAAe;AACzB;AAAA,MACF;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO,KAAK;AAAA,EAAA;AAEH,aAAA,MAAM,aAAa,MAAM;AACpC,MAAI,QAAQ;AACV,QAAI,UAAU,OAAO;AACd,WAAA,WAAW,QAAQ,QAAQ;AAChC,UAAI,YAAY,QAAQ,OAAO,KAAK,GAAG;AACf,8BAAA,aAAa,MAAM,QAAQ,OAAO;AAAA,MAC1D;AACA,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,MAAM,MAAM,QAAQ,eAAe;AAChE,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,EAAA;AAGF,cAAY,MAAM;AACT,WAAA,cAAc,IAAI,GAAG,QAAQ;AAAA,KACnC,MAAM;AACX;AAEA,SAAS,WAAW,MAAM,MAAM,SAAS,iBAAiB,UAAU,OAAO;AACzE,MAAI,QAAQ;AACN,QAAA,WAAW,IAAI,GAAG;AACpB,eAAS,OAAO;AAAA,IAClB;AACA,UAAM,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI,CAAA;AAC9C,UAAM,cAAc,KAAK,UAAU,KAAK,QAAQ,IAAI,SAAS;AAC3D,UAAI,OAAO,aAAa;AACtB;AAAA,MACF;AACc;AACR,YAAA,QAAQ,mBAAmB,MAAM;AACvC,YAAM,MAAM,2BAA2B,MAAM,QAAQ,MAAM,IAAI;AACzD;AACQ;AACP,aAAA;AAAA,IAAA;AAET,QAAI,SAAS;AACX,YAAM,QAAQ,WAAW;AAAA,IAAA,OACpB;AACL,YAAM,KAAK,WAAW;AAAA,IACxB;AACO,WAAA;AAAA,EAAA,OAC6C;AACpD,UAAM,UAAU;AAAA,OACb,iBAAiB,IAAI,KAAK,KAAK,QAAQ,OAAO,EAAE,GAAG,QAAQ,UAAU,EAAE;AAAA,IAAA;AAE1E;AAAA,MACE,GAAG,OAAO;AAAA,IAAA;AAAA,EAEd;AACF;AACA,MAAM,aAAa,CAAC,cAAc,CAAC,MAAM,SAAS;AAAA;AAAA,GAE/C,CAAC,yBAAyB,cAAc,SAAS,WAAW,WAAW,IAAI,SAAS,KAAK,GAAG,IAAI,GAAG,MAAM;AAAA;AAE5G,MAAM,gBAAgB,WAAW,IAAI;AACrC,MAAM,YAAY,WAAW,GAAG;AAChC,MAAM,iBAAiB,WAAW,IAAI;AACtC,MAAM,YAAY,WAAW,GAAG;AAChC,MAAM,kBAAkB,WAAW,KAAK;AACxC,MAAM,cAAc,WAAW,IAAI;AACnC,MAAM,mBAAmB,WAAW,IAAI;AACxC,MAAM,oBAAoB;AAAA,EACxB;AACF;AACA,MAAM,kBAAkB;AAAA,EACtB;AACF;AACA,SAAS,gBAAgB,MAAM,SAAS,iBAAiB;AAC5C,aAAA,MAAM,MAAM,MAAM;AAC/B;AAcA,MAAM,oBAAoB,CAAC,MAAM;AAC/B,MAAI,CAAC;AACI,WAAA;AACT,MAAI,oBAAoB,CAAC;AAChB,WAAA,eAAe,CAAC,KAAK,EAAE;AACzB,SAAA,kBAAkB,EAAE,MAAM;AACnC;AACA,MAAM;AAAA;AAAA;AAAA,EAGmC,uBAAA,uBAAO,OAAO,IAAI,GAAG;AAAA,IAC1D,GAAG,CAAC,MAAM;AAAA;AAAA;AAAA,IAGV,KAAK,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ;IAClC,OAAO,CAAC,MAAM,EAAE;AAAA,IAChB,QAAQ,CAAC,MAAkD,gBAAgB,EAAE,KAAK;AAAA,IAClF,QAAQ,CAAC,MAAkD,gBAAgB,EAAE,KAAK;AAAA,IAClF,QAAQ,CAAC,MAAkD,gBAAgB,EAAE,KAAK;AAAA,IAClF,OAAO,CAAC,MAAkD,gBAAgB,EAAE,IAAI;AAAA,IAChF,SAAS,CAAC,MAAM,kBAAkB,EAAE,MAAM;AAAA,IAC1C,OAAO,CAAC,MAAM,kBAAkB,EAAE,IAAI;AAAA,IACtC,OAAO,CAAC,MAAM,EAAE;AAAA,IAChB,UAAU,CAAC,MAA4B,qBAAqB,CAAC;AAAA,IAC7D,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,MAAM;AACvC,QAAE,OAAO,QAAQ;AACjB,eAAS,EAAE,MAAM;AAAA,IAAA;AAAA;AAAA,IAGnB,QAAQ,CAAC,MAA4B,cAAc,KAAK,CAAC;AAAA,EAAI,CAC9D;AAAA;AAEH,MAAM,mBAAmB,CAAC,QAAQ,QAAQ,OAAO,QAAQ;AACzD,MAAM,kBAAkB,CAAC,OAAO,QAAQ,UAAU,aAAa,CAAC,MAAM,mBAAmB,OAAO,OAAO,GAAG;AAC1G,MAAM,8BAA8B;AAAA,EAClC,IAAI,EAAE,GAAG,SAAA,GAAY,KAAK;AAClB,UAAA,EAAE,KAAK,YAAY,MAAM,OAAO,aAAa,MAAM,WAAe,IAAA;AACxE,QAAiD,QAAQ,WAAW;AAC3D,aAAA;AAAA,IACT;AACI,QAAA;AACA,QAAA,IAAI,CAAC,MAAM,KAAK;AACZC,YAAAA,KAAI,YAAY,GAAG;AACzB,UAAIA,OAAM,QAAQ;AAChB,gBAAQA,IAAG;AAAA,UACT,KAAK;AACH,mBAAO,WAAW,GAAG;AAAA,UACvB,KAAK;AACH,mBAAO,KAAK,GAAG;AAAA,UACjB,KAAK;AACH,mBAAO,IAAI,GAAG;AAAA,UAChB,KAAK;AACH,mBAAO,MAAM,GAAG;AAAA,QACpB;AAAA,MACS,WAAA,gBAAgB,YAAY,GAAG,GAAG;AAC3C,oBAAY,GAAG,IAAI;AACnB,eAAO,WAAW,GAAG;AAAA,MAAA,WACZ,SAAS,aAAa,OAAO,MAAM,GAAG,GAAG;AAClD,oBAAY,GAAG,IAAI;AACnB,eAAO,KAAK,GAAG;AAAA,MAAA;AAAA;AAAA;AAAA,SAId,kBAAkB,SAAS,aAAa,CAAC,MAAM,OAAO,iBAAiB,GAAG;AAAA,QAC3E;AACA,oBAAY,GAAG,IAAI;AACnB,eAAO,MAAM,GAAG;AAAA,MAAA,WACP,QAAQ,aAAa,OAAO,KAAK,GAAG,GAAG;AAChD,oBAAY,GAAG,IAAI;AACnB,eAAO,IAAI,GAAG;AAAA,iBACmB,mBAAmB;AACpD,oBAAY,GAAG,IAAI;AAAA,MACrB;AAAA,IACF;AACM,UAAA,eAAe,oBAAoB,GAAG;AAC5C,QAAI,WAAW;AACf,QAAI,cAAc;AAChB,UAAI,QAAQ,UAAU;AACd,cAAA,UAAU,OAAO,GAAG;AAAA,MACqC,WACT,QAAQ,UAAU;AAClE,cAAA,UAAU,OAAO,GAAG;AAAA,MAC5B;AACA,aAAO,aAAa,QAAQ;AAAA,IAAA;AAAA;AAAA,OAG3B,YAAY,KAAK,kBAAkB,YAAY,UAAU,GAAG;AAAA,MAC7D;AACO,aAAA;AAAA,IAAA,WACE,QAAQ,aAAa,OAAO,KAAK,GAAG,GAAG;AAChD,kBAAY,GAAG,IAAI;AACnB,aAAO,IAAI,GAAG;AAAA,IAAA;AAAA;AAAA,MAGd,mBAAmB,WAAW,OAAO,kBAAkB,OAAO,kBAAkB,GAAG;AAAA,MACnF;AACA;AACE,eAAO,iBAAiB,GAAG;AAAA,MAC7B;AAAA,IACsD,WAAA,6BAA6B,CAAC,SAAS,GAAG;AAAA;AAAA,IAElG,IAAI,QAAQ,KAAK,MAAM,IAAI;AACrB,UAAA,SAAS,aAAa,iBAAiB,IAAI,CAAC,CAAC,KAAK,OAAO,MAAM,GAAG,GAAG;AACvE;AAAA,UACE,YAAY,KAAK;AAAA,YACf;AAAA,UACD,CAAA;AAAA,QAAA;AAAA,MACH,WACS,aAAa,0BAA0B;AAChD;AAAA,UACE,YAAY,KAAK,UAAU,GAAG,CAAC;AAAA,QAAA;AAAA,MAEnC;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,EAAE,GAAG,SAAS,GAAG,KAAK,OAAO;AAC/B,UAAM,EAAE,MAAM,YAAY,IAAA,IAAQ;AAC9B,QAAA,gBAAgB,YAAY,GAAG,GAAG;AACpC,iBAAW,GAAG,IAAI;AACX,aAAA;AAAA,IAAA,WAC+C,WAAW,mBAAmB,OAAO,YAAY,GAAG,GAAG;AACtG,aAAA,yCAAyC,GAAG,qBAAqB;AACjE,aAAA;AAAA,IAAA,WACE,SAAS,aAAa,OAAO,MAAM,GAAG,GAAG;AAClD,WAAK,GAAG,IAAI;AACL,aAAA;AAAA,IACE,WAAA,OAAO,SAAS,OAAO,GAAG,GAAG;AACO,aAAO,8BAA8B,GAAG,wBAAwB;AACtG,aAAA;AAAA,IACT;AACI,QAAA,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,CAAC,KAAK,UAAU;AACD;AAAA,QAC3C,yCAAyC,GAAG;AAAA,MAAA;AAEvC,aAAA;AAAA,IAAA,OACF;AACL,UAAiD,OAAO,SAAS,WAAW,OAAO,kBAAkB;AAC5F,eAAA,eAAe,KAAK,KAAK;AAAA,UAC9B,YAAY;AAAA,UACZ,cAAc;AAAA,UACd;AAAA,QAAA,CACD;AAAA,MAAA,OACI;AACL,YAAI,GAAG,IAAI;AAAA,MACb;AAAA,IACF;AACO,WAAA;AAAA,EACT;AAAA,EACA,IAAI;AAAA,IACF,GAAG,EAAE,MAAM,YAAY,aAAa,KAAK,YAAY,aAAa;AAAA,KACjE,KAAK;AACF,QAAA;AACJ,WAAO,CAAC,CAAC,YAAY,GAAG,KAAK,SAAS,aAAa,OAAO,MAAM,GAAG,KAAK,gBAAgB,YAAY,GAAG,MAAM,kBAAkB,aAAa,CAAC,MAAM,OAAO,iBAAiB,GAAG,KAAK,OAAO,KAAK,GAAG,KAAK,OAAO,qBAAqB,GAAG,KAAK,OAAO,WAAW,OAAO,kBAAkB,GAAG;AAAA,EAC3R;AAAA,EACA,eAAe,QAAQ,KAAK,YAAY;AAClC,QAAA,WAAW,OAAO,MAAM;AACnB,aAAA,EAAE,YAAY,GAAG,IAAI;AAAA,IACnB,WAAA,OAAO,YAAY,OAAO,GAAG;AACtC,WAAK,IAAI,QAAQ,KAAK,WAAW,OAAO,IAAI;AAAA,IAC9C;AACA,WAAO,QAAQ,eAAe,QAAQ,KAAK,UAAU;AAAA,EACvD;AACF;AACuD;AACzB,8BAAA,UAAU,CAAC,WAAW;AAChD;AAAA,MACE;AAAA,IAAA;AAEK,WAAA,QAAQ,QAAQ,MAAM;AAAA,EAAA;AAEjC;AACA,SAAS,uBAAuB,UAAU;AACxC,QAAM,SAAS,CAAA;AACR,SAAA,eAAe,QAAQ,KAAK;AAAA,IACjC,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,KAAK,MAAM;AAAA,EAAA,CACZ;AACD,SAAO,KAAK,mBAAmB,EAAE,QAAQ,CAAC,QAAQ;AACzC,WAAA,eAAe,QAAQ,KAAK;AAAA,MACjC,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,KAAK,MAAM,oBAAoB,GAAG,EAAE,QAAQ;AAAA;AAAA;AAAA,MAG5C,KAAK;AAAA,IAAA,CACN;AAAA,EAAA,CACF;AACM,SAAA;AACT;AACA,SAAS,2BAA2B,UAAU;AACtC,QAAA;AAAA,IACJ;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,EACzB,IAAA;AACJ,MAAI,cAAc;AAChB,WAAO,KAAK,YAAY,EAAE,QAAQ,CAAC,QAAQ;AAClC,aAAA,eAAe,KAAK,KAAK;AAAA,QAC9B,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,KAAK,MAAM,SAAS,MAAM,GAAG;AAAA,QAC7B,KAAK;AAAA,MAAA,CACN;AAAA,IAAA,CACF;AAAA,EACH;AACF;AACA,SAAS,gCAAgC,UAAU;AAC3C,QAAA,EAAE,KAAK,WAAe,IAAA;AAC5B,SAAO,KAAK,MAAM,UAAU,CAAC,EAAE,QAAQ,CAAC,QAAQ;AAC1C,QAAA,CAAC,WAAW,iBAAiB;AAC/B,UAAI,iBAAiB,IAAI,CAAC,CAAC,GAAG;AAC5B;AAAA,UACE,2BAA2B,KAAK;AAAA,YAC9B;AAAA,UACD,CAAA;AAAA,QAAA;AAEH;AAAA,MACF;AACO,aAAA,eAAe,KAAK,KAAK;AAAA,QAC9B,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,KAAK,MAAM,WAAW,GAAG;AAAA,QACzB,KAAK;AAAA,MAAA,CACN;AAAA,IACH;AAAA,EAAA,CACD;AACH;AAyCA,SAAS,sBAAsB,OAAO;AAC7B,SAAA,QAAQ,KAAK,IAAI,MAAM;AAAA,IAC5B,CAAC,YAAYR,QAAO,WAAWA,EAAC,IAAI,MAAM;AAAA,IAC1C,CAAC;AAAA,EACC,IAAA;AACN;AA6DA,SAAS,yBAAyB;AAC1B,QAAA,QAA+B,uBAAA,OAAO,IAAI;AACzC,SAAA,CAAC,MAAM,QAAQ;AAChB,QAAA,MAAM,GAAG,GAAG;AACP,aAAA,GAAG,IAAI,cAAc,GAAG,2BAA2B,MAAM,GAAG,CAAC,GAAG;AAAA,IAAA,OAClE;AACL,YAAM,GAAG,IAAI;AAAA,IACf;AAAA,EAAA;AAEJ;AACA,IAAI,oBAAoB;AACxB,SAAS,eAAe,UAAU;AAC1B,QAAA,UAAU,qBAAqB,QAAQ;AAC7C,QAAM,aAAa,SAAS;AAC5B,QAAM,MAAM,SAAS;AACD,sBAAA;AACpB,MAAI,QAAQ,cAAc;AACfS,eAAA,QAAQ,cAAc,UAAU,IAAI;AAAA,EAC/C;AACM,QAAA;AAAA;AAAA,IAEJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV;AAAA,IACA,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA;AAAA,IAER;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,EACE,IAAA;AACE,QAAA,2BAAuE,uBAAA;AAC9B;AACvC,UAAA,CAAC,YAAY,IAAI,SAAS;AAChC,QAAI,cAAc;AAChB,iBAAW,OAAO,cAAc;AAC9B,iCAAyB,SAAqB,GAAG;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACA,WAAS,iBAAiB;AACxB,QAAI,eAAe;AACC,wBAAA,eAAe,KAAK,wBAAwB;AAAA,IAChE;AAAA,EACF;AAC+B;AACd;EACjB;AACA,MAAI,SAAS;AACX,eAAW,OAAO,SAAS;AACnB,YAAA,gBAAgB,QAAQ,GAAG;AAC7B,UAAA,WAAW,aAAa,GAAG;AACkB;AACtC,iBAAA,eAAe,KAAK,KAAK;AAAA,YAC9B,OAAO,cAAc,KAAK,UAAU;AAAA,YACpC,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,UAAA,CACX;AAAA,QAGH;AAC+C;AAC7C,mCAAyB,WAAyB,GAAG;AAAA,QACvD;AAAA,MAAA,OACoD;AACpD;AAAA,UACE,WAAW,GAAG,eAAe,OAAO,aAAa;AAAA,QAAA;AAAA,MAErD;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa;AACkC,QAAA,CAAC,WAAW,WAAW,GAAG;AACzE;AAAA,QACE;AAAA,MAAA;AAAA,IAEJ;AACA,UAAM,OAAO,YAAY,KAAK,YAAY,UAAU;AACH,QAAAX,YAAU,IAAI,GAAG;AAChE;AAAA,QACE;AAAA,MAAA;AAAA,IAEJ;AACI,QAAA,CAACN,WAAS,IAAI,GAAG;AAC0B,aAAO,iCAAiC;AAAA,IAAA,OAChF;AACI,eAAA,OAAO,SAAS,IAAI;AACkB;AAC7C,mBAAW,OAAO,MAAM;AACtB,mCAAyB,QAAmB,GAAG;AAC/C,cAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,GAAG;AACtB,mBAAA,eAAe,KAAK,KAAK;AAAA,cAC9B,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,KAAK,MAAM,KAAK,GAAG;AAAA,cACnB,KAAK;AAAA,YAAA,CACN;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACoB,sBAAA;AACpB,MAAI,iBAAiB;AACnB,eAAW,OAAO,iBAAiB;AAC3B,YAAA,MAAM,gBAAgB,GAAG;AAC/B,YAAMkB,QAAM,WAAW,GAAG,IAAI,IAAI,KAAK,YAAY,UAAU,IAAI,WAAW,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,YAAY,UAAU,IAAI;AAC9H,UAAiDA,UAAQ,MAAM;AACtD,eAAA,sBAAsB,GAAG,kBAAkB;AAAA,MACpD;AACA,YAAMC,OAAM,CAAC,WAAW,GAAG,KAAK,WAAW,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,UAAU,IAAgD,MAAM;AACjI;AAAA,UACE,8CAA8C,GAAG;AAAA,QAAA;AAAA,MACnD;AAEF,YAAMC,KAAI,SAAS;AAAA,QACjB,KAAAF;AAAAA,QACA,KAAAC;AAAAA,MAAA,CACD;AACM,aAAA,eAAe,KAAK,KAAK;AAAA,QAC9B,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,KAAK,MAAMC,GAAE;AAAA,QACb,KAAK,CAAC,MAAMA,GAAE,QAAQ;AAAA,MAAA,CACvB;AAC8C;AAC7C,iCAAyB,YAA2B,GAAG;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AACA,MAAI,cAAc;AAChB,eAAW,OAAO,cAAc;AAC9B,oBAAc,aAAa,GAAG,GAAG,KAAK,YAAY,GAAG;AAAA,IACvD;AAAA,EACF;AACA,WAAS,eAAe;AACtB,QAAI,gBAAgB;AAClB,YAAM,WAAW,WAAW,cAAc,IAAI,eAAe,KAAK,UAAU,IAAI;AAChF,cAAQ,QAAQ,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AACjC,gBAAA,KAAK,SAAS,GAAG,CAAC;AAAA,MAAA,CAC3B;AAAA,IACH;AAAA,EACF;AAC+B;AAChB;EACf;AAoBO;AACL,QAAI,SAAS;AACFH,iBAAA,SAAS,UAAU,GAAG;AAAA,IACjC;AAAA,EACF;AACS,WAAA,sBAAsBI,WAAU,MAAM;AACzC,QAAA,QAAQ,IAAI,GAAG;AACZ,WAAA,QAAQ,CAAC,UAAUA,UAAS,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,eAC/C,MAAM;AACN,MAAAA,UAAA,KAAK,KAAK,UAAU,CAAC;AAAA,IAChC;AAAA,EACF;AACA,wBAAsB,eAAe,WAAW;AAChD,wBAAsB,WAAW,OAAO;AACxC,wBAAsB,gBAAgB,YAAY;AAClD,wBAAsB,WAAW,OAAO;AACxC,wBAAsB,aAAa,SAAS;AAC5C,wBAAsB,eAAe,WAAW;AAChD,wBAAsB,iBAAiB,aAAa;AACpD,wBAAsB,iBAAiB,aAAa;AACpD,wBAAsB,mBAAmB,eAAe;AACxD,wBAAsB,iBAAiB,aAAa;AACpD,wBAAsB,aAAa,SAAS;AAC5C,wBAAsB,kBAAkB,cAAc;AAClD,MAAA,QAAQ,MAAM,GAAG;AACnB,QAAI,OAAO,QAAQ;AACjB,YAAM,UAAU,SAAS,YAAY,SAAS,UAAU,CAAA;AACjD,aAAA,QAAQ,CAAC,QAAQ;AACf,eAAA,eAAe,SAAS,KAAK;AAAA,UAClC,KAAK,MAAM,WAAW,GAAG;AAAA,UACzB,KAAK,CAAC,QAAQ,WAAW,GAAG,IAAI;AAAA,QAAA,CACjC;AAAA,MAAA,CACF;AAAA,IAAA,WACQ,CAAC,SAAS,SAAS;AAC5B,eAAS,UAAU;IACrB;AAAA,EACF;AACI,MAAA,UAAU,SAAS,WAAW,MAAM;AACtC,aAAS,SAAS;AAAA,EACpB;AACA,MAAI,gBAAgB,MAAM;AACxB,aAAS,eAAe;AAAA,EAC1B;AACI,MAAA;AACF,aAAS,aAAa;AACpB,MAAA;AACF,aAAS,aAAa;AACpB,MAAA,SAAS,IAAI,iBAAiB;AAChC,aAAS,IAAI,gBAAgB,SAAS,UAAU,UAAU;AAAA,EAC5D;AACF;AACA,SAAS,kBAAkB,eAAe,KAAK,2BAA2B,MAAM;AAC1E,MAAA,QAAQ,aAAa,GAAG;AAC1B,oBAAgB,gBAAgB,aAAa;AAAA,EAC/C;AACA,aAAW,OAAO,eAAe;AACzB,UAAA,MAAM,cAAc,GAAG;AACzB,QAAA;AACA,QAAArB,WAAS,GAAG,GAAG;AACjB,UAAI,aAAa,KAAK;AACT,mBAAA;AAAA,UACT,IAAI,QAAQ;AAAA,UACZ,IAAI;AAAA,UACJ;AAAA,QAAA;AAAA,MACF,OACK;AACM,mBAAA,OAAO,IAAI,QAAQ,GAAG;AAAA,MACnC;AAAA,IAAA,OACK;AACL,iBAAW,OAAO,GAAG;AAAA,IACvB;AACI,QAAA,MAAM,QAAQ,GAAG;AACZ,aAAA,eAAe,KAAK,KAAK;AAAA,QAC9B,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,KAAK,MAAM,SAAS;AAAA,QACpB,KAAK,CAAC,MAAM,SAAS,QAAQ;AAAA,MAAA,CAC9B;AAAA,IAAA,OACI;AACL,UAAI,GAAG,IAAI;AAAA,IACb;AAC+C;AAC7C,+BAAyB,UAAuB,GAAG;AAAA,IACrD;AAAA,EACF;AACF;AACA,SAASiB,WAAS,MAAM,UAAU,MAAM;AACtC;AAAA,IACE,QAAQ,IAAI,IAAI,KAAK,IAAI,CAACK,OAAMA,GAAE,KAAK,SAAS,KAAK,CAAC,IAAI,KAAK,KAAK,SAAS,KAAK;AAAA,IAClF;AAAA,IACA;AAAA,EAAA;AAEJ;AACA,SAAS,cAAc,KAAK,KAAK,YAAY,KAAK;AAC1C,QAAA,SAAS,IAAI,SAAS,GAAG,IAAI,iBAAiB,YAAY,GAAG,IAAI,MAAM,WAAW,GAAG;AACvF,MAAA,SAAS,GAAG,GAAG;AACX,UAAA,UAAU,IAAI,GAAG;AACnB,QAAA,WAAW,OAAO,GAAG;AACvB,YAAM,QAAQ,OAAO;AAAA,IAAA,OAC+B;AAC7C,aAAA,2CAA2C,GAAG,KAAK,OAAO;AAAA,IACnE;AAAA,EAAA,WACS,WAAW,GAAG,GAAG;AAC1B,UAAM,QAAQ,IAAI,KAAK,UAAU,CAAC;AAAA,EAAA,WACzBtB,WAAS,GAAG,GAAG;AACpB,QAAA,QAAQ,GAAG,GAAG;AACZ,UAAA,QAAQ,CAACK,OAAM,cAAcA,IAAG,KAAK,YAAY,GAAG,CAAC;AAAA,IAAA,OACpD;AACL,YAAM,UAAU,WAAW,IAAI,OAAO,IAAI,IAAI,QAAQ,KAAK,UAAU,IAAI,IAAI,IAAI,OAAO;AACpF,UAAA,WAAW,OAAO,GAAG;AACjB,cAAA,QAAQ,SAAS,GAAG;AAAA,MAAA,OAC0B;AACpD,eAAO,2CAA2C,IAAI,OAAO,KAAK,OAAO;AAAA,MAC3E;AAAA,IACF;AAAA,EAAA,OACoD;AAC7C,WAAA,0BAA0B,GAAG,KAAK,GAAG;AAAA,EAC9C;AACF;AACA,SAAS,qBAAqB,UAAU;AACtC,QAAM,OAAO,SAAS;AACtB,QAAM,EAAE,QAAQ,SAAS,eAAA,IAAmB;AACtC,QAAA;AAAA,IACJ,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,QAAQ,EAAE,sBAAsB;AAAA,EAAA,IAC9B,SAAS;AACP,QAAA,SAAS,MAAM,IAAI,IAAI;AACzB,MAAA;AACJ,MAAI,QAAQ;AACC,eAAA;AAAA,EAAA,WACF,CAAC,aAAa,UAAU,CAAC,UAAU,CAAC,gBAAgB;AAC7D;AACa,iBAAA;AAAA,IACb;AAAA,EAAA,OACK;AACL,eAAW,CAAA;AACX,QAAI,aAAa,QAAQ;AACV,mBAAA;AAAA,QACX,CAACkB,OAAM,aAAa,UAAUA,IAAG,uBAAuB,IAAI;AAAA,MAAA;AAAA,IAEhE;AACa,iBAAA,UAAU,MAAM,qBAAqB;AAAA,EACpD;AACI,MAAAvB,WAAS,IAAI,GAAG;AACZ,UAAA,IAAI,MAAM,QAAQ;AAAA,EAC1B;AACO,SAAA;AACT;AACA,SAAS,aAAa,IAAI,MAAM,QAAQ,UAAU,OAAO;AACvD,QAAM,EAAE,QAAQ,SAAS,eAAA,IAAmB;AAC5C,MAAI,gBAAgB;AACL,iBAAA,IAAI,gBAAgB,QAAQ,IAAI;AAAA,EAC/C;AACA,MAAI,QAAQ;AACH,WAAA;AAAA,MACL,CAACuB,OAAM,aAAa,IAAIA,IAAG,QAAQ,IAAI;AAAA,IAAA;AAAA,EAE3C;AACA,aAAW,OAAO,MAAM;AAClB,QAAA,WAAW,QAAQ,UAAU;AACc;AAAA,QAC3C;AAAA,MAAA;AAAA,IACF,OACK;AACL,YAAM,QAAQ,0BAA0B,GAAG,KAAK,UAAU,OAAO,GAAG;AACpE,SAAG,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG;AAAA,IACxD;AAAA,EACF;AACO,SAAA;AACT;AACA,MAAM,4BAA4B;AAAA,EAChC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA;AAAA,EAEP,SAAS;AAAA,EACT,UAAU;AAAA;AAAA,EAEV,cAAc;AAAA,EACd,SAAS;AAAA,EACT,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,SAAS;AAAA,EACT,eAAe;AAAA,EACf,eAAe;AAAA,EACf,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,gBAAgB;AAAA;AAAA,EAEhB,YAAY;AAAA,EACZ,YAAY;AAAA;AAAA,EAEZ,OAAO;AAAA;AAAA,EAEP,SAAS;AAAA,EACT,QAAQ;AACV;AACA,SAAS,YAAY,IAAI,MAAM;AAC7B,MAAI,CAAC,MAAM;AACF,WAAA;AAAA,EACT;AACA,MAAI,CAAC,IAAI;AACA,WAAA;AAAA,EACT;AACA,SAAO,SAAS,eAAe;AACrB,WAAA;AAAA,MACN,WAAW,EAAE,IAAI,GAAG,KAAK,MAAM,IAAI,IAAI;AAAA,MACvC,WAAW,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,IAAI;AAAA,IAAA;AAAA,EAC7C;AAEJ;AACA,SAAS,YAAY,IAAI,MAAM;AAC7B,SAAO,mBAAmB,gBAAgB,EAAE,GAAG,gBAAgB,IAAI,CAAC;AACtE;AACA,SAAS,gBAAgB,KAAK;AACxB,MAAA,QAAQ,GAAG,GAAG;AAChB,UAAM,MAAM,CAAA;AACZ,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC;AAAA,IACrB;AACO,WAAA;AAAA,EACT;AACO,SAAA;AACT;AACA,SAAS,eAAe,IAAI,MAAM;AAChC,SAAO,KAAK,CAAC,GAAG,IAAI,IAAI,CAAG,EAAA,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI;AAClD;AACA,SAAS,mBAAmB,IAAI,MAAM;AAC7B,SAAA,KAAK,OAA8B,uBAAA,OAAO,IAAI,GAAG,IAAI,IAAI,IAAI;AACtE;AACA,SAAS,yBAAyB,IAAI,MAAM;AAC1C,MAAI,IAAI;AACN,QAAI,QAAQ,EAAE,KAAK,QAAQ,IAAI,GAAG;AACzB,aAAA,CAAC,GAAmB,oBAAI,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;AAAA,IACtD;AACO,WAAA;AAAA,MACW,uBAAO,OAAO,IAAI;AAAA,MAClC,sBAAsB,EAAE;AAAA,MACxB,sBAAsB,QAAQ,OAAO,OAAO,CAAA,CAAE;AAAA,IAAA;AAAA,EAChD,OACK;AACE,WAAA;AAAA,EACT;AACF;AACA,SAAS,kBAAkB,IAAI,MAAM;AACnC,MAAI,CAAC;AACI,WAAA;AACT,MAAI,CAAC;AACI,WAAA;AACT,QAAM,SAAS,OAAuB,uBAAO,OAAO,IAAI,GAAG,EAAE;AAC7D,aAAW,OAAO,MAAM;AACf,WAAA,GAAG,IAAI,eAAe,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC;AAAA,EACjD;AACO,SAAA;AACT;AAEA,SAASC,YAAU,UAAU,UAAU,YAAY,QAAQ,OAAO;AAChE,QAAM,QAAQ,CAAA;AACd,QAAM,QAAQ,CAAA;AACL,WAAA,gBAAuC,uBAAA,OAAO,IAAI;AAC9C,eAAA,UAAU,UAAU,OAAO,KAAK;AAC7C,aAAW,OAAO,SAAS,aAAa,CAAC,GAAG;AACtC,QAAA,EAAE,OAAO,QAAQ;AACnB,YAAM,GAAG,IAAI;AAAA,IACf;AAAA,EACF;AAC+C;AAC7C,kBAAc,YAAY,CAAA,GAAI,OAAO,QAAQ;AAAA,EAC/C;AACA,MAAI,YAAY;AACd,aAAS,QAAQ,QAAQ,QAAQ,gBAAgB,KAAK;AAAA,EAAA,OACjD;AACD,QAAA,CAAC,SAAS,KAAK,OAAO;AACxB,eAAS,QAAQ;AAAA,IAAA,OACZ;AACL,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AACA,WAAS,QAAQ;AACnB;AACA,SAAS,eAAe,UAAU;AAClC;AACA,SAAS,YAAY,UAAU,UAAU,cAAc,WAAW;AAC1D,QAAA;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO,EAAE,UAAU;AAAA,EACjB,IAAA;AACE,QAAA,kBAAkB,MAAM,KAAK;AAC7B,QAAA,CAAC,OAAO,IAAI,SAAS;AAC3B,MAAI,kBAAkB;AACtB;AAAA;AAAA;AAAA;AAAA,IAIE,CAA+C,eAAe,MAAO,aAAa,YAAY,MAAM,EAAE,YAAY;AAAA,IAClH;AACA,QAAI,YAAY,GAAG;AACX,YAAA,gBAAgB,SAAS,MAAM;AACrC,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AACzC,YAAA,MAAM,cAAc,CAAC;AACzB,YAAI,eAAe,SAAS,cAAc,GAAG,GAAG;AAC9C;AAAA,QACF;AACM,cAAA,QAAQ,SAAS,GAAG;AAC1B,YAAI,SAAS;AACP,cAAA,OAAO,OAAO,GAAG,GAAG;AAClB,gBAAA,UAAU,MAAM,GAAG,GAAG;AACxB,oBAAM,GAAG,IAAI;AACK,gCAAA;AAAA,YACpB;AAAA,UAAA,OACK;AACC,kBAAA,eAAe,SAAS,GAAG;AACjC,kBAAM,YAAY,IAAIC;AAAAA,cACpB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YAAA;AAAA,UAEJ;AAAA,QAAA,OACK;AACD,cAAA,UAAU,MAAM,GAAG,GAAG;AACxB,kBAAM,GAAG,IAAI;AACK,8BAAA;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EAAA,OACK;AACL,QAAI,aAAa,UAAU,UAAU,OAAO,KAAK,GAAG;AAChC,wBAAA;AAAA,IACpB;AACI,QAAA;AACJ,eAAW,OAAO,iBAAiB;AACjC,UAAI,CAAC;AAAA,MACL,CAAC,OAAO,UAAU,GAAG;AAAA;AAAA,QAEnB,WAAW,UAAU,GAAG,OAAO,OAAO,CAAC,OAAO,UAAU,QAAQ,IAAI;AACpE,YAAI,SAAS;AACP,cAAA;AAAA,WACH,aAAa,GAAG,MAAM;AAAA,UACvB,aAAa,QAAQ,MAAM,SAAS;AAClC,kBAAM,GAAG,IAAIA;AAAAA,cACX;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YAAA;AAAA,UAEJ;AAAA,QAAA,OACK;AACL,iBAAO,MAAM,GAAG;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,QAAI,UAAU,iBAAiB;AAC7B,iBAAW,OAAO,OAAO;AACvB,YAAI,CAAC,YAAY,CAAC,OAAO,UAAU,GAAG,KAAK,MAAM;AAC/C,iBAAO,MAAM,GAAG;AACE,4BAAA;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,iBAAiB;AACX,YAAA,UAAU,OAAO,QAAQ;AAAA,EACnC;AAC+C;AAC7C,kBAAc,YAAY,CAAA,GAAI,OAAO,QAAQ;AAAA,EAC/C;AACF;AACA,SAAS,aAAa,UAAU,UAAU,OAAO,OAAO;AACtD,QAAM,CAAC,SAAS,YAAY,IAAI,SAAS;AACzC,MAAI,kBAAkB;AAClB,MAAA;AACJ,MAAI,UAAU;AACZ,aAAS,OAAO,UAAU;AACpB,UAAA,eAAe,GAAG,GAAG;AACvB;AAAA,MACF;AACM,YAAA,QAAQ,SAAS,GAAG;AACtB,UAAA;AACJ,UAAI,WAAW,OAAO,SAAS,WAAW,SAAS,GAAG,CAAC,GAAG;AACxD,YAAI,CAAC,gBAAgB,CAAC,aAAa,SAAS,QAAQ,GAAG;AACrD,gBAAM,QAAQ,IAAI;AAAA,QAAA,OACb;AACL,WAAC,kBAAkB,gBAAgB,CAAA,IAAK,QAAQ,IAAI;AAAA,QACtD;AAAA,iBACS,CAAC,eAAe,SAAS,cAAc,GAAG,GAAG;AACtD,YAAI,EAAE,OAAO,UAAU,UAAU,MAAM,GAAG,GAAG;AAC3C,gBAAM,GAAG,IAAI;AACK,4BAAA;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,cAAc;AACV,UAAA,kBAAkB,MAAM,KAAK;AACnC,UAAM,aAAa,iBAAiB;AACpC,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AACtC,YAAA,MAAM,aAAa,CAAC;AAC1B,YAAM,GAAG,IAAIA;AAAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW,GAAG;AAAA,QACd;AAAA,QACA,CAAC,OAAO,YAAY,GAAG;AAAA,MAAA;AAAA,IAE3B;AAAA,EACF;AACO,SAAA;AACT;AACA,SAASA,mBAAiB,SAAS,OAAO,KAAK,OAAO,UAAU,UAAU;AAClE,QAAA,MAAM,QAAQ,GAAG;AACvB,MAAI,OAAO,MAAM;AACT,UAAA,aAAa,OAAO,KAAK,SAAS;AACpC,QAAA,cAAc,UAAU,QAAQ;AAClC,YAAM,eAAe,IAAI;AACrB,UAAA,IAAI,SAAS,YAAY,CAAC,IAAI,eAAe,WAAW,YAAY,GAAG;AACnE,cAAA,EAAE,cAAkB,IAAA;AAC1B,YAAI,OAAO,eAAe;AACxB,kBAAQ,cAAc,GAAG;AAAA,QAAA,OACpB;AACC,gBAAA,QAAQ,mBAAmB,QAAQ;AACjC,kBAAA,cAAc,GAAG,IAAI,aAAa;AAAA,YACxC;AAAA,YACA;AAAA,UAAA;AAEI;QACR;AAAA,MAAA,OACK;AACG,gBAAA;AAAA,MACV;AAAA,IACF;AACI,QAAA;AAAA,MAAI;AAAA;AAAA,IAAA,GAAqB;AACvB,UAAA,YAAY,CAAC,YAAY;AACnB,gBAAA;AAAA,MACC,WAAA;AAAA,QAAI;AAAA;AAAA,MAAA,MAA4B,UAAU,MAAM,UAAU,UAAU,GAAG,IAAI;AAC5E,gBAAA;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACO,SAAA;AACT;AACA,SAAS,sBAAsB,MAAM,YAAY,UAAU,OAAO;AAChE,QAAM,QAAQ,WAAW;AACnB,QAAA,SAAS,MAAM,IAAI,IAAI;AAC7B,MAAI,QAAQ;AACH,WAAA;AAAA,EACT;AACA,QAAM,MAAM,KAAK;AACjB,QAAM,aAAa,CAAA;AACnB,QAAM,eAAe,CAAA;AACrB,MAAI,aAAa;AACU,MAAA,CAAC,WAAW,IAAI,GAAG;AACtC,UAAA,cAAc,CAAC,SAAS;AACf,mBAAA;AACb,YAAM,CAAC,OAAO,IAAI,IAAI,sBAAsB,MAAM,YAAY,IAAI;AAClE,aAAO,YAAY,KAAK;AACpB,UAAA;AACW,qBAAA,KAAK,GAAG,IAAI;AAAA,IAAA;AAE7B,QAAI,CAAC,WAAW,WAAW,OAAO,QAAQ;AAC7B,iBAAA,OAAO,QAAQ,WAAW;AAAA,IACvC;AACA,QAAI,KAAK,SAAS;AAChB,kBAAY,KAAK,OAAO;AAAA,IAC1B;AACA,QAAI,KAAK,QAAQ;AACV,WAAA,OAAO,QAAQ,WAAW;AAAA,IACjC;AAAA,EACF;AACI,MAAA,CAAC,OAAO,CAAC,YAAY;AACnB,QAAAzB,WAAS,IAAI,GAAG;AACZ,YAAA,IAAI,MAAM,SAAS;AAAA,IAC3B;AACO,WAAA;AAAA,EACT;AACI,MAAA,QAAQ,GAAG,GAAG;AAChB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAiD,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG;AAC3D,eAAA,kDAAkD,IAAI,CAAC,CAAC;AAAA,MACjE;AACA,YAAM,gBAAgB,SAAS,IAAI,CAAC,CAAC;AACjC,UAAA,iBAAiB,aAAa,GAAG;AACnC,mBAAW,aAAa,IAAI;AAAA,MAC9B;AAAA,IACF;AAAA,aACS,KAAK;AACmC,QAAA,CAACA,WAAS,GAAG,GAAG;AAC/D,aAAO,yBAAyB,GAAG;AAAA,IACrC;AACA,eAAW,OAAO,KAAK;AACf,YAAA,gBAAgB,SAAS,GAAG;AAC9B,UAAA,iBAAiB,aAAa,GAAG;AAC7B,cAAA,MAAM,IAAI,GAAG;AACnB,cAAM,OAAO,WAAW,aAAa,IAAI,QAAQ,GAAG,KAAK,WAAW,GAAG,IAAI,EAAE,MAAM,IAAA,IAAQ,OAAO,CAAA,GAAI,GAAG;AACzG,YAAI,MAAM;AACR,gBAAM,eAAe,aAAa,SAAS,KAAK,IAAI;AACpD,gBAAM,cAAc,aAAa,QAAQ,KAAK,IAAI;AAClD;AAAA,YAAK;AAAA;AAAA,UAAA,IAAsB,eAAe;AAC1C;AAAA,YAAK;AAAA;AAAA,UAAsB,IAAI,cAAc,KAAK,eAAe;AACjE,cAAI,eAAe,MAAM,OAAO,MAAM,SAAS,GAAG;AAChD,yBAAa,KAAK,aAAa;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACM,QAAA,MAAM,CAAC,YAAY,YAAY;AACjC,MAAAA,WAAS,IAAI,GAAG;AACZ,UAAA,IAAI,MAAM,GAAG;AAAA,EACrB;AACO,SAAA;AACT;AACA,SAAS,iBAAiB,KAAK;AAC7B,MAAI,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe,GAAG,GAAG;AACnC,WAAA;AAAA,EAAA,OAC6C;AAC7C,WAAA,uBAAuB,GAAG,2BAA2B;AAAA,EAC9D;AACO,SAAA;AACT;AACA,SAAS0B,UAAQ,MAAM;AACrB,MAAI,SAAS,MAAM;AACV,WAAA;AAAA,EACT;AACI,MAAA,OAAO,SAAS,YAAY;AAC9B,WAAO,KAAK,QAAQ;AAAA,EAAA,WACX,OAAO,SAAS,UAAU;AACnC,UAAM,OAAO,KAAK,eAAe,KAAK,YAAY;AAClD,WAAO,QAAQ;AAAA,EACjB;AACO,SAAA;AACT;AACA,SAAS,WAAW,GAAG,GAAG;AACxB,SAAOA,UAAQ,CAAC,MAAMA,UAAQ,CAAC;AACjC;AACA,SAAS,aAAa,MAAM,eAAe;AACrC,MAAA,QAAQ,aAAa,GAAG;AAC1B,WAAO,cAAc,UAAU,CAACC,OAAM,WAAWA,IAAG,IAAI,CAAC;AAAA,EAAA,WAChD,WAAW,aAAa,GAAG;AACpC,WAAO,WAAW,eAAe,IAAI,IAAI,IAAI;AAAA,EAC/C;AACO,SAAA;AACT;AACA,SAAS,cAAc,UAAU,OAAO,UAAU;AAC1C,QAAA,iBAAiB,MAAM,KAAK;AAC5B,QAAA,UAAU,SAAS,aAAa,CAAC;AACvC,aAAW,OAAO,SAAS;AACrB,QAAA,MAAM,QAAQ,GAAG;AACrB,QAAI,OAAO;AACT;AACFC;AAAAA,MACE;AAAA,MACA,eAAe,GAAG;AAAA,MAClB;AAAA,MAC4C,gBAAgB,cAAc;AAAA,MAC1E,CAAC,OAAO,UAAU,GAAG,KAAK,CAAC,OAAO,UAAU,UAAU,GAAG,CAAC;AAAA,IAAA;AAAA,EAE9D;AACF;AACA,SAASA,eAAa,MAAM,OAAO,MAAM,OAAO,UAAU;AACxD,QAAM,EAAE,MAAM,UAAU,WAAW,cAAc;AACjD,MAAI,YAAY,UAAU;AACjB,WAAA,6BAA6B,OAAO,GAAG;AAC9C;AAAA,EACF;AACI,MAAA,SAAS,QAAQ,CAAC,UAAU;AAC9B;AAAA,EACF;AACA,MAAI,QAAQ,QAAQ,SAAS,QAAQ,CAAC,WAAW;AAC/C,QAAI,UAAU;AACd,UAAM,QAAQ,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAC1C,UAAM,gBAAgB,CAAA;AACtB,aAAS,IAAI,GAAG,IAAI,MAAM,UAAU,CAAC,SAAS,KAAK;AAC3C,YAAA,EAAE,OAAO,iBAAiBC,aAAW,OAAO,MAAM,CAAC,CAAC;AAC5C,oBAAA,KAAK,gBAAgB,EAAE;AAC3B,gBAAA;AAAA,IACZ;AACA,QAAI,CAAC,SAAS;AACZ,aAAOC,wBAAsB,MAAM,OAAO,aAAa,CAAC;AACxD;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,CAAC,UAAU,OAAO,KAAK,GAAG;AAClC,WAAA,2DAA2D,OAAO,IAAI;AAAA,EAC/E;AACF;AACA,MAAMC,iBAA+B;AAAA,EACnC;AACF;AACA,SAASF,aAAW,OAAO,MAAM;AAC3B,MAAA;AACE,QAAA,eAAeH,UAAQ,IAAI;AAC7B,MAAAK,eAAa,YAAY,GAAG;AAC9B,UAAMJ,KAAI,OAAO;AACTA,YAAAA,OAAM,aAAa;AACvB,QAAA,CAAC,SAASA,OAAM,UAAU;AAC5B,cAAQ,iBAAiB;AAAA,IAC3B;AAAA,EAAA,WACS,iBAAiB,UAAU;AACpC,YAAQ3B,WAAS,KAAK;AAAA,EAAA,WACb,iBAAiB,SAAS;AACnC,YAAQ,QAAQ,KAAK;AAAA,EAAA,WACZ,iBAAiB,QAAQ;AAClC,YAAQ,UAAU;AAAA,EAAA,OACb;AACL,YAAQ,iBAAiB;AAAA,EAC3B;AACO,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ;AACA,SAAS8B,wBAAsB,MAAM,OAAO,eAAe;AACrD,MAAA,cAAc,WAAW,GAAG;AAC9B,WAAO,0BAA0B,IAAI;AAAA,EACvC;AACI,MAAA,UAAU,6CAA6C,IAAI,eAAe,cAAc,IAAI,UAAU,EAAE,KAAK,KAAK,CAAC;AACjH,QAAA,eAAe,cAAc,CAAC;AAC9B,QAAA,eAAe,UAAU,KAAK;AAC9B,QAAA,gBAAgBE,aAAW,OAAO,YAAY;AAC9C,QAAA,gBAAgBA,aAAW,OAAO,YAAY;AAChD,MAAA,cAAc,WAAW,KAAKC,eAAa,YAAY,KAAK,CAACC,YAAU,cAAc,YAAY,GAAG;AACtG,eAAW,eAAe,aAAa;AAAA,EACzC;AACA,aAAW,SAAS,YAAY;AAC5B,MAAAD,eAAa,YAAY,GAAG;AAC9B,eAAW,cAAc,aAAa;AAAA,EACxC;AACO,SAAA;AACT;AACA,SAASD,aAAW,OAAO,MAAM;AAC/B,MAAI,SAAS,UAAU;AACrB,WAAO,IAAI,KAAK;AAAA,EAAA,WACP,SAAS,UAAU;AACrB,WAAA,GAAG,OAAO,KAAK,CAAC;AAAA,EAAA,OAClB;AACL,WAAO,GAAG,KAAK;AAAA,EACjB;AACF;AACA,SAASC,eAAa,MAAM;AAC1B,QAAM,gBAAgB,CAAC,UAAU,UAAU,SAAS;AACpD,SAAO,cAAc,KAAK,CAAC,SAAS,KAAK,YAAA,MAAkB,IAAI;AACjE;AACA,SAASC,eAAa,MAAM;AAC1B,SAAO,KAAK,KAAK,CAAC,SAAS,KAAK,YAAA,MAAkB,SAAS;AAC7D;AAEA,IAAI;AACJ,IAAI;AACJ,SAAS,aAAa,UAAU,MAAM;AACpC,MAAI,SAAS,WAAW,OAAO,eAAe,eAAe;AAC3D,SAAK,KAAK,OAAO,IAAI,IAAI,SAAS,GAAG,EAAE;AAAA,EACzC;AACwE;AACpD,sBAAA,UAAU,MAAM,YAAY,IAAI,KAAK,QAAQ,KAAK,IAAA,CAAK;AAAA,EAC3E;AACF;AACA,SAAS,WAAW,UAAU,MAAM;AAClC,MAAI,SAAS,WAAW,OAAO,eAAe,eAAe;AAC3D,UAAM,WAAW,OAAO,IAAI,IAAI,SAAS,GAAG;AAC5C,UAAM,SAAS,WAAW;AAC1B,SAAK,KAAK,MAAM;AACX,SAAA;AAAA,MACH,IAAI,oBAAoB,UAAU,SAAS,IAAI,CAAC,KAAK,IAAI;AAAA,MACzD;AAAA,MACA;AAAA,IAAA;AAEF,SAAK,WAAW,QAAQ;AACxB,SAAK,WAAW,MAAM;AAAA,EACxB;AACwE;AACtD,oBAAA,UAAU,MAAM,YAAY,IAAI,KAAK,QAAQ,KAAK,IAAA,CAAK;AAAA,EACzE;AACF;AACA,SAAS,cAAc;AACrB,MAAI,cAAc,QAAQ;AACjB,WAAA;AAAA,EACT;AACA,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa;AAC3C,gBAAA;AACZ,WAAO,OAAO;AAAA,EAAA,OACT;AACO,gBAAA;AAAA,EACd;AACO,SAAA;AACT;AAEA,MAAM,0BAA0B;AAIhC,MAAM,WAAW,OAAO,IAAI,OAAO;AACnC,MAAM,OAAO,OAAO,IAAI,OAAO;AAC/B,MAAM,UAAU,OAAO,IAAI,OAAO;AAClC,MAAM,SAAS,OAAO,IAAI,OAAO;AAMjC,SAAS,QAAQ,OAAO;AACf,SAAA,QAAQ,MAAM,gBAAgB,OAAO;AAC9C;AAyQA,MAAM,kBAAkB,iBAAiB;AACzC,IAAI,MAAM;AACV,SAAS,wBAAwB,OAAO,QAAQ,UAAU;AACxD,QAAM,OAAO,MAAM;AACnB,QAAM,cAAc,SAAS,OAAO,aAAa,MAAM,eAAe;AACtE,QAAM,WAAW;AAAA,IACf,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA;AAAA,IAEN,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,IAET,QAAQ;AAAA,IACR,QAAQ;AAAA;AAAA,IAER,OAAO,IAAI;AAAA,MACT;AAAA;AAAA,IAEF;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,aAAa;AAAA,IACb,WAAW;AAAA,IACX,UAAU,SAAS,OAAO,WAAW,OAAO,OAAO,WAAW,QAAQ;AAAA,IACtE,aAAa;AAAA,IACb,aAAa,CAAC;AAAA;AAAA,IAEd,YAAY;AAAA,IACZ,YAAY;AAAA;AAAA,IAEZ,cAAc,sBAAsB,MAAM,UAAU;AAAA,IACpD,cAAc,sBAAsB,MAAM,UAAU;AAAA;AAAA,IAEpD,MAAM;AAAA;AAAA,IAEN,SAAS;AAAA;AAAA,IAET,eAAe;AAAA;AAAA,IAEf,cAAc,KAAK;AAAA;AAAA,IAEnB,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA;AAAA,IAEZ;AAAA,IACA,YAAY,WAAW,SAAS,YAAY;AAAA,IAC5C,UAAU;AAAA,IACV,eAAe;AAAA;AAAA;AAAA,IAGf,WAAW;AAAA,IACX,aAAa;AAAA,IACb,eAAe;AAAA,IACf,IAAI;AAAA,IACJ,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,GAAG;AAAA,IACH,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA;AAAA,IAEJ,kCAAkC,IAAI;AAAA,IACtC,yBAAyB,CAAC;AAAA,IAC1B,2BAA2B,CAAC;AAAA,IAC5B,KAAK,CAAC;AAAA,IACN,KAAK,CAAC;AAAA,EAAA;AAEuC;AACpC,aAAA,MAAM,uBAAuB,QAAQ;AAAA,EAGhD;AACS,WAAA,OAAO,SAAS,OAAO,OAAO;AACvC,WAAS,OAAO,KAAK,KAAK,MAAM,QAAQ;AACxC,MAAI,MAAM,IAAI;AACZ,UAAM,GAAG,QAAQ;AAAA,EACnB;AACO,SAAA;AACT;AACA,IAAI,kBAAkB;AACtB,MAAM,qBAAqB,MAAM,mBAAmB;AACpD,IAAI;AACJ,IAAI;AACJ;AACE,+BAA6B,CAAC,MAAM;AAChB,sBAAA;AAAA,EAAA;AAEpB,uBAAqB,CAAC,MAAM;AACF,4BAAA;AAAA,EAAA;AAE5B;AACA,MAAM,qBAAqB,CAAC,aAAa;AACvC,QAAM,OAAO;AACb,6BAA2B,QAAQ;AACnC,WAAS,MAAM;AACf,SAAO,MAAM;AACX,aAAS,MAAM;AACf,+BAA2B,IAAI;AAAA,EAAA;AAEnC;AACA,MAAM,uBAAuB,MAAM;AACd,qBAAA,gBAAgB,MAAM;AACzC,6BAA2B,IAAI;AACjC;AACA,MAAM,uCAAuC,gBAAgB;AAC7D,SAAS,sBAAsB,MAAM,EAAE,eAAe;AACpD,MAAI,aAAa,IAAI,KAAK,YAAY,IAAI,GAAG;AAC3C;AAAA,MACE,oEAAoE;AAAA,IAAA;AAAA,EAExE;AACF;AACA,SAAS,oBAAoB,UAAU;AAC9B,SAAA,SAAS,MAAM,YAAY;AACpC;AACA,IAAI,wBAAwB;AAC5B,SAAS,eAAe,UAAU,QAAQ,OAAO;AAC/C,WAAS,mBAAmB,KAAK;AAC3B,QAAA;AAAA,IACJ;AAAA;AAAA,EAAA,IAEE,SAAS;AACP,QAAA,aAAa,oBAAoB,QAAQ;AACrCV,cAAA,UAAU,OAAO,YAAY,KAAK;AAC5C,QAAM,cAAc,aAAa,uBAAuB,UAAU,KAAK,IAAI;AAC3E,WAAS,mBAAmB,KAAK;AAC1B,SAAA;AACT;AACA,SAAS,uBAAuB,UAAU,OAAO;AAC/C,QAAMW,aAAY,SAAS;AACoB;AAC7C,QAAIA,WAAU,MAAM;AAClB,4BAAsBA,WAAU,MAAM,SAAS,WAAW,MAAM;AAAA,IAClE;AACA,QAAIA,WAAU,YAAY;AACxB,YAAM,QAAQ,OAAO,KAAKA,WAAU,UAAU;AAC9C,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,8BAAsB,MAAM,CAAC,GAAG,SAAS,WAAW,MAAM;AAAA,MAC5D;AAAA,IACF;AACA,QAAIA,WAAU,YAAY;AACxB,YAAM,QAAQ,OAAO,KAAKA,WAAU,UAAU;AAC9C,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACf,8BAAA,MAAM,CAAC,CAAC;AAAA,MAChC;AAAA,IACF;AACI,QAAAA,WAAU,mBAAmB,iBAAiB;AAChD;AAAA,QACE;AAAA,MAAA;AAAA,IAEJ;AAAA,EACF;AACS,WAAA,cAAqC,uBAAA,OAAO,IAAI;AACzD,WAAS,QAAQ,QAAQ,IAAI,MAAM,SAAS,KAAK,2BAA2B,CAAC;AAC9B;AAC7C,+BAA2B,QAAQ;AAAA,EACrC;AACM,QAAA,EAAE,MAAU,IAAAA;AAClB,MAAI,OAAO;AACH,UAAA,eAAe,SAAS,eAAe,MAAM,SAAS,IAAI,mBAAmB,QAAQ,IAAI;AACzF,UAAA,QAAQ,mBAAmB,QAAQ;AAC3B;AACd,UAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QAC8C,gBAAgB,SAAS,KAAK;AAAA,QAC1E;AAAA,MACF;AAAA,IAAA;AAEY;AACR;AACF,QAAA7B,YAAU,WAAW,GAAG;AACd,kBAAA,KAAK,sBAAsB,oBAAoB;AACZ;AAC7C;AAAA,UACE;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA,OACK;AACa,wBAAA,UAAU,aAAa,KAAK;AAAA,IAChD;AAAA,EAAA,OACK;AACL,yBAAqB,UAAU,KAAK;AAAA,EACtC;AACF;AACA,SAAS,kBAAkB,UAAU,aAAa,OAAO;AACnD,MAAA,WAAW,WAAW,GAAG;AAC3B;AACE,eAAS,SAAS;AAAA,IACpB;AAAA,EAAA,WACSN,WAAS,WAAW,GAAG;AACiB,QAAA,QAAQ,WAAW,GAAG;AACrE;AAAA,QACE;AAAA,MAAA;AAAA,IAEJ;AACwE;AACtE,eAAS,wBAAwB;AAAA,IACnC;AACS,aAAA,aAAa,UAAU,WAAW;AACI;AAC7C,sCAAgC,QAAQ;AAAA,IAC1C;AAAA,EAAA,WACsD,gBAAgB,QAAQ;AAC9E;AAAA,MACE,8CAA8C,gBAAgB,OAAO,SAAS,OAAO,WAAW;AAAA,IAAA;AAAA,EAEpG;AACA,uBAAqB,UAAU,KAAK;AACtC;AACA,IAAI;AACJ,MAAM,gBAAgB,MAAM,CAAC;AAC7B,SAAS,qBAAqB,UAAU,OAAO,aAAa;AAC1D,QAAMmC,aAAY,SAAS;AACvB,MAAA,CAAC,SAAS,QAAQ;AACX,aAAA,SAASA,WAAU,UAAU;AAAA,EACxC;AACiC;AACzB,UAAA,QAAQ,mBAAmB,QAAQ;AAC3B;AACV,QAAA;AACF,qBAAe,QAAQ;AAAA,IAAA,UACvB;AACc;AACR;IACR;AAAA,EACF;AACA,MAAiD,CAACA,WAAU,UAAU,SAAS,WAAW,QAAQ,CAAC,OAAO;AACxG,QAAIA,WAAU,UAAU;AACtB;AAAA,QACE;AAAA,MAAA;AAAA,IACF,OACK;AACL,aAAO,mDAAmD;AAAA,IAC5D;AAAA,EACF;AACF;AACA,SAAS,cAAc,UAAU;AAC/B,SAAO,SAAS,eAAe,SAAS,aAAa,IAAI;AAAA,IACvD,SAAS;AAAA,IACmC;AAAA,MAC1C,IAAI,QAAQ,KAAK;AACT,cAAA,UAAU,OAAO,QAAQ;AAC/B,eAAO,OAAO,GAAG;AAAA,MACnB;AAAA,MACA,MAAM;AACJ,eAAO,iCAAiC;AACjC,eAAA;AAAA,MACT;AAAA,MACA,iBAAiB;AACf,eAAO,iCAAiC;AACjC,eAAA;AAAA,MACT;AAAA,IAAA;AAAA,EAMF;AAEJ;AACA,SAAS,cAAc,UAAU;AAC/B,SAAO,SAAS,eAAe,SAAS,aAAa,IAAI,MAAM,SAAS,OAAO;AAAA,IAC7E,IAAI,QAAQ,KAAK;AACT,YAAA,UAAU,OAAO,QAAQ;AAC/B,aAAO,OAAO,GAAG;AAAA,IACnB;AAAA,EACD,CAAA;AACH;AACA,SAAS,mBAAmB,UAAU;AAC9B,QAAA,SAAS,CAAC,YAAY;AACqB;AAC7C,UAAI,SAAS,SAAS;AACpB,eAAO,kDAAkD;AAAA,MAC3D;AACA,UAAI,WAAW,MAAM;AACnB,YAAI,cAAc,OAAO;AACzB,YAAI,gBAAgB,UAAU;AACxB,cAAA,QAAQ,OAAO,GAAG;AACN,0BAAA;AAAA,UAAA,WACL,MAAM,OAAO,GAAG;AACX,0BAAA;AAAA,UAChB;AAAA,QACF;AACA,YAAI,gBAAgB,UAAU;AAC5B;AAAA,YACE,sDAAsD,WAAW;AAAA,UAAA;AAAA,QAErE;AAAA,MACF;AAAA,IACF;AACS,aAAA,UAAU,WAAW;EAAC;AAEc;AAC7C,WAAO,OAAO,OAAO;AAAA,MACnB,IAAI,QAAQ;AACV,eAAO,cAAc,QAAQ;AAAA,MAC/B;AAAA,MACA,IAAI,QAAQ;AACV,eAAO,cAAc,QAAQ;AAAA,MAC/B;AAAA,MACA,IAAI,OAAO;AACT,eAAO,CAAC,UAAU,SAAS,SAAS,KAAK,OAAO,GAAG,IAAI;AAAA,MACzD;AAAA,MACA;AAAA,IAAA,CACD;AAAA,EAUH;AACF;AACA,SAAS,eAAe,UAAU;AAChC,MAAI,SAAS,SAAS;AACb,WAAA,SAAS,gBAAgB,SAAS,cAAc,IAAI,MAAM,UAAU,QAAQ,SAAS,OAAO,CAAC,GAAG;AAAA,MACrG,IAAI,QAAQ,KAAK;AACf,YAAI,OAAO,QAAQ;AACjB,iBAAO,OAAO,GAAG;AAAA,QACnB;AACO,eAAA,SAAS,MAAM,GAAG;AAAA,MAC3B;AAAA,MACA,IAAI,QAAQ,KAAK;AACR,eAAA,OAAO,UAAU,OAAO;AAAA,MACjC;AAAA,IACD,CAAA;AAAA,EACH;AACF;AACA,MAAM,aAAa;AACnB,MAAM,WAAW,CAAC,QAAQ,IAAI,QAAQ,YAAY,CAACf,OAAMA,GAAE,YAAY,CAAC,EAAE,QAAQ,SAAS,EAAE;AAC7F,SAAS,iBAAiBe,YAAW,kBAAkB,MAAM;AACpD,SAAA,WAAWA,UAAS,IAAIA,WAAU,eAAeA,WAAU,OAAOA,WAAU,QAAQ,mBAAmBA,WAAU;AAC1H;AACA,SAAS,oBAAoB,UAAUA,YAAW,SAAS,OAAO;AAC5D,MAAA,OAAO,iBAAiBA,UAAS;AACjC,MAAA,CAAC,QAAQA,WAAU,QAAQ;AAC7B,UAAM,QAAQA,WAAU,OAAO,MAAM,iBAAiB;AACtD,QAAI,OAAO;AACT,aAAO,MAAM,CAAC;AAAA,IAChB;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,YAAY,SAAS,QAAQ;AAClC,UAAA,oBAAoB,CAAC,aAAa;AACtC,iBAAW,OAAO,UAAU;AACtB,YAAA,SAAS,GAAG,MAAMA,YAAW;AACxB,iBAAA;AAAA,QACT;AAAA,MACF;AAAA,IAAA;AAEK,WAAA;AAAA,MACL,SAAS,cAAc,SAAS,OAAO,KAAK;AAAA,IACzC,KAAA,kBAAkB,SAAS,WAAW,UAAU;AAAA,EACvD;AACA,SAAO,OAAO,SAAS,IAAI,IAAI,SAAS,QAAQ;AAClD;AAKA,MAAM,WAAW,CAAC,iBAAiB,iBAAiB;AAClD,QAAMf,KAAI,WAAW,iBAAiB,cAAc,qBAAqB;AAC1B;AAC7C,UAAM,IAAI;AACV,QAAI,KAAK,EAAE,WAAW,OAAO,uBAAuB;AAClDA,SAAE,iBAAiB;AAAA,IACrB;AAAA,EACF;AACOA,SAAAA;AACT;AAuDA,MAAM,UAAU;AAChB,MAAM,OAAmD;AAGzD,SAAS,UAAU,QAAQ;AACzB,SAAO,MAAM,MAAM;AACrB;AAKA,MAAM,YAAY;AAClB,MAAM,aAAa;AACnB,SAAS,KAAK,SAAS,KAAK;AAC1B,QAAM,SAAS,CAAA;AACf,WAAS,SAAS,GAAG;AACf,QAAA,SAAS,KAAK,IAAI,MAAM;AACvB,SAAA;AACT;AACA,SAAS,SAAS,SAAS,KAAK;AAC9B,YAAU,UAAU,OAAO;AAC3B,MAAI,YAAY;AACd;AACI,QAAA,kBAAkB,aAAa,OAAO;AACtC,QAAA,cAAc,aAAa,GAAG;AAChC,MAAA,mBAAmB,cAAc,eAAe,YAAY;AAC9D,aAAS,OAAO,KAAK;AACb,YAAA,eAAe,QAAQ,GAAG;AAChC,UAAI,iBAAiB,QAAQ;AAC3B,gBAAQ,GAAG,IAAI;AAAA,MAAA,OACV;AACI,iBAAA,cAAc,IAAI,GAAG,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACS,WAAA,mBAAmB,aAAa,eAAe,WAAW;AAC/D,QAAA,QAAQ,UAAU,IAAI,QAAQ;AAC5B,UAAA,QAAQ,CAAC,MAAMgB,WAAU;AAClB,iBAAA,QAAQA,MAAK,GAAG,IAAI;AAAA,MAAA,CAC9B;AAAA,IACH;AAAA,EACF;AACF;AACA,SAAS,MAAM,SAAS,KAAK,MAAM,QAAQ;AACzC,YAAU,UAAU,OAAO;AAC3B,MAAI,YAAY;AACd;AACI,QAAA,kBAAkB,aAAa,OAAO;AACtC,QAAA,cAAc,aAAa,GAAG;AACpC,MAAI,mBAAmB,YAAY;AAC7B,QAAA,eAAe,cAAc,OAAO,KAAK,OAAO,EAAE,SAAS,OAAO,KAAK,GAAG,EAAE,QAAQ;AAC5E,gBAAA,QAAQ,MAAM,OAAO;AAAA,IAAA,OAC1B;AACL,eAAS,OAAO,SAAS;AACvB,cAAM,eAAe,UAAU,QAAQ,GAAG,CAAC;AACrC,cAAA,WAAW,IAAI,GAAG;AAClB,cAAA,cAAc,aAAa,YAAY;AACvC,cAAA,UAAU,aAAa,QAAQ;AACjC,YAAA,eAAe,aAAa,eAAe,YAAY;AACzD,cAAI,gBAAgB,UAAU;AAC5B;AAAA,cACE;AAAA,eACC,QAAQ,KAAK,KAAK,OAAO,OAAO;AAAA,cACjC;AAAA,YAAA;AAAA,UAEJ;AAAA,QAAA,WACS,eAAe,WAAW;AACnC,cAAI,WAAW,WAAW;AACxB;AAAA,cACE;AAAA,eACC,QAAQ,KAAK,KAAK,OAAO,OAAO;AAAA,cACjC;AAAA,YAAA;AAAA,UACF,OACK;AACD,gBAAA,aAAa,SAAS,SAAS,QAAQ;AACzC;AAAA,gBACE;AAAA,iBACC,QAAQ,KAAK,KAAK,OAAO,OAAO;AAAA,gBACjC;AAAA,cAAA;AAAA,YACF,OACK;AACQ,2BAAA,QAAQ,CAAC,MAAMA,WAAU;AACpC;AAAA,kBACE;AAAA,kBACA,SAASA,MAAK;AAAA,mBACb,QAAQ,KAAK,KAAK,OAAO,OAAO,MAAM,MAAMA,SAAQ;AAAA,kBACrD;AAAA,gBAAA;AAAA,cACF,CACD;AAAA,YACH;AAAA,UACF;AAAA,QAAA,WACS,eAAe,YAAY;AAChC,cAAA,WAAW,cAAc,OAAO,KAAK,YAAY,EAAE,SAAS,OAAO,KAAK,QAAQ,EAAE,QAAQ;AAC5F;AAAA,cACE;AAAA,eACC,QAAQ,KAAK,KAAK,OAAO,OAAO;AAAA,cACjC;AAAA,YAAA;AAAA,UACF,OACK;AACL,qBAAS,UAAU,cAAc;AAC/B;AAAA,gBACE,aAAa,MAAM;AAAA,gBACnB,SAAS,MAAM;AAAA,iBACd,QAAQ,KAAK,KAAK,OAAO,OAAO,MAAM,MAAM;AAAA,gBAC7C;AAAA,cAAA;AAAA,YAEJ;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EAAA,WACS,mBAAmB,WAAW;AACvC,QAAI,eAAe,WAAW;AAClB,gBAAA,QAAQ,MAAM,OAAO;AAAA,IAAA,OAC1B;AACD,UAAA,QAAQ,SAAS,IAAI,QAAQ;AACrB,kBAAA,QAAQ,MAAM,OAAO;AAAA,MAAA,OAC1B;AACG,gBAAA,QAAQ,CAAC,MAAMA,WAAU;AACzB,gBAAA,MAAM,IAAIA,MAAK,GAAG,OAAO,MAAMA,SAAQ,KAAK,MAAM;AAAA,QAAA,CACzD;AAAA,MACH;AAAA,IACF;AAAA,EAAA,OACK;AACK,cAAA,QAAQ,MAAM,OAAO;AAAA,EACjC;AACF;AACA,SAAS,UAAU,QAAQ,GAAG,GAAG;AAC/B,SAAO,CAAC,IAAI;AACd;AAEA,SAAS,mBAAmB,UAAU;AAC7B,SAAA7B,QAAM,SAAS,SAAS,MAAM;AACvC;AACA,SAAS,eAAe,UAAU;AAChC,QAAM,MAAM,SAAS;AACrB,QAAM,YAAY,IAAI;AAClB,MAAA,aAAa,UAAU,QAAQ;AAO3B,UAAA,SAAS,UAAU,MAAM,CAAC;AAChC,cAAU,SAAS;AACnB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,aAAO,CAAC;IACV;AAAA,EACF;AACF;AACA,SAAS,SAAS,UAAU,IAAI;AAC9B,QAAM,MAAM,SAAS;AACrB,MAAI,CAAC,IAAI,uBAAuB,CAAC,mBAAmB,QAAQ,GAAG;AAO7D,WAAO,WAAW,MAAM,GAAG,KAAK,SAAS,KAAK,CAAC;AAAA,EACjD;AAOI,MAAA;AACA,MAAA,CAAC,IAAI,uBAAuB;AAC9B,QAAI,wBAAwB;EAC9B;AACI,MAAA,sBAAsB,KAAK,MAAM;AACnC,QAAI,IAAI;AACN;AAAA,QACE,GAAG,KAAK,SAAS,KAAK;AAAA,QACtB;AAAA,QACA;AAAA,MAAA;AAAA,eAEO,UAAU;AACnB,eAAS,SAAS,KAAK;AAAA,IACzB;AAAA,EAAA,CACD;AACM,SAAA,IAAI,QAAQ,CAAC8B,aAAY;AACnBA,eAAAA;AAAAA,EAAA,CACZ;AACH;AAEA,SAAS,MAAM,KAAK,MAAM;AACxB,QAAM,UAAU,GAAG;AACnB,QAAM,OAAO,OAAO;AAChB,MAAA,SAAS,YAAY,QAAQ,MAAM;AACjC,QAAA,OAAO,KAAK,IAAI,GAAG;AACnB,QAAA,OAAO,SAAS,aAAa;AACxB,aAAA;AAAA,IACT;AACI,QAAA,QAAQ,GAAG,GAAG;AAChB,YAAM,MAAM,IAAI;AACT,aAAA,IAAI,MAAM,GAAG;AACf,WAAA,IAAI,KAAK,IAAI;AAClB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,aAAK,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,IAAI;AAAA,MAC9B;AAAA,IAAA,OACK;AACL,aAAO,CAAA;AACF,WAAA,IAAI,KAAK,IAAI;AAClB,iBAAW,QAAQ,KAAK;AAClB,YAAA,OAAO,KAAK,IAAI,GAAG;AACrB,eAAK,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AACO,WAAA;AAAA,EACT;AACA,MAAI,SAAS,UAAU;AACd,WAAA;AAAA,EACT;AACF;AACA,SAAS,SAAS,KAAK;AACd,SAAA,MAAM,KAAK,OAAO,YAAY,kCAAkC,QAAQ,IAAwB,oBAAA,IAAA,CAAK;AAC9G;AAEA,SAAS,kBAAkB,UAAU,MAAM;AACzC,QAAM,OAAO,SAAS;AAChB,QAAA,MAA6B,uBAAA,OAAO,IAAI;AACzC,OAAA,QAAQ,CAAC,QAAQ;AAChB,QAAA,GAAG,IAAI,KAAK,GAAG;AAAA,EAAA,CACpB;AACM,SAAA;AACT;AACA,SAAS,MAAM,UAAU,MAAM,SAAS;AACtC,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AACA,SAAO,SAAS,IAAI;AACf,OAAA,MAAM,SAAS,OAAO,CAAA;AACtB,OAAA,MAAM,SAAS,OAAO,CAAA;AAC3B,QAAM,MAAM,SAAS;AACrB,QAAM,SAAS,IAAI;AACf,MAAA,WAAW,UAAU,WAAW,aAAa;AAC/C,SAAK,KAAK;AAEV,UAAM,aAAa,IAAI;AACjB,UAAA,OAAO,OAAO,KAAK,IAAI;AAC7B,UAAM,WAAW,KAAK,MAAM,WAAW,kBAAkB,YAAY,IAAI,CAAC;AAC1E,QAAI,OAAO,KAAK,QAAQ,EAAE,QAAQ;AAOhC,UAAI,sBAAsB;AACf,iBAAA,QAAQ,UAAU,MAAM;AACjC,YAAI,sBAAsB;AAC1B,uBAAe,QAAQ;AAAA,MAAA,CACxB;AACgB;IAAA,OACZ;AACL,qBAAe,QAAQ;AAAA,IACzB;AAAA,EACF;AACF;AAEA,SAAS,cAAc,WAAW;AAChC,YAAU,iBAAiB,YAAY,SAAS,UAAU,IAAI;AACrD,WAAA,SAAS,KAAK,GAAG,EAAE;AAAA,EAAA;AAE9B;AAEA,SAAS,eAAe,SAAS,UAAU,YAAY;AAC5C,WAAA,WAAW,OAAO,iBAAiB;AAAA,IAC1C;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEF,QAAM,kBAAkB,QAAQ;AAChC,MAAI,iBAAiB;AACb,UAAA,OAAO,OAAO,KAAK,eAAe;AACxC,QAAI,KAAK,QAAQ;AACf,YAAM,MAAM,SAAS;AACjB,UAAA,CAAC,IAAI,eAAe;AACtB,YAAI,gBAAgB;MACtB;AACI,UAAA,cAAc,KAAK,GAAG,IAAI;AAAA,IAChC;AAAA,EACF;AACA,SAAO,SAAS,IAAI;AACtB;AAEA,SAAS,SAAS,UAAU,YAAY,OAAO;AACvC,QAAA;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,EAAE,QAAQ,YAAY;AAAA,EACzB,IAAA;AACJ,MAAI,gBAAgB,aAAa;AAC/B;AAAA,EACF;AACA,MAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,yBAAyB;AACzD;AAAA,EACF;AACA,MAAI,WAAW;AACb,qBAAiB,cAAc;AAAA,MAC7B,CAAC,gBAAgB,eAAe,aAAa,MAAM,UAAU;AAAA,IAAA;AAE/D,+BAA2B,wBAAwB;AAAA,MACjD,CAAC,gBAAgB,eAAe,aAAa,MAAM,UAAU;AAAA,IAAA;AAE/D;AAAA,EACF;AACM,QAAA,QAAQ,gBAAgB,cAAc,gBAAgB;AACtD,QAAA,cAAc,CAAC,SAAS;AACxB,QAAA,KAAK,WAAW,GAAG;AACrB,aAAO;IACT;AACM,UAAA;AAAA;AAAA;AAAA,OAGH,OAAO,oBAAoB,IAAI,KAAK,CAAI,GAAA;AAAA,QACvC,OAAO,oBAAoB,QAAQ,KAAK,CAAC;AAAA,MAC3C;AAAA;AAEK,WAAA,KAAK,OAAO,CAAC,gBAAgB;AAClC,YAAM,WAAW,4BAA4B,cAAc,YAAY,CAAC;AACpE,UAAA,SAAS,aAAa,MAAM;AACvB,eAAA;AAAA,MACT;AACe,qBAAA,aAAa,UAAU,UAAU;AACzC,aAAA;AAAA,IAAA,CACR;AAAA,EAAA;AAEH,QAAM,QAAQ,MAAM;AAClB,QAAI,eAAe;AACX,YAAA,OAAO,YAAY,aAAa;AACtC,UAAI,KAAK,UAAU,SAAS,SAAS,SAAS,MAAM,QAAQ;AAC1D,iBAAS,MAAM,OAAO,QAAQ,EAAE,IAAI,EAAA,GAAK,MAAM;AAC7C,sBAAY,IAAI;AAAA,QAAA,CACjB;AAAA,MACH;AAAA,IACF;AAAA,EAAA;AAEE,MAAA,2BAA2B,wBAAwB,QAAQ;AAC7D,aAAS,UAAU,MAAM;AACC,8BAAA,QAAQ,CAAC,gBAAgB;AAC3C,YAAA,QAAQ,YAAY,CAAC,GAAG;AACd,sBAAA,EAAE,QAAQ,CAAC,MAAM;AACZ,2BAAA,aAAa,GAAG,UAAU;AAAA,UAAA,CAC1C;AAAA,QAAA,OACI;AACU,yBAAA,aAAa,YAAY,GAAG,UAAU;AAAA,QACvD;AAAA,MAAA,CACD;AAAA,IAAA,CACF;AAAA,EACH;AACA,MAAI,OAAO,UAAU;AACnB,WAAO,SAAS,KAAK;AAAA,EAAA,OAChB;AACL,aAAS,UAAU,KAAK;AAAA,EAC1B;AACF;AACA,SAAS,OAAO,OAAO;AACjB,MAAArC,WAAS,KAAK,GAAG;AACnB,YAAQ,KAAK;AAAA,EACf;AACO,SAAA;AACT;AACA,SAAS,4BAA4B,cAAc,IAAI;AACrD,QAAM,aAAa,aAAa;AAAA,IAC9B,CAAC,QAAQ,QAAQ,IAAI,cAAc,IAAI,OAAO,OAAO;AAAA,EAAA;AAEvD,MAAI,YAAY;AACd,UAAM,KAAK,WAAW;AACtB,QAAI,IAAI;AACC,aAAA,eAAe,GAAG,CAAC,KAAK;AAAA,IACjC;AACA,WAAO,OAAO,UAAU;AAAA,EAC1B;AACO,SAAA;AACT;AACA,SAAS,eAAe,EAAE,GAAAK,IAAG,GAAAiC,GAAE,GAAG,UAAU,YAAY;AAClD,MAAA,WAAWjC,EAAC,GAAG;AACjBA,OAAE,UAAU,CAAA,CAAE;AAAA,EAAA,OACT;AACC,UAAA,YAAY,SAASA,EAAC;AACtB,UAAA,SAAS,MAAMA,EAAC;AACtB,QAAI,aAAa,QAAQ;AACvB,UAAIiC,IAAG;AACL,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,YAAI,CAAC,QAAQjC,GAAE,KAAK,GAAG;AACrBA,aAAE,QAAQ;QACZ;AACA,cAAM,WAAWA,GAAE;AACnB,YAAI,SAAS,QAAQ,QAAQ,MAAM,IAAI;AACrC,mBAAS,KAAK,QAAQ;AACtB,cAAI,CAAC,UAAU;AACb;AAAA,UACF;AACA,cAAI,SAAS,GAAG;AACd,4BAAgB,MAAM,OAAO,UAAU,QAAQ,GAAG,SAAS,CAAC;AAAA,UAC9D;AAAA,QACF;AAAA,iBACS,WAAW;AAChB,YAAA,OAAO,YAAYA,EAAC,GAAG;AACzB,qBAAWA,EAAC,IAAI;AAAA,QAClB;AAAA,MAAA,WACS,MAAMA,EAAC,GAAG;AACnBA,WAAE,QAAQ;AAAA,MAAA,OAC0C;AACpD,gBAAQA,EAAC;AAAA,MACX;AAAA,IAAA,OACoD;AACpD,cAAQA,EAAC;AAAA,IACX;AAAA,EACF;AACF;AACA,SAAS,QAAQkC,MAAK;AACpB,OAAK,8BAA8BA,MAAK,IAAI,OAAOA,IAAG,GAAG;AAC3D;AAEA,MAAM,wBAAwB;AAC9B,SAAS,eAAe,cAAc,SAAS;AAC7C,QAAM,WAAW,aAAa,YAAY,wBAAwB,cAAc,QAAQ,iBAAiB,IAAI;AACpF;AACvB,aAAS,IAAI,kBAAkB;AACtB,aAAA,IAAI,YAAY;EAC3B;AACI,MAAA,QAAQ,WAAW,OAAO;AAC5B,aAAS,SAAS;AAAA,EACpB;AACA,MAAI,QAAQ,eAAe;AACjB,YAAA,cAAc,UAAU,OAAO;AAAA,EACzC;AAC+C;AAC7C,uBAAmB,YAAY;AAC/B,iBAAa,UAAU,OAAO;AAAA,EAChC;AAC+C;AAC7C,iBAAa,UAAU,MAAM;AAAA,EAC/B;AACA,iBAAe,QAAQ;AACwB;AAC7C,eAAW,UAAU,MAAM;AAAA,EAC7B;AACyB;AACnB,QAAA,QAAQ,mBAAmB,SAAS,OAAO;AACrC,cAAA,gBAAgB,IAAI,UAAU,KAAK,eAAe,QAAQ,KAAK,SAAS,KAAK;AAAA,IACvF;AAAA,EACF;AACA,oBAAkB,QAAQ;AACqB;AAC3B;AAClB,eAAW,UAAU,OAAO;AAAA,EAC9B;AACA,SAAO,SAAS;AAClB;AACA,MAAM,2BAA2B,CAAC,UAAU;AACtC,MAAA;AACJ,aAAW,OAAO,OAAO;AACvB,QAAI,QAAQ,WAAW,QAAQ,WAAW,KAAK,GAAG,GAAG;AACnD,OAAC,QAAQ,MAAM,KAAK,GAAG,IAAI,MAAM,GAAG;AAAA,IACtC;AAAA,EACF;AACO,SAAA;AACT;AACA,SAAS,oBAAoB,UAAU;AAC/B,QAAA;AAAA,IACJ,MAAMJ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,MAAAK;AAAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAAC;AAAAA,IACA,YAAY;AAAA,MACV,KAAK;AAAA,QACH,QAAQ;AAAA,UACN,kBAAkB,EAAE,0BAAAC,0BAAyB;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,EACE,IAAA;AACK,WAAA,qCAAqC;AAC9C,WAAS,gBAAgB;AACzB,WAAS,0BAA0B;AACnC,WAAS,4BAA4B;AACrC,WAAS,MAAM;AACfA,4BAAyBD,IAAG;AAC5B,WAAS,YAAY,SAAS,cAAc,IAAI,IAAI;AAChD,MAAA;AACE,QAAA,OAAO,4BAA4B,QAAQ;AAC7C,MAAA;AACE,QAAA,MAAM,YAAY,GAAG;AACN,uBAAA,cAAc,OAAO,cAAc,KAAK;AACzD,YAAM,aAAa,aAAa;AAChC,eAAS,OAAO;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF,OACK;AACL;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACAN,WAAU,QAAQ,QAAQ,yBAAyB,KAAK;AAAA,MAAA;AAE1D,YAAM,UAAUA;AACP,eAAA,QAAQ,SAAS,IAAI,QAAQ,OAAO,EAAE,OAAO,OAAO,MAAAK,MAAK,CAAC,IAAI;AAAA,QACrE;AAAA,QACA;AAAA;AAAA,MAAA;AAAA,IAGJ;AAAA,WACO,KAAK;AACA,gBAAA,KAAK,UAAU,CAAC;AACnB,aAAA;AAAA,EACX;AACA,WAAS,QAAQ;AACjB,8BAA4B,IAAI;AACzB,SAAA;AACT;AACA,SAAS,iBAAiB,cAAc,OAAO,cAAc,mBAAmB;AAC1E,MAAA,SAAS,qBAAqB,iBAAiB,OAAO;AACxD,UAAM,OAAO,OAAO,KAAK,iBAAiB,EAAE;AAAA,MAC1C,CAAC,QAAQ,QAAQ,WAAW,QAAQ;AAAA,IAAA;AAElC,QAAA,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,QAAI,gBAAgB,KAAK,KAAK,eAAe,GAAG;AACzC,WAAA,QAAQ,CAAC,QAAQ;AAChB,YAAA,CAAC,gBAAgB,GAAG,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,eAAe;AACtD,gBAAA,GAAG,IAAI,kBAAkB,GAAG;AAAA,QACpC;AAAA,MAAA,CACD;AAAA,IAAA,OACI;AACA,WAAA,QAAQ,CAAC,QAAQ,MAAM,GAAG,IAAI,kBAAkB,GAAG,CAAC;AAAA,IAC3D;AAAA,EACF;AACF;AACA,MAAM,2BAA2B,CAAC,aAAa;AAC/B;AACG;AACH;AAChB;AACA,SAAS,+BAA+B;AACtC,QAAM,kBAAkB,KAAK;AAC7B,MAAI,CAAC,mBAAmB,gBAAgB,WAAW,GAAG;AACpD;AAAA,EACF;AAEM,QAAA,aAAa,KAAK,IAAI;AAC5B,QAAM,UAAU,WAAW;AACrB,QAAA,WAAkC,uBAAA,OAAO,IAAI;AACnD,kBAAgB,QAAQ,CAAC,EAAE,MAAM,OAAAJ,QAAO,WAAW;AAC3C,UAAA,oBAAoB,mBAAmB,SAAS,IAAI;AAC1D,UAAM,WAAW,SAASA,MAAK,IAAI,GAAG,IAAI,IAAIA,MAAK,KAAK,GAAG,IAAI,IAAIA,MAAK;AACxE,QAAI,OAAO,sBAAsB,eAAe,OAAO,kBAAkBA,MAAK,MAAM,aAAa;AAC/F,eAAS,QAAQ,IAAI;AAAA,IAAA,OAChB;AACL,YAAM,qBAAqB;AAAA,QACzB;AAAA,QACA,kBAAkBA,MAAK;AAAA,MAAA;AAEzB,aAAO,KAAK,kBAAkB,EAAE,QAAQ,CAAC,SAAS;AAChD,iBAAS,WAAW,MAAM,IAAI,IAAI,mBAAmB,IAAI;AAAA,MAAA,CAC1D;AAAA,IACH;AAAA,EAAA,CACD;AACD,kBAAgB,SAAS;AACzB,MAAI,OAAO,KAAK,QAAQ,EAAE,QAAQ;AAOhC,eAAW,QAAQ,QAAQ;AAAA,EAC7B;AACF;AACA,SAAS,cAAc,EAAE,QAAAvC,SAAQ,QAAA8C,QAAA,GAAU,SAAS;AAClD9C,UAAO,eAAe8C,QAAO,eAAe;AAC9C;AACA,SAAS,kBAAkB,UAAU;AACnC,QAAM,oBAAoB,6BAA6B;AAAA,IACrD;AAAA,EAAA;AAEF,WAAS,qBAAqB,MAAM,WAAW,MAAM,SAAS,iBAAiB,CAAC;AAChF,QAAM,oBAAoB,MAAM;AAC1B,QAAA,CAAC,SAAS,WAAW;AACvB,sBAAgB,MAAM;AACpB,iBAAS,UAAU,IAAI;AAAA,SACtB,QAAQ;AACoC;AAC7C,qBAAa,UAAU,OAAO;AAAA,MAChC;AACM,YAAA,UAAU,oBAAoB,QAAQ,CAAC;AACE;AAC7C,mBAAW,UAAU,OAAO;AAAA,MAC9B;AACwE;AACtE,+BAAuB,QAAQ;AAAA,MACjC;AAAA,IAAA,OACK;AACL,YAAM,EAAE,MAAM,IAAI,EAAA,IAAM;AACuB;AAC1B,2BAAA,QAAQ,SAAS,KAAK;AAAA,MAC3C;AACA,oBAAc,UAAU,KAAK;AACJ;AACzB,UAAI,IAAI;AACNC,yBAAe,EAAE;AAAA,MACnB;AACA,oBAAc,UAAU,IAAI;AACmB;AAC7C,qBAAa,UAAU,OAAO;AAAA,MAChC;AACM,YAAA,UAAU,oBAAoB,QAAQ,CAAC;AACE;AAC7C,mBAAW,UAAU,OAAO;AAAA,MAC9B;AACA,UAAI,GAAG;AACL,8BAAsB,CAAC;AAAA,MACzB;AACwE;AACtE,iCAAyB,QAAQ;AAAA,MACnC;AAC+C;AAC3B;MACpB;AAAA,IACF;AAAA,EAAA;AAEI/C,QAAAA,UAAS,SAAS,SAAS,IAAI;AAAA,IACnC;AAAA,IACA;AAAA,IACA,MAAM,SAAS8C,OAAM;AAAA,IACrB,SAAS;AAAA;AAAA,EAAA;AAGL,QAAAA,UAAS,SAAS,SAAS,MAAM;AACrC,QAAI9C,QAAO,OAAO;AAChBA,cAAO,IAAI;AAAA,IACb;AAAA,EAAA;AAEF,EAAA8C,QAAO,KAAK,SAAS;AACrB,gBAAc,UAAU,IAAI;AACmB;AAC7C9C,YAAO,UAAU,SAAS,MAAM,CAACgD,OAAMD,iBAAe,SAAS,KAAKC,EAAC,IAAI;AACzEhD,YAAO,YAAY,SAAS,MAAM,CAACgD,OAAMD,iBAAe,SAAS,KAAKC,EAAC,IAAI;AAC3E,IAAAF,QAAO,gBAAgB;AAAA,EACzB;AAC+B;AACtB,IAAAA;EACT;AACF;AACA,SAAS,iBAAiB,UAAU;AAClC,QAAM,EAAE,KAAK,OAAO,QAAAA,SAAQ,OAAO;AACnC,MAAI,KAAK;AACPC,qBAAe,GAAG;AAAA,EACpB;AACyB;AACvB,UAAM,iBAAiB,SAAS;AAChC,QAAI,gBAAgB;AACZ,YAAA,YAAY,eAAe,IAAI;AACrC,YAAM,SAAS,eAAe,QAAQ,KAAK,SAAS;AAC9C,YAAAR,SAAQ,UAAU,QAAQ,MAAM;AACtC,UAAIA,SAAQ,IAAI;AACJ,kBAAA,OAAOA,QAAO,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,QAAM,KAAK;AACX,MAAIO,SAAQ;AACV,IAAAA,QAAO,SAAS;AAAA,EAClB;AACA,MAAI,IAAI;AACN,0BAAsB,EAAE;AAAA,EAC1B;AACA,wBAAsB,MAAM;AAC1B,aAAS,cAAc;AAAA,EAAA,CACxB;AACuE;AACtE,6BAAyB,QAAQ;AAAA,EACnC;AACF;AACA,MAAM,eAAe,aAAa;AAClC,SAAS,YAAY;AACf,MAAA,OAAO,WAAW,aAAa;AAC1B,WAAA;AAAA,EACT;AACI,MAAA,OAAO,eAAe,aAAa;AAC9B,WAAA;AAAA,EACT;AACI,MAAA,OAAO,WAAW,aAAa;AAC1B,WAAA;AAAA,EACT;AACI,MAAA,OAAO,OAAO,aAAa;AACtB,WAAA;AAAA,EACT;AACF;AACA,SAAS,aAAa,eAAe,YAAY,MAAM;AACrD,QAAM,SAAS;AACf,SAAO,UAAU;AACuD;AACtD,oBAAA,OAAO,8BAA8B,MAAM;AAAA,EAC7D;AACM,QAAA,MAAM,aAAa,eAAe,SAAS;AACjD,QAAM,aAAa,IAAI;AACvB,gBAAc,WAAW,MAAM;AACzBG,QAAAA,eAAc,CAAC,iBAAiB;AACpC,iBAAa,aAAa;AAC1B,iBAAa,YAAY;AAClB,WAAA;AAAA,EAAA;AAET,QAAMC,mBAAkB,SAASC,kBAAiB,cAAc,SAAS;AACvE,WAAO,eAAeF,aAAY,YAAY,GAAG,OAAO;AAAA,EAAA;AAEpD,QAAA,mBAAmB,SAAS,kBAAkB,WAAW;AACtD,WAAA,aAAa,iBAAiB,UAAU,CAAC;AAAA,EAAA;AAE9C,MAAA,QAAQ,SAAS,QAAQ;AAC3B,kBAAc,SAAS;AACvB,UAAM,WAAW;AAAA,MACfA,aAAY,EAAE,MAAM,eAAe;AAAA,MACnC;AAAA,QACE,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,OAAO,CAAC;AAAA,QACR,OAAO;AAAA,MACT;AAAA,IAAA;AAEF,QAAI,YAAY,SAAS;AAC+C;AACtE,sBAAgB,KAAK,OAAO;AAAA,IAC9B;AACA,aAAS,OAAO;AAChB,aAAS,mBAAmBC;AAC5B,aAAS,oBAAoB;AAC7B,eAAW,eAAe;AACnB,WAAA;AAAA,EAAA;AAEL,MAAA,UAAU,SAAS,UAAU;AAC/B,SAAK,wBAAwB;AAAA,EAAA;AAExB,SAAA;AACT;AA+CA,SAAS,oBAAoB,MAAM,MAAM,YAAY,UAAU;AACvD,MAAA,WAAW,IAAI,GAAG;AAClB,eAAW,MAAM,KAAK,KAAK,UAAU,GAAG,QAAQ;AAAA,EACpD;AACJ;AACA,SAASE,YAAU,SAAS,UAAU,YAAY;AACxC,QAAA,SAAS,QAAQ,UAAU,WAAW;AACxC,MAAA,CAAC,UAAU,WAAW,aAAa;AAEnC;AAAA,EACJ;AACA,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,SAAS;AACnC,QAAI,mBAAmB,MAAM,QAAQ,IAAI,GAAG,KAAK,GAAG;AAC1C,YAAA,QAAQ,QAAQ,IAAI;AACtB,UAAA,QAAQ,KAAK,GAAG;AACV,cAAA,QAAQ,CAAC,SAAS,oBAAoB,MAAM,MAAM,YAAY,QAAQ,CAAC;AAAA,MAAA,OAE5E;AACmB,4BAAA,MAAM,OAAO,YAAY,QAAQ;AAAA,MACzD;AAAA,IACJ;AAAA,EAAA,CACH;AACL;AAEA,SAASC,eAAa,SAAS,UAAU,YAAY;AACvCD,cAAA,SAAS,UAAU,UAAU;AAC3C;AAEA,SAAS,IAAI,QAAQ,KAAK,KAAK;AACnB,SAAA,OAAO,GAAG,IAAI;AAC1B;AACA,SAAS,YAAY,WAAW,MAAM;AAC5B,QAAA,KAAK,KAAK,MAAM;AACtB,MAAI,IAAI;AACG,WAAA,GAAG,GAAG,IAAI;AAAA,EACrB;AACQ,UAAA,MAAM,UAAU,MAAM,YAAY;AACnC,SAAA;AACX;AAEA,SAAS,mBAAmB,KAAK;AACvB,QAAA,mBAAmB,IAAI,OAAO;AACpC,SAAO,SAAS,aAAa,KAAK,UAAU,MAAM;AAC9C,QAAI,kBAAkB;AACD,uBAAA,KAAK,UAAU,IAAI;AAAA,IACxC;AACA,UAAM,cAAc,IAAI;AACxB,QAAI,CAAC,eAAe,CAAC,YAAY,OAAO;AAC9B,YAAA;AAAA,IACV;AACI,QAAA,YAAY,QAAQ,GAAG;AACvB;AACgB,oBAAA,MAAM,UAAU,UAAU,GAAG;AAAA,MAC7C;AAAA,IAAA,OAEC;AACD,eAAS,KAAK,MAAM,WAAW,SAAS,EAAE,QAAQ,MAAM,KAAK;AAAA,IACjE;AAAA,EAAA;AAER;AACA,SAAS,aAAa,IAAI,MAAM;AAC5B,SAAO,KAAK,CAAC,GAAG,IAAI,IAAI,CAAG,EAAA,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI;AACpD;AACA,SAAS,0BAA0B,uBAAuB;AACpC,oBAAA,QAAQ,CAAC,SAAS;AAChC,0BAAsB,IAAI,IAAI;AAAA,EAAA,CACjC;AACL;AAEA,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,IAAI,OAAO,SAAS,YAAY;AAC5B,aAAW,SAAU,KAAK;AACtB,UAAM,OAAO,GAAG,EAAE,QAAQ,iBAAiB,EAAE;AAC7C,QAAI,CAAC,MAAM,KAAK,GAAG,GAAG;AACZ,YAAA,IAAI,MAAM,0FAA0F;AAAA,IAC9G;AAEA,WAAO,KAAK,MAAM,KAAK,IAAI,SAAS,EAAE;AAClC,QAAA;AACJ,QAAI,SAAS;AACT,QAAA;AACA,QAAA;AACJ,QAAI,IAAI;AACD,WAAA,IAAI,IAAI,UAAS;AACpB,eACK,IAAI,QAAQ,IAAI,OAAO,GAAG,CAAC,KAAK,KAC5B,IAAI,QAAQ,IAAI,OAAO,GAAG,CAAC,KAAK,MAC/B,KAAK,IAAI,QAAQ,IAAI,OAAO,GAAG,CAAC,MAAM,KACvC,KAAK,IAAI,QAAQ,IAAI,OAAO,GAAG,CAAC;AACzC,gBACI,OAAO,KACD,OAAO,aAAc,UAAU,KAAM,GAAG,IACxC,OAAO,KACH,OAAO,aAAc,UAAU,KAAM,KAAM,UAAU,IAAK,GAAG,IAC7D,OAAO,aAAc,UAAU,KAAM,KAAM,UAAU,IAAK,KAAK,SAAS,GAAG;AAAA,IAC7F;AACO,WAAA;AAAA,EAAA;AAEf,OACK;AAEU,aAAA;AACf;AACA,SAAS,iBAAiB,KAAK;AACpB,SAAA,mBAAmB,SAAS,GAAG,EACjC,MAAM,EAAE,EACR,IAAI,SAAU7B,IAAG;AACX,WAAA,OAAO,OAAOA,GAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;AAAA,EAAA,CAC9D,EACI,KAAK,EAAE,CAAC;AACjB;AACA,SAAS,qBAAqB;AAC1B,QAAM,QAAQ+B,MAAI,eAAe,cAAc,KAAK;AAC9C,QAAA,WAAW,MAAM,MAAM,GAAG;AAChC,MAAI,CAAC,SAAS,SAAS,WAAW,GAAG;AAC1B,WAAA;AAAA,MACH,KAAK;AAAA,MACL,MAAM,CAAC;AAAA,MACP,YAAY,CAAC;AAAA,MACb,cAAc;AAAA,IAAA;AAAA,EAEtB;AACI,MAAA;AACA,MAAA;AACA,eAAW,KAAK,MAAM,iBAAiB,SAAS,CAAC,CAAC,CAAC;AAAA,WAEhD,OAAO;AACV,UAAM,IAAI,MAAM,wBAAwB,MAAM,OAAO;AAAA,EACzD;AACS,WAAA,eAAe,SAAS,MAAM;AACvC,SAAO,SAAS;AAChB,SAAO,SAAS;AACT,SAAA;AACX;AACA,SAAS,WAAW,kBAAkB;AACjB,mBAAA,eAAe,SAAU,QAAQ;AACxC,UAAA,EAAE,SAAS;AACV,WAAA,KAAK,QAAQ,MAAM,IAAI;AAAA,EAAA;AAEjB,mBAAA,qBAAqB,SAAU,cAAc;AACpD,UAAA,EAAE,eAAe;AACvB,WAAO,KAAK,aAAa,OAAO,KAAK,WAAW,QAAQ,YAAY,IAAI;AAAA,EAAA;AAE5E,mBAAiB,kBAAkB,WAAY;AACrC,UAAA,EAAE,iBAAiB;AAClB,WAAA,eAAe,KAAK;EAAI;AAEvC;AAEA,SAAS,QAAQ,KAAK;AAClB,QAAM,YAAY,IAAI;AAQZ,YAAA,eAAe,yBAAyB,KAAK,kBAAkB;AACzE,4BAA0B,UAAU,qBAAqB;AACzD,QAAM,mBAAmB,UAAU;AACnC;AACI,eAAW,gBAAgB;AAAA,EAC/B;AACyB;AACrB,qBAAiB,OAAO;AACxB,qBAAiB,gBAAgBD;AACjC,qBAAiB,cAAc;AAAA,EACnC;AACA;AACIC,UAAI,uBAAuB,GAAG;AAAA,EAClC;AACJ;AAEA,MAAM,cAAqB,uBAAA,OAAO,IAAI;AAOtC,SAAS,yBAAyBV,MAAK;AACnC,SAAO,YAAYA,IAAG;AAC1B;AACA,SAAS,uBAAuB,IAAI;AAChC,MAAI,CAAC,IAAI;AACL;AAAA,EACJ;AACA,QAAM,CAACA,MAAK,OAAO,IAAI,GAAG,MAAM,GAAG;AAC/B,MAAA,CAAC,YAAYA,IAAG,GAAG;AACnB;AAAA,EACJ;AACA,SAAO,YAAYA,IAAG,EAAE,SAAS,OAAO,CAAC;AAC7C;AAEA,IAAI,SAAS;AAAA,EACT,QAAQ,KAAK;AACT,YAAQ,GAAG;AACP,QAAA,OAAO,iBAAiB,2BACxB;AACJ,UAAM,WAAW,IAAI;AACjB,QAAA,QAAQ,SAAS,MAAM,eAAe;AACtC,YAAM,WAAW,SAAS,KAAK,KAAK,aAAa;AACjD,YAAM3B,aAAY;AAClB,UAAIA,YAAW;AACXA,mBAAU,QAAQ;AAAA,MAAA,OAEjB;AAEG,YAAA,OAAO,yBAAyB,aAAa;AAE7C,+BAAqB,QAAQ;AAAA,QACjC;AAAA,MACJ;AACO,aAAA;AAAA,IAAA;AAAA,EAEf;AACJ;AACA,SAAS,eAAe;AACpB,QAAM,SAII;AACV,MAAI,OAAO,WAAW,eAClB,OAAO,OAAO,MAAM,MAAM,aAAa;AACvC,WAAO,OAAO,MAAM;AAAA,EAAA,WAGf,OAAO,OAAO,aAAa;AAGhC,WAAO,GAAG,MAAM;AAAA,EACpB;AACJ;AAoBA,SAAS,IAAI,OAAO,KAAK;AACrB,QAAM,WAAW;AACjB,QAAM,MAAM,SAAS;AAEf,QAAA,WAAW,OAAO,QAAQ,gBAC3B,IAAI,gBAAgB,eACjB,IAAI,gBAAgB,WACpB,IAAI,gBAAgB,cACvB,SAAS,GAAG,KAAK,OAAO,QAAQ,YAC/B,MAAM,MACN;AACA,QAAA,OAAO,MAAM,SAAS,QAAQ;AACpC,QAAM,aAAa,IAAI;AACvB,MAAI,CAAC,OAAO;AAER,WAAO,WAAW,IAAI;AACf,WAAA;AAAA,EACX;AACM,QAAA,kBAAkB,WAAW,IAAI;AACvC,MAAI,iBAAiB;AAEjB,oBAAgB,QAAQ;AAAA,EAAA,OAEvB;AAED,eAAW,IAAI,IAAI,cAAc,OAAO,QAAQ;AAAA,EACpD;AACO,SAAA;AACX;AACA,SAAS,cAAc,cAAc,UAAU;AACrC,QAAA,UAAU,CAAC+B,OAAM;AACnB,iBAAaA,EAAC;AACV,QAAA,OAAO,CAACA,EAAC;AACT,QAAA,YAAY,SAAS,IAAI,wBAAwB;AAC7C,UAAA,OAAOA,GAAE,WAAW,UAAU;AAC9BA,WAAE,SAAS,SAAS,IAAI,uBAAuBA,GAAE,MAAM;AAAA,MAC3D;AAAA,IACJ;AACA,QAAIA,GAAE,UAAUA,GAAE,OAAO,UAAU;AAC/B,aAAOA,GAAE,OAAO;AAAA,IACpB;AACA,UAAM,aAAa,QAAQ;AACrB,UAAA,SAAS,MAAM,2BAA2B,8BAA8BA,IAAG,UAAU,GAAG,UAAU,GAAyC,IAAI;AAErJ,UAAM,cAAcA,GAAE;AAChB,UAAA,YAAY,cACZ,YAAY,UACR,OAAO,YAAY,QAAQ,SAAS,MAAM,SAC1C,QACJ;AACN,QAAI,QAAQ,SAASA,GAAE,IAAI,KAAK,CAAC,WAAW;AACxC,iBAAW,MAAM;AAAA,IAAA,OAEhB;AACD,YAAM,MAAM;AACRA,UAAAA,GAAE,SAAS,YAAY,QAAQ,GAAG,KAAKvC,YAAU,GAAG,IAAI;AACxD;AAAA,MACJ;AACO,aAAA;AAAA,IACX;AAAA,EAAA;AAEJ,UAAQ,QAAQ;AACT,SAAA;AACX;AAEA,MAAM,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,SAAS,aAAa,OAAO,UAAU;AAC/B,MAAA,MAAM,QAAQ,MAAM,QAAQ;AAC5B,UAAM,iBAAiB;AACvB,UAAM,kBAAkB;AACxB,UAAM,2BAA2B;AACjC,QAAI,CAAC,OAAO,OAAO,QAAQ,GAAG;AAC1B,YAAM,SAAS;IACnB;AACI,QAAA,OAAO,OAAO,UAAU,GAAG;AAC3B,YAAM,SAAS,OAAO,MAAM,WAAW,WAAW,MAAM,SAAS;AAC3D,YAAA,OAAO,WAAW,MAAM;AAAA,IAClC;AAEA,QAAI,cAAc,MAAM,MAAM,KAC1B,OAAO,MAAM,QAAQ,SAAS,KAC9B,CAAC,OAAO,MAAM,QAAQ,OAAO,GAAG;AAC1B,YAAA,OAAO,QAAQ,MAAM,OAAO;AAAA,IACtC;AACI,QAAA,cAAc,MAAM,MAAM,GAAG;AAC7B,YAAM,SAAS,OAAO,IAAI,MAAM,QAAQ,MAAM,MAAM;AAAA,IACxD;AAAA,EACJ;AACJ;AACA,SAAS,8BAA8BuC,IAAG,OAAO;AACzC,MAAA,QAAQ,KAAK,GAAG;AAChB,UAAM,eAAeA,GAAE;AACvBA,OAAE,2BAA2B,MAAM;AACf,sBAAA,aAAa,KAAKA,EAAC;AACnCA,SAAE,WAAW;AAAA,IAAA;AAEV,WAAA,MAAM,IAAI,CAAC,OAAO,CAACA,OAAM,CAACA,GAAE,YAAY,GAAGA,EAAC,CAAC;AAAA,EAAA,OAEnD;AACM,WAAA;AAAA,EACX;AACJ;AAKA,SAAS,KAAK,QAAQ,YAAY;AAC1B,MAAA;AACJ,MAAI,QAAQ,MAAM,KAAK,SAAS,MAAM,GAAG;AAC/B,UAAA,IAAI,MAAM,OAAO,MAAM;AAC7B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC3C,UAAI,CAAC,IAAI,WAAW,OAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IACvC;AAAA,EAAA,WAEK,OAAO,WAAW,UAAU;AACjC,QAA+C,CAAC,OAAO,UAAU,MAAM,GAAG;AACjE,WAAA,mDAAmD,MAAM,GAAG;AACjE,aAAO;IACX;AACM,UAAA,IAAI,MAAM,MAAM;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,UAAI,CAAC,IAAI,WAAW,IAAI,GAAG,GAAG,CAAC;AAAA,IACnC;AAAA,EAAA,WAEK7C,WAAS,MAAM,GAAG;AACnB,QAAA,OAAO,OAAO,QAAQ,GAAG;AACnB,YAAA,MAAM,KAAK,QAAQ,CAAC,MAAM,MAAM,WAAW,MAAM,GAAG,CAAC,CAAC;AAAA,IAAA,OAE3D;AACK,YAAA,OAAO,OAAO,KAAK,MAAM;AACzB,YAAA,IAAI,MAAM,KAAK,MAAM;AAC3B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AACnC,cAAA,MAAM,KAAK,CAAC;AAClB,YAAI,CAAC,IAAI,WAAW,OAAO,GAAG,GAAG,KAAK,CAAC;AAAA,MAC3C;AAAA,IACJ;AAAA,EAAA,OAEC;AACD,UAAM,CAAA;AAAA,EACV;AACO,SAAA;AACX;AA2IA,MAAM,IAAI,CAAC,OAAO,QAAQ,IAAI,OAAO,GAAG;AACxC,MAAM,IAAI,CAAC,QAAQ,eAAe,KAAK,QAAQ,UAAU;AAMzD,MAAM,IAAI,CAAC,WAAW,YAAY,OAAO,QAAQ,GAAG,OAAO;AAE3D,MAAM,IAAI,CAAC,UAAU,eAAe,KAAK;AACzC,MAAM,IAAI,CAAC,QAAQ,gBAAgB,GAAG;AAOtC,SAASc,YAAU,eAAe,YAAY,MAAM;AAChD,oBAAkB,cAAc,SAAS;AACzC,SAAO,aAAa,eAAe,SAAS,EAAE,IAAI,MAAM;AAC5D;AACA,MAAM,eAAeA;AC5sLrB,SAASsC,sBAAoB;AACzB,MAAI,iBAAiB;AACrB;AACU,UAAA,cAAc,GAAG;AACvB,UAAM,WAAW,eAAe,YAAY,WAAW,YAAY,WAAW;AAC7D,qBAAA,gBAAgB,QAAQ,KAAK;AAAA,EAClD;AACO,SAAA;AACX;AAEA,SAAS,qBAAqB,MAAM,KAAK;AACrC,UAAQ,KAAK,GAAG,IAAI,KAAK,GAAG,EAAE;AAClC;AACA,SAAS,iBAAiB,MAAM,MAAM,UAAU,QAAQ;AACpD,MAAI,CAAC,QAAQ;AACA,aAAA;AAAA,EACb;AACA,aAAW,OAAO,UAAU;AACxB,UAAM,SAAS,aAAa,KAAK,KAAK,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC,OAAO,MAAM,GAAG,CAAC;AACzE,QAAA,SAAS,MAAM,GAAG;AAClB,aAAO,MAAM,MAAM;AAAA,IACvB;AAAA,EACJ;AACJ;AACA,SAAS,kBAAkB,MAAM,MAAM,UAAU,QAAQ;AACrD,MAAI,CAAC,UAAU;AACX;AAAA,EACJ;AACI,MAAA,CAAC,QAAQ,QAAQ,GAAG;AACb,WAAA,iBAAiB,MAAM,KAAK,CAAC,4BAAY,OAAO,IAAI,GAAG,UAAU,MAAM;AAAA,EAClF;AACA,QAAM,MAAM,SAAS;AACrB,QAAM,UAAU,KAAK;AACrB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AACpB,UAAA,OAAO,SAAS,CAAC;AACjB,UAAA,OAAc,uBAAA,OAAO,IAAI;AAC/B,QAAI,UAAU,GAAG;AACb,WAAK,KAAK,IAAI,IAAI,KAAK,CAAC;AAAA,IAC5B;AACiB,qBAAA,MAAM,MAAM,EAAE,CAAC,KAAK,IAAI,GAAG,QAAQ,MAAM;AAAA,EAC9D;AACJ;AACA,SAAS,aAAa,MAAM,OAAO,MAAM,UAAU;AAC3C,MAAA,CAAC,cAAc,IAAI,GAAG;AACf,WAAA,EAAE,MAAM;EACnB;AACA,QAAM,EAAE,MAAM,UAAU,UAAA,IAAc;AAEtC,MAAI,YAAY,UAAU;AACtB,WAAO,6BAA6B,OAAO;AAAA,EAC/C;AAEI,MAAA,SAAS,QAAQ,CAAC,UAAU;AAC5B;AAAA,EACJ;AAEA,MAAI,QAAQ,MAAM;AACd,QAAI,UAAU;AACd,UAAM,QAAQ,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAC1C,UAAM,gBAAgB,CAAA;AAEtB,aAAS,IAAI,GAAG,IAAI,MAAM,UAAU,CAAC,SAAS,KAAK;AACzC,YAAA,EAAE,OAAO,iBAAiB,WAAW,OAAO,MAAM,CAAC,CAAC;AAC5C,oBAAA,KAAK,gBAAgB,EAAE;AAC3B,gBAAA;AAAA,IACd;AACA,QAAI,CAAC,SAAS;AACH,aAAA,sBAAsB,MAAM,OAAO,aAAa;AAAA,IAC3D;AAAA,EACJ;AAEA,MAAI,WAAW;AACX,WAAO,UAAU,KAAK;AAAA,EAC1B;AACJ;AACA,MAAM,uCAAqC,uCAAuC;AAClF,SAAS,WAAW,OAAO,MAAM;AACzB,MAAA;AACE,QAAA,eAAe,QAAQ,IAAI;AAC7B,MAAA,aAAa,YAAY,GAAG;AAC5B,UAAMzB,KAAI,OAAO;AACT,YAAAA,OAAM,aAAa;AAEvB,QAAA,CAAC,SAASA,OAAM,UAAU;AAC1B,cAAQ,iBAAiB;AAAA,IAC7B;AAAA,EAAA,WAEK,iBAAiB,UAAU;AAChC,YAAQ3B,WAAS,KAAK;AAAA,EAAA,WAEjB,iBAAiB,SAAS;AAC/B,YAAQ,QAAQ,KAAK;AAAA,EAAA,OAEpB;AACD;AACI,cAAQ,iBAAiB;AAAA,IAC7B;AAAA,EACJ;AACO,SAAA;AAAA,IACH;AAAA,IACA;AAAA,EAAA;AAER;AACA,SAAS,sBAAsB,MAAM,OAAO,eAAe;AACnD,MAAA,UAAU,6CAA6C,IAAI,eAC9C,cAAc,IAAI,UAAU,EAAE,KAAK,IAAI,CAAC;AACnD,QAAA,eAAe,cAAc,CAAC;AAC9B,QAAA,eAAe,UAAU,KAAK;AAC9B,QAAA,gBAAgB,WAAW,OAAO,YAAY;AAC9C,QAAA,gBAAgB,WAAW,OAAO,YAAY;AAEhD,MAAA,cAAc,WAAW,KACzB,aAAa,YAAY,KACzB,CAAC,UAAU,cAAc,YAAY,GAAG;AACxC,eAAW,eAAe,aAAa;AAAA,EAC3C;AACA,aAAW,SAAS,YAAY;AAE5B,MAAA,aAAa,YAAY,GAAG;AAC5B,eAAW,cAAc,aAAa;AAAA,EAC1C;AACO,SAAA;AACX;AACA,SAAS,QAAQ,MAAM;AACnB,QAAM,QAAQ,QAAQ,KAAK,SAAS,EAAE,MAAM,oBAAoB;AACzD,SAAA,QAAQ,MAAM,CAAC,IAAI;AAC9B;AACA,SAAS,WAAW,OAAO,MAAM;AAC7B,MAAI,SAAS,UAAU;AACnB,WAAO,IAAI,KAAK;AAAA,EAAA,WAEX,SAAS,UAAU;AACjB,WAAA,GAAG,OAAO,KAAK,CAAC;AAAA,EAAA,OAEtB;AACD,WAAO,GAAG,KAAK;AAAA,EACnB;AACJ;AACA,SAAS,aAAa,MAAM;AACxB,QAAM,gBAAgB,CAAC,UAAU,UAAU,SAAS;AACpD,SAAO,cAAc,KAAK,CAAC,SAAS,KAAK,YAAA,MAAkB,IAAI;AACnE;AACA,SAAS,aAAa,MAAM;AACxB,SAAO,KAAK,KAAK,CAAC,SAAS,KAAK,YAAA,MAAkB,SAAS;AAC/D;AAEA,SAAS,SAAS,IAAI;AAClB,SAAO,WAAY;AACX,QAAA;AACO,aAAA,GAAG,MAAM,IAAI,SAAS;AAAA,aAE1B6C,IAAG;AAEN,cAAQ,MAAMA,EAAC;AAAA,IACnB;AAAA,EAAA;AAER;AAEA,IAAI,mBAAmB;AACvB,MAAM,kBAAkB,CAAA;AACxB,SAAS,kBAAkB,IAAI,MAAM,UAAU,YAAY,OAAO;AAC9D,kBAAgB,EAAE,IAAI;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEG,SAAA;AACX;AAEA,SAAS,eAAe,IAAI,KAAK,QAAQ;AACjC,MAAA,OAAO,OAAO,UAAU;AAClB,UAAA,OAAO,gBAAgB,EAAE;AAC/B,QAAI,MAAM;AACF,UAAA,CAAC,KAAK,WAAW;AACjB,eAAO,gBAAgB,EAAE;AAAA,MAC7B;AACO,aAAA,KAAK,SAAS,KAAK,MAAM;AAAA,IACpC;AAAA,EACJ;AACO,SAAA;AACX;AACA,MAAM,cAAc;AACpB,MAAM,WAAW;AACjB,MAAM,eAAe;AACrB,SAAS,gBAAgB,MAAM;AAC3B,QAAM,eAAe,CAAA;AACrB,aAAW,QAAQ,MAAM;AACf,UAAA,KAAK,KAAK,IAAI;AAChB,QAAA,WAAW,EAAE,GAAG;AACH,mBAAA,IAAI,IAAI,SAAS,EAAE;AAChC,aAAO,KAAK,IAAI;AAAA,IACpB;AAAA,EACJ;AACO,SAAA;AACX;AACA,SAAS,gBAAgB,QAAQ,MAAM;AACnC,MAAI,CAAC,UAAU,OAAO,QAAQ,OAAO,MAAM,IAAI;AAC3C,WAAO,OAAO;AAAA,EAClB;AACA,SAAO,OAAO,OAAO,UAAU,OAAO,QAAQ,OAAO,CAAC;AAC1D;AACA,SAAS,uBAAuB,MAAM,OAAO,CAAA,GAAI,EAAE,WAAW,cAAkB,IAAA,IAAI;AAC5E,MAAA,CAAC,cAAc,IAAI,GAAG;AACtB,WAAO,CAAA;AAAA,EACX;AACA,QAAM,EAAE,SAAS,MAAM,SAAS,IAAI,gBAAgB,IAAI;AAClD,QAAA,aAAa,WAAW,OAAO;AAC/B,QAAA,UAAU,WAAW,IAAI;AACzB,QAAA,cAAc,WAAW,QAAQ;AACvC,QAAM,aAAa;AACD,oBAAA,YAAY,MAAM,CAAC,QAAQ;AACzC,UAAM,OAAO;AACb,QAAI,SAAS,gBAAgB,IAAI,QAAQ,IAAI;AAClC,eAAA,SAAS,KAAK,UAAU,GAAG;AAClC,QAAA,IAAI,WAAW,OAAO,OAAO;AAC7B,iBAAW,aAAa,KAAK,cAAc,KAAK,IAAI;AACpD,oBAAc,QAAQ,GAAG;AAAA,IAAA,OAExB;AACD,iBAAW,KAAK,GAAG;AAAA,IACvB;AACA,mBAAe,SAAS,GAAG;AAAA,EAAA,CAC9B;AACM,SAAA;AACX;AAEA,MAAM,eAAe;AACrB,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,qBAAqB,CAAA;AAC3B,MAAM,qBAAqB,CAAA;AAC3B,SAAS,YAAY,MAAM,QAAQ;AAC/B,SAAO,SAAU,MAAM;AACZ,WAAA,KAAK,MAAM,MAAM,KAAK;AAAA,EAAA;AAErC;AACA,SAAS,MAAM,OAAO,MAAM,QAAQ;AAChC,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC7B,UAAA,OAAO,MAAM,CAAC;AACpB,QAAI,SAAS;AACT,gBAAU,QAAQ,QAAQ,YAAY,MAAM,MAAM,CAAC;AAAA,IAAA,OAElD;AACK,YAAA,MAAM,KAAK,MAAM,MAAM;AACzB,UAAAvC,YAAU,GAAG,GAAG;AACN,kBAAA,QAAQ,QAAQ,GAAG;AAAA,MACjC;AACA,UAAI,QAAQ,OAAO;AACR,eAAA;AAAA,UACH,OAAO;AAAA,UAAE;AAAA,UACT,QAAQ;AAAA,UAAE;AAAA,QAAA;AAAA,MAElB;AAAA,IACJ;AAAA,EACJ;AACA,SAAQ,WAAW;AAAA,IACf,KAAK,UAAU;AACX,aAAO,SAAS,IAAI;AAAA,IACxB;AAAA,IACA,QAAQ;AAAA,IAAE;AAAA,EAAA;AAElB;AACA,SAAS,eAAe+C,eAAc,UAAU,IAAI;AAChD,GAAC,cAAc,WAAW,aAAa,EAAE,QAAQ,CAAC,SAAS;AACjD,UAAA,QAAQA,cAAa,IAAI;AAC3B,QAAA,CAAC,QAAQ,KAAK,GAAG;AACjB;AAAA,IACJ;AACM,UAAA,cAAc,QAAQ,IAAI;AAChC,YAAQ,IAAI,IAAI,SAAS,oBAAoB,KAAK;AAC9C,YAAM,OAAO,KAAK,OAAO,EAAE,KAAK,CAACC,SAAQ;AACrC,eAAQ,WAAW,WAAW,KAAK,YAAYA,IAAG,KAAMA;AAAAA,MAAA,CAC3D;AAAA,IAAA;AAAA,EACL,CACH;AACM,SAAA;AACX;AACA,SAAS,mBAAmB,QAAQ,aAAa;AAC7C,QAAM,mBAAmB,CAAA;AACrB,MAAA,QAAQ,mBAAmB,WAAW,GAAG;AACxB,qBAAA,KAAK,GAAG,mBAAmB,WAAW;AAAA,EAC3D;AACM,QAAA,cAAc,mBAAmB,MAAM;AAC7C,MAAI,eAAe,QAAQ,YAAY,WAAW,GAAG;AAChC,qBAAA,KAAK,GAAG,YAAY,WAAW;AAAA,EACpD;AACiB,mBAAA,QAAQ,CAAC,SAAS;AACjB,kBAAA,KAAK,WAAW,KAAK;AAAA,EAAA,CACtC;AACM,SAAA;AACX;AACA,SAAS,uBAAuB,QAAQ;AAC9B,QAAA,cAAqB,uBAAA,OAAO,IAAI;AACtC,SAAO,KAAK,kBAAkB,EAAE,QAAQ,CAAC,SAAS;AAC9C,QAAI,SAAS,eAAe;AACxB,kBAAY,IAAI,IAAI,mBAAmB,IAAI,EAAE,MAAM;AAAA,IACvD;AAAA,EAAA,CACH;AACK,QAAA,oBAAoB,mBAAmB,MAAM;AACnD,MAAI,mBAAmB;AACnB,WAAO,KAAK,iBAAiB,EAAE,QAAQ,CAAC,SAAS;AAC7C,UAAI,SAAS,eAAe;AACZ,oBAAA,IAAI,KAAK,YAAY,IAAI,KAAK,CAAI,GAAA,OAAO,kBAAkB,IAAI,CAAC;AAAA,MAChF;AAAA,IAAA,CACH;AAAA,EACL;AACO,SAAA;AACX;AACA,SAAS,UAAU,QAAQ,KAAK,SAAS,QAAQ;AACvC,QAAA,cAAc,uBAAuB,MAAM;AACjD,MAAI,eAAe,OAAO,KAAK,WAAW,EAAE,QAAQ;AAC5C,QAAA,QAAQ,YAAY,MAAM,GAAG;AAC7B,YAAM,MAAM,MAAM,YAAY,QAAQ,OAAO;AACtC,aAAA,IAAI,KAAK,CAACC,aAAY;AAElB,eAAA,IAAI,eAAe,uBAAuB,MAAM,GAAGA,QAAO,GAAG,GAAG,MAAM;AAAA,MAAA,CAChF;AAAA,IAAA,OAEA;AACD,aAAO,IAAI,eAAe,aAAa,OAAO,GAAG,GAAG,MAAM;AAAA,IAC9D;AAAA,EACJ;AACO,SAAA,IAAI,SAAS,GAAG,MAAM;AACjC;AAEA,SAAS,YAAY,MAAM;AACvB,MAAI,cAAc,IAAI,KAClB,CAAC,aAAa,UAAU,YAAY,EAAE,KAAK,CAAC,OAAO,WAAW,KAAK,EAAE,CAAC,CAAC,GAAG;AACnE,WAAA;AAAA,EACX;AACO,SAAA;AACX;AACA,SAAS,cAAc,SAAS;AAQrB,SAAA;AACX;AACA,SAAS,YAAY,MAAM,IAAI;AAC3B,SAAO,CAAC,OAAO,OAAO,SAAS;AACvB,QAAA,YAAY,IAAI,GAAG;AACnB,aAAO,mBAAmB,MAAM,UAAU,MAAM,IAAI,MAAM,IAAI,CAAC;AAAA,IACnE;AACA,WAAO,mBAAmB,MAAM,cAAc,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjE,gBAAA,MAAM,IAAI,OAAO,MAAM,EAAE,SAAS,SAAS,MAAM,QAAQ,GAAG,IAAI;AAAA,IAC7E,CAAA,CAAC,CAAC;AAAA,EAAA;AAEX;AAEA,SAAS,cAAc,MAAM,SAAS;AAClC,OAAK,CAAC;AACN;AACI;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,IAAI,MAAM,KAAK;AAClC,QAAM,SAAS;AAAA,IACX,QAAQ,OAAO;AAAA,EAAA;AAEnB,SAAO,eAAe,IAAI,OAAQ,OAAO,CAAC,GAAI,MAAM,CAAC;AACzD;AACA,SAAS,WAAW,IAAI,MAAM,QAAQ,SAAS,CAAA,GAAI;AAC/C,QAAM,eAAe,OAAO;AAC5B,MAAI,YAAY;AAChB,MAAI,CAAC,QAAQ;AACG,gBAAA;AAAA,EAEP,WAAA,OAAO,QAAQ,YAAY,MAAM,GAAG;AAC7B,gBAAA;AAAA,EAAA,OAEX;AACD,gBAAY,eAAe,MAAM;AAAA,EACrC;AACA;AACI,WAAO,OAAO;AAAA,EAClB;AACA,MAAI,MAAM,OAAO,EAAE,QAAQ,UAAA,GAAa,MAAM;AACvC,SAAA,eAAe,IAAI,GAAG;AACjC;AACA,SAAS,gBAAgB,MAAM,MAAM,UAAU,SAAS;AACP;AACvB,sBAAA,MAAM,MAAM,QAAQ;AAAA,EAC1C;AACM,QAAA,SAAS,cAAc,IAAI;AACjC,MAAI,QAAQ;AACD,WAAA;AAAA,EACX;AACJ;AACA,SAAS,YAAY,QAAQ;AACzB,MAAI,CAAC,UAAU,SAAS,MAAM,GAAG;AACtB,WAAA;AAAA,EACX;AACA,MAAI,OAAO,OAAO;AAEd,QAAK,OAAO,eAAe,eAAe,CAAC,WAAW,gBAAiB;AACnE,cAAQ,MAAM,OAAO,UAAU,OAAO,OAAO,KAAK;AAAA,IACtD;AACA,WAAO,OAAO;AAAA,EAClB;AACO,SAAA;AACX;AACA,SAAS,eAAe,MAAM,IAAI,UAAU,SAAS;AACjD,SAAO,CAAC,SAAS;AACb,UAAM,KAAK,uBAAuB,MAAM,MAAM,OAAO;AACrD,UAAM,SAAS,gBAAgB,MAAM,CAAC,IAAI,GAAG,QAAQ;AACrD,QAAI,QAAQ;AACD,aAAA,WAAW,IAAI,MAAM,MAAM;AAAA,IACtC;AACA,WAAO,GAAG,MAAM;AAAA,MACZ,SAAS,CAAC,QAAQ,cAAc,IAAI,MAAM,GAAG;AAAA,MAC7C,QAAQ,CAACC,SAAQ,WAAW,WAAW,IAAI,MAAM,YAAYA,OAAM,GAAG,MAAM;AAAA,IAAA,CAC/E;AAAA,EAAA;AAET;AACA,SAAS,eAAe,MAAM,IAAI,UAAU,SAAS;AACjD,SAAO,IAAI,SAAS;AAChB,UAAM,SAAS,gBAAgB,MAAM,MAAM,QAAQ;AACnD,QAAI,QAAQ;AACF,YAAA,IAAI,MAAM,MAAM;AAAA,IAC1B;AACO,WAAA,GAAG,MAAM,MAAM,IAAI;AAAA,EAAA;AAElC;AACA,SAAS,gBAAgB,MAAM,IAAI,UAAU,SAAS;AAClD,SAAO,eAAe,MAAM,IAAI,UAAU,OAAO;AACrD;AACA,SAAS,cAAc,MAAM,IAAI,UAAU,SAAS;AAChD,SAAO,eAAe,MAAM,IAA8C,QAAoB;AAClG;AACA,SAAS,eAAe,MAAM,IAAI,UAAU,SAAS;AAC1C,SAAA,YAAY,MAAM,gBAAgB,MAAM,IAA8C,UAAsB,OAAO,CAAC;AAC/H;AAEA,MAAM,aAAa;AACnB,MAAM,iBAAiB;AAAA,EACnB;AAAA,IACI,MAAM;AAAA,IACN,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,UAAU;AAAA,EACd;AACJ;AAEA,MAAM,MAAM;AACZ,MAAM,oBAAoB;AAC1B,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,SAAS,mBAAmB;AAClB,QAAA,EAAE,aAAa,YAAY,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG,GAAG,iBAAiB;AAAA,IAC5E,UAAU,GAAG,cAAA,EAAgB;AAAA,EAAA,CAChC;AAES,gBAAA;AACF,cAAA;AACZ,UAAQ,aAAa;AACzB;AACA,MAAM,SAAS,cAAc,YAAY,CAAC,QAAQ,mBAAmB;AACjE,MAAI,gBAAgB,GAAG;AACF;EACrB;AACA,WAAS,OAAO,MAAM;AACtB,MAAI,WAAW,GAAG;AACP,WAAA;AAAA,EACX;AACA,MAAI,QAAQ,kBAAkB;AAC1B,MAAA,SAAU,SAAS,oBAAqB;AAC5C,MAAI,SAAS,GAAG;AACZ,aAAS,CAAC;AAAA,EACd;AACS,WAAA,KAAK,MAAM,SAAS,GAAG;AAChC,MAAI,WAAW,GAAG;AACV,QAAA,cAAc,KAAK,CAAC,OAAO;AAClB,eAAA;AAAA,IAAA,OAER;AACQ,eAAA;AAAA,IACb;AAAA,EACJ;AACO,SAAA,SAAS,IAAI,CAAC,SAAS;AAClC,GAAG,cAAc;AAEjB,SAAS,MAAM,MAAM,aAAa,MAAM;AACpC,MAAI,UAAU;AACV,SAAK,KAAK,QAAQ;AAAA,EACtB;AACA,UAAQ,IAAI,EAAE,MAAM,SAAS,IAAI;AACrC;AAEA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB;AAC/B,MAAM,yBAAyB;AAAA,EAC3B;AAAA,IACI,MAAM;AAAA,IACN,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,UAAU;AAAA,EACd;AACJ;AACA,MAAM,4BAA4B;AAElC,SAAS,qBAAqBH,eAAc,aAAa;AACrD,SAAO,KAAK,WAAW,EAAE,QAAQ,CAAC,SAAS;AACvC,QAAI,WAAW,YAAY,IAAI,CAAC,GAAG;AAC/BA,oBAAa,IAAI,IAAI,UAAUA,cAAa,IAAI,GAAG,YAAY,IAAI,CAAC;AAAA,IACxE;AAAA,EAAA,CACH;AACL;AACA,SAAS,sBAAsBA,eAAc,aAAa;AAClD,MAAA,CAACA,iBAAgB,CAAC,aAAa;AAC/B;AAAA,EACJ;AACA,SAAO,KAAK,WAAW,EAAE,QAAQ,CAAC,SAAS;AACjC,UAAA,QAAQA,cAAa,IAAI;AACzB,UAAA,OAAO,YAAY,IAAI;AAC7B,QAAI,QAAQ,KAAK,KAAK,WAAW,IAAI,GAAG;AACpC,aAAO,OAAO,IAAI;AAAA,IACtB;AAAA,EAAA,CACH;AACL;AACA,SAAS,UAAU,WAAW,UAAU;AACpC,QAAM,MAAM,WACN,YACI,UAAU,OAAO,QAAQ,IACzB,QAAQ,QAAQ,IACZ,WACA,CAAC,QAAQ,IACjB;AACC,SAAA,MAAM,YAAY,GAAG,IAAI;AACpC;AACA,SAAS,YAAY,OAAO;AACxB,QAAM,MAAM,CAAA;AACZ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,IAAI,QAAQ,MAAM,CAAC,CAAC,MAAM,IAAI;AAC1B,UAAA,KAAK,MAAM,CAAC,CAAC;AAAA,IACrB;AAAA,EACJ;AACO,SAAA;AACX;AACA,MAAM,iBAAiB,cAAc,qBAAqB,CAAC,QAAQ,gBAAgB;AAC/E,MAAI,SAAS,MAAM,KAAK,cAAc,WAAW,GAAG;AAC3B,yBAAA,mBAAmB,MAAM,MAAM,mBAAmB,MAAM,IAAI,CAAA,IAAK,WAAW;AAAA,EAAA,WAE5F,cAAc,MAAM,GAAG;AAC5B,yBAAqB,oBAAoB,MAAM;AAAA,EACnD;AACJ,GAAG,sBAAsB;AACzB,MAAM,oBAAoB,cAAc,wBAAwB,CAAC,QAAQ,gBAAgB;AACjF,MAAA,SAAS,MAAM,GAAG;AACd,QAAA,cAAc,WAAW,GAAG;AACN,4BAAA,mBAAmB,MAAM,GAAG,WAAW;AAAA,IAAA,OAE5D;AACD,aAAO,mBAAmB,MAAM;AAAA,IACpC;AAAA,EAAA,WAEK,cAAc,MAAM,GAAG;AAC5B,0BAAsB,oBAAoB,MAAM;AAAA,EACpD;AACJ,GAAG,yBAAyB;AAC5B,MAAM,eAAe,CAAA;AAErB,MAAM,SAAS;AACf,MAAM,aAAa;AAAA,EACf;AAAA,IACI,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,EACd;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,EACd;AACJ;AACA,MAAM,WAAW;AACjB,MAAM,eAAe;AACrB,MAAM,UAAU;AAChB,MAAM,cAAc;AAAA,EAChB;AAAA,IACI,MAAM;AAAA,IACN,MAAM,CAAC,QAAQ,KAAK;AAAA,EACxB;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,MAAM,CAAC,UAAU,MAAM;AAAA,EAC3B;AACJ;AACA,MAAM,WAAW;AACjB,MAAM,eAAe;AAAA,EACjB;AAAA,IACI,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,EACd;AACJ;AAEA,MAAM,SAAS;AAAA,EACX,cAAc;AACL,SAAA,WAAW,IAAII;EACxB;AAAA,EACA,GAAG,MAAM,UAAU;AACf,WAAO,KAAK,SAAS,GAAG,MAAM,QAAQ;AAAA,EAC1C;AAAA,EACA,KAAK,MAAM,UAAU;AACjB,WAAO,KAAK,SAAS,KAAK,MAAM,QAAQ;AAAA,EAC5C;AAAA,EACA,IAAI,MAAM,UAAU;AAChB,QAAI,CAAC,MAAM;AACF,WAAA,SAAS,IAAI;AAClB;AAAA,IACJ;AACK,SAAA,SAAS,IAAI,MAAM,QAAQ;AAAA,EACpC;AAAA,EACA,KAAK,SAAS,MAAM;AAChB,SAAK,SAAS,KAAK,MAAM,GAAG,IAAI;AAAA,EACpC;AACJ;AACA,MAAM,WAAW,IAAI;AACrB,MAAM,MAAM,cAAc,QAAQ,CAAC,MAAM,aAAa;AACzC,WAAA,GAAG,MAAM,QAAQ;AAC1B,SAAO,MAAM,SAAS,IAAI,MAAM,QAAQ;AAC5C,GAAG,UAAU;AACb,MAAM,QAAQ,cAAc,UAAU,CAAC,MAAM,aAAa;AAC7C,WAAA,KAAK,MAAM,QAAQ;AAC5B,SAAO,MAAM,SAAS,IAAI,MAAM,QAAQ;AAC5C,GAAG,YAAY;AACf,MAAM,OAAO,cAAc,SAAS,CAAC,MAAM,aAAa;AAEhD,MAAA,CAAC,QAAQ,IAAI;AACb,WAAO,OAAO,CAAC,IAAI,IAAI,CAAA;AACtB,OAAA,QAAQ,CAACzC,OAAM;AACP,aAAA,IAAIA,IAAG,QAAQ;AAAA,EAAA,CAC3B;AACL,GAAG,WAAW;AACd,MAAM,QAAQ,cAAc,UAAU,CAAC,SAAS,SAAS;AAC5C,WAAA,KAAK,MAAM,GAAG,IAAI;AAC/B,GAAG,YAAY;AAEf,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,qBAAqB,SAAS;AAC/B,MAAA;AACO,WAAA,KAAK,MAAM,OAAO;AAAA,WAEtB6B,IAAG;AAAA,EAAE;AACL,SAAA;AACX;AAKA,SAAS,mBAAmB,MAAM;AAC1B,MAAA,KAAK,SAAS,WAAW;AACf,cAAA;AAAA,EAAA,WAEL,KAAK,SAAS,YAAY;AAC/B,UAAM,KAAK;AACX,gBAAY,KAAK;AACS,8BAAA,KAAK,KAAK,MAAM;AAAA,EAAA,WAErC,KAAK,SAAS,WAAW;AAC9B,UAAM,UAAU;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,qBAAqB,KAAK,OAAO;AAAA,IAAA;AAE3C,aAAS,IAAI,GAAG,IAAI,uBAAuB,QAAQ,KAAK;AAC9C,YAAA,WAAW,uBAAuB,CAAC;AACzC,eAAS,OAAO;AAEhB,UAAI,QAAQ,SAAS;AACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EAAA,WAEK,KAAK,SAAS,SAAS;AACL,2BAAA,QAAQ,CAAC,aAAa;AAChC,eAAA;AAAA,QACL,MAAM;AAAA,QACN,MAAM,qBAAqB,KAAK,OAAO;AAAA,MAAA,CAC1C;AAAA,IAAA,CACJ;AAAA,EACL;AACJ;AACA,MAAM,sBAAsB,CAAA;AAC5B,SAAS,0BAA0Ba,MAAK,QAAQ;AACxB,sBAAA,QAAQ,CAAC,aAAa;AACtC,aAASA,MAAK,MAAM;AAAA,EAAA,CACvB;AACD,sBAAoB,SAAS;AACjC;AACA,MAAM,yBAAyB;AAC/B,MAAM,kBAAkB,eAAe,wBAAwB,CAAC,GAAG,EAAE,SAAS,aAAa;AAC/E,UAAA,UAAU,KAAK,MAAM;AACrB,QAAA,OAAO,YAAY,aAAa;AACtB,gBAAA;AACJ,YAAA;AACM,kBAAA;AAAA,IAChB;AACoB,wBAAA,KAAK,CAACA,MAAK,WAAW;AACtC,UAAIA,MAAK;AACG,gBAAA,EAAE,KAAAA,KAAAA,CAAK;AAAA,MAAA,OAEd;AACD,eAAO,MAAM;AAAA,MACjB;AAAA,IAAA,CACH;AACG,QAAA,OAAO,QAAQ,aAAa;AAC5B,gCAA0B,KAAK,SAAS;AAAA,IAC5C;AAAA,EAAA,CACH;AACL,CAAC;AACD,MAAM,yBAAyB,CAAA;AAE/B,MAAM,gBAAgB,CAAC,OAAO;AAC1B,MAAI,uBAAuB,QAAQ,EAAE,MAAM,IAAI;AAC3C,2BAAuB,KAAK,EAAE;AAAA,EAClC;AACJ;AACA,MAAM,iBAAiB,CAAC,OAAO;AAC3B,MAAI,CAAC,IAAI;AACL,2BAAuB,SAAS;AAAA,EAAA,OAE/B;AACKtB,UAAAA,SAAQ,uBAAuB,QAAQ,EAAE;AAC/C,QAAIA,SAAQ,IAAI;AACW,6BAAA,OAAOA,QAAO,CAAC;AAAA,IAC1C;AAAA,EACJ;AACJ;AAEA,MAAM,cAAc;AACpB,MAAM,iBAAiB;AAEvB,MAAM,qBAAqB,CAAC,qBAAqB;AACjD,MAAM,YAAY,CAAC,WAAW,gBAAgB,cAAc,eAAe;AAE3E,MAAM,YAAY,CAAC,qBAAqB;AACxC,MAAM,kBAAkB;AACxB,SAAS,aAAa,MAAM;AACxB,SAAO,eAAe,KAAK,IAAI,KAAK,mBAAmB,QAAQ,IAAI,MAAM;AAC7E;AACA,SAAS,UAAU,MAAM;AACrB,SAAO,YAAY,KAAK,IAAI,KAAK,UAAU,QAAQ,IAAI,MAAM;AACjE;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,gBAAgB,KAAK,IAAI,KAAK,SAAS;AAClD;AACA,SAAS,UAAU,MAAM;AACd,SAAA,UAAU,QAAQ,IAAI,MAAM;AACvC;AACA,SAAS,cAAc,MAAM;AACrB,MAAA,aAAa,IAAI,KAAK,UAAU,IAAI,KAAK,cAAc,IAAI,GAAG;AACvD,WAAA;AAAA,EACX;AACO,SAAA;AACX;AAEA,IAAI,CAAC,QAAQ,UAAU,SAAS;AACpB,UAAA,UAAU,UAAU,SAAU,WAAW;AAC7C,UAAM,UAAU,KAAK;AACd,WAAA,KAAK,KAAK,CAAC,UAAU,QAAQ,QAAQ,aAAa,UAAU,CAAC,EAAE,KAAK,MAAM,KAAK,GAAG,CAAC,WAAW,QAAQ,QAAQ,aAAa,UAAU,CAAC,EAAE,KAAK,MAAM;AAChJ,YAAA;AAAA,IACT,CAAA,CAAC;AAAA,EAAA;AAEV;AACA,SAAS,UAAU,MAAM,KAAK;AACtB,MAAA,CAAC,cAAc,IAAI,GAAG;AACf,WAAA;AAAA,EACX;AACI,MAAA,CAAC,WAAW,GAAG,GAAG;AACX,WAAA;AAAA,EACX;AACA,SAAO,SAAS,WAAW,UAAU,OAAO,MAAM;AAC1C,QAAA,WAAW,QAAQ,OAAO,KAC1B,WAAW,QAAQ,IAAI,KACvB,WAAW,QAAQ,QAAQ,GAAG;AAC9B,aAAO,mBAAmB,MAAM,UAAU,MAAM,KAAK,SAAS,IAAI,CAAC;AAAA,IACvE;AACA,WAAO,mBAAmB,MAAM,cAAc,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC3E,gBAAU,MAAM,KAAK,OAAO,CAAA,GAAI,SAAS;AAAA,QACrC,SAAS;AAAA,QACT,MAAM;AAAA,MAAA,CACT,GAAG,IAAI;AAAA,IACX,CAAA,CAAC,CAAC;AAAA,EAAA;AAEX;AAEA,MAAM,YAAY,CAAC,WAAW,QAAQ,UAAU,UAAU;AAC1D,SAAS,YAAYuB,YAAW;AACnB,WAAA,gBAAgB,YAAY,QAAQ,aAAa;AACtD,WAAO,SAAU,KAAK;AAClB,aAAO,OAAO,mBAAmB,YAAY,KAAK,WAAW,CAAC;AAAA,IAAA;AAAA,EAEtE;AACS,WAAA,YAAY,YAAY,UAAU,aAAa,IAAI,cAAc,CAAA,GAAI,eAAe,OAAO;AAC5F,QAAA,cAAc,QAAQ,GAAG;AAEzB,YAAM,SAAU,iBAAiB,OAAO,WAAW,CAAA;AAC/C,UAAA,WAAW,UAAU,GAAG;AACxB,qBAAa,WAAW,UAAU,MAAM,KAAK,CAAA;AAAA,MACjD;AACA,iBAAW,OAAO,UAAU;AACpB,YAAA,OAAO,YAAY,GAAG,GAAG;AACrB,cAAA,YAAY,WAAW,GAAG;AAC1B,cAAA,WAAW,SAAS,GAAG;AACvB,wBAAY,UAAU,SAAS,GAAG,GAAG,UAAU,MAAM;AAAA,UACzD;AACA,cAAI,CAAC,WAAW;AAEZ,oBAAQ,KAAK,SAAS,UAAU,SAAS,GAAG,EAAE;AAAA,UAAA,WAEzC,SAAS,SAAS,GAAG;AAEnB,mBAAA,SAAS,IAAI,SAAS,GAAG;AAAA,UAAA,WAE3B,cAAc,SAAS,GAAG;AAE/B,mBAAO,UAAU,OAAO,UAAU,OAAO,GAAG,IAAI,UAAU;AAAA,UAC9D;AAAA,QAEK,WAAA,UAAU,QAAQ,GAAG,MAAM,IAAI;AAC9B,gBAAA,WAAW,SAAS,GAAG;AACzB,cAAA,WAAW,QAAQ,GAAG;AACtB,mBAAO,GAAG,IAAI,gBAAgB,YAAY,UAAU,WAAW;AAAA,UACnE;AAAA,QAAA,OAEC;AACD,cAAI,CAAC,gBAAgB,CAAC,OAAO,QAAQ,GAAG,GAAG;AAChC,mBAAA,GAAG,IAAI,SAAS,GAAG;AAAA,UAC9B;AAAA,QACJ;AAAA,MACJ;AACO,aAAA;AAAA,IAAA,WAEF,WAAW,QAAQ,GAAG;AACvB,UAAA,WAAW,UAAU,GAAG;AACb,mBAAA,UAAU,CAAA,CAAE;AAAA,MAC3B;AACW,iBAAA,gBAAgB,YAAY,UAAU,WAAW;AAAA,IAChE;AACO,WAAA;AAAA,EACX;AACA,WAAS,mBAAmB,YAAY,KAAK,aAAa,kBAAkB,OAAO;AAC3E,QAAA,WAAWA,WAAU,WAAW,GAAG;AAE7BA,YAAAA,WAAU,YAAY,YAAY,GAAG;AAAA,IAC/C;AACA,UAAM,sBAAsB,mBAAoB;AAChD,WAAO,YAAY,YAAY,KAAK,aAAa,CAAA,GAAI,mBAAmB;AAAA,EAC5E;AACO,SAAA,SAAS,QAAQ,YAAY,QAAQ;AAOlC,UAAA,cAAc,OAAOA,YAAW,UAAU;AAChD,QAAI,CAAC,eAAe,OAAO,GAAG,UAAU,MAAM,YAAY;AAC/C,aAAA;AAAA,IACX;AACM,UAAA,cAAc,eAChB,WAAWA,WAAU,WAAW,KAChC,aAAa,UAAU,KACvB,UAAU,UAAU;AAClB,UAAA,YAAY,eAAe,WAAW,MAAM;AAC9C,QAAA,CAAC,eAAe,CAAC,QAAQ;AAEzB,aAAO,WAAY;AACP,gBAAA,MAAM,aAAa,UAAU,EAAE;AAAA,MAAA;AAAA,IAE/C;AACI,QAAA,CAAC,eAAe,CAAC,WAAW;AACrB,aAAA;AAAA,IACX;AACM,UAAA,WAAWA,WAAU,UAAU;AAC9B,WAAA,SAAU,MAAM,MAAM;AAErB,UAAA,UAAU,YAAY;AACtB,UAAA,WAAW,QAAQ,GAAG;AACtB,kBAAU,SAAS,IAAI;AAAA,MAC3B;AACA,aAAO,YAAY,YAAY,MAAM,QAAQ,MAAM,QAAQ,WAAW;AAChE,YAAA,OAAO,CAAC,IAAI;AACd,UAAA,OAAO,SAAS,aAAa;AAC7B,aAAK,KAAK,IAAI;AAAA,MAClB;AACM,YAAA,cAAc,GAAG,QAAQ,QAAQ,UAAU,EAAE,MAAM,IAAI,IAAI;AACjE,UAAI,aAAa,UAAU,KAAK,UAAU,UAAU,GAAG;AAC/C,YAAA,eAAe,CAAC,YAAY,UAAU;AACtC,sBAAY,WAAW;AAAA,QAC3B;AAAA,MACJ;AACI,UAAA,UAAU,UAAU,GAAG;AAEvB,eAAO,mBAAmB,YAAY,aAAa,QAAQ,aAAa,aAAa,UAAU,CAAC;AAAA,MACpG;AACO,aAAA;AAAA,IAAA;AAAA,EACX;AAER;AAEA,MAAM,YAAY,MAAM;AAEd,QAAA,MAAM,WAAW,MAAM,KAAK,OAAO,EAAE,cAAc,MAAM;AAC3D,MAAA,OAAO,IAAI,KAAK;AAChB,WAAO,IAAI,IAAI;AAAA,EACnB;AACA,SAAOP,oBAAkB;AAC7B;AACA,MAAM,YAAY,CAAC,WAAW;AAC1B,QAAM,MAAM,WAAW,MAAM,KAAK,OAAO;AACzC,MAAI,CAAC,KAAK;AACC,WAAA;AAAA,EACX;AACM,QAAA,YAAY,IAAI,IAAI;AAC1B,MAAI,cAAc,QAAQ;AACtB,QAAI,IAAI,UAAU;AAClB,4BAAwB,QAAQ,CAAC,OAAO,GAAG,EAAE,OAAQ,CAAA,CAAC;AAC/C,WAAA;AAAA,EACX;AACO,SAAA;AACX;AACA,MAAM,0BAA0B,CAAA;AAChC,MAAM,iBAAiB,CAAC,OAAO;AAC3B,MAAI,wBAAwB,QAAQ,EAAE,MAAM,IAAI;AAC5C,4BAAwB,KAAK,EAAE;AAAA,EACnC;AACJ;AACA,IAAI,OAAO,WAAW,aAAa;AAC/B,SAAO,YAAY;AACvB;AAEA,MAAM,WAAW;AACjB,IAAI;AACJ,SAAS,YAAYQ,UAAS,IAAI;AACvB,SAAA,SAAS,YAAY,GAAG,OAAO;AACvB,eAAA,YAAYA,QAAO,eAAe,QAAQ;AACrD,QAAI,CAAC,UAAU;AACA,iBAAA,KAAK,IAAQ,IAAA,KAAK,KAAK,MAAM,KAAK,WAAW,GAAG;AAC3D,SAAG,WAAW;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA,MAAA,CACT;AAAA,IACL;AACA,UAAM,WAAW;AAAA,EAAA;AAEzB;AACA,SAAS,kBAAkB,SAAS,OAAO;AACvC,MAAI,QAAQ,UAAU;AAClB,UAAM,WAAW,QAAQ;AACzB,UAAM,iBAAiB;AAAA,MACnB,KAAK,SAAS;AAAA,MACd,MAAM,SAAS;AAAA,MACf,OAAO,QAAQ,cAAc,SAAS;AAAA,MACtC,QAAQ,QAAQ,eAAe,SAAS;AAAA,IAAA;AAAA,EAEhD;AACJ;AACA,SAAS,UAAU,QAAQ,UAAU;AACjC,MAAI,SAAS;AACb,MAAI,YAAY;AAChB,MAAI,YACC,OAA6B;AACrB,aAAA;AACG,gBAAA;AAAA,EAAA,OAEX;AACD,aAAS,OAAO,MAAM,GAAG,EAAE,CAAC,KAAK;AACjC,gBAAY,OAAO,MAAM,GAAG,EAAE,CAAC,KAAK;AAAA,EACxC;AACO,SAAA;AAAA,IACH,QAAQ,OAAO,kBAAkB;AAAA,IACjC;AAAA,EAAA;AAER;AACA,SAAS,mBAAmB,SAAS,OAAO;AACxC,QAAM,EAAE,QAAQ,IAAI,QAAQ,IAAI,SAAS,IAAI,WAAW,IAAI,OAAO,SAAAlD,UAAS,UAAU,iBAAiB,YAAY,YAAY,kBAAuB,IAAA;AAGtJ,QAAM,EAAE,QAAQ,UAAA,IAAc,UAAU,QAAQ,QAAQ;AACxD,MAAI,cAAcA;AAEd,MAAA,aAAa,iBAAiB,SAAS,KAAK;AAE5C,MAAA,cAAc,eAAe,KAAK;AAElC,MAAA,YAAY,YAAY,OAAO;AAEnC,MAAI,qBAAqB;AAEzB,MAAI,oBAAoB;AAExB,MAAI,cAAc;AAElB,QAAM,gBAAgB,YAAY,IAAI,QAAQ,MAAM,GAAG;AAEvD,QAAM,aAAa;AAAA,IACf,OAAO;AAAA,IACP,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,aAAa,eAAe,YAAY;AAAA,IACxC,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,aAA6C;AAAA,IAC7C;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,WAAW;AAAA,IACX,cAAc;AAAA;AAAA,IAEd,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,IAAI;AAAA,IACJ,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,EAAA;AAEf,SAAO,OAAO,UAAU;AAC5B;AACA,SAAS,iBAAiB,SAAS,OAAO;AAElC,MAAA,aAAa,QAAQ,cAAc;AACvC;AACI,UAAM,iBAAiB;AAAA,MACnB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,KAAK;AAAA,IAAA;AAEH,UAAA,qBAAqB,OAAO,KAAK,cAAc;AAC/C,UAAA,SAAS,MAAM;AACrB,aAAS0B,SAAQ,GAAGA,SAAQ,mBAAmB,QAAQA,UAAS;AACtD,YAAA,KAAK,mBAAmBA,MAAK;AACnC,UAAI,OAAO,QAAQ,EAAE,MAAM,IAAI;AAC3B,qBAAa,eAAe,EAAE;AAC9B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACO,SAAA;AACX;AACA,SAAS,eAAe,OAAO;AAE3B,MAAI,cAAc;AAClB,MAAI,aAAa;AACb,kBAAc,YAAY;EAC9B;AACO,SAAA;AACX;AACA,SAAS,eAAe,iBAAiB;AAC9B,SAAA,YAAY,UAAc,IAAA;AACrC;AACA,SAAS,YAAY,SAAS;AAC1B,QAAM,YAAY;AAEd,MAAA,YAAY,QAAQ,YAAY;AACpC;AACI,QAAI,QAAQ,aAAa;AACrB,kBAAY,QAAQ;AAAA,IAEf,WAAA,QAAQ,QAAQ,QAAQ,KAAK,KAAK;AACvC,kBAAY,QAAQ,KAAK;AAAA,IAC7B;AAAA,EACJ;AACO,SAAA;AACX;AAEA,MAAM,gBAAgB;AAAA,EAClB,aAAa,CAAC,SAAS,UAAU;AAC7B,sBAAkB,SAAS,KAAK;AACpB,gBAAA,EAAE,SAAS,KAAK;AAC5B,uBAAmB,SAAS,KAAK;AAAA,EACrC;AACJ;AAEA,MAAM,oBAAoB;AAE1B,MAAM,aAAa,CAAA;AAEnB,MAAM,eAAe;AAAA,EACjB,KAAK,UAAU,QAAQ;AACf,QAAA,eAAe,SAAS,SAAS,OAAO;AACxC,QAAA,MAAM,YAAY,GAAG;AACrB;AAAA,IACJ;AACA,UAAM,OAAO,SAAS;AAClB,QAAA,CAAC,QAAQ,IAAI,GAAG;AAChB;AAAA,IACJ;AACA,UAAM,MAAM,KAAK;AACjB,QAAI,CAAC,KAAK;AACN;AAAA,IACJ;AACA,QAAI,eAAe,GAAG;AACH,qBAAA;AAAA,IAAA,WAEV,gBAAgB,KAAK;AAC1B,qBAAe,MAAM;AAAA,IACzB;AACA,QAAI,eAAe,GAAG;AACX,aAAA,UAAU,KAAK,YAAY;AAClC,aAAO,OAAO,KAAK,OAAO,CAAC,MAAMA,WAAUA,SAAQ,eAAe,SAAS,KAAK,YAAY,IAAI,IAAI;AAAA,IAAA,OAEnG;AACM,aAAA,UAAU,KAAK,CAAC;AAAA,IAC3B;AACO,WAAA;AAAA,MACH,WAAW;AAAA,MACX,MAAM;AAAA,IAAA;AAAA,EAEd;AACJ;AAEA,MAAM,kBAAkB;AAAA,EACpB,KAAK,UAAU,QAAQ;AACnB,WAAO,YAAY,SAAS;AAAA,EAChC;AACJ;AAEA,MAAM,gBAAgB;AAAA,EAClB,aAAa,CAAC,SAAS,UAAU;AAC7B,UAAM,EAAE,OAAO,OAAO,SAAS,IAAI,WAAW,GAAO,IAAA;AACjD,QAAA,aAAa,iBAAiB,SAAS,KAAK;AAC5C,QAAA,cAAc,eAAe,KAAK;AAC1B,gBAAA,EAAE,SAAS,KAAK;AAC5B,UAAM,EAAE,QAAQ,UAAA,IAAc,UAAU,QAAQ,QAAQ;AAChD,YAAA,WAAW,OAAO,OAAO;AAAA,MAC7B;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb;AAAA,MACA;AAAA,IACH,CAAA,CAAC;AAAA,EACN;AACJ;AAEA,MAAM,iBAAiB;AAAA,EACnB,aAAa,CAAC,SAAS,UAAU;AAC7B,UAAM,EAAE,SAAA1B,UAAS,UAAU,YAAY,UAAU;AAC7C,QAAA,YAAY,YAAY,OAAO;AACnC,QAAI,gBAAgB,YAAY,IAAI,QAAQ,MAAM,GAAG;AACrD,UAAM,aAAa;AAAA,MACf,aAAaA;AAAA,MACb;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,aAAa,eAAe,YAAY;AAAA,MACxC,WAAW;AAAA,MACX,aAA6C;AAAA,MAC7C,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,IAAA;AAEvB,WAAO,OAAO,UAAU;AAAA,EAC5B;AACJ;AAEA,MAAM,gBAAgB;AAAA,EAClB,aAAa,CAAC,SAAS,UAAU;AAC7B,sBAAkB,SAAS,KAAK;AACxB,YAAA,WAAW,OAAO,OAAO;AAAA,MAC7B,WAAW;AAAA,MACX,cAAc;AAAA,IACjB,CAAA,CAAC;AAAA,EACN;AACJ;AAEA,MAAM,yBAAyB;AAAA,EAC3B,aAAa,SAAU,SAAS,OAAO;AAC7B,UAAA,EAAE,wBAA4B,IAAA;AACpC,UAAM,mBAAmB;AACzB,QAAI,4BAA4B,MAAM;AAClC,YAAM,mBAAmB;AAAA,IAAA,WAEpB,4BAA4B,OAAO;AACxC,YAAM,mBAAmB;AAAA,IAC7B;AAAA,EACJ;AACJ;AAEA,MAAM,UAAU;AAAA,EACZ,KAAK,UAAU;AACX,UAAM,MAAM,OAAO,EAAE,cAAc,KAAK,CAAC,KAAK;AAC1C,QAAA,CAAC,IAAI,KAAK;AACN,UAAA,CAAC,GAAG,kBAAkB;AACtB,WAAG,mBAAmB;MAC1B;AACG,SAAA,iBAAiB,KAAK,QAAQ;AAAA,IAAA,OAEhC;AACD,iBAAW,UAAU,UAAU,IAAI,IAAI,CAAC;AAAA,IAC5C;AAAA,EACJ;AACJ;AACA,MAAM,WAAW;AAAA,EACb,KAAK,UAAU;AACX,UAAM,MAAM,OAAO,EAAE,cAAc,KAAK,CAAC,KAAK;AAC1C,QAAA,CAAC,IAAI,KAAK;AACN,UAAA,CAAC,GAAG,kBAAkB;AACtB;AAAA,MACJ;AACA,YAAM0B,SAAQ,GAAG,iBAAiB,UAAU,CAAC,OAAO,OAAO,QAAQ;AACnE,UAAIA,WAAU,IAAI;AACX,WAAA,iBAAiB,OAAOA,QAAO,CAAC;AAAA,MACvC;AAAA,IAAA,WAEK,SAAS,OAAO;AACrB,YAAM,WAAW,IAAI,IAAI,EAAE,QAAQ;AACnC,UAAI,UAAU;AACV,cAAMA,SAAQ,SAAS,QAAQ,SAAS,KAAK;AAC7C,YAAIA,SAAQ,IAAI;AACH,mBAAA,OAAOA,QAAO,CAAC;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,MAAM,eAAe;AAAA,EACjB,OAAO;AACH,QAAI,GAAG,iBAAiB;AACpB,UAAI,GAAG,wBAAwB;AAC3B;AAAA,MACJ;AACA,SAAG,yBAAyB;AAC5B,cAAQ,KAAK,2IAA2I;AAAA,IAC5J;AAAA,EACJ;AACJ;AACA,MAAM,kBAAkB;AAExB,MAAM,WAAW;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,SAAS,QAAQ,KAAKuB,YAAW,WAAW,IAAI;AACtC,QAAA,UAAU,YAAYA,UAAS;AACrC,QAAM,mBAAmB;AAAA,IACrB,IAAI,QAAQ,KAAK;AACT,UAAA,OAAO,QAAQ,GAAG,GAAG;AACrB,eAAO,OAAO,GAAG;AAAA,MACrB;AACI,UAAA,OAAO,KAAK,GAAG,GAAG;AAClB,eAAO,UAAU,KAAK,IAAI,GAAG,CAAC;AAAA,MAClC;AACI,UAAA,OAAO,UAAU,GAAG,GAAG;AACvB,eAAO,UAAU,KAAK,SAAS,GAAG,CAAC;AAAA,MACvC;AAGA,aAAO,UAAU,KAAK,QAAQ,KAAK,SAAS,GAAG,CAAC,CAAC;AAAA,IACrD;AAAA,EAAA;AAEJ,SAAO,IAAI,MAAM,IAAI,gBAAgB;AACzC;AAEA,SAAS,gBAAgB,WAAW;AAChC,SAAO,SAASE,aAAY,EAAE,SAAS,SAAS,MAAM,YAAa;AAC3D,QAAA;AACA,QAAA,UAAU,OAAO,GAAG;AACd,YAAA;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,QACA,UAAU,UAAU,OAAO;AAAA,MAAA;AAEpB,iBAAA,OAAO,KAAK,QAAQ,GAAG;AAAA,IAAA,OAEjC;AACK,YAAA;AAAA,QACF,QAAQ,yBAAyB,UAAU;AAAA,MAAA;AAEpC,iBAAA,IAAI,KAAK,KAAK,GAAG;AAAA,IAChC;AACW,eAAA,QAAQ,KAAK,SAAS,GAAG;AAAA,EAAA;AAE5C;AAEA,MAAM,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,MAAM,uBAAuB,CAAC,YAAY,UAAU,SAAS;AAC7D,MAAM,eAAe,GAAG,uBAClB,GAAG,yBACH;AACN,SAAS,QAAQ,KAAK;AAClB,MAAI,gBACA,aAAa,UAAU,QACvB,qBAAqB,SAAS,GAAG,GAAG;AAC7B,WAAA;AAAA,EACX;AACO,SAAA,WAAW,QAAQ,GAAG,IAAI,MAAM,OAAO,GAAG,GAAG,MAAM;AAC9D;AACA,SAAS,SAAS;AACd,QAAM,QAAQ,CAAA;AACd,aAAW,OAAO,IAAI;AACd,QAAA,QAAQ,GAAG,GAAG;AAER,YAAA,GAAG,IAAI,GAAG,GAAG;AAAA,IACvB;AAAA,EACJ;AACA,MAAI,OAAO,eAAe,eACtB,OAAO,uBAAuB,aAAa;AAC3C,eAAW,KAAK;AAAA,EACpB;AACO,SAAA;AACX;AAEA,MAAMC,UAAQ,CAAC,aAAa,wBAAwB,iBAAiB;AAErE,MAAM,cAAc,gBAAgB;AAAA,EAChC,OAAO,CAAC,QAAQ;AAAA,EAChB,OAAO,CAAC,QAAQ;AAAA,EAChB,SAAS,CAAC,OAAO;AAAA,EACjB,MAAM,CAAC,QAAQ;AACnB,CAAC;AACD,SAAS,mBAAmB,WAAW;AAC7B,QAAA,MAAa,uBAAA,OAAO,IAAI;AACxBA,UAAA,QAAQ,CAAC,SAAS;AAChB,QAAA,IAAI,IAAI,UAAU,IAAI;AAAA,EAAA,CAC7B;AACM,SAAA;AACX;AAMA,SAAS,sBAAsB;AACrB,QAAA,QAAQ,KAAK;AACnB,QAAM,QAAQ,MAAM;AACd,QAAA,KAAK,SAAS,MAAM,WAAW;AACjC,QAAI,UAAU,QAAQ;AAElB,aAAO,MAAM,KAAK,MAAM,UAAU,MAAM;AAAA,IAC5C;AACA,WAAO,MAAM,KAAK,MAAM,mBAAmB,SAAS,CAAC;AAAA,EAAA;AAElD,SAAA;AACX;AACA,MAAM,OAAO,OAAO;AACpB,IAAI,CAAC,KAAK,QAAQ,gBAAgB,GAAG;AACjC,OAAK,iBAAiB,KAAK;AAC/B;AACA,IAAI,CAAC,KAAK,QAAQ,eAAe,GAAG;AAChC,OAAK,gBAAgB,KAAK;AAC9B;AACA,IAAI,CAAC,KAAK,QAAQ,eAAe,GAAG;AAChC,OAAK,gBAAgB,KAAK;AAC9B;AACA,IAAI,WAAW,KAAK,kBAAkB,KAAK,eAAe;AAC1D,IAAI,CAAC,UAAU;AACX,aAAW,KAAK;AACpB;AACA,MAAM,OAAO,WAAW,SAAS,OAAO;AACxC,MAAM,oBAAoB,QAAQ,KAAK,QAAQ,YACzC,KAAK,QAAQ,oBACb,KAAK;AAEX,IAAI,+BAA4B,OAAO;AAAA,EACrC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,MAAM,gBAAgB;AAAA,EAClB,KAAK,UAAU,QAAQ;AAGnB,QAAI,SAAS,oBAAoB,CAAC,OAAO,gBAAgB;AAErD,aAAO,iBAAiB,SAAS;AAAA,IACrC;AAEA,QAAI,SAAS,mBAAmB,CAAC,OAAO,eAAe;AAEnD,aAAO,gBAAgB,SAAS;AAAA,IACpC;AAAA,EACJ;AACJ;AAEA,IAAI,mCAAgC,OAAO;AAAA,EACzC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,MAAM,OAAO,OAAO;AACpB,IAAI,QAAQ,QAAQ,OAAO,WAAW,IAAI;AC76C1C,SAAS,kBAAkB,OAAO,MAAM,IAAI;AACxC,MAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;AAC5B,WAAA,QAAQ,QAAQ,IAAI;AAC/B,SAAO,MACF,MAAM,GAAG,EACT,OAAO,CAAC,SAASC,UAAS;AACpB,WAAA,QAAQ,KAAK,CAAC,WAAW;AAC5B,UAAI,UAAU;AACH,eAAA,QAAQ,QAAQ,MAAM;AAC1B,aAAA,iBAAiBA,OAAM,MAAM,EAAE;AAAA,IAAA,CACzC;AAAA,EACF,GAAA,QAAQ,QAAQ,IAAI,CAAC;AAC5B;AACA,MAAM,iBAAiB;AACvB,SAAS,iBAAiBA,OAAM,MAAM,IAAI;AACtC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC9B,UAAA,SAASZ,MAAI,cAAc;AAAA,MAC7B,KAAK,QAAQY,KAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MAC/B,UAAU;AAAA;AAAA,MACV,OAAO;AACH,gBAAQ,IAAI;AAAA,MAChB;AAAA,IAAA,CACH;AACK,UAAA,QAAQ,WAAW,MAAM;AAE3B,aAAO,MAAM;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,MAAA,CACX;AACD,cAAQ,IAAI;AAAA,OACb,cAAc;AACV,WAAA,OAAO,CAAClB,OAAM;AACjB,mBAAa,KAAK;AAClB,cAAQ,MAAM;AAAA,IAAA,CACjB;AACM,WAAA,QAAQ,CAACA,OAAM;AAClB,mBAAa,KAAK;AAClB,cAAQ,IAAI;AAAA,IAAA,CACf;AACM,WAAA,QAAQ,CAACA,OAAM;AAClB,mBAAa,KAAK;AAClB,cAAQ,IAAI;AAAA,IAAA,CACf;AAAA,EAAA,CACJ;AACL;AAEA,SAAS,cAAc,MAAM,MAAM;AAC3B,MAAA;AACO,WAAA;AAAA,MACH;AAAA,MACA,MAAM,WAAW,IAAI;AAAA,IAAA;AAAA,WAGtBA,IAAG;AAAA,EAEV;AACO,SAAA;AAAA,IACH;AAAA,IACA,MAAM,CAAC;AAAA,EAAA;AAEf;AACA,SAAS,WAAW,MAAM;AACtB,SAAO,KAAK,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAC;AAC3C;AACA,SAAS,UAAU,KAAK,QAAQ,GAAG;AAC/B,MAAI,SAAS,GAAG;AACL,WAAA;AAAA,MACH,MAAM;AAAA,MACN,OAAO;AAAA,IAAA;AAAA,EAEf;AACA,QAAM,OAAO,OAAO;AACpB,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,aAAa,GAAG;AAAA,IAC3B,KAAK;AACD,aAAO,aAAa,GAAG;AAAA,IAC3B,KAAK;AACD,aAAO,cAAc,GAAG;AAAA,IAC5B,KAAK;AACM,aAAA,aAAa,KAAK,KAAK;AAAA,IAClC,KAAK;AACD,aAAO,gBAAgB;AAAA,IAC3B,KAAK;AACD,aAAO,eAAe,GAAG;AAAA,IAC7B,KAAK,UACD;AACI,aAAO,aAAa,GAAG;AAAA,IAC3B;AAAA,IACJ,KAAK;AACD,aAAO,aAAa,GAAG;AAAA,EAC/B;AACJ;AACA,SAAS,eAAe,OAAO;AACpB,SAAA;AAAA,IACH,MAAM;AAAA,IACN,OAAO,YAAY,MAAM,IAAI;AAAA,EAAA;AAErC;AACA,SAAS,kBAAkB;AAChB,SAAA;AAAA,IACH,MAAM;AAAA,EAAA;AAEd;AACA,SAAS,cAAc,OAAO;AACnB,SAAA;AAAA,IACH,MAAM;AAAA,IACN,OAAO,OAAO,KAAK;AAAA,EAAA;AAE3B;AACA,SAAS,aAAa,OAAO;AAClB,SAAA;AAAA,IACH,MAAM;AAAA,IACN,OAAO,OAAO,KAAK;AAAA,EAAA;AAE3B;AACA,SAAS,aAAa,OAAO;AAClB,SAAA;AAAA,IACH,MAAM;AAAA,IACN,OAAO,OAAO,KAAK;AAAA,EAAA;AAE3B;AACA,SAAS,aAAa,OAAO;AAClB,SAAA;AAAA,IACH,MAAM;AAAA,IACN;AAAA,EAAA;AAER;AACA,SAAS,aAAa,OAAO;AAClB,SAAA;AAAA,IACH,MAAM;AAAA,IACN,OAAO,MAAM;AAAA,EAAA;AAErB;AACA,SAAS,aAAa,OAAO,OAAO;AAChC,MAAI,UAAU,MAAM;AACT,WAAA;AAAA,MACH,MAAM;AAAA,IAAA;AAAA,EAEd;AACA;AACQ,QAAA,0BAA0B,KAAK,GAAG;AAC3B,aAAA,8BAA8B,OAAO,KAAK;AAAA,IACrD;AACI,QAAA,4BAA4B,KAAK,GAAG;AAC7B,aAAA,gCAAgC,OAAO,KAAK;AAAA,IACvD;AACI,QAAA,aAAa,KAAK,GAAG;AACd,aAAA,iBAAiB,OAAO,KAAK;AAAA,IACxC;AACI,QAAA,sBAAsB,KAAK,GAAG;AACvB,aAAA,0BAA0B,OAAO,KAAK;AAAA,IACjD;AAAA,EACJ;AACI,MAAA,MAAM,QAAQ,KAAK,GAAG;AACf,WAAA;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,QACH,YAAY,MAAM,IAAI,CAAC,GAAG,MAAM,mBAAmB,GAAG,GAAG,QAAQ,CAAC,CAAC;AAAA,MACvE;AAAA,IAAA;AAAA,EAER;AACA,MAAI,iBAAiB,KAAK;AACf,WAAA;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,aAAa,OAAO,MAAM,IAAI;AAAA,MAC9B,OAAO;AAAA,QACH,SAAS,MAAM,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,eAAe,GAAG,QAAQ,CAAC,CAAC;AAAA,MACtE;AAAA,IAAA;AAAA,EAER;AACA,MAAI,iBAAiB,KAAK;AACf,WAAA;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,aAAa,OAAO,MAAM,IAAI;AAAA,MAC9B,OAAO;AAAA,QACH,SAAS,MAAM,KAAK,MAAM,SAAS,EAAE,IAAI,CAAC,MAAM,eAAe,GAAG,QAAQ,CAAC,CAAC;AAAA,MAChF;AAAA,IAAA;AAAA,EAER;AACA,MAAI,iBAAiB,SAAS;AACnB,WAAA;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,QACH,YAAY,CAAC;AAAA,MACjB;AAAA,IAAA;AAAA,EAER;AACA,MAAI,iBAAiB,QAAQ;AAClB,WAAA;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO,OAAO,KAAK;AAAA,MACnB,WAAW;AAAA,IAAA;AAAA,EAEnB;AACA,MAAI,iBAAiB,MAAM;AAChB,WAAA;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO,OAAO,KAAK;AAAA,MACnB,WAAW;AAAA,IAAA;AAAA,EAEnB;AACA,MAAI,iBAAiB,OAAO;AACjB,WAAA;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO,MAAM,WAAW,OAAO,KAAK;AAAA,MACpC,WAAW,MAAM,QAAQ;AAAA,IAAA;AAAA,EAEjC;AACA,MAAI,YAAY;AAChB;AACI,UAAM,cAAc,MAAM;AAC1B,QAAI,aAAa;AAEb,UAAI,YAAY,kBAAkB;AAElB,oBAAA,YAAY,iBAAmB,EAAA;AAAA,MAC/C;AAAA,IACJ;AAAA,EACJ;AACO,SAAA;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,UAAU,qBAAqB,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC;AAAA,IACxG;AAAA,EAAA;AAER;AACA,SAAS,0BAA0B,OAAO;AACtC,SAAO,MAAM,KAAK,4BAA4B,MAAM,CAAC;AACzD;AACA,SAAS,4BAA4B,OAAO;AACxC,SAAO,MAAM,QAAQ,MAAM,OAAO,QAAQ,MAAM;AACpD;AACA,SAAS,8BAA8B,OAAO,OAAO;AAC1C,SAAA;AAAA,IACH,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,MAAMmB,MAAK,MAAM,qBAAqB,MAAMA,QAAO,QAAQ,CAAC,CAAC;AAAA,IAChH;AAAA,EAAA;AAER;AACA,SAAS,gCAAgC,OAAO,OAAO;AAC5C,SAAA;AAAA,IACH,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,MAAM,IAAI,EAAE,IAAI,CAAC,CAAC,MAAMA,MAAK,MAAM,qBAAqB,MAAMA,QAAO,QAAQ,CAAC,CAAC;AAAA,IAC9G;AAAA,EAAA;AAER;AACA,SAAS,aAAa,OAAO;AACzB,SAAO,MAAM,SAAS,MAAM,WAAW,QAAQ,MAAM,YAAY;AACrE;AACA,SAAS,iBAAiB,OAAO,OAAO;AAC7B,SAAA;AAAA,IACH,MAAM;AAAA;AAAA;AAAA,IAGN,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,KAAK,EAC3B,OAAO,CAAC,CAAC,IAAI,MAAM;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACF,SAAS,IAAI,CAAC,EACX,IAAI,CAAC,CAAC,MAAMA,MAAK,MAAM,qBAAqB,MAAMA,QAAO,QAAQ,CAAC,CAAC;AAAA,IAC5E;AAAA,EAAA;AAER;AACA,SAAS,sBAAsB,OAAO;AAC1B,SAAA,OAAO,MAAM,qBAAqB,cACtC,OAAO,MAAM,gBAAgB,cAC7B,MAAM;AACd;AACA,SAAS,0BAA0B,OAAO,OAAO;AACtC,SAAA;AAAA,IACH,MAAM;AAAA,IACN,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,MAAM,OAAO,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,qBAAqB,MAAM,OAAO,QAAQ,CAAC,CAAC;AAAA,IACjH;AAAA,EAAA;AAER;AACA,SAAS,qBAAqB,MAAM,OAAO,OAAO;AACxC,QAAA,SAAS,UAAU,OAAO,KAAK;AACrC,SAAO,OAAO;AACP,SAAA;AACX;AACA,SAAS,mBAAmB,OAAO5B,QAAO,OAAO;AACvC,QAAA,SAAS,UAAU,OAAO,KAAK;AAC9B,SAAA,OAAO,GAAGA,MAAK;AACf,SAAA;AACX;AACA,SAAS,eAAe,OAAO,OAAO;AAC3B,SAAA;AAAA,IACH,OAAO,UAAU,OAAO,KAAK;AAAA,EAAA;AAErC;AACA,SAAS,eAAe,OAAO,OAAO;AAC3B,SAAA;AAAA,IACH,KAAK,UAAU,MAAM,CAAC,GAAG,KAAK;AAAA,IAC9B,OAAO,UAAU,MAAM,CAAC,GAAG,KAAK;AAAA,EAAA;AAExC;AAEA,MAAM,gBAAgB,CAAC,OAAO,QAAQ,SAAS,QAAQ,OAAO;AAC9D,IAAI,cAAc;AAClB,MAAM,eAAe,CAAA;AACrB,MAAM,eAAe,CAAA;AACrB,SAAS,oBAAoB,UAAU;AACnC,MAAI,eAAe,MAAM;AACR,iBAAA,KAAK,GAAG,QAAQ;AAC7B;AAAA,EACJ;AACY,cAAA,KAAK,UAAU,OAAO,OAAO;AAAA,IACrC,MAAM;AAAA,IACN,MAAM;AAAA,EAAA,GACP,YAAY,CAAC,CAAC;AACrB;AACA,SAAS,eAAe,OAAO,QAAQ,IAAI;AACzB,gBAAA;AACP,SAAA,OAAO,cAAc,KAAK;AACjC,MAAI,SAAS,QAAQ,aAAa,SAAS,GAAG;AACpC,UAAA,WAAW,aAAa;AAC9B,iBAAa,SAAS;AACtB,wBAAoB,QAAQ;AAAA,EAChC;AACJ;AACA,MAAM,kBAAgC,8BAAc,OAAO,CAAC,SAAS,SAAS;AAC1E,UAAQ,IAAI,IAAI,QAAQ,IAAI,EAAE,KAAK,OAAO;AACnC,SAAA;AACX,GAAG,CAAE,CAAA;AACL,MAAM,cAAc;AACpB,SAAS,iBAAiB;AACtB,WAAS,YAAY,MAAM;AACvB,WAAO,YAAa,MAAM;AAChB,YAAA,eAAe,CAAC,GAAG,IAAI;AAC7B,UAAI,aAAa,QAAQ;AACrB,cAAM,cAAc,aAAa,aAAa,SAAS,CAAC;AAExD,YAAI,OAAO,gBAAgB,YAAY,YAAY,KAAK,WAAW,GAAG;AAClE,uBAAa,IAAI;AAAA,QACrB;AAAA,MACJ;AAC2C;AACvB,wBAAA,IAAI,EAAE,GAAG,YAAY;AAAA,MACzC;AACA,0BAAoB,CAAC,cAAc,MAAM,IAAI,CAAC,CAAC;AAAA,IAAA;AAAA,EAEvD;AAEA,MAAI,qBAAqB;AACP,kBAAA,QAAQ,CAAC,SAAS;AACpB,cAAA,IAAI,IAAI,YAAY,IAAI;AAAA,IAAA,CACnC;AACD,WAAO,SAAS,iBAAiB;AACf,oBAAA,QAAQ,CAAC,SAAS;AACpB,gBAAA,IAAI,IAAI,gBAAgB,IAAI;AAAA,MAAA,CACvC;AAAA,IAAA;AAAA,EACL,OAEC;AACD;AACI,UAAI,OAAOe,UAAQ,eAAeA,MAAI,OAAO;AACzC,cAAM,SAASA,MAAI;AACnB,YAAI,QAAQ;AAEJA,gBAAA,QAAQ,YAAa,MAAM;AAC3B,kBAAM,CAAC,MAAM,UAAU,GAAG,IAAI,IAAI;AAE3B,mBAAA,MAAM,IAAI,GAAG,IAAI;AACJ,gCAAA,CAAC,cAAc,MAAM,CAAC,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC;AAAA,UAAA;AAElE,iBAAO,SAAS,iBAAiB;AAC7BA,kBAAI,QAAQ;AAAA,UAAA;AAAA,QAEpB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,SAAS,iBAAiB;AAAA,EAAA;AACrC;AACA,SAAS,oBAAoB;AACzB,QAAM,QAAQ,QAAQ;AACtB,QAAM,MAAM;AACR,MAAA;AAEA,YAAQ,MAAM;AAAA,WAEX,IAAI;AACA,WAAA;AAAA,EACX;AAEM,QAAA,aAAa,QAAQ,QAAQ;AACnC,UAAQ,MAAM;AACP,SAAA;AACX;AAEA,IAAI,YAAY;AAKhB,MAAM,iCAAiB;AACvB,MAAM,aAAa,CAAA;AACnB,SAAS,kBAAkB,QAAQ;AAC/B,MAAI,aAAa,MAAM;AACZ,WAAA,QAAQ,CAAC,UAAU;AACtB,iBAAW,IAAI,KAAK;AAAA,IAAA,CACvB;AACD;AAAA,EACJ;AACA,QAAM,OAAO,OACR,IAAI,CAAC,QAAQ;AACd,UAAM,qBAAqB,OAAO,aAAa,OAAO,YAAY;AAC5D,UAAA,SAAS,qBAAqB,gCAAgC;AACpE,QAAI,oBAAoB;AACpB,YAAM,IAAI;AAAA,IACd;AACI,QAAA,eAAe,SAAS,IAAI,OAAO;AAC/B,UAAA,IAAI,WAAW,CAAC,IAAI,MAAM,SAAS,IAAI,OAAO,GAAG;AACjD,eAAO,GAAG,MAAM,GAAG,IAAI,OAAO;AAAA,EAC5C,IAAI,KAAK;AAAA,MACC;AACA,aAAO,GAAG,MAAM,GAAG,IAAI,KAAK;AAAA,IAChC;AACA,QAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACrC,UAAA;AACO,eAAA,SAAS,KAAK,UAAU,GAAG;AAAA,eAE/Bc,MAAK;AACD,eAAA,SAAS,OAAOA,IAAG;AAAA,MAC9B;AAAA,IACJ;AACO,WAAA,SAAS,OAAO,GAAG;AAAA,EAAA,CAC7B,EACI,OAAO,OAAO;AACf,MAAA,KAAK,SAAS,GAAG;AACP,cAAA,KAAK,UAAU,OAAO,OAAO;AAAA,MACnC,MAAM;AAAA,MACN;AAAA,IAAA,GACD,UAAU,CAAC,CAAC;AAAA,EACnB;AACJ;AACA,SAAS,aAAa,OAAO,QAAQ,IAAI;AACzB,cAAA;AACL,SAAA,OAAO,YAAY,KAAK;AAC/B,MAAI,SAAS,QAAQ,WAAW,OAAO,GAAG;AAChC,UAAA,SAAS,MAAM,KAAK,UAAU;AACpC,eAAW,MAAM;AACjB,sBAAkB,MAAM;AAAA,EAC5B;AACJ;AACA,SAAS,cAAc;AACnB,WAASC,SAAQ,OAAO;AAChB,QAAA;AAIA,UAAI,OAAO,0BAA0B,eACjC,iBAAiB,yBACjB,MAAM,kBAAkB,SACxB,MAAM,OAAO,WACb,MAAM,OAAO,QAAQ,SAAS,mDAAmD,GAAG;AACpF;AAAA,MACJ;AACA,UAAI,MAAuC;AACvC,wBAAgB,MAAM,KAAK;AAAA,MAC/B;AACkB,wBAAA,CAAC,KAAK,CAAC;AAAA,aAEtB,KAAK;AACR,sBAAgB,MAAM,GAAG;AAAA,IAC7B;AAAA,EACJ;AACI,MAAA,OAAOf,MAAI,YAAY,YAAY;AACnCA,UAAI,QAAQe,QAAO;AAAA,EACvB;AACI,MAAA,OAAOf,MAAI,yBAAyB,YAAY;AAChDA,UAAI,qBAAqBe,QAAO;AAAA,EACpC;AACA,SAAO,SAASC,YAAW;AACnB,QAAA,OAAOhB,MAAI,aAAa,YAAY;AACpCA,YAAI,SAASe,QAAO;AAAA,IACxB;AACI,QAAA,OAAOf,MAAI,0BAA0B,YAAY;AACjDA,YAAI,sBAAsBe,QAAO;AAAA,IACrC;AAAA,EAAA;AAER;AAEA,SAAS,2BAA2B;AAChC,QAAM,QAAQ;AACd,QAAM,OAAO;AACb,QAAM,KAAK;AAIL,QAAA,OAAO,OAAO,SAAS;AAEzB,MAAA,eAAe,OAAO,MAAM;AAAA,MAAM,YAAY;AAC9C,MAAA,iBAAiB,OAAO,MAAM;AAAA,MAAM,eAAe;AAEvD,SAAO,QAAQ,UAAU,KAAK,MAAM;AAChC,QAAI,MAAM;AACN,qBAAe,YAAY;AAC3B,uBAAiB,eAAe;AAAA,IACpC;AACA,WAAO,kBAAkB,OAAO,MAAM,EAAE,EAAE,KAAK,CAAC,WAAW;AACvD,UAAI,CAAC,QAAQ;AACI;AACE;AACC,wBAAA,MAAM,UAAU,0BAA0B,CAAC;AAC3C,wBAAA,MAAM,UAAU,wBAAwB,CAAC;AACzC,wBAAA,MAAM,UAAU,0BAA0B,CAAC;AACpD,eAAA;AAAA,MACX;AAC0B;AAC1B,aAAO,QAAQ,MAAM;AAID,wBAAA,MAAM,UAAU,2CAA2C,CAAC;AAC/D;AACE;MAAA,CAClB;AACD,qBAAe,CAAC,SAAS;AAIrB,eAAO,KAAK;AAAA,UACR;AAAA,QAAA,CACH;AAAA,MAAA,CACJ;AACD,mBAAa,CAAC,SAAS;AAInB,eAAO,KAAK;AAAA,UACR;AAAA,QAAA,CACH;AAAA,MAAA,CACJ;AACM,aAAA;AAAA,IAAA,CACV;AAAA,EAAA,CACJ;AACL;AACA,MAAM,aAAa;AACnB,SAAS,UAAU,OAAO;AACtB,SAAO,GAAG,UAAU,GAAG,KAAK,GAAG,UAAU;AAC7C;AACA,SAAS,4BAA4B;AAC7B,MAAA,OAAOE,SAAO,aAAa;AAE3BA,SAAG,kBAAkB;AAAA,EAAA,WAGhB,OAAO,OAAO,aAAa;AAEhC,OAAG,kBAAkB;AAAA,EAAA,WAEhB,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EAAA,WAEhB,OAAO,SAAS,aAAa;AAClC,SAAK,kBAAkB;AAAA,EAAA,WAElB,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EAAA,WAEhB,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EAAA,WAEhB,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EAAA,WAEhB,OAAO,QAAQ,aAAa;AACjC,QAAI,kBAAkB;AAAA,EAAA,WAEjB,OAAO,QAAQ,aAAa;AACjC,QAAI,kBAAkB;AAAA,EAAA,WAEjB,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EACzB;AACJ;AACA,yBAAyB;;;;;;;;ACnlBzB,SAAS,WAAW,QAAQ,YAAY;AACpC,MAAI,CAAC,QAAQ;AACT;AAAA,EACJ;AACM,QAAA,MAAM,OAAO,MAAM,GAAG;AAC5B,QAAM,MAAM,IAAI;AAChB,MAAI,QAAQ,GAAG;AACA,eAAA,UAAU,IAAI,CAAC;AAAA,EAAA,WAErB,QAAQ,GAAG;AACL,eAAA,UAAU,IAAI,CAAC;AACf,eAAA,WAAW,IAAI,CAAC;AAAA,EAC/B;AACJ;AACA,MAAM,SAAS,CAAC,iBAAiB;AACjC,SAAS,iBAAiB,6BAA6B,YAAY;AACxD,SAAA,QAAQ,CAAC,SAAS;AACjB,QAAA,OAAO,YAAY,IAAI,GAAG;AACE,kCAAA,IAAI,IAAI,WAAW,IAAI;AAAA,IACvD;AAAA,EAAA,CACH;AACL;AACA,MAAM,aAAa;AACnB,SAAS,mBAAmB,WAAW,YAAY;AAC/C,MAAI,YAAY;AACZ,WAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,SAAS;AAChC,YAAA,UAAU,KAAK,MAAM,UAAU;AACrC,UAAI,SAAS;AACH,cAAA,cAAc,QAAQ,CAAC;AACnB,kBAAA,IAAI,IAAI,WAAW,IAAI;AACvB,kBAAA,WAAW,IAAI,WAAW,WAAW;AAAA,MACnD;AAAA,IAAA,CACH;AAAA,EACL;AACJ;AACA,SAAS,mBAAmB,SAAS,gBAAgB;AAC7C,MAAA,CAAC,QAAQ,cAAc,GAAG;AAC1B;AAAA,EACJ;AACe,iBAAA,QAAQ,CAAC,eAAe;AAC3B,YAAA,UAAU,IAAI,SAAU,MAAM;AAClC,aAAO,KAAK,IAAI,UAAU,EAAE,IAAI;AAAA,IAAA;AAAA,EACpC,CACH;AACL;AACA,SAAS,oBAAoB,YAAY,UAAU,OAAO;AAChD,QAAA,aAAa,WAAW,oBAAoB,QAAQ;AAC/C,aAAA,QAAQ,CAAC,cAAc;AACxB7B,UAAAA,OAAM,UAAU,WAAW;AAC3BA,UAAAA,IAAG,IAAI,UAAU,OAAO;AAAA,EAAA,CACjC;AACL;AACA,SAAS,SAAS,UAAU,YAAY;AAC7B,SAAA,eAAe,UAAU,QAAQ;AAAA,IACpC,MAAM;AACF,YAAM,QAAQ,CAAA;AACM,0BAAA,YAAY,MAAM,KAAK;AACrC,YAAA,gBAAgB,WAAW,oBAAoB,QAAQ;AAC/C,oBAAA,QAAQ,CAAC,cAAc;AAC3BA,cAAAA,OAAM,UAAU,WAAW;AACjC,YAAI,CAACA,MAAK;AACN;AAAA,QACJ;AACI,YAAA,CAAC,MAAMA,IAAG,GAAG;AACPA,gBAAAA,IAAG,IAAI;QACjB;AACA,cAAMA,IAAG,EAAE,KAAK,UAAU,OAAO,SAAS;AAAA,MAAA,CAC7C;AACM,aAAA;AAAA,IACX;AAAA,EAAA,CACH;AACL;AACA,SAAS,cAAc,UAAU,QAAQ;AAErC,QAAM,YAAY,SAAS;AAE3B,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AACtC,UAAA,UAAU,UAAU,CAAC;AACvB,QAAA,QAAQ,OAAO,YAAY,QAAQ;AAC5B,aAAA;AAAA,IACX;AAAA,EACJ;AAEI,MAAA;AACJ,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,eAAW,cAAc,UAAU,CAAC,GAAG,MAAM;AAC7C,QAAI,UAAU;AACH,aAAA;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,oBAAoB;AACzB,MAAI,iBAAiB;AACrB;AACU,UAAA,cAAc,GAAG;AACvB,UAAM,WAAW,eAAe,YAAY,WAAW,YAAY,WAAW;AAC7D,qBAAA,gBAAgB,QAAQ,KAAK;AAAA,EAClD;AACO,SAAA;AACX;AAEA,MAAM,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,SAAS,aAAa,SAAS,KAAK;AACzB,SAAA,SAASC,MAAK,UAAU,MAAM;AACjC,UAAM,QAAQ,IAAI;AAClB,QAAI,SAAS,OAAO;AACV,YAAA,SAAS,EAAE,UAAU;AAC3B;AACU,cAAA,aAAa,OAAO,MAAM;AAAA,MACpC;AAAA,IACJ;AACA,WAAO,QAAQ,MAAM,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AAAA,EAAA;AAEnD;AACA,SAAS,iBAAiB,UAAU,SAAS;AACzC,QAAM,MAAM,SAAS;AAErB,MAAI,SAAS,QAAQ;AACrB,MAAI,UAAU,QAAQ;AACtB,MAAI,cAAc;AAClB,MAAI,SAAS,QAAQ;AACrB;AAEI,WAAO,iBAAiB,KAAK;AAAA;AAAA,MAEzB,CAAC,eAAe,GAAG;AAAA,QACf,MAAM;AACF,gBAAM,KAAK,KAAK,OAAO,KAAK,eAAe;AAEpC,iBAAA,OAAO,SAAY,KAAK;AAAA,QACnC;AAAA,MACJ;AAAA,IAAA,CACH;AAAA,EACL;AAEA,MAAI,MAAM;AACe;AACrB,QAAI,QAAQ;EAChB;AAEA,WAAS,QAAQ;AACjB,MAAI,QAAQ,QAAQ,KAAK,KAAK,QAAQ,MAAM,QAAQ;AACxC,YAAA,MAAM,QAAQ,CAAC,SAAS;AACnB,eAAA,MAAM,IAAI,IAAI;AAAA,IAAA,CAC1B;AACG,QAAA,SAAS,MAAM,iBAAiB,GAAG;AACnC,eAAS,MAAM,UAAU;AAAA,IAC7B;AAAA,EACJ;AACA,MAAI,wBAAwB,WAAY;AAEpC;AACW,aAAA,QAAQ,WAAW;IAC9B;AAAA,EAAA;AAEJ,MAAI,WAAW;AACf,MAAI,YAAY;AAEhB,WAAS,OAAO,aAAa,SAAS,MAAM,GAAG;AACnD;AACA,SAAS,sBAAsB,UAAU,SAAS;AAC9C,mBAAiB,UAAU,OAAO;AAClC,QAAM,MAAM,SAAS;AACV,aAAA,QAAQ,CAAC,WAAW;AACvB,QAAA,MAAM,IAAI,YAAa,MAAM;AAC7B,YAAM,aAAa,IAAI;AACnB,UAAA,cAAc,WAAW,MAAM,GAAG;AAClC,eAAO,WAAW,MAAM,EAAE,MAAM,YAAY,IAAI;AAAA,MACpD;AAAA,IAAA;AAAA,EACJ,CACH;AACL;AACA,SAAS,UAAU,UAAU,YAAYsB,QAAO;AAC5C,QAAM,MAAM,SAAS;AACrBA,SAAM,QAAQ,CAAC,SAAS;AAChB,QAAA,OAAO,YAAY,IAAI,GAAG;AAC1B,eAAS,IAAI,IAAI,IAAI,IAAI,IAAI,WAAW,IAAI;AAAA,IAChD;AAAA,EAAA,CACH;AACL;AACA,SAAS,QAAQ,MAAM;AACb,QAAA,QAAQ,KAAK,EAAE,IAAI;AACrB,MAAA,SAAS,MAAM,QAAQ;AAChB,WAAA;AAAA,EACX;AACO,SAAA;AACX;AACA,SAAS,SAAS,MAAM,MAAM;AAC1B,MAAI,SAAS,WAAW;AACX,aAAA,KAAK,MAAM,IAAI;AACxB,SAAK,EAAE,YAAY;AACZ,WAAA;AAAA,EACX;AACM,QAAA,QAAQ,KAAK,EAAE,IAAI;AAClB,SAAA,SAAS,eAAe,OAAO,IAAI;AAC9C;AAEA,MAAM,kBAAkB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAKJ;AACA,SAAS,UAAU,YAAY,QAAQ,oBAAI,OAAO;AAC9C,MAAI,YAAY;AACZ,WAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,SAAS;AACtC,UAAI,mBAAmB,MAAM,WAAW,IAAI,CAAC,GAAG;AAC5C,cAAM,IAAI,IAAI;AAAA,MAClB;AAAA,IAAA,CACH;AACwB;AACrB,YAAM,EAAE,SAAS,gBAAgB,OAAA,IAAW;AAC5C,UAAI,QAAQ;AACR,eAAO,QAAQ,CAAC,UAAU,UAAU,OAAO,KAAK,CAAC;AAAA,MACrD;AACA,UAAI,gBAAgB;AAChB,kBAAU,gBAAgB,KAAK;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AACO,SAAA;AACX;AACA,SAAS,SAAS,WAAW,MAAM,UAAU;AACrC,MAAA,SAAS,QAAQ,IAAI,MAAM,MAAM,CAAC,OAAO,WAAW,IAAI,GAAG;AACjD,cAAA,IAAI,IAAI,SAAU,MAAM;AAC9B,aAAO,KAAK,OAAO,KAAK,IAAI,UAAU,MAAM,IAAI;AAAA,IAAA;AAAA,EAExD;AACJ;AACA,MAAM,gBAAgB,CAAC,QAAQ;AAC/B,SAAS,UAAU,WAAW,OAAO,WAAW,eAAe;AAC3D,QAAM,QAAQ,CAAC,SAAS,SAAS,WAAW,MAAM,QAAQ,CAAC;AAC/D;AACA,SAAS,iBAAiB,WAAW,YAAY,WAAW,eAAe;AAC7D,YAAA,UAAU,EAAE,QAAQ,CAAC,SAAS,SAAS,WAAW,MAAM,QAAQ,CAAC;AAC/E;AACA,SAAS,iBAAiB,WAAW,cAAc;AAC/C,MAAI,CAAC,cAAc;AACf;AAAA,EACJ;AACM,QAAA,QAAQ,OAAO,KAAK,+BAA+B;AACnD,QAAA,QAAQ,CAAC,SAAS;AAChB,QAAA,eAAe,gCAAgC,IAAI,GAAG;AAC7C,eAAA,WAAW,MAAM,CAAA,CAAE;AAAA,IAChC;AAAA,EAAA,CACH;AACL;AACA,MAAM,6CAA2C,MAAM;AACnD,QAAM,eAAe,CAAA;AACf,QAAA,MAAM,WAAW,MAAM,KAAK,OAAO,EAAE,cAAc,MAAM;AAC/D,MAAI,OAAO,IAAI,OAAO,IAAI,IAAI,GAAG;AAC7B,UAAM,SAAS,IAAI,IAAI,EAAE,WAAW;AAChC,QAAA,QAAQ,MAAM,GAAG;AACX,YAAA,QAAQ,OAAO,KAAK,+BAA+B;AAClD,aAAA,QAAQ,CAAC,UAAU;AAChB,cAAA,QAAQ,CAAC,SAAS;AAChB,cAAA,OAAO,OAAO,IAAI,KAAK,CAAC,aAAa,SAAS,IAAI,GAAG;AACrD,yBAAa,KAAK,IAAI;AAAA,UAC1B;AAAA,QAAA,CACH;AAAA,MAAA,CACJ;AAAA,IACL;AAAA,EACJ;AACO,SAAA;AACX,CAAC;AACD,SAAS,sBAAsB,WAAW;AAC5B,YAAA,WAAW,uBAAuB;AAChD;AAEA,MAAM,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,SAAS,SAAS,UAAU,iBAAiB;AACzC,QAAM,mBAAmB,SAAS;AASlC,QAAM,aAAa;AAAA,IACf,YAAa,SAAS,YAAY,SAAS,SAAS,cAAe,CAAC;AAAA,IACpE,KAAK;AAAA;AAAA,IACL,SAAS,SAAS;AACd,WAAK,MAAM;AACX,YAAM,MAAM,iBAAiB;AAC7B,UAAI,KAAK,OAAO,IAAI,UAAU,IAAI,WAAW;AAGzC;AAAA,MACJ;AACA,uBAAiB,kBAAkB;AAAA,QAC/B,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO,CAAC;AAAA,MAAA,CACX;AACD,UAAI,aAAa,KAAK;AACb,eAAA,UAAU,WAAW,OAAO;AAAA,IACzC;AAAA,EAAA;AAEJ,QAAM,kBAAkB,GAAG;AAC3B,MAAI,iBAAiB;AACD,oBAAA,QAAQ,CAAC,OAAO;AACjB,iBAAA,UAAU,IAAI,gBAAgB;AAAA,IAAA,CAC5C;AACD,oBAAgB,SAAS;AAAA,EAC7B;AACA,aAAW,QAAQ;AACb,QAAA,aAAa,SAAS,EAAE;AAC9B,YAAU,YAAY,KAAK;AAC3B,mBAAiB,YAAY,UAAU;AACd;AACrB,UAAM,UAAU,WAAW;AAChB,eAAA,OAAO,YAAY,OAAO;AAAA,EACzC;AACO,SAAA;AACX;AACA,SAAS,cAAc,iBAAiB;AAC7B,SAAA,SAAShD,WAAU,IAAI;AACnB,WAAA,IAAI,SAAS,EAAE,CAAC;AAAA,EAAA;AAE/B;AACA,SAAS,wBAAwB,iBAAiB;AACvC,SAAA,SAASA,WAAU,IAAI;AACpB,UAAA,aAAa,SAAS,EAAE;AAC9B,UAAM,MAAM,WAAW,MAAM,KACzB,OAAO;AAAA,MACH,cAAc;AAAA,IAAA,CACjB;AACL,QAAI,CAAC;AACD;AACD,OAAA,EAAE,IAAI,SAAS;AAClB,UAAM,aAAa,IAAI;AACvB,QAAI,YAAY;AACZ,aAAO,KAAK,WAAW,UAAU,EAAE,QAAQ,CAAC,SAAS;AACjD,YAAI,CAAC,OAAO,YAAY,IAAI,GAAG;AAC3B,qBAAW,IAAI,IAAI,WAAW,WAAW,IAAI;AAAA,QACjD;AAAA,MAAA,CACH;AAAA,IACL;AACA,WAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,SAAS;AACtC,UAAI,CAAC,OAAO,KAAK,IAAI,GAAG;AAChB,YAAA,IAAI,IAAI,WAAW,IAAI;AAAA,MAC/B;AAAA,IAAA,CACH;AACD,qBAAiB,YAAY,EAAE;AAAA,EAK/B;AAER;AACA,SAAS,iBAAiB,YAAY,IAAI;AAClC,MAAA,WAAW,WAAW,QAAQ,GAAG;AACjC,UAAM,OAAO,GAAG,wBAAwB,GAAG,qBAAqB;AAChE,eAAW,SAAS,IAAI;AAAA,EAC5B;AACA,MAAI,WAAW,WAAW,MAAM,KAAK,GAAG,WAAW;AAC5C,OAAA,UAAU,CAAC,SAAS;AAChB,SAAA,UAAU,UAAU,IAAI;AAAA,IAAA,CAC9B;AAAA,EACL;AACA,MAAI,WAAW,WAAW,MAAM,KAAK,GAAG,WAAW;AAC5C,OAAA,UAAU,CAAC,SAAS;AAChB,SAAA,UAAU,UAAU,IAAI;AAAA,IAAA,CAC9B;AAAA,EACL;AACJ;AACA,SAAS,WAAW,OAAO;AACjB,QAAA,SAAS,IAAI,kBAAA,CAAmB;AAC/B,SAAA,eAAe,OAAO,WAAW;AAAA,IACpC,MAAM;AACF,aAAO,OAAO;AAAA,IAClB;AAAA,IACA,IAAI,GAAG;AACH,aAAO,QAAQ;AAAA,IACnB;AAAA,EAAA,CACH;AACL;AAEA,MAAM,eAAe;AAAA;AAAA;AAAA,EAGjB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AACJ;AACA,SAAS,iBAAiB,SAAS,aAAa,OAAO;AACnD,QAAM,aAAa,CAAA;AACnB,MAAI,CAAC,YAAY;AASJ,QAAA,gBAAT,SAAuB,QAAQ;AACrB,YAAA,SAAgB,uBAAA,OAAO,IAAI;AAE7B,gBAAA,OAAO,QAAQ,CAAC,aAAa;AACzB,eAAO,QAAQ,IAAI;AAAA,MAAA,CACtB;AACL,WAAK,QAAQ;AAAA,QACT;AAAA,MAAA,CACH;AAAA,IAAA;AAfQ,iBAAA,QAAQ,CAAC,SAAS;AAC3B,iBAAW,IAAI,IAAI;AAAA,QACf,MAAM;AAAA,QACN,OAAO;AAAA,MAAA;AAAA,IACX,CACH;AAYD,eAAW,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,OAAO,CAAC;AAAA,IAAA;AAEZ;AACI,iBAAW,GAAG,WAAW;AAAA,IAC7B;AAAA,EACJ;AACA,MAAI,QAAQ,WAAW;AAEnB,QAAI,QAAQ,UAAU,SAAS,iBAAsB,GAAG;AACpD,UAAI,CAAC,QAAQ,cAAc,CAAC,QAAQ,WAAW,MAAM;AACjD,mBAAW,OAAO;AAAA,UACd,MAAM;AAAA,UACN,OAAO;AAAA,QAAA;AAAA,MAEf;AACA,UAAI,CAAC,QAAQ,cAAc,CAAC,QAAQ,WAAW,OAAO;AAClD,mBAAW,QAAQ;AAAA,UACf,MAAM;AAAA,UACN,OAAO;AAAA,QAAA;AAAA,MAEf;AAAA,IACJ;AAAA,EACJ;AACO,SAAA;AACX;AACA,SAAS,qBAAqB,SAAS;AACnC,QAAM,aAAa,CAAA;AACnB;AACS,QAAA,WAAW,QAAQ,aAAc;AAClC,iBAAW,kBAAkB,IAAI;AAAA,QAC7B,MAAM;AAAA,QACN,OAAO;AAAA,MAAA;AAEX,iBAAW,kBAAkB,IAAI;AAAA,QAC7B,MAAM;AAAA,QACN,OAAO;AAAA,MAAA;AAEX,iBAAW,mBAAmB,IAAI;AAAA,QAC9B,MAAM;AAAA,QACN,OAAO;AAAA,MAAA;AAEX,iBAAW,eAAe,IAAI;AAAA,QAC1B,MAAM;AAAA,QACN,OAAO;AAAA,MAAA;AAAA,IAEf;AAAA,EACJ;AACO,SAAA;AACX;AAMA,SAAS,UAAU,oBAAoB;AAC/B,MAAA,CAAC,mBAAmB,YAAY;AAChC,uBAAmB,aAAa;EACpC;AACO,SAAA,mBAAmB,YAAY,iBAAiB,kBAAkB,GAAG,qBAAqB,mBAAmB,OAAO,CAAC;AAChI;AACA,MAAM,aAAa,CAAC,QAAQ,QAAQ,SAAS,QAAQ,OAAO,IAAI;AAChE,SAAS,cAAc,MAAM,cAAc;AAEvC,MAAI,QAAQ,IAAI,KAAK,KAAK,WAAW,GAAG;AACpC,WAAO,KAAK,CAAC;AAAA,EACjB;AACO,SAAA;AACX;AACA,SAAS,kBAAkB,MAAM,cAAc;AACrC,QAAA,MAAM,cAAc,IAAI;AAC9B,SAAO,WAAW,QAAQ,GAAG,MAAM,KAAK,MAAM;AAClD;AAMA,SAAS,cAAc,EAAE,WAAW,GAAG,UAAU;AACzC,MAAA,QAAQ,QAAQ,GAAG;AACV,aAAA,QAAQ,CAAC,QAAQ;AACtB,iBAAW,GAAG,IAAI;AAAA,QACd,MAAM;AAAA,QACN,OAAO;AAAA,MAAA;AAAA,IACX,CACH;AAAA,EAAA,WAEI,cAAc,QAAQ,GAAG;AAC9B,WAAO,KAAK,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AAC7B,YAAA,OAAO,SAAS,GAAG;AACrB,UAAA,cAAc,IAAI,GAAG;AAErB,YAAI,QAAQ,KAAK;AACb,YAAA,WAAW,KAAK,GAAG;AACnB,kBAAQ,MAAM;AAAA,QAClB;AACA,cAAM,OAAO,KAAK;AACb,aAAA,OAAO,kBAAkB,IAAI;AAClC,mBAAW,GAAG,IAAI;AAAA,UACd,MAAM,KAAK;AAAA,UACX;AAAA,QAAA;AAAA,MACJ,OAEC;AAED,mBAAW,GAAG,IAAI;AAAA,UACd,MAAM,kBAAkB,IAAI;AAAA,QAAA;AAAA,MAEpC;AAAA,IAAA,CACH;AAAA,EACL;AACJ;AACA,SAAS,cAAc,YAAYuD,SAAQ;AAC9BA,UAAAA,UACH,kBAAkB,UAAU,IAC5B,uBAAuB,iBAAiB,WAAW,EAAE,CAAC,MAAM;AACtE;AACA,SAAS,kBAAkB,YAAY;AACnC,QAAM,YAAY,CAAA;AACd,MAAA,cAAc,UAAU,GAAG;AAC3B,WAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,SAAS;AACtC,UAAI,aAAa,QAAQ,IAAI,MAAM,IAAI;AACnC,kBAAU,IAAI,IAAI,iBAAiB,WAAW,IAAI,CAAC;AAAA,MACvD;AAAA,IAAA,CACH;AAAA,EACL;AACO,SAAA;AACX;AACA,SAAS,cAAc,IAAI;AAEvB,QAAM,aAAa,GAAG;AAClB,MAAA,QAAQ,WAAW,SAAS,KAC5B,WAAW,UAAU,SAAS,kBAAkB,GAAG;AAChD,OAAA,OAAO,cAAc,MAAM;AACvB,SAAA,UACC,GAAG,OAAO,QAAQ;AAAA,QACd,MAAM,GAAG;AAAA,QACT,OAAO,GAAG;AAAA,MAAA,CACb;AAAA,IAAA,GACN;AAAA,MACC,WAAW;AAAA,IAAA,CACd;AAAA,EACL;AACJ;AACA,SAAS,iBAAiB,MAAM;AACrB,SAAA;AACX;AAEA,SAAS,SAAS,GAAG;AACjB,SAAO;AACX;AACA,SAAS,kBAAkB,kBAAkB;AACnC,QAAA,UAAU,SAASC,WAAU;AACzB,UAAA,KAAK,KAAK,WAAW;AAC3B,QAAI,CAAC,IAAI;AACL;AAAA,IACJ;AACA,QAAI,KAAK,KAAK;AACV,2BAAqB,iBAAiB,EAAE,GAAG,KAAK,IAAI,CAAC;AAAA,IAAA,WAEhD,iBAAiB,KAAK,WAAW,EAAE,MAAM,KAAK;AAEd,2CAAA,iBAAiB,EAAE,GAAG,IAAI;AAAA,IACnE;AAAA,EAAA;AAEJ;AACQ,QAAA,CAAC,iBAAiB,WAAW;AAC7B,uBAAiB,YAAY;IACjC;AACA,qBAAiB,UAAU,KAAK;AAAA,EACpC;AACJ;AACA,SAAS,qCAAqC,IAAI,YAAY;AAC1D,QAAM,YAAY,WAAW;AAC7B,QAAM,YAAY,uBAAuB,EAAE,KAAK,CAAA;AAChD,MAAI,gBAAgB,WAAW,WAAW,KAAK,GAAG;AAC9C,eAAW,QAAQ,SAAS;AAAA,EAChC;AACJ;AACA,SAAS,qBAAqB,IAAI,UAAU;AAClC,QAAA,YAAY,MAAM,SAAS,KAAK;AACtC,QAAM,YAAY,uBAAuB,EAAE,KAAK,CAAA;AAC5C,MAAA,gBAAgB,WAAW,SAAS,GAAG;AAC3B,gBAAA,UAAU,WAAW,WAAW,KAAK;AAC7C,QAAA,YAAY,SAAS,MAAM,GAAG;AAC9B,oBAAc,SAAS,MAAM;AAAA,IACjC;AACA;AACI,eAAS,OAAO;AAAA,IACpB;AAAA,EACJ;AACJ;AACA,SAAS,gBAAgB,WAAW,WAAW,WAAW,MAAM;AACtD,QAAA,WAAW,OAAO,KAAK,SAAS;AACtC,MAAI,YAAY,SAAS,WAAW,OAAO,KAAK,SAAS,EAAE,QAAQ;AACxD,WAAA;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAChC,UAAA,MAAM,SAAS,CAAC;AACtB,QAAI,UAAU,GAAG,MAAM,UAAU,GAAG,GAAG;AAC5B,aAAA;AAAA,IACX;AAAA,EACJ;AACO,SAAA;AACX;AACA,SAAS,cAAc,YAAY;AAC/B,QAAM,eAAe,WAAW;AAChC,MAAI,WAAW,WAAW;AAC1B,MAAI,CAAC,UAAU;AACA,eAAA,QAAQ,WAAW;EAClC;AACA,QAAM,YAAY,CAAA;AACd,MAAA,QAAQ,YAAY,GAAG;AACV,iBAAA,QAAQ,CAAC,aAAa;AAE/B,gBAAU,KAAK,SAAS,QAAQ,UAAU,OAAY,CAAC;AACvD,UAAI,aAAa,oBAAoB;AAC7B,YAAA,QAAQ,QAAQ,GAAG;AACnB,mBAAS,KAAK,MAAM;AACpB,mBAAS,KAAK,YAAY;AAAA,QAAA,OAEzB;AACD,mBAAS,OAAO;AAAA,YACZ,MAAM;AAAA,YACN,SAAS;AAAA,UAAA;AAEb,mBAAS,aAAa;AAAA,YAClB,MAAM,CAAC,QAAQ,QAAQ,SAAS,OAAO,QAAQ,IAAI;AAAA,YACnD,SAAS;AAAA,UAAA;AAAA,QAEjB;AAAA,MACJ;AAAA,IAAA,CACH;AAAA,EACL;AACO,SAAA;AACX;AACA,SAAS,aAAa,kBAAkB,YAAY;AAChD,mBAAiB,OAAO;AACP,mBAAA,YAAY,cAAc,UAAU;AACzD;AAEA,SAAS,eAAe,YAAY,EAAE,OAAO,OAAAR,QAAO,QAAAO,SAAQ,iBAAiB,cAAAE,eAAc,YAAAC,aAAY,eAAAC,kBAAkB;AACrH,eAAa,WAAW,WAAW;AACnC,QAAM,UAAU;AAAA,IACZ,eAAe;AAAA;AAAA,IAEf,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,EAAA;AAEjB,MAAA,QAAQ,WAAW,MAAM,GAAG;AACjB,eAAA,OAAO,QAAQ,CAAC,SAAS;AAC5B,UAAAzE,WAAS,KAAK,OAAO,GAAG;AACjB,eAAA,SAAS,KAAK,OAAO;AAAA,MAChC;AAAA,IAAA,CACH;AAAA,EACL;AACA,MAAI,WAAW,SAAS;AACb,WAAA,SAAS,WAAW,OAAO;AAAA,EACtC;AACA,QAAM,qBAAqB;AAAA,IACvB;AAAA,IACA,WAAWyE,eAAc,EAAE,OAAAX,QAAO,QAAAO,SAAQ,cAAAE,eAAc,YAAY;AAAA,IACpE,eAAe;AAAA,MACX,OAAO;AAIH,aAAK,OAAO,KAAK,IAAI,UAAU,YAAY;AAAA,MAC/C;AAAA,MACA,OAAO;AACH,aAAK,OAAO,KAAK,IAAI,UAAU,YAAY;AAAA,MAC/C;AAAA,MACA,OAAOG,OAAM;AACT,aAAK,OAAO,KAAK,IAAI,UAAU,gBAAgBA,KAAI;AAAA,MACvD;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,KAAKF;AAAAA,IACT;AAAA,EAAA;AAEqB;AACrB,iBAAa,oBAAoB,UAAU;AAAA,EAC/C;AACA,YAAU,kBAAkB;AAC5B,oBAAkB,kBAAkB;AACpC,mBAAiB,oBAAoB,UAAU;AAC5B,qBAAA,mBAAmB,SAAS,WAAW,cAAc;AACxE;AACuB,uBAAA,mBAAmB,SAAS,WAAW,OAAO;AAAA,EACrE;AACA,MAAI,OAAO;AACP,UAAM,oBAAoB,EAAE,YAAAA,YAAY,CAAA;AAAA,EAC5C;AACO,SAAA;AACX;AACA,SAAS,oBAAoBG,eAAc;AAChC,SAAA,SAAS5B,iBAAgB,qBAAqB;AACjD,WAAO,UAAU,eAAe,qBAAqB4B,aAAY,CAAC;AAAA,EAAA;AAE1E;AACA,IAAI;AACJ,IAAI;AACJ,SAAS,WAAW;AAOhB,SAAO,OAAS,EAAA;AACpB;AACA,SAAS,iBAAiB,cAAc,SAAS;AAC7C,MAAI,CAAC,oBAAoB;AACrB,yBAAqB,SAAW,EAAA;AAAA,EACpC;AACM,QAAA,QAAQ,mBAAmB,cAAc,OAAO;AAC/C,SAAA,eAAe,MAAM,CAAC,KAAK;AACtC;AACA,SAAS,kBAAkB,UAAU;AACjC,MAAI,CAAC,qBAAqB;AACtB,0BAAsB,SAAW,EAAA;AAAA,EACrC;AACA,SAAO,oBAAoB,QAAQ;AACvC;AAEA,SAAS,UAAU,YAAYA,eAAc;AACzC,QAAM,EAAE,OAAO,OAAAb,QAAO,QAAAO,SAAQ,cAAAE,eAAc,YAAAC,aAAY,eAAAC,eAAAA,IAAkBE;AACpE,QAAA,yBAAyB,eAAe,YAAY;AAAA,IACtD,OAAAb;AAAAA,IACA,QAAAO;AAAAA,IACA,iBAAiB;AAAA,IACjB,cAAAE;AAAAA,IACA,YAAAC;AAAAA,IACA,eAAAC;AAAAA,EAAA,CACH;AACD,gBAAc,yBAAyB,WAAW,WAAW,YAAY,KAAK;AAC9E,QAAM,UAAU,uBAAuB;AAC/B,UAAA,SAAS,SAAU,OAAO;AAC9B;AACI,WAAK,UAAU;AAAA,IACnB;AACA,SAAK,QAAQ;AAAA,MACT,UAAU,gBAAgB,KAAK,QAAQ,eAAe,KAAK,CAAC;AAAA,IAAA;AAEhE,WAAO,KAAK,OAAO,KAAK,IAAI,UAAU,SAAS,KAAK;AAAA,EAAA;AAExD,YAAU,SAAS,eAAe;AAClC;AACI,qBAAiB,SAAS,UAAU;AAAA,EACxC;AACiB,mBAAA,SAAS,WAAW,cAAc;AACnD,wBAAsB,OAAO;AAC7B,WAAS,MAAM,wBAAwB,EAAE,YAAAD,YAAY,CAAA;AAC9C,SAAA;AACX;AACA,SAAS,eAAeG,eAAc;AAC3B,SAAA,SAASC,YAAW,gBAAgB;AACvC,WAAO,UAAU,UAAU,gBAAgBD,aAAY,CAAC;AAAA,EAAA;AAEhE;AAEA,SAAS,oBAAoB,iBAAiB;AACnC,SAAA,SAAS7D,WAAU,IAAI;AACT,qBAAA,SAAS,EAAE,GAAG,EAAE;AAAA,EAGjC;AAER;AAEA,MAAM,SAAS;AACf,MAAM,cAAc;AACpB,SAAS,iBAAiB,YAAY;AAClC,QAAM,kBAAkB,WAAW;AAC7B,QAAA,kBAAkB,SAAU,UAAU,MAAM;AACvC,WAAA,gBAAgB,MAAM,YAAY;AAAA,MACrC,eAAe,KAAK;AAAA,MACpB,GAAG;AAAA,IAAA,CACN;AAAA,EAAA;AAGD,MAAA;AACA,eAAW,eAAe;AAAA,WAEvB,OAAO;AACV,eAAW,gBAAgB;AAAA,EAC/B;AACJ;AACA,SAAS,oBAAoB,MAAM,SAAS,aAAa;AAC/C,QAAA,UAAU,QAAQ,IAAI;AAC5B,MAAI,CAAC,SAAS;AACF,YAAA,IAAI,IAAI,WAAY;AACxB,uBAAiB,IAAI;AAAA,IAAA;AAAA,EACzB,OAEC;AACO,YAAA,IAAI,IAAI,YAAa,MAAM;AAC/B,uBAAiB,IAAI;AACd,aAAA,QAAQ,MAAM,MAAM,IAAI;AAAA,IAAA;AAAA,EAEvC;AACJ;AACA,OAAO,SAAU,SAAS;AACtB,sBAAoB,SAAS,OAAO;AACpC,SAAO,OAAO,OAAO;AACzB;AACA,YAAY,SAAU,SAAS;AAC3B,sBAAoB,WAAW,OAAO;AAEtC,QAAM,iBAAiB,QAAQ,cAAc,QAAQ,WAAW;AAChE,MAAI,CAAC,gBAAgB;AACjB,cAAU,OAAO;AACjB,sBAAkB,OAAO;AAAA,EAC7B;AACA,SAAO,YAAY,OAAO;AAC9B;AAGA,SAAS,cAAc,EAAE,OAAAgD,QAAO,QAAAO,SAAQ,cAAAE,eAAc,cAAe;AAC1D,SAAA;AAAA,IACH,WAAW;AACP,UAAI,aAAa,KAAK;AACX,iBAAA,WAAW,IAAI,IAAI;AAC9B,YAAM,kBAAkB;AAAA,QACpB,QAAQ,KAAK;AAAA,MAAA;AAGjBA,oBAAa,MAAM,eAAe;AAElC,YAAM,aAAa;AACb,YAAA,oBAAoBF,QAAO,UAAU;AAC3C,UAAI,YAAY;AAChB,WAAK,MAAM,iBAAiB;AAAA,QACxB,MAAM;AAAA,QACN,OAAO,cAAc,WAAW,iBAAiB;AAAA,MAAA,GAClD;AAAA,QACC,QAAQ,oBAAoB,SAAS;AAAA,QACrC;AAAA,QACA,OAAO,WAAW,MAAM,CAAC;AAAA;AAAA,QACzB,iBAAiB,gBAAgB,UAAU,gBAAgB,OAAO;AAAA,QAClE,cAAc,UAAU,SAAS;AAC7B,mBAAS,UAAU,UAAU;AACnB,oBAAA,UAAU,YAAYP,MAAK;AACrC,gCAAsB,UAAU,OAAO;AAAA,QAC3C;AAAA,MAAA,CACH;AAUD,UAAI,CAAC,mBAAmB;AACpB,sBAAc,KAAK,GAAG;AAAA,MAC1B;AAAA,IACJ;AAAA,IACA,QAAQ;AAMJ,UAAI,KAAK,KAAK;AACV;AACS,eAAA,IAAI,UAAU,SAAS;AACvB,eAAA,IAAI,UAAU,QAAQ;AAAA,QAC/B;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,WAAW;AACP,UAAI,KAAK,KAAK;AACe,iCAAA,KAAK,IAAI,EAAE,GAAG;AACvC,0BAAkB,KAAK,GAAG;AAAA,MAC9B;AAAA,IACJ;AAAA,EAAA;AAER;AAEA,MAAM,QAAQ,CAAC,aAAa,wBAAwB,iBAAiB;AACrE,SAAS,OAAO,YAAY;AACjB,SAAA,CAAC,CAAC,WAAW;AACxB;AACA,SAAS,aAAa,YAAY,QAAQ;AAC3B,aAAA,aAAa,OAAO,MAAM;AACzC;AACA,SAAS,WAAW,OAAO;AAEjB,QAAA,SAAU,MAAM,UAClB,MAAM;AACV,QAAM,SAAS,OAAO;AAClB,MAAA;AACJ,MAAI,QAAQ;AACG,eAAA,cAAc,KAAK,KAAK,MAAM;AAAA,EAC7C;AACA,MAAI,CAAC,UAAU;AACX,eAAW,KAAK;AAAA,EACpB;AACA,SAAO,SAAS;AACpB;AAEA,IAAI,sCAAmC,OAAO;AAAA,EAC5C,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,MAAM,YAAY,cAAc;AAChC,MAAM,aAAa,eAAe,YAAY;AAC9C,MAAM,kBAAkB,oBAAoB,YAAY;AACxD,MAAM,kBAAkB,oBAAoB;AAC5C,MAAM,sBAAsB,wBAAwB;AACpD;AACO,KAAA,YAAY,OAAO,YAAY;AAClC,KAAG,aAAa;AAChB,KAAG,kBAAkB;AAClB,KAAA,kBAAkB,OAAO,kBACxB;AACD,KAAA,sBAAsB,OAAO,sBAC5B;AACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4]}