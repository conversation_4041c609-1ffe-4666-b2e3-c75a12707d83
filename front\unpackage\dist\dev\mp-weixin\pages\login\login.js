"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_jeecgUser = require("../../api/jeecg-user.js");
const utils_captcha = require("../../utils/captcha.js");
const utils_auth = require("../../utils/auth.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 0,
      loading: false,
      showPassword: false,
      needCaptcha: true,
      // 默认需要验证码
      captchaImage: "",
      captchaLoading: false,
      // 登录表单
      loginForm: {
        username: "",
        password: "",
        captcha: "",
        checkKey: ""
      },
      // 来源页面（用于登录成功后跳转）
      redirectUrl: ""
    };
  },
  computed: {
    canLogin() {
      const { username, password, captcha } = this.loginForm;
      const basicValid = username.trim() && password.trim();
      if (this.needCaptcha) {
        return basicValid && captcha.trim();
      }
      return basicValid;
    }
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/login/login.vue:164", "🔐 登录页面加载:", options);
    this.getSystemInfo();
    if (options.redirect) {
      this.redirectUrl = decodeURIComponent(options.redirect);
    }
    this.initCaptcha();
  },
  methods: {
    // 获取系统信息
    getSystemInfo() {
      try {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        this.statusBarHeight = systemInfo.statusBarHeight || 0;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:185", "获取系统信息失败:", error);
      }
    },
    // 初始化验证码
    initCaptcha() {
      return __async(this, null, function* () {
        if (!this.needCaptcha)
          return;
        common_vendor.index.__f__("log", "at pages/login/login.vue:193", "🔄 初始化验证码");
        yield this.loadCaptcha();
      });
    },
    // 加载验证码
    loadCaptcha() {
      return __async(this, null, function* () {
        if (!this.needCaptcha)
          return;
        this.captchaLoading = true;
        try {
          const result = yield utils_captcha.getCaptcha(true);
          if (result.success) {
            this.captchaImage = utils_captcha.getCaptchaImageUrl();
            this.loginForm.checkKey = utils_captcha.getCaptchaKey();
            common_vendor.index.__f__("log", "at pages/login/login.vue:210", "✅ 验证码加载成功:", {
              hasImage: !!this.captchaImage,
              checkKey: this.loginForm.checkKey,
              imageFormat: this.captchaImage ? this.captchaImage.substring(0, 30) + "..." : ""
            });
          } else {
            common_vendor.index.__f__("error", "at pages/login/login.vue:216", "❌ 验证码加载失败:", result.message);
            common_vendor.index.showToast({
              title: result.message || "验证码加载失败",
              icon: "none"
            });
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/login/login.vue:223", "❌ 验证码加载异常:", error);
          common_vendor.index.showToast({
            title: "验证码加载失败",
            icon: "none"
          });
        } finally {
          this.captchaLoading = false;
        }
      });
    },
    // 刷新验证码
    handleRefreshCaptcha() {
      return __async(this, null, function* () {
        if (!this.needCaptcha || this.captchaLoading)
          return;
        common_vendor.index.__f__("log", "at pages/login/login.vue:237", "🔄 刷新验证码");
        this.loginForm.captcha = "";
        yield this.loadCaptcha();
      });
    },
    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword;
    },
    // 处理登录
    handleLogin() {
      return __async(this, null, function* () {
        if (!this.canLogin || this.loading)
          return;
        this.loading = true;
        try {
          common_vendor.index.__f__("log", "at pages/login/login.vue:258", "🔐 开始登录:", this.loginForm.username);
          const loginData = {
            username: this.loginForm.username.trim(),
            password: this.loginForm.password.trim()
          };
          if (this.needCaptcha) {
            loginData.captcha = this.loginForm.captcha.trim();
            loginData.checkKey = this.loginForm.checkKey;
          }
          const result = yield api_jeecgUser.login(loginData);
          if (result.success) {
            common_vendor.index.__f__("log", "at pages/login/login.vue:276", "✅ 登录成功:", result.data);
            yield this.saveAuthInfo(result.data);
            common_vendor.index.showToast({
              title: "登录成功",
              icon: "success",
              duration: 1500
            });
            setTimeout(() => {
              this.handleLoginSuccess();
            }, 1500);
          } else {
            common_vendor.index.__f__("error", "at pages/login/login.vue:293", "❌ 登录失败:", result.message);
            common_vendor.index.showToast({
              title: result.message || "登录失败",
              icon: "none",
              duration: 2e3
            });
            if (result.message && (result.message.includes("验证码") || result.message.includes("captcha"))) {
              yield this.handleRefreshCaptcha();
            }
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/login/login.vue:308", "❌ 登录异常:", error);
          common_vendor.index.showToast({
            title: error.message || "登录失败，请重试",
            icon: "none",
            duration: 2e3
          });
        } finally {
          this.loading = false;
        }
      });
    },
    // 保存认证信息
    saveAuthInfo(loginResData) {
      return __async(this, null, function* () {
        try {
          common_vendor.index.__f__("log", "at pages/login/login.vue:324", "💾 保存认证信息:", loginResData);
          const token = loginResData.token;
          if (token) {
            utils_auth.setToken(token);
            common_vendor.index.__f__("log", "at pages/login/login.vue:330", "✅ Token保存成功:", token.substring(0, 20) + "...");
          } else {
            common_vendor.index.__f__("warn", "at pages/login/login.vue:332", "⚠️ 登录响应中未找到token");
          }
          utils_auth.setUserInfo(loginResData.userInfo);
          common_vendor.index.__f__("log", "at pages/login/login.vue:351", "✅ 用户信息保存成功:", loginResData.userInfo);
          this.updateGlobalAuthState(token, loginResData.userInfo);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/login/login.vue:360", "❌ 保存认证信息失败:", error);
          common_vendor.index.showToast({
            title: "保存用户信息失败",
            icon: "none"
          });
          throw error;
        }
      });
    },
    // 更新全局认证状态
    updateGlobalAuthState(token, userInfo) {
      try {
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.isLogin = true;
          app.globalData.token = token;
          app.globalData.userInfo = userInfo;
          common_vendor.index.__f__("log", "at pages/login/login.vue:379", "✅ 全局状态更新成功");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:388", "❌ 更新全局状态失败:", error);
      }
    },
    // 刷新认证状态
    refreshAuthState() {
      return __async(this, null, function* () {
        try {
          if (typeof utils_auth.refreshUserInfo === "function") {
            yield utils_auth.refreshUserInfo();
            common_vendor.index.__f__("log", "at pages/login/login.vue:398", "✅ 认证状态刷新成功");
          }
          common_vendor.index.$emit("authStateChanged", {
            isAuthenticated: true,
            timestamp: Date.now()
          });
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages/login/login.vue:408", "⚠️ 刷新认证状态失败:", error);
        }
      });
    },
    // 处理登录成功
    handleLoginSuccess() {
      try {
        common_vendor.index.__f__("log", "at pages/login/login.vue:415", "🎉 处理登录成功跳转");
        this.loginForm.captcha = "";
        this.loginForm.checkKey = "";
        if (this.redirectUrl) {
          common_vendor.index.__f__("log", "at pages/login/login.vue:423", "📍 跳转到重定向页面:", this.redirectUrl);
          const tabBarPages = [
            "pages/index/index",
            "pages/course/list",
            "pages/checkin/checkin",
            "pages/leaderboard/leaderboard",
            "pages/profile/profile"
          ];
          const cleanUrl = this.redirectUrl.replace(/^\//, "").split("?")[0];
          const isTabBarPage = tabBarPages.includes(cleanUrl);
          if (isTabBarPage) {
            common_vendor.index.switchTab({
              url: `/${cleanUrl}`,
              success: () => {
                common_vendor.index.__f__("log", "at pages/login/login.vue:442", "✅ 成功跳转到tabBar页面:", cleanUrl);
              },
              fail: (error) => {
                common_vendor.index.__f__("error", "at pages/login/login.vue:445", "❌ 跳转tabBar页面失败:", error);
                common_vendor.index.switchTab({ url: "/pages/index/index" });
              }
            });
          } else {
            common_vendor.index.redirectTo({
              url: this.redirectUrl,
              success: () => {
                common_vendor.index.__f__("log", "at pages/login/login.vue:455", "✅ 成功跳转到重定向页面:", this.redirectUrl);
              },
              fail: (error) => {
                common_vendor.index.__f__("error", "at pages/login/login.vue:458", "❌ 跳转重定向页面失败:", error);
                common_vendor.index.switchTab({ url: "/pages/index/index" });
              }
            });
          }
        } else {
          common_vendor.index.__f__("log", "at pages/login/login.vue:466", "📍 跳转到首页");
          common_vendor.index.switchTab({
            url: "/pages/index/index",
            success: () => {
              common_vendor.index.__f__("log", "at pages/login/login.vue:470", "✅ 成功跳转到首页");
            },
            fail: (error) => {
              common_vendor.index.__f__("error", "at pages/login/login.vue:473", "❌ 跳转首页失败:", error);
            }
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:479", "❌ 处理登录成功跳转失败:", error);
        common_vendor.index.switchTab({ url: "/pages/index/index" });
      }
    },
    // 微信登录
    handleWechatLogin() {
      return __async(this, null, function* () {
        try {
          common_vendor.index.__f__("log", "at pages/login/login.vue:489", "🔐 微信登录");
          common_vendor.index.showToast({
            title: "功能开发中",
            icon: "none"
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/login/login.vue:499", "微信登录失败:", error);
        }
      });
    },
    // 钉钉登录
    handleDingtalkLogin() {
      return __async(this, null, function* () {
      });
    },
    // 跳转到注册页面
    goToRegister() {
      common_vendor.index.navigateTo({
        url: "/pages/register/register"
      });
    },
    // 跳转到忘记密码页面
    goToForgotPassword() {
      common_vendor.index.navigateTo({
        url: "/pages/forgot-password/forgot-password"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: common_assets._imports_0,
    c: $data.loading,
    d: $data.loginForm.username,
    e: common_vendor.o(($event) => $data.loginForm.username = $event.detail.value),
    f: $data.showPassword ? "text" : "password",
    g: $data.loading,
    h: $data.loginForm.password,
    i: common_vendor.o(($event) => $data.loginForm.password = $event.detail.value),
    j: common_vendor.n($data.showPassword ? "icon-eye" : "icon-eye-close"),
    k: common_vendor.o((...args) => $options.togglePassword && $options.togglePassword(...args)),
    l: $data.needCaptcha
  }, $data.needCaptcha ? common_vendor.e({
    m: $data.loading,
    n: $data.loginForm.captcha,
    o: common_vendor.o(($event) => $data.loginForm.captcha = $event.detail.value),
    p: $data.captchaLoading
  }, $data.captchaLoading ? {} : $data.captchaImage ? {
    r: $data.captchaImage
  } : {}, {
    q: $data.captchaImage,
    s: common_vendor.o((...args) => $options.handleRefreshCaptcha && $options.handleRefreshCaptcha(...args))
  }) : {}, {
    t: $data.loading
  }, $data.loading ? {} : {}, {
    v: common_vendor.t($data.loading ? "登录中..." : "登录"),
    w: $data.loading ? 1 : "",
    x: !$options.canLogin || $data.loading,
    y: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args)),
    z: common_vendor.o((...args) => $options.goToRegister && $options.goToRegister(...args)),
    A: common_vendor.o((...args) => $options.goToForgotPassword && $options.goToForgotPassword(...args)),
    B: common_vendor.o((...args) => $options.handleWechatLogin && $options.handleWechatLogin(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
