/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.mp-page-wrapper.data-v-4fd45c90 {
  min-height: 100vh;
  background-color: #f8f9fa;
}
.mp-navbar.data-v-4fd45c90 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.mp-navbar .mp-navbar-content.data-v-4fd45c90 {
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  position: relative;
}
.mp-navbar .mp-navbar-left.data-v-4fd45c90 {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.mp-navbar .mp-navbar-left .mp-navbar-back.data-v-4fd45c90 {
  font-size: 24px;
  color: white;
  font-weight: bold;
}
.mp-navbar .mp-navbar-title.data-v-4fd45c90 {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: white;
}
.mp-navbar .mp-navbar-right.data-v-4fd45c90 {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.mp-page-content.data-v-4fd45c90 {
  min-height: 100vh;
  position: relative;
}
.mp-loading.data-v-4fd45c90 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}
.mp-loading .mp-loading-spinner.data-v-4fd45c90 {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin-4fd45c90 1s linear infinite;
  margin-bottom: 16px;
}
.mp-loading .mp-loading-text.data-v-4fd45c90 {
  color: #666;
  font-size: 14px;
}
@keyframes spin-4fd45c90 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.mp-error.data-v-4fd45c90 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}
.mp-error .mp-error-icon.data-v-4fd45c90 {
  font-size: 48px;
  margin-bottom: 16px;
}
.mp-error .mp-error-text.data-v-4fd45c90 {
  color: #666;
  font-size: 16px;
  margin-bottom: 24px;
  text-align: center;
}
.mp-error .mp-error-retry.data-v-4fd45c90 {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 24px;
  font-size: 14px;
}
.mp-content.data-v-4fd45c90 {
  min-height: calc(100vh - 44px);
}
.mp-safe-area-bottom.data-v-4fd45c90 {
  height: env(safe-area-inset-bottom);
  background-color: #f8f9fa;
}

/* 小程序特定样式 */
.mp-page-wrapper.data-v-4fd45c90 {
  /* 小程序环境下的特殊处理 */
  overflow-x: hidden;
}
.mp-navbar.data-v-4fd45c90 {
  /* 小程序导航栏优化 */
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}

/* H5特定样式 */