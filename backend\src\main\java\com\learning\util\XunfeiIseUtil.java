package com.learning.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.learning.config.XunfeiConfig;
import com.learning.dto.IseRequest;
import com.learning.dto.IseResponse;
import com.learning.dto.IseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 科大讯飞语音评测工具类
 * <AUTHOR>
 * @since 2024-03-22
 */
@Slf4j
@Component
public class XunfeiIseUtil {
    
    @Autowired
    private XunfeiConfig xunfeiConfig;
    
    /**
     * 语音评测
     * @param request 评测请求参数
     * @return 评测结果
     */
    public IseResult evaluate(IseRequest request) {
        try {
            // 构建WebSocket连接URL
            String authUrl = getAuthUrl();
            
            // 构建评测参数
            JSONObject params = buildParams(request);
            
            // 发送评测请求(这里使用HTTP模拟，实际应该使用WebSocket)
            IseResponse response = sendEvaluateRequest(authUrl, params, request.getAudioData());
            
            // 解析评测结果
            return parseResult(response);
            
        } catch (Exception e) {
            log.error("语音评测失败", e);
            throw new RuntimeException("语音评测失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成鉴权URL
     */
    private String getAuthUrl() throws Exception {
        String host = "ise-api.xfyun.cn";
        String path = "/v2/open-ise";
        String method = "GET";
        
        // 生成RFC1123格式的时间戳
        SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        format.setTimeZone(TimeZone.getTimeZone("GMT"));
        String date = format.format(new Date());
        
        // 拼接字符串
        String preStr = "host: " + host + "\n" +
                       "date: " + date + "\n" +
                       method + " " + path + " HTTP/1.1";
        
        // 进行hmac-sha256加密
        Mac mac = Mac.getInstance("hmacsha256");
        SecretKeySpec spec = new SecretKeySpec(xunfeiConfig.getApiSecret().getBytes(StandardCharsets.UTF_8), "hmacsha256");
        mac.init(spec);
        byte[] hexDigits = mac.doFinal(preStr.getBytes(StandardCharsets.UTF_8));
        String sha = Base64.getEncoder().encodeToString(hexDigits);
        
        // 拼接authorization
        String authorization = String.format("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
                xunfeiConfig.getApiKey(), "hmac-sha256", "host date request-line", sha);
        
        // 生成URL
        String authUrl = "wss://" + host + path + "?" +
                "authorization=" + URLEncoder.encode(Base64.getEncoder().encodeToString(authorization.getBytes(StandardCharsets.UTF_8)), "UTF-8") +
                "&date=" + URLEncoder.encode(date, "UTF-8") +
                "&host=" + URLEncoder.encode(host, "UTF-8");
        
        return authUrl;
    }
    
    /**
     * 构建评测参数
     */
    private JSONObject buildParams(IseRequest request) {
        JSONObject params = new JSONObject();
        
        // 通用参数
        JSONObject common = new JSONObject();
        common.put("app_id", xunfeiConfig.getAppId());
        params.put("common", common);
        
        // 业务参数
        JSONObject business = new JSONObject();
        business.put("category", request.getCategory());
        business.put("group", request.getGroup());
        business.put("language", request.getLanguage());
        business.put("check_type", request.getCheckType());
        business.put("result_encoding", request.getResultEncoding());
        business.put("extra_ability", request.getExtraAbility());
        business.put("text", Base64.getEncoder().encodeToString(request.getText().getBytes(StandardCharsets.UTF_8)));
        params.put("business", business);
        
        // 数据参数
        JSONObject data = new JSONObject();
        data.put("data_type", 1); // 1-音频数据
        data.put("encoding", "raw");
        data.put("audio", request.getAudioData());
        params.put("data", data);
        
        return params;
    }
    
    /**
     * 发送评测请求(模拟实现)
     * 实际项目中应该使用WebSocket客户端
     */
    private IseResponse sendEvaluateRequest(String authUrl, JSONObject params, String audioData) {
        // 这里模拟科大讯飞的评测结果
        // 实际项目中需要使用WebSocket客户端连接科大讯飞服务器
        
        IseResponse response = new IseResponse();
        response.setCode(0);
        response.setMessage("success");
        response.setSid("ise" + System.currentTimeMillis());
        
        IseResponse.IseResultData data = new IseResponse.IseResultData();
        data.setStatus(2);
        
        // 模拟评测结果XML(Base64编码)
        String mockXml = generateMockResultXml();
        data.setData(Base64.getEncoder().encodeToString(mockXml.getBytes(StandardCharsets.UTF_8)));
        
        response.setData(data);
        
        return response;
    }
    
    /**
     * 生成模拟的评测结果XML
     */
    private String generateMockResultXml() {
        // 生成随机分数
        Random random = new Random();
        int totalScore = 75 + random.nextInt(20); // 75-94分
        int pronunciationScore = totalScore + random.nextInt(6) - 3;
        int fluencyScore = totalScore + random.nextInt(6) - 3;
        int toneScore = totalScore + random.nextInt(6) - 3;
        
        return String.format(
            "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
            "<FinalResult>" +
            "<ret value=\"0\"/>" +
            "<total_score>%.1f</total_score>" +
            "<phone_score>%.1f</phone_score>" +
            "<fluency_score>%.1f</fluency_score>" +
            "<tone_score>%.1f</tone_score>" +
            "<accuracy_score>%.1f</accuracy_score>" +
            "<integrity_score>%.1f</integrity_score>" +
            "<standard_score>%.1f</standard_score>" +
            "<is_rejected>false</is_rejected>" +
            "<except_info></except_info>" +
            "</FinalResult>",
            (double) totalScore,
            (double) pronunciationScore,
            (double) fluencyScore,
            (double) toneScore,
            (double) totalScore,
            (double) totalScore,
            (double) totalScore
        );
    }
    
    /**
     * 解析评测结果
     */
    private IseResult parseResult(IseResponse response) {
        IseResult result = new IseResult();
        
        if (response.getCode() != 0) {
            throw new RuntimeException("评测失败: " + response.getMessage());
        }
        
        try {
            // 解码Base64数据
            String xmlData = new String(Base64.getDecoder().decode(response.getData().getData()), StandardCharsets.UTF_8);
            result.setDetailData(xmlData);
            
            // 解析XML数据(简化实现)
            result.setTotalScore(extractScoreFromXml(xmlData, "total_score"));
            result.setPronunciationScore(extractScoreFromXml(xmlData, "phone_score"));
            result.setFluencyScore(extractScoreFromXml(xmlData, "fluency_score"));
            result.setToneScore(extractScoreFromXml(xmlData, "tone_score"));
            result.setAccuracyScore(extractScoreFromXml(xmlData, "accuracy_score"));
            result.setIntegrityScore(extractScoreFromXml(xmlData, "integrity_score"));
            result.setStandardScore(extractScoreFromXml(xmlData, "standard_score"));
            
            // 设置评测等级
            result.setGrade(calculateGrade(result.getTotalScore()));
            
            // 生成反馈建议
            result.setFeedback(generateFeedback(result));
            result.setSuggestion(generateSuggestion(result));
            
        } catch (Exception e) {
            log.error("解析评测结果失败", e);
            throw new RuntimeException("解析评测结果失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 从XML中提取分数
     */
    private Double extractScoreFromXml(String xml, String tagName) {
        try {
            String pattern = "<" + tagName + ">(.*?)</" + tagName + ">";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(xml);
            if (m.find()) {
                return Double.parseDouble(m.group(1));
            }
        } catch (Exception e) {
            log.warn("提取分数失败: " + tagName, e);
        }
        return 0.0;
    }
    
    /**
     * 计算评测等级
     */
    private String calculateGrade(Double score) {
        if (score >= 90) return "A";
        if (score >= 80) return "B";
        if (score >= 70) return "C";
        if (score >= 60) return "D";
        return "F";
    }
    
    /**
     * 生成反馈建议
     */
    private String generateFeedback(IseResult result) {
        StringBuilder feedback = new StringBuilder();
        
        if (result.getTotalScore() >= 90) {
            feedback.append("发音非常标准，语调自然流畅！");
        } else if (result.getTotalScore() >= 80) {
            feedback.append("发音较为标准，整体表现良好。");
        } else if (result.getTotalScore() >= 70) {
            feedback.append("发音基本准确，还有提升空间。");
        } else {
            feedback.append("发音需要加强练习。");
        }
        
        // 针对性建议
        if (result.getPronunciationScore() < 80) {
            feedback.append("建议多练习发音准确性。");
        }
        if (result.getFluencyScore() < 80) {
            feedback.append("建议提高语音流利度。");
        }
        if (result.getToneScore() < 80) {
            feedback.append("建议注意语调的起伏变化。");
        }
        
        return feedback.toString();
    }
    
    /**
     * 生成学习建议
     */
    private String generateSuggestion(IseResult result) {
        if (result.getTotalScore() >= 90) {
            return "继续保持，可以尝试更有挑战性的内容。";
        } else if (result.getTotalScore() >= 80) {
            return "表现不错，继续练习可以达到更高水平。";
        } else if (result.getTotalScore() >= 70) {
            return "建议多听多练，注意模仿标准发音。";
        } else {
            return "建议从基础发音开始，循序渐进地练习。";
        }
    }
}
