package com.learning.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: kuma_lesson
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Data
@TableName("kuma_lesson")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="kuma_lesson对象", description="kuma_lesson")
public class KumaLesson implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**系统标识*/
	@Excel(name = "系统标识", width = 15)
    @ApiModelProperty(value = "系统标识")
    private java.lang.String sysSign;
	/**所属课程ID*/
	@Excel(name = "所属课程ID", width = 15)
    @ApiModelProperty(value = "所属课程ID")
    private java.lang.String courseId;
	/**课时标题*/
	@Excel(name = "课时标题", width = 15)
    @ApiModelProperty(value = "课时标题")
    private java.lang.String title;
	/**课时描述*/
	@Excel(name = "课时描述", width = 15)
    @ApiModelProperty(value = "课时描述")
    private java.lang.String description;
	/**视频URL*/
	@Excel(name = "视频URL", width = 15)
    @ApiModelProperty(value = "视频URL")
    private java.lang.String videoUrl;
	/**课时时长(分钟)*/
	@Excel(name = "课时时长(分钟)", width = 15)
    @ApiModelProperty(value = "课时时长(分钟)")
    private java.lang.Integer duration;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private java.lang.Integer sort;
	/**状态：0-禁用 1-启用*/
	@Excel(name = "状态：0-禁用 1-启用", width = 15)
    @ApiModelProperty(value = "状态：0-禁用 1-启用")
    private java.lang.Integer status;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**删除标志（0-未删除，1-已删除）*/
	@Excel(name = "删除标志（0-未删除，1-已删除）", width = 15)
    @ApiModelProperty(value = "删除标志（0-未删除，1-已删除）")
    @TableLogic
    private java.lang.Integer delFlag;
}
