<script>
export default {
	// 定义全局数据
	globalData: {
		systemInfo: null,
		statusBarHeight: 0,
		navBarHeight: 44,
		windowHeight: 0,
		windowWidth: 0,
		userInfo: null,
		token: null,
		version: '1.0.0',
		isLogin: false
	},

	onLaunch: function(options) {
		console.log('🚀 App Launch - 口语便利店启动')
		console.log('启动参数:', options)

		// 记录启动场景
		this.globalData.scene = options.scene || 0
		this.globalData.path = options.path || ''
		this.globalData.query = options.query || {}

		this.initApp()
	},

	onShow: function(options) {
		console.log('👁️ App Show')
		console.log('显示参数:', options)

		// 检查网络状态
		this.checkNetworkStatus()

		// 更新场景值
		if (options && options.scene) {
			this.globalData.scene = options.scene
		}
	},

	onHide: function() {
		console.log('🙈 App Hide')
	},

	onError: function(err) {
		console.error('💥 App Error:', err)
		this.addError({
			type: 'app_error',
			message: err,
			timestamp: Date.now()
		})
	},

	methods: {
		initApp() {
			try {
				console.log('🔧 开始初始化应用...')

				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync()
				console.log('📱 系统信息:', {
					platform: systemInfo.platform,
					version: systemInfo.version,
					model: systemInfo.model,
					pixelRatio: systemInfo.pixelRatio
				})

				// 设置全局数据
				this.globalData.systemInfo = systemInfo
				this.globalData.statusBarHeight = systemInfo.statusBarHeight || 0
				this.globalData.navBarHeight = (systemInfo.statusBarHeight || 0) + 44
				this.globalData.windowHeight = systemInfo.windowHeight || 0
				this.globalData.windowWidth = systemInfo.windowWidth || 0
				this.globalData.platform = systemInfo.platform

				// 检查登录状态
				this.checkLoginStatus()

				// 初始化网络监听
				this.initNetworkListener()

				// 检查更新（仅小程序）
				this.checkForUpdates()

				console.log('✅ 应用初始化完成')

			} catch (error) {
				console.error('❌ 应用初始化失败:', error)
				this.addError({
					type: 'init_error',
					message: error.message,
					timestamp: Date.now()
				})
			}
		},

		checkLoginStatus() {
			try {
				const token = uni.getStorageSync('token')
				const userInfo = uni.getStorageSync('userInfo')

				if (token) {
					this.globalData.token = token
					this.globalData.isLogin = true
					console.log('发现本地token')
				}

				if (userInfo) {
					this.globalData.userInfo = userInfo
					console.log('发现本地用户信息')
				}
			} catch (error) {
				console.error('检查登录状态失败:', error)
			}
		},

		// 全局方法：设置用户信息
		setUserInfo(userInfo) {
			this.globalData.userInfo = userInfo
			this.globalData.isLogin = !!userInfo
			if (userInfo) {
				uni.setStorageSync('userInfo', userInfo)
			} else {
				uni.removeStorageSync('userInfo')
			}
		},

		// 全局方法：设置token
		setToken(token) {
			this.globalData.token = token
			this.globalData.isLogin = !!token
			if (token) {
				uni.setStorageSync('token', token)
			} else {
				uni.removeStorageSync('token')
			}
		},

		// 全局方法：清除登录信息
		clearLoginInfo() {
			this.globalData.userInfo = null
			this.globalData.token = null
			this.globalData.isLogin = false
			uni.removeStorageSync('userInfo')
			uni.removeStorageSync('token')
		},

		// 初始化网络监听
		initNetworkListener() {
			// 监听网络状态变化
			uni.onNetworkStatusChange((res) => {
				console.log('🌐 网络状态变化:', res)

				if (!res.isConnected) {
					uni.showToast({
						title: '网络连接已断开',
						icon: 'none'
					})
				} else {
					console.log('✅ 网络已连接:', res.networkType)
				}
			})
		},

		// 检查网络状态
		checkNetworkStatus() {
			uni.getNetworkType({
				success: (res) => {
					console.log('🌐 当前网络类型:', res.networkType)
				},
				fail: (err) => {
					console.error('❌ 获取网络状态失败:', err)
				}
			})
		},

		// 检查小程序更新
		checkForUpdates() {
			// #ifdef MP-WEIXIN || MP-DINGTALK
			if (uni.getUpdateManager) {
				const updateManager = uni.getUpdateManager()

				updateManager.onCheckForUpdate((res) => {
					console.log('🔄 检查更新结果:', res.hasUpdate)
				})

				updateManager.onUpdateReady(() => {
					uni.showModal({
						title: '更新提示',
						content: '新版本已经准备好，是否重启应用？',
						success: (res) => {
							if (res.confirm) {
								updateManager.applyUpdate()
							}
						}
					})
				})

				updateManager.onUpdateFailed(() => {
					console.error('❌ 新版本下载失败')
				})
			}
			// #endif
		},

		// 添加错误记录
		addError(error) {
			if (!this.globalData.errors) {
				this.globalData.errors = []
			}

			this.globalData.errors.unshift({
				...error,
				id: Date.now()
			})

			// 只保留最近50条错误
			if (this.globalData.errors.length > 50) {
				this.globalData.errors = this.globalData.errors.slice(0, 50)
			}
		},

		// 清除错误记录
		clearErrors() {
			this.globalData.errors = []
		},

		// 获取场景值描述
		getSceneDesc(scene) {
			const sceneMap = {
				1001: '发现栏小程序主入口',
				1005: '顶部搜索框的搜索结果页',
				1006: '发现栏小程序主入口搜索框的搜索结果页',
				1007: '单人聊天会话中的小程序消息卡片',
				1008: '群聊会话中的小程序消息卡片',
				1011: '扫描二维码',
				1012: '长按图片识别二维码',
				1013: '手机相册选取二维码',
				1014: '小程序模版消息',
				1017: '前往体验版的入口页',
				1019: '微信钱包',
				1020: '公众号 profile 页相关小程序列表',
				1022: '聊天顶部置顶小程序入口',
				1023: '安卓系统桌面图标',
				1024: '小程序 profile 页',
				1025: '扫描一维码',
				1026: '附近小程序列表',
				1027: '顶部搜索框搜索结果页"使用过的小程序"列表',
				1028: '我的卡包',
				1029: '卡券详情页',
				1030: '自动化测试下打开小程序',
				1031: '长按图片识别一维码',
				1032: '手机相册选取一维码',
				1034: '微信支付完成页',
				1035: '公众号自定义菜单',
				1036: 'App 分享消息卡片',
				1037: '小程序打开小程序',
				1038: '从另一个小程序返回',
				1039: '摇电视',
				1042: '添加好友搜索框的搜索结果页',
				1043: '公众号模板消息',
				1044: '带 shareTicket 的小程序消息卡片',
				1045: '朋友圈广告',
				1046: '朋友圈广告详情页',
				1047: '扫描小程序码',
				1048: '长按图片识别小程序码',
				1049: '手机相册选取小程序码',
				1052: '卡券的适用门店列表',
				1053: '搜一搜的结果页',
				1054: '顶部搜索框小程序快捷入口',
				1056: '音乐播放器菜单',
				1057: '钱包中的银行卡详情页',
				1058: '公众号文章',
				1059: '体验版小程序绑定邀请页',
				1064: '微信连Wi-Fi状态栏',
				1067: '公众号文章广告',
				1068: '附近小程序列表广告',
				1069: '移动应用',
				1071: '钱包中的银行卡详情页',
				1072: '二维码收款页面',
				1073: '客服消息列表下拉刷新',
				1074: '公众号会话下拉刷新',
				1077: '摇周边',
				1078: '连Wi-Fi成功页',
				1079: '微信游戏中心',
				1081: '客服会话菜单小程序入口',
				1082: '从微信聊天主界面下拉，搜索框内搜索小程序',
				1084: '公众号阅读完毕页',
				1089: '微信聊天主界面下拉，"最近使用"栏（聊天主界面下拉）',
				1090: '长按小程序右上角菜单唤出最近使用历史',
				1091: '公众号文章商品广告',
				1092: '城市服务入口',
				1095: '小程序广告组件',
				1096: '聊天记录',
				1097: '微信支付签约页',
				1099: '页面内嵌插件',
				1102: '公众号 profile 页服务预览',
				1103: '发现栏小游戏',
				1104: '微信聊天主界面下拉，"我的小程序"栏',
				1106: '小程序 profile 页',
				1107: '系统桌面图标',
				1108: '小程序 profile 页',
				1109: '微信聊天主界面下拉，搜索框内搜索小程序',
				1110: '小程序 profile 页',
				1111: '扫描二维码',
				1112: '长按图片识别二维码',
				1113: '手机相册选取二维码',
				1114: '小程序模版消息',
				1117: '前往体验版的入口页'
			}

			return sceneMap[scene] || `未知场景(${scene})`
		}
	}
}
</script>

<style lang="scss">
/*每个页面公共css */
@import 'uni.scss';

/* 全局样式重置 - 小程序兼容 */
view, text, button, input, textarea, image, scroll-view {
	box-sizing: border-box;
}

page {
	background-color: #f6f6f6;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
	line-height: 1.6;
}

/* 通用类 */
.container {
	padding: 0 32rpx;
}

.flex {
	display: flex;
}

.flex-column {
	display: flex;
	flex-direction: column;
}

.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}

.flex-between {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.flex-around {
	display: flex;
	align-items: center;
	justify-content: space-around;
}

.text-center {
	text-align: center;
}

.text-left {
	text-align: left;
}

.text-right {
	text-align: right;
}

/* 文字大小 */
.text-xs {
	font-size: 24rpx;
}

.text-sm {
	font-size: 28rpx;
}

.text-base {
	font-size: 32rpx;
}

.text-lg {
	font-size: 36rpx;
}

.text-xl {
	font-size: 40rpx;
}

.text-2xl {
	font-size: 48rpx;
}

/* 文字颜色 */
.text-primary {
	color: $uni-color-primary;
}

.text-white {
	color: #ffffff;
}

.text-gray-600 {
	color: #666666;
}

.text-gray-800 {
	color: #333333;
}

.text-green-500 {
	color: #10b981;
}

/* 背景颜色 */
.bg-white {
	background-color: #ffffff;
}

.bg-primary {
	background: $primary-gradient;
}

/* 边距 */
.m-0 { margin: 0; }
.mt-1 { margin-top: 8rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-3 { margin-top: 24rpx; }
.mt-4 { margin-top: 32rpx; }
.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-3 { margin-bottom: 24rpx; }
.mb-4 { margin-bottom: 32rpx; }

.p-0 { padding: 0; }
.pt-1 { padding-top: 8rpx; }
.pt-2 { padding-top: 16rpx; }
.pt-3 { padding-top: 24rpx; }
.pt-4 { padding-top: 32rpx; }
.pb-1 { padding-bottom: 8rpx; }
.pb-2 { padding-bottom: 16rpx; }
.pb-3 { padding-bottom: 24rpx; }
.pb-4 { padding-bottom: 32rpx; }

/* 圆角 */
.rounded {
	border-radius: 8rpx;
}

.rounded-lg {
	border-radius: 16rpx;
}

.rounded-xl {
	border-radius: 24rpx;
}

.rounded-full {
	border-radius: 50%;
}

/* 阴影 */
.shadow {
	box-shadow: 0 8rpx 64rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
	box-shadow: 0 16rpx 96rpx rgba(0, 0, 0, 0.15);
}

/* 卡片样式 */
.card {
	background: rgba(255, 255, 255, 0.95);
	/* backdrop-filter: blur(20rpx); 小程序不支持 */
	border-radius: $card-border-radius;
	box-shadow: $card-shadow;
	padding: 48rpx;
}

/* 按钮样式 */
.btn {
	padding: 24rpx 48rpx;
	border-radius: $button-border-radius;
	font-weight: bold;
	text-align: center;
	transition: all 0.3s ease;
}

.btn-primary {
	background: $primary-gradient;
	color: white;
	box-shadow: $button-shadow;
}

/* 渐变背景 */
.gradient-primary {
	background: $primary-gradient;
}

.gradient-secondary {
	background: $secondary-gradient;
}

.gradient-success {
	background: $success-gradient;
}

.gradient-warning {
	background: $warning-gradient;
}

.gradient-orange {
	background: $orange-gradient;
}
</style>
