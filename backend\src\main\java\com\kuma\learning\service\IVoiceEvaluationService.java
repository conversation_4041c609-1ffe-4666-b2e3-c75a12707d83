package com.kuma.learning.service;

import com.kuma.learning.dto.IseRequest;
import com.kuma.learning.dto.IseResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * 语音评测服务接口
 * <AUTHOR>
 * @since 2024-03-22
 */
public interface IVoiceEvaluationService {
    
    /**
     * 语音评测
     * @param audioFile 音频文件
     * @param text 待评测文本
     * @param language 语言类型
     * @param category 题型
     * @return 评测结果
     */
    IseResult evaluate(MultipartFile audioFile, String text, String language, String category);
    
    /**
     * 语音评测(Base64音频数据)
     * @param request 评测请求
     * @return 评测结果
     */
    IseResult evaluate(IseRequest request);
    
    /**
     * 获取支持的语言类型
     * @return 语言类型列表
     */
    String[] getSupportedLanguages();
    
    /**
     * 获取支持的题型
     * @param language 语言类型
     * @return 题型列表
     */
    String[] getSupportedCategories(String language);
}
