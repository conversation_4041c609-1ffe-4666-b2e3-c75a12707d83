<template>
	<view class="page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 自定义导航栏 -->
		<view class="nav-bar">
			<view class="nav-content">
				<view class="nav-left" @click="goBack">
					<text class="iconfont icon-arrow-left"></text>
				</view>
				<view class="nav-title">
					<text class="title">跟读练习</text>
					<text class="subtitle">第{{ currentSentence }}/{{ totalSentences }}句</text>
				</view>
				<view class="nav-right" @click="exitPractice">
					<text class="iconfont icon-close"></text>
				</view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="content">
			<!-- 进度圆环 -->
			<view class="progress-section">
				<view class="progress-circle">
					<text class="progress-number">{{ currentSentence }}</text>
					<text class="progress-total">/ {{ totalSentences }}</text>
				</view>
			</view>

			<!-- 当前句子 -->
			<view class="sentence-card card">
				<view class="sentence-header">
					<text class="instruction">请跟读以下句子</text>
				</view>
				<view class="sentence-content">
					<text class="japanese-text">{{ currentSentenceData.japanese }}</text>
					<text class="chinese-text">{{ currentSentenceData.chinese }}</text>
				</view>
				
				<!-- 原音播放控制 -->
				<view class="audio-controls">
					<view class="control-btn" @click="playOriginal">
						<text class="iconfont icon-play"></text>
					</view>
					<view class="control-btn" @click="repeatOriginal">
						<text class="iconfont icon-repeat"></text>
					</view>
					<view class="control-btn" @click="adjustVolume">
						<text class="iconfont icon-volume"></text>
					</view>
				</view>
			</view>

			<!-- 录音区域 -->
			<view class="recording-section card">
				<text class="recording-instruction">{{ recordingInstruction }}</text>
				<view class="record-button" :class="{ recording: isRecording, completed: hasRecorded }" @click="toggleRecording">
					<text class="iconfont" :class="getRecordIcon()"></text>
				</view>
				<text class="record-hint">{{ recordingHint }}</text>
			</view>

			<!-- AI评分结果 -->
			<view v-if="showScore" class="score-section card">
				<view class="score-header">
					<text class="iconfont icon-robot"></text>
					<text class="score-title">AI评分结果</text>
				</view>
				
				<view class="total-score">{{ totalScore }}分</view>
				
				<view class="score-details">
					<view class="score-item">
						<text class="score-label">发音准确度</text>
						<view class="score-bar-container">
							<view class="score-bar">
								<view class="score-fill" :style="{ width: pronunciationScore + '%' }"></view>
							</view>
							<text class="score-value">{{ pronunciationScore }}%</text>
						</view>
					</view>
					
					<view class="score-item">
						<text class="score-label">流利度</text>
						<view class="score-bar-container">
							<view class="score-bar">
								<view class="score-fill" :style="{ width: fluencyScore + '%' }"></view>
							</view>
							<text class="score-value">{{ fluencyScore }}%</text>
						</view>
					</view>
					
					<view class="score-item">
						<text class="score-label">语调</text>
						<view class="score-bar-container">
							<view class="score-bar">
								<view class="score-fill" :style="{ width: toneScore + '%' }"></view>
							</view>
							<text class="score-value">{{ toneScore }}%</text>
						</view>
					</view>
				</view>
				
				<view v-if="aiFeedback" class="ai-feedback">
					<text class="iconfont icon-lightbulb"></text>
					<text class="feedback-text">{{ aiFeedback }}</text>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view v-if="showScore" class="action-buttons">
				<button class="btn-secondary" @click="retryRecording">
					<text class="iconfont icon-redo"></text>
					重新录音
				</button>
				<button class="btn-primary" @click="nextSentence">
					<text class="iconfont icon-arrow-right"></text>
					下一句
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import { practiceApi, courseApi } from '@/api/index.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			courseId: '',
			courseTitle: '',
			loading: true,
			currentSentence: 1,
			totalSentences: 0,
			isRecording: false,
			hasRecorded: false,
			showScore: false,
			recordingInstruction: '点击按钮开始录音',
			recordingHint: '长按录音，松开结束',
			// 录音相关
			recorderManager: null,
			audioFilePath: '',
			// 当前句子数据
			currentSentenceData: {
				japanese: 'ラーメンとギョーザをお願いします',
				chinese: '请给我拉面和饺子'
			},
			// AI评分结果
			totalScore: 85,
			pronunciationScore: 90,
			fluencyScore: 80,
			toneScore: 85,
			aiFeedback: '建议：「ギョーザ」的发音可以更清晰一些',
			// 所有句子数据
			sentences: [
				{
					japanese: 'いらっしゃいませ。何名様ですか？',
					chinese: '欢迎光临。请问几位？'
				},
				{
					japanese: '二人です。',
					chinese: '两个人。'
				},
				{
					japanese: 'ラーメンとギョーザをお願いします',
					chinese: '请给我拉面和饺子'
				}
			]
		}
	},
	
	onLoad(options) {
		this.courseId = options.courseId
		this.courseTitle = decodeURIComponent(options.title || '跟读练习')
		this.initPage()
	},

	onUnload() {
		// 清理录音管理器
		if (this.recorderManager) {
			this.recorderManager.stop()
		}
	},

	methods: {
		initPage() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight
			this.initRecorder()
			this.loadPracticeData()
		},

		// 初始化录音管理器
		initRecorder() {
			this.recorderManager = uni.getRecorderManager()

			this.recorderManager.onStart(() => {
				console.log('录音开始')
			})

			this.recorderManager.onStop((res) => {
				console.log('录音结束', res)
				this.audioFilePath = res.tempFilePath
				this.submitRecording(res.tempFilePath)
			})

			this.recorderManager.onError((err) => {
				console.error('录音错误', err)
				uni.showToast({
					title: '录音失败，请重试',
					icon: 'none'
				})
				this.isRecording = false
			})
		},

		// 加载练习数据
		async loadPracticeData() {
			if (!this.courseId) {
				uni.showToast({
					title: '课程ID不能为空',
					icon: 'none'
				})
				return
			}

			this.loading = true

			try {
				console.log('加载练习数据:', this.courseId)

				// 获取课程详情和练习句子
				const response = await courseApi.getCourseDetail(this.courseId)

				if (response.success && response.data) {
					this.updatePracticeData(response.data)
				} else {
					throw new Error(response.message || '获取练习数据失败')
				}

			} catch (error) {
				console.error('加载练习数据失败:', error)

				uni.showToast({
					title: '练习数据加载失败，请重试',
					icon: 'none',
					duration: 2000
				})
			} finally {
				this.loading = false
			}
		},

		// 更新练习数据
		updatePracticeData(courseData) {
			if (courseData.dialogues && courseData.dialogues.length > 0) {
				this.sentences = courseData.dialogues.map(dialogue => ({
					japanese: dialogue.text,
					chinese: dialogue.translation
				}))
				this.totalSentences = this.sentences.length
				this.currentSentenceData = this.sentences[0]
			} else {
				console.log('⚠️ 暂无练习数据')
			}
		},


		
		goBack() {
			uni.navigateBack()
		},
		
		exitPractice() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出练习吗？当前进度将不会保存。',
				success: (res) => {
					if (res.confirm) {
						uni.navigateBack()
					}
				}
			})
		},
		
		playOriginal() {
			// 播放原音
			console.log('播放原音')
			uni.showToast({
				title: '播放原音',
				icon: 'none'
			})
		},
		
		repeatOriginal() {
			// 重复播放
			console.log('重复播放')
		},
		
		adjustVolume() {
			// 调节音量
			console.log('调节音量')
		},
		
		toggleRecording() {
			if (this.isRecording) {
				this.stopRecording()
			} else {
				this.startRecording()
			}
		},
		
		async startRecording() {
			try {
				// 检查录音权限
				await this.checkRecordPermission()

				this.isRecording = true
				this.recordingInstruction = '正在录音...'
				this.recordingHint = '再次点击结束录音'

				// 开始录音
				this.recorderManager.start({
					duration: 60000, // 最长60秒
					sampleRate: 16000,
					numberOfChannels: 1,
					encodeBitRate: 96000,
					format: 'mp3'
				})

			} catch (error) {
				console.error('开始录音失败:', error)
				uni.showToast({
					title: '录音权限被拒绝',
					icon: 'none'
				})
				this.isRecording = false
			}
		},

		stopRecording() {
			if (!this.isRecording) return

			this.isRecording = false
			this.hasRecorded = true
			this.recordingInstruction = '录音完成'
			this.recordingHint = '正在分析中...'

			// 停止录音
			this.recorderManager.stop()
		},

		// 检查录音权限
		async checkRecordPermission() {
			return new Promise((resolve, reject) => {
				uni.getSetting({
					success: (res) => {
						if (res.authSetting['scope.record']) {
							resolve(true)
						} else {
							uni.authorize({
								scope: 'scope.record',
								success: () => resolve(true),
								fail: () => reject(new Error('录音权限被拒绝'))
							})
						}
					},
					fail: () => reject(new Error('获取权限设置失败'))
				})
			})
		},

		// 提交录音进行评分
		async submitRecording(audioFilePath) {
			try {
				uni.showLoading({ title: '正在评分...' })

				// 准备评分数据
				const practiceData = {
					courseId: this.courseId,
					sentenceIndex: this.currentSentence - 1,
					targetText: this.currentSentenceData.japanese,
					audioFile: audioFilePath,
					language: 'ja_JP' // 日语
				}

				console.log('提交录音评分:', practiceData)

				// 调用AI评分接口
				const response = await practiceApi.submitPractice(practiceData)

				if (response.success && response.data) {
					this.updateScoreResult(response.data)
				} else {
					throw new Error(response.message || '评分失败')
				}

			} catch (error) {
				console.error('提交录音失败:', error)

				uni.showToast({
					title: '评分失败，请重试',
					icon: 'none',
					duration: 2000
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 更新评分结果
		updateScoreResult(scoreData) {
			this.totalScore = scoreData.totalScore || 85
			this.pronunciationScore = scoreData.pronunciationScore || 90
			this.fluencyScore = scoreData.fluencyScore || 80
			this.toneScore = scoreData.toneScore || 85
			this.aiFeedback = scoreData.feedback || '发音不错，继续加油！'

			this.showScore = true
			this.recordingHint = '评分完成'
		},


		
		showAIScore() {
			this.showScore = true
			this.recordingHint = '评分完成'
		},
		
		retryRecording() {
			this.isRecording = false
			this.hasRecorded = false
			this.showScore = false
			this.recordingInstruction = '点击按钮开始录音'
			this.recordingHint = '长按录音，松开结束'
		},
		
		nextSentence() {
			if (this.currentSentence < this.totalSentences) {
				this.currentSentence++
				this.retryRecording()
				// 更新当前句子数据
				if (this.sentences[this.currentSentence - 1]) {
					this.currentSentenceData = this.sentences[this.currentSentence - 1]
				}
			} else {
				this.completePractice()
			}
		},
		
		completePractice() {
			uni.showModal({
				title: '练习完成',
				content: '恭喜完成本课练习！',
				showCancel: false,
				success: () => {
					uni.navigateBack()
				}
			})
		},
		
		getRecordIcon() {
			if (this.hasRecorded) {
				return 'icon-check'
			} else if (this.isRecording) {
				return 'icon-stop'
			} else {
				return 'icon-microphone'
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.status-bar {
	background: transparent;
}

.nav-bar {
	background: transparent;
	
	.nav-content {
		height: 120rpx;
		display: flex;
		align-items: center;
		padding: 0 32rpx;
		
		.nav-left, .nav-right {
			width: 88rpx;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.nav-left, .nav-right {
			.iconfont {
				color: white;
				font-size: 40rpx;
			}
		}
		
		.nav-title {
			flex: 1;
			text-align: center;
			
			.title {
				display: block;
				color: white;
				font-size: 36rpx;
				font-weight: bold;
				line-height: 1.2;
			}
			
			.subtitle {
				display: block;
				color: rgba(255, 255, 255, 0.8);
				font-size: 28rpx;
			}
		}
	}
}

.content {
	padding: 40rpx;
}

.progress-section {
	text-align: center;
	margin-bottom: 60rpx;
	
	.progress-circle {
		width: 240rpx;
		height: 240rpx;
		border-radius: 50%;
		background: conic-gradient(#10b981 0deg 108deg, rgba(255,255,255,0.3) 108deg 360deg);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin: 0 auto;
		position: relative;
		
		&::before {
			content: '';
			position: absolute;
			width: 180rpx;
			height: 180rpx;
			background: rgba(255,255,255,0.9);
			border-radius: 50%;
		}
		
		.progress-number {
			position: relative;
			z-index: 1;
			font-size: 48rpx;
			font-weight: bold;
			color: #10b981;
			line-height: 1;
		}
		
		.progress-total {
			position: relative;
			z-index: 1;
			font-size: 28rpx;
			color: #666;
		}
	}
}

.sentence-card {
	text-align: center;
	margin-bottom: 40rpx;
	
	.instruction {
		display: block;
		color: #666;
		font-size: 28rpx;
		margin-bottom: 32rpx;
	}
	
	.sentence-content {
		margin-bottom: 48rpx;
		
		.japanese-text {
			display: block;
			font-size: 48rpx;
			color: #333;
			line-height: 1.4;
			margin-bottom: 24rpx;
		}
		
		.chinese-text {
			display: block;
			font-size: 32rpx;
			color: #666;
		}
	}
	
	.audio-controls {
		display: flex;
		justify-content: center;
		gap: 40rpx;
		
		.control-btn {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			background: rgba(255,255,255,0.9);
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
			
			.iconfont {
				color: #666;
				font-size: 36rpx;
			}
		}
	}
}

.recording-section {
	text-align: center;
	margin-bottom: 40rpx;
	
	.recording-instruction {
		display: block;
		color: #666;
		font-size: 28rpx;
		margin-bottom: 32rpx;
	}
	
	.record-button {
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, #ff6b6b, #ee5a24);
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 32rpx;
		box-shadow: 0 16rpx 64rpx rgba(255, 107, 107, 0.4);
		transition: all 0.3s ease;
		
		&.recording {
			background: linear-gradient(135deg, #ff4757, #c44569);
			animation: pulse 1.5s infinite;
		}
		
		&.completed {
			background: linear-gradient(135deg, #2ed573, #1e90ff);
		}
		
		.iconfont {
			color: white;
			font-size: 64rpx;
		}
	}
	
	.record-hint {
		color: #666;
		font-size: 24rpx;
	}
}

@keyframes pulse {
	0% { transform: scale(1); }
	50% { transform: scale(1.1); }
	100% { transform: scale(1); }
}

.score-section {
	margin-bottom: 40rpx;
	
	.score-header {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 32rpx;
		
		.iconfont {
			color: #f59e0b;
			font-size: 40rpx;
			margin-right: 16rpx;
		}
		
		.score-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.total-score {
		text-align: center;
		font-size: 72rpx;
		font-weight: bold;
		color: #f59e0b;
		margin-bottom: 48rpx;
	}
	
	.score-details {
		.score-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 24rpx;
			
			.score-label {
				color: #333;
				font-size: 28rpx;
			}
			
			.score-bar-container {
				display: flex;
				align-items: center;
				
				.score-bar {
					width: 200rpx;
					height: 16rpx;
					background: rgba(255,255,255,0.3);
					border-radius: 8rpx;
					overflow: hidden;
					margin-right: 16rpx;
					
					.score-fill {
						height: 100%;
						background: linear-gradient(90deg, #00b894, #00cec9);
						border-radius: 8rpx;
						transition: width 0.5s ease;
					}
				}
				
				.score-value {
					font-size: 24rpx;
					font-weight: 500;
					color: #333;
				}
			}
		}
	}
	
	.ai-feedback {
		display: flex;
		align-items: flex-start;
		padding: 24rpx;
		background: rgba(255,255,255,0.5);
		border-radius: 16rpx;
		margin-top: 32rpx;
		
		.iconfont {
			color: #f59e0b;
			font-size: 32rpx;
			margin-right: 16rpx;
			margin-top: 4rpx;
		}
		
		.feedback-text {
			flex: 1;
			color: #333;
			font-size: 28rpx;
			line-height: 1.5;
		}
	}
}

.action-buttons {
	display: flex;
	gap: 24rpx;
	
	.btn-secondary, .btn-primary {
		flex: 1;
		padding: 24rpx;
		border-radius: 32rpx;
		font-weight: bold;
		font-size: 28rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		
		.iconfont {
			margin-right: 16rpx;
			font-size: 32rpx;
		}
	}
	
	.btn-secondary {
		background: rgba(255,255,255,0.9);
		color: #333;
	}
	
	.btn-primary {
		background: linear-gradient(135deg, #16a34a, #22c55e);
		color: white;
		box-shadow: 0 16rpx 64rpx rgba(34, 197, 94, 0.3);
	}
}

/* 字体图标 */
.icon-close::before { content: '\e018'; }
.icon-volume::before { content: '\e019'; }
.icon-robot::before { content: '\e020'; }
.icon-check::before { content: '\e021'; }
.icon-stop::before { content: '\e022'; }
.icon-redo::before { content: '\e023'; }
</style>
