"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const api_jeecgUser = require("../../api/jeecg-user.js");
const mixins_authMixin = require("../../mixins/auth-mixin.js");
const _sfc_main = {
  mixins: [mixins_authMixin.authMixin],
  data() {
    return {
      statusBarHeight: 0,
      loading: true,
      userInfo: {
        name: "加载中...",
        avatar: "/static/images/default-avatar.png",
        studyDays: 0,
        level: "新手学员",
        isVip: false,
        nickname: "",
        username: ""
      },
      statistics: [
        {
          key: "studyDays",
          value: "0",
          label: "学习天数"
        },
        {
          key: "continuousDays",
          value: "0",
          label: "连续打卡"
        },
        {
          key: "averageScore",
          value: "0",
          label: "平均分数"
        }
      ],
      studyMenus: [
        {
          key: "report",
          label: "学习报告",
          description: "查看详细学习数据",
          icon: "icon-chart",
          iconBg: "linear-gradient(135deg, #667eea, #764ba2)",
          iconColor: "white"
        },
        {
          key: "favorites",
          label: "我的收藏",
          description: "收藏的课程和词汇",
          icon: "icon-bookmark",
          iconBg: "linear-gradient(135deg, #56ab2f, #a8e6cf)",
          iconColor: "white"
          // },
          // {
          // 	key: 'download',
          // 	label: '离线下载',
          // 	description: '下载课程离线学习',
          // 	icon: 'icon-download',
          // 	iconBg: 'linear-gradient(135deg, #ff9a9e, #fecfef)',
          // 	iconColor: 'white'
        }
      ],
      settingMenus: [
        {
          key: "notification",
          label: "消息通知",
          description: "学习提醒设置",
          icon: "icon-bell",
          iconBg: "linear-gradient(135deg, #a8edea, #fed6e3)",
          iconColor: "white"
        },
        {
          key: "settings",
          label: "通用设置",
          description: "语言、主题等设置",
          icon: "icon-settings",
          iconBg: "linear-gradient(135deg, #667eea, #764ba2)",
          iconColor: "white"
        },
        {
          key: "help",
          label: "帮助中心",
          description: "常见问题与反馈",
          icon: "icon-help",
          iconBg: "linear-gradient(135deg, #56ab2f, #a8e6cf)",
          iconColor: "white"
        },
        {
          key: "logout",
          label: "退出登录",
          description: "安全退出当前账号",
          icon: "icon-logout",
          iconBg: "linear-gradient(135deg, #ff6b6b, #ee5a24)",
          iconColor: "white"
        }
      ]
    };
  },
  onLoad() {
    this.initPage();
  },
  onShow() {
    this.loadUserProfile();
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.loadUserProfile().finally(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  methods: {
    // 获取当前用户头像
    getCurrentUserAvatar() {
      if (this.currentUser && this.currentUser.avatar) {
        return this.currentUser.avatar;
      }
      return this.userInfo.avatar || "/static/images/default-avatar.png";
    },
    // 检查是否VIP用户
    isVipUser() {
      if (this.currentUser && this.currentUser.vipStatus) {
        return this.currentUser.vipStatus.isVip || false;
      }
      return true;
    },
    initPage() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight;
      this.checkAuthStatus();
      const localUserInfo = common_vendor.index.getStorageSync("userInfo");
      if (localUserInfo) {
        this.updateUserInfo(localUserInfo);
      }
    },
    // 加载用户资料 - 使用jeecg-boot接口
    loadUserProfile() {
      return __async(this, null, function* () {
        this.loading = true;
        try {
          const [userResponse, statsResponse] = yield Promise.all([
            api_jeecgUser.getUserInfo(),
            api_index.userApi.getStudyStats()
          ]);
          if (userResponse.success) {
            this.updateUserInfo(userResponse.data);
            common_vendor.index.setStorageSync("userInfo", userResponse.data);
          }
          if (statsResponse.success) {
            this.updateStatistics(statsResponse.data);
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/profile/profile.vue:258", "加载用户资料失败:", error);
          if (!this.userInfo.username) {
            common_vendor.index.showToast({
              title: "加载失败，请下拉刷新",
              icon: "none"
            });
          }
        } finally {
          this.loading = false;
        }
      });
    },
    // 更新用户信息
    updateUserInfo(userData) {
      this.userInfo = __spreadProps(__spreadValues({}, this.userInfo), {
        name: userData.nickname || userData.username || "用户",
        avatar: userData.avatar || "/static/images/default-avatar.png",
        studyDays: userData.studyDays || 0,
        level: userData.level || "新手学员",
        isVip: userData.isVip || false,
        nickname: userData.nickname || "",
        username: userData.username || ""
      });
    },
    // 更新统计数据
    updateStatistics(statsData) {
      this.statistics = [
        {
          key: "studyDays",
          value: String(statsData.totalStudyDays || 0),
          label: "学习天数"
        },
        {
          key: "continuousDays",
          value: String(statsData.continuousCheckinDays || 0),
          label: "连续打卡"
        },
        {
          key: "averageScore",
          value: String(Math.round(statsData.averageScore || 0)),
          label: "平均分数"
        }
      ];
    },
    changeAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.uploadAvatar(res.tempFilePaths[0]);
        }
      });
    },
    uploadAvatar(filePath) {
      return __async(this, null, function* () {
        try {
          common_vendor.index.showLoading({ title: "上传中..." });
          const response = yield api_index.userApi.uploadAvatar(filePath);
          if (response.success) {
            this.userInfo.avatar = response.data.avatarUrl || filePath;
            const localUserInfo = common_vendor.index.getStorageSync("userInfo");
            if (localUserInfo) {
              localUserInfo.avatar = this.userInfo.avatar;
              common_vendor.index.setStorageSync("userInfo", localUserInfo);
            }
            common_vendor.index.showToast({
              title: "头像更新成功",
              icon: "success"
            });
          } else {
            throw new Error(response.message || "上传失败");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/profile/profile.vue:345", "上传头像失败:", error);
          this.userInfo.avatar = filePath;
          common_vendor.index.showToast({
            title: "上传失败，使用本地图片",
            icon: "none"
          });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    },
    goToVip() {
      common_vendor.index.navigateTo({
        url: "/pages/vip/vip"
      });
    },
    handleMenuClick(item) {
      switch (item.key) {
        case "report":
          this.goToReport();
          break;
        case "favorites":
          this.goToFavorites();
          break;
        case "download":
          this.goToDownload();
          break;
        case "notification":
          this.goToNotification();
          break;
        case "settings":
          this.goToSettings();
          break;
        case "help":
          this.goToHelp();
          break;
        case "logout":
          this.handleLogout();
          break;
        default:
          common_vendor.index.showToast({
            title: "功能开发中",
            icon: "none"
          });
      }
    },
    goToReport() {
      common_vendor.index.navigateTo({
        url: "/pages/report/report"
      });
    },
    goToFavorites() {
      common_vendor.index.navigateTo({
        url: "/pages/favorites/favorites"
      });
    },
    goToDownload() {
      common_vendor.index.navigateTo({
        url: "/pages/download/download"
      });
    },
    goToNotification() {
      common_vendor.index.navigateTo({
        url: "/pages/settings/notification"
      });
    },
    goToSettings() {
      common_vendor.index.navigateTo({
        url: "/pages/settings/settings"
      });
    },
    goToHelp() {
      common_vendor.index.navigateTo({
        url: "/pages/help/help"
      });
    },
    handleLogout() {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            this.performLogout();
          }
        }
      });
    },
    performLogout() {
      return __async(this, null, function* () {
        try {
          common_vendor.index.showLoading({ title: "退出中..." });
          const logoutResult = yield api_jeecgUser.logout();
          if (logoutResult.success) {
            common_vendor.index.showToast({
              title: "退出成功",
              icon: "success"
            });
          } else {
            common_vendor.index.showToast({
              title: "已退出登录",
              icon: "success"
            });
          }
          setTimeout(() => {
            common_vendor.index.reLaunch({
              url: "/pages/index/index"
            });
          }, 1e3);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/profile/profile.vue:471", "退出登录失败:", error);
          common_vendor.index.showToast({
            title: "已退出登录",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.reLaunch({
              url: "/pages/index/index"
            });
          }, 1e3);
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: $options.getCurrentUserAvatar(),
    c: common_vendor.o((...args) => $options.changeAvatar && $options.changeAvatar(...args)),
    d: common_vendor.t(_ctx.getCurrentUserName()),
    e: common_vendor.t($data.userInfo.studyDays),
    f: common_vendor.t($data.userInfo.level),
    g: common_vendor.f($data.statistics, (stat, k0, i0) => {
      return {
        a: common_vendor.t(stat.value),
        b: common_vendor.t(stat.label),
        c: stat.key
      };
    }),
    h: !$options.isVipUser()
  }, !$options.isVipUser() ? {
    i: common_vendor.o((...args) => $options.goToVip && $options.goToVip(...args))
  } : {}, {
    j: common_vendor.f($data.studyMenus, (item, k0, i0) => {
      return {
        a: common_vendor.n(item.icon),
        b: item.iconColor,
        c: item.iconBg,
        d: common_vendor.t(item.label),
        e: common_vendor.t(item.description),
        f: item.key,
        g: common_vendor.o(($event) => $options.handleMenuClick(item), item.key)
      };
    }),
    k: common_vendor.f($data.settingMenus, (item, k0, i0) => {
      return {
        a: common_vendor.n(item.icon),
        b: item.iconColor,
        c: item.iconBg,
        d: common_vendor.t(item.label),
        e: common_vendor.t(item.description),
        f: item.key,
        g: common_vendor.o(($event) => $options.handleMenuClick(item), item.key)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-dd383ca2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/profile.js.map
