# 前后端联调指南

## 🎯 联调准备

### 环境要求

#### 后端环境
- Java 8+
- Maven 3.6+
- MySQL 5.7+
- Redis (可选)

#### 前端环境
- Node.js 16+
- npm 8+ 或 yarn 1.22+
- 现代浏览器

### 服务端口

| 服务 | 端口 | 地址 | 说明 |
|------|------|------|------|
| 后端API | 8080 | http://localhost:8080 | Spring Boot服务 |
| 前端H5 | 3000 | http://localhost:3000 | Vite开发服务器 |
| MySQL | 3306 | localhost:3306 | 数据库服务 |

## 🚀 启动步骤

### 1. 启动后端服务

```bash
# 进入后端目录
cd backend

# 启动Spring Boot应用
mvn spring-boot:run

# 或者使用IDE启动主类
# KumaApplication.java
```

### 2. 启动前端服务

```bash
# 进入前端目录
cd front

# 安装依赖（首次运行）
npm install

# 启动开发服务器
npm run dev:h5

# 或使用联调脚本（推荐）
npm run debug
```

### 3. 验证连接

访问以下地址验证服务状态：

- **前端应用**: http://localhost:3000
- **API测试**: http://localhost:3000/#/pages/debug/api-test
- **调试面板**: http://localhost:3000/#/pages/debug/debug
- **后端健康检查**: http://localhost:8080/kuma/app/common/health

## 🔧 API接口配置

### 环境变量配置

前端 `.env` 文件：

```bash
# API配置
VITE_API_BASE_URL=http://localhost:8080
VITE_API_PREFIX=/kuma
VITE_API_TIMEOUT=10000

# 调试配置
VITE_DEBUG=true
VITE_CONSOLE_LOG=true
```

### 接口路径映射

| 功能模块 | 前端路径 | 后端路径 | 说明 |
|----------|----------|----------|------|
| 用户管理 | `/app/user/*` | `/kuma/app/user/*` | 用户相关接口 |
| 课程管理 | `/app/course/*` | `/kuma/app/course/*` | 课程相关接口 |
| 练习评测 | `/app/practice/*` | `/kuma/app/practice/*` | 练习相关接口 |
| 排行榜 | `/app/leaderboard/*` | `/kuma/app/leaderboard/*` | 排行榜接口 |
| 通用接口 | `/app/common/*` | `/kuma/app/common/*` | 通用功能接口 |

### 代理配置

Vite开发服务器代理配置（`vite.config.js`）：

```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '/kuma')
    }
  }
}
```

## 🧪 接口测试

### 使用API测试页面

1. 访问 http://localhost:3000/#/pages/debug/api-test
2. 点击"测试连接"验证后端连接
3. 选择要测试的接口分类
4. 点击"测试"按钮执行接口调用
5. 查看测试结果和响应数据

### 手动测试接口

```bash
# 测试后端健康检查
curl http://localhost:8080/kuma/app/common/health

# 测试课程列表接口
curl http://localhost:8080/kuma/app/course/list

# 测试用户信息接口（需要token）
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8080/kuma/app/user/info
```

## 🔍 调试工具

### 1. 调试面板

访问：http://localhost:3000/#/pages/debug/debug

功能：
- 查看应用信息和系统状态
- 检查全局数据和登录状态
- 查看错误统计和日志
- 导出调试信息

### 2. API测试工具

访问：http://localhost:3000/#/pages/debug/api-test

功能：
- 测试后端连接状态
- 批量测试API接口
- 查看请求响应详情
- 接口性能监控

### 3. 浏览器开发者工具

- **Network标签**：查看HTTP请求和响应
- **Console标签**：查看前端日志和错误
- **Application标签**：查看本地存储和Cookie

## 🚨 常见问题

### 1. 后端连接失败

**症状**：前端显示"连接失败"或网络错误

**解决方案**：
1. 确认后端服务已启动：`curl http://localhost:8080/kuma/app/common/health`
2. 检查端口是否被占用：`netstat -an | grep 8080`
3. 查看后端日志是否有错误
4. 确认防火墙设置

### 2. CORS跨域问题

**症状**：浏览器控制台显示CORS错误

**解决方案**：
1. 后端添加CORS配置
2. 使用代理服务器
3. 检查请求头设置

### 3. 接口404错误

**症状**：接口返回404 Not Found

**解决方案**：
1. 检查接口路径是否正确
2. 确认后端Controller路径映射
3. 查看后端路由配置

### 4. 认证失败

**症状**：接口返回401 Unauthorized

**解决方案**：
1. 检查Token是否有效
2. 确认请求头Authorization格式
3. 验证后端认证逻辑

## 📋 联调检查清单

### 启动前检查

- [ ] 后端服务正常启动
- [ ] 数据库连接正常
- [ ] 前端依赖安装完成
- [ ] 环境变量配置正确

### 功能测试检查

- [ ] 用户登录/注册功能
- [ ] 课程列表和详情页面
- [ ] 练习功能和语音评测
- [ ] 打卡功能和历史记录
- [ ] 排行榜数据显示

### 性能检查

- [ ] 接口响应时间 < 2秒
- [ ] 页面加载时间 < 3秒
- [ ] 无内存泄漏
- [ ] 错误处理正常

## 🛠️ 开发建议

### 1. 接口设计规范

- 使用RESTful API设计
- 统一的响应格式
- 合理的HTTP状态码
- 详细的错误信息

### 2. 前端开发规范

- 统一的错误处理
- 加载状态提示
- 网络异常处理
- 用户友好的提示信息

### 3. 调试技巧

- 使用浏览器开发者工具
- 添加详细的日志输出
- 使用API测试工具验证接口
- 定期检查网络请求

## 📞 技术支持

### 联系方式

- 开发团队：Kuma Team
- 技术文档：项目README.md
- 问题反馈：项目Issues

### 相关文档

- [API接口文档](./API接口文档.md)
- [错误解决方案](./错误解决方案.md)
- [部署指南](./部署指南.md)

---

**更新时间**: 2024-03-22  
**版本**: v1.0.0  
**状态**: ✅ 联调环境就绪
