<template>
	<view class="page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 页面内容 -->
		<view class="content">
			<!-- 头部标题 -->
			<view class="header">
				<text class="title">学习排行榜</text>
				<text class="subtitle">与朋友一起进步</text>
			</view>

			<!-- 时间范围切换 -->
			<view class="tab-buttons">
				<view 
					class="tab-button" 
					v-for="(tab, index) in timeTabs" 
					:key="index"
					:class="{ active: activeTimeTab === index }"
					@click="switchTimeTab(index)"
				>
					{{ tab.name }}
				</view>
			</view>

			<!-- 前三名领奖台 -->
			<view class="podium">
				<!-- 第二名 -->
				<view class="podium-item second">
					<view class="podium-base">
						<view class="podium-avatar">
							<image :src="topUsers[1].avatar" mode="aspectFill"></image>
						</view>
						<text class="podium-name">{{ topUsers[1].name }}</text>
						<text class="podium-days">{{ topUsers[1].days }}天</text>
					</view>
				</view>

				<!-- 第一名 -->
				<view class="podium-item first">
					<view class="podium-base">
						<text class="crown iconfont icon-crown"></text>
						<view class="podium-avatar">
							<image :src="topUsers[0].avatar" mode="aspectFill"></image>
						</view>
						<text class="podium-name">{{ topUsers[0].name }}</text>
						<text class="podium-days">{{ topUsers[0].days }}天</text>
					</view>
				</view>

				<!-- 第三名 -->
				<view class="podium-item third">
					<view class="podium-base">
						<view class="podium-avatar">
							<image :src="topUsers[2].avatar" mode="aspectFill"></image>
						</view>
						<text class="podium-name">{{ topUsers[2].name }}</text>
						<text class="podium-days">{{ topUsers[2].days }}天</text>
					</view>
				</view>
			</view>

			<!-- 排行榜列表 -->
			<view class="ranking-list">
				<view class="ranking-header">
					<text class="ranking-title">完整排行榜</text>
					<text class="ranking-desc">基于{{ timeTabs[activeTimeTab].desc }}连续打卡天数</text>
				</view>

				<view 
					class="ranking-item" 
					v-for="(user, index) in rankingList" 
					:key="user.id"
					:class="{ 'current-user': user.isCurrentUser }"
				>
					<view class="rank-number">{{ user.rank }}</view>
					<view class="user-avatar">
						<image :src="user.avatar" mode="aspectFill"></image>
					</view>
					<view class="user-info">
						<text class="user-name">{{ user.name }}</text>
						<text class="user-streak">连续打卡 {{ user.days }}天</text>
					</view>
					<view class="user-score">
						<text class="score-number">{{ user.days }}</text>
						<text class="score-unit">天</text>
					</view>
				</view>

				<!-- 激励信息 -->
				<view class="motivation-card">
					<text class="iconfont icon-trophy"></text>
					<view class="motivation-content">
						<text class="motivation-title">继续加油！</text>
						<text class="motivation-desc">再坚持 {{ daysToNext }} 天就能超越前一名</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { leaderboardApi } from '@/api/index.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			loading: true,
			activeTimeTab: 1, // 默认选中本月
			daysToNext: 0,
			currentUserRank: 0,
			timeTabs: [
				{ name: '本周', desc: '本周', value: 'week' },
				{ name: '本月', desc: '本月', value: 'month' },
				{ name: '总榜', desc: '总', value: 'all' }
			],
			topUsers: [
				{
					id: 1,
					name: '田中君',
					avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face',
					days: 31
				},
				{
					id: 2,
					name: '小樱',
					avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face',
					days: 28
				},
				{
					id: 3,
					name: '美咲',
					avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face',
					days: 25
				}
			],
			rankingList: [
				{
					id: 4,
					rank: 4,
					name: '佐藤',
					avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face',
					days: 22,
					isCurrentUser: false
				},
				{
					id: 5,
					rank: 5,
					name: '山田',
					avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=48&h=48&fit=crop&crop=face',
					days: 20,
					isCurrentUser: false
				},
				{
					id: 6,
					rank: 8,
					name: '我 (小明)',
					avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=48&h=48&fit=crop&crop=face',
					days: 12,
					isCurrentUser: true
				},
				{
					id: 7,
					rank: 9,
					name: '花子',
					avatar: 'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=48&h=48&fit=crop&crop=face',
					days: 11,
					isCurrentUser: false
				},
				{
					id: 8,
					rank: 10,
					name: '太郎',
					avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=48&h=48&fit=crop&crop=face',
					days: 10,
					isCurrentUser: false
				}
			]
		}
	},
	
	onLoad() {
		this.initPage()
	},

	onShow() {
		this.loadRankingData()
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.loadRankingData().finally(() => {
			uni.stopPullDownRefresh()
		})
	},

	methods: {
		initPage() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight
		},

		// 加载排行榜数据
		async loadRankingData() {
			this.loading = true

			try {
				const timeRange = this.timeTabs[this.activeTimeTab].value
				console.log('加载排行榜数据:', timeRange)

				// 根据时间范围调用不同的API
				let response
				switch (timeRange) {
					case 'week':
					case 'month':
					case 'all':
						response = await leaderboardApi.getCheckinRanking({
							timeRange,
							limit: 50
						})
						break
					default:
						response = await leaderboardApi.getCheckinRanking({
							timeRange: 'month',
							limit: 50
						})
				}

				if (response.success && response.data) {
					this.updateRankingData(response.data)
				} else {
					throw new Error(response.message || '获取排行榜数据失败')
				}

			} catch (error) {
				console.error('加载排行榜数据失败:', error)

				 // 使用默认数据
				// this.setDefaultRankingData()

				// uni.showToast({
				// 	title: '加载失败，显示示例数据',
				// 	icon: 'none'
				// })
			} finally {
				this.loading = false
			}
		},

		// 更新排行榜数据
		updateRankingData(data) {
			const { rankings, currentUser } = data

			if (rankings && rankings.length > 0) {
				// 更新前三名
				this.topUsers = rankings.slice(0, 3).map(user => ({
					id: user.userId,
					name: user.nickname || user.username,
					avatar: user.avatar || '/static/images/default-avatar.png',
					days: user.continuousDays || user.totalDays || 0
				}))

				// 更新完整排行榜（从第4名开始）
				this.rankingList = rankings.slice(3).map(user => ({
					id: user.userId,
					rank: user.rank,
					name: user.nickname || user.username,
					avatar: user.avatar || '/static/images/default-avatar.png',
					days: user.continuousDays || user.totalDays || 0,
					isCurrentUser: currentUser && user.userId === currentUser.userId
				}))

				// 如果当前用户在前三名中，需要特殊处理
				if (currentUser && currentUser.rank <= 3) {
					this.currentUserRank = currentUser.rank
					this.daysToNext = 0
				} else if (currentUser) {
					this.currentUserRank = currentUser.rank
					this.calculateDaysToNext(currentUser, rankings)
				}
			}
		},

		// 计算超越前一名需要的天数
		calculateDaysToNext(currentUser, rankings) {
			const currentRank = currentUser.rank
			if (currentRank > 1) {
				const prevUser = rankings.find(user => user.rank === currentRank - 1)
				if (prevUser) {
					const currentDays = currentUser.continuousDays || currentUser.totalDays || 0
					const prevDays = prevUser.continuousDays || prevUser.totalDays || 0
					this.daysToNext = Math.max(0, prevDays - currentDays + 1)
				}
			}
		},

		// 设置默认排行榜数据
		setDefaultRankingData() {
			// 保持现有的默认数据
			console.log('使用默认排行榜数据')
		},

		// 切换时间范围
		switchTimeTab(index) {
			if (this.activeTimeTab === index) return

			this.activeTimeTab = index
			this.loadRankingData()
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-bar {
	background: transparent;
}

.content {
	padding-bottom: 200rpx;
}

.header {
	text-align: center;
	padding: 48rpx 40rpx;
	
	.title {
		display: block;
		color: white;
		font-size: 48rpx;
		font-weight: bold;
		margin-bottom: 16rpx;
	}
	
	.subtitle {
		color: rgba(255, 255, 255, 0.8);
		font-size: 32rpx;
	}
}

.tab-buttons {
	display: flex;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 24rpx;
	padding: 8rpx;
	margin: 0 40rpx 40rpx;
	
	.tab-button {
		flex: 1;
		padding: 16rpx 32rpx;
		text-align: center;
		border-radius: 16rpx;
		font-size: 28rpx;
		font-weight: 500;
		color: rgba(255, 255, 255, 0.7);
		transition: all 0.2s;
		
		&.active {
			background: rgba(255, 255, 255, 0.9);
			color: #667eea;
		}
	}
}

.podium {
	display: flex;
	justify-content: center;
	align-items: flex-end;
	padding: 40rpx;
	gap: 20rpx;
	
	.podium-item {
		text-align: center;
		color: white;
		
		.podium-avatar {
			border-radius: 50%;
			margin: 0 auto 16rpx;
			border: 6rpx solid white;
			overflow: hidden;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.podium-base {
			background: rgba(255, 255, 255, 0.2);
			border-radius: 16rpx 16rpx 0 0;
			padding: 32rpx 24rpx 16rpx;
			position: relative;
		}
		
		.podium-name {
			display: block;
			font-size: 28rpx;
			font-weight: 500;
			margin-bottom: 8rpx;
		}
		
		.podium-days {
			font-size: 24rpx;
			opacity: 0.8;
		}
		
		&.first {
			.podium-avatar {
				width: 160rpx;
				height: 160rpx;
				border-color: #ffd700;
			}
			
			.podium-base {
				height: 200rpx;
				background: rgba(255, 215, 0, 0.3);
			}
			
			.crown {
				position: absolute;
				top: -30rpx;
				left: 50%;
				transform: translateX(-50%);
				color: #ffd700;
				font-size: 48rpx;
			}
		}
		
		&.second {
			.podium-avatar {
				width: 120rpx;
				height: 120rpx;
				border-color: #c0c0c0;
			}
			
			.podium-base {
				height: 160rpx;
				background: rgba(192, 192, 192, 0.3);
			}
		}
		
		&.third {
			.podium-avatar {
				width: 120rpx;
				height: 120rpx;
				border-color: #cd7f32;
			}
			
			.podium-base {
				height: 120rpx;
				background: rgba(205, 127, 50, 0.3);
			}
		}
	}
}

.ranking-list {
	background: #f8fafc;
	border-radius: 48rpx 48rpx 0 0;
	margin-top: 40rpx;
	padding: 40rpx 0;
	min-height: 800rpx;
	
	.ranking-header {
		padding: 0 48rpx 32rpx;
		
		.ranking-title {
			display: block;
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 8rpx;
		}
		
		.ranking-desc {
			color: #666;
			font-size: 28rpx;
		}
	}
}

.ranking-item {
	display: flex;
	align-items: center;
	padding: 32rpx 48rpx;
	background: white;
	margin: 16rpx 32rpx;
	border-radius: 32rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	
	&.current-user {
		background: linear-gradient(135deg, #667eea, #764ba2);
		color: white;
		
		.rank-number {
			background: rgba(255, 255, 255, 0.2);
			color: white;
		}
		
		.user-name, .user-streak, .score-number, .score-unit {
			color: white;
		}
	}
	
	.rank-number {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		background: #f1f5f9;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: bold;
		color: #64748b;
		margin-right: 24rpx;
	}
	
	.user-avatar {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		margin-right: 24rpx;
		overflow: hidden;
		
		image {
			width: 100%;
			height: 100%;
		}
	}
	
	.user-info {
		flex: 1;
		
		.user-name {
			display: block;
			font-weight: bold;
			color: #333;
			font-size: 32rpx;
			margin-bottom: 4rpx;
		}
		
		.user-streak {
			color: #666;
			font-size: 24rpx;
		}
	}
	
	.user-score {
		text-align: right;
		
		.score-number {
			display: block;
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			line-height: 1;
		}
		
		.score-unit {
			color: #666;
			font-size: 24rpx;
		}
	}
}

.motivation-card {
	display: flex;
	align-items: center;
	background: linear-gradient(135deg, #dbeafe, #e0e7ff);
	border-radius: 32rpx;
	padding: 32rpx;
	margin: 48rpx 32rpx 0;
	
	.iconfont {
		color: #f59e0b;
		font-size: 48rpx;
		margin-right: 24rpx;
	}
	
	.motivation-content {
		flex: 1;
		
		.motivation-title {
			display: block;
			font-weight: bold;
			color: #333;
			font-size: 32rpx;
			margin-bottom: 8rpx;
		}
		
		.motivation-desc {
			color: #666;
			font-size: 28rpx;
		}
	}
}

/* 字体图标 */
.icon-crown::before { content: '\e027'; }
.icon-trophy::before { content: '\e028'; }
</style>
