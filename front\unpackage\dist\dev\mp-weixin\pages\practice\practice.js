"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 0,
      courseId: "",
      courseTitle: "",
      loading: true,
      currentSentence: 1,
      totalSentences: 0,
      isRecording: false,
      hasRecorded: false,
      showScore: false,
      recordingInstruction: "点击按钮开始录音",
      recordingHint: "长按录音，松开结束",
      // 录音相关
      recorderManager: null,
      audioFilePath: "",
      // 当前句子数据
      currentSentenceData: {
        japanese: "ラーメンとギョーザをお願いします",
        chinese: "请给我拉面和饺子"
      },
      // AI评分结果
      totalScore: 85,
      pronunciationScore: 90,
      fluencyScore: 80,
      toneScore: 85,
      aiFeedback: "建议：「ギョーザ」的发音可以更清晰一些",
      // 所有句子数据
      sentences: [
        {
          japanese: "いらっしゃいませ。何名様ですか？",
          chinese: "欢迎光临。请问几位？"
        },
        {
          japanese: "二人です。",
          chinese: "两个人。"
        },
        {
          japanese: "ラーメンとギョーザをお願いします",
          chinese: "请给我拉面和饺子"
        }
      ]
    };
  },
  onLoad(options) {
    this.courseId = options.courseId;
    this.courseTitle = decodeURIComponent(options.title || "跟读练习");
    this.initPage();
  },
  onUnload() {
    if (this.recorderManager) {
      this.recorderManager.stop();
    }
  },
  methods: {
    initPage() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight;
      this.initRecorder();
      this.loadPracticeData();
    },
    // 初始化录音管理器
    initRecorder() {
      this.recorderManager = common_vendor.index.getRecorderManager();
      this.recorderManager.onStart(() => {
        common_vendor.index.__f__("log", "at pages/practice/practice.vue:202", "录音开始");
      });
      this.recorderManager.onStop((res) => {
        common_vendor.index.__f__("log", "at pages/practice/practice.vue:206", "录音结束", res);
        this.audioFilePath = res.tempFilePath;
        this.submitRecording(res.tempFilePath);
      });
      this.recorderManager.onError((err) => {
        common_vendor.index.__f__("error", "at pages/practice/practice.vue:212", "录音错误", err);
        common_vendor.index.showToast({
          title: "录音失败，请重试",
          icon: "none"
        });
        this.isRecording = false;
      });
    },
    // 加载练习数据
    loadPracticeData() {
      return __async(this, null, function* () {
        if (!this.courseId) {
          common_vendor.index.showToast({
            title: "课程ID不能为空",
            icon: "none"
          });
          return;
        }
        this.loading = true;
        try {
          common_vendor.index.__f__("log", "at pages/practice/practice.vue:234", "加载练习数据:", this.courseId);
          const response = yield api_index.courseApi.getCourseDetail(this.courseId);
          if (response.success && response.data) {
            this.updatePracticeData(response.data);
          } else {
            throw new Error(response.message || "获取练习数据失败");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/practice/practice.vue:246", "加载练习数据失败:", error);
          this.setDefaultPracticeData();
          common_vendor.index.showToast({
            title: "加载失败，使用示例数据",
            icon: "none"
          });
        } finally {
          this.loading = false;
        }
      });
    },
    // 更新练习数据
    updatePracticeData(courseData) {
      if (courseData.dialogues && courseData.dialogues.length > 0) {
        this.sentences = courseData.dialogues.map((dialogue) => ({
          japanese: dialogue.text,
          chinese: dialogue.translation
        }));
        this.totalSentences = this.sentences.length;
        this.currentSentenceData = this.sentences[0];
      } else {
        this.setDefaultPracticeData();
      }
    },
    // 设置默认练习数据
    setDefaultPracticeData() {
      this.totalSentences = this.sentences.length;
      this.currentSentenceData = this.sentences[this.currentSentence - 1] || this.sentences[0];
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    exitPractice() {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出练习吗？当前进度将不会保存。",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateBack();
          }
        }
      });
    },
    playOriginal() {
      common_vendor.index.__f__("log", "at pages/practice/practice.vue:298", "播放原音");
      common_vendor.index.showToast({
        title: "播放原音",
        icon: "none"
      });
    },
    repeatOriginal() {
      common_vendor.index.__f__("log", "at pages/practice/practice.vue:307", "重复播放");
    },
    adjustVolume() {
      common_vendor.index.__f__("log", "at pages/practice/practice.vue:312", "调节音量");
    },
    toggleRecording() {
      if (this.isRecording) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },
    startRecording() {
      return __async(this, null, function* () {
        try {
          yield this.checkRecordPermission();
          this.isRecording = true;
          this.recordingInstruction = "正在录音...";
          this.recordingHint = "再次点击结束录音";
          this.recorderManager.start({
            duration: 6e4,
            // 最长60秒
            sampleRate: 16e3,
            numberOfChannels: 1,
            encodeBitRate: 96e3,
            format: "mp3"
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/practice/practice.vue:342", "开始录音失败:", error);
          common_vendor.index.showToast({
            title: "录音权限被拒绝",
            icon: "none"
          });
          this.isRecording = false;
        }
      });
    },
    stopRecording() {
      if (!this.isRecording)
        return;
      this.isRecording = false;
      this.hasRecorded = true;
      this.recordingInstruction = "录音完成";
      this.recordingHint = "正在分析中...";
      this.recorderManager.stop();
    },
    // 检查录音权限
    checkRecordPermission() {
      return __async(this, null, function* () {
        return new Promise((resolve, reject) => {
          common_vendor.index.getSetting({
            success: (res) => {
              if (res.authSetting["scope.record"]) {
                resolve(true);
              } else {
                common_vendor.index.authorize({
                  scope: "scope.record",
                  success: () => resolve(true),
                  fail: () => reject(new Error("录音权限被拒绝"))
                });
              }
            },
            fail: () => reject(new Error("获取权限设置失败"))
          });
        });
      });
    },
    // 提交录音进行评分
    submitRecording(audioFilePath) {
      return __async(this, null, function* () {
        try {
          common_vendor.index.showLoading({ title: "正在评分..." });
          const practiceData = {
            courseId: this.courseId,
            sentenceIndex: this.currentSentence - 1,
            targetText: this.currentSentenceData.japanese,
            audioFile: audioFilePath,
            language: "ja_JP"
            // 日语
          };
          common_vendor.index.__f__("log", "at pages/practice/practice.vue:397", "提交录音评分:", practiceData);
          const response = yield api_index.practiceApi.submitPractice(practiceData);
          if (response.success && response.data) {
            this.updateScoreResult(response.data);
          } else {
            throw new Error(response.message || "评分失败");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/practice/practice.vue:409", "提交录音失败:", error);
          this.showMockScore();
          common_vendor.index.showToast({
            title: "评分失败，显示模拟结果",
            icon: "none"
          });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    },
    // 更新评分结果
    updateScoreResult(scoreData) {
      this.totalScore = scoreData.totalScore || 85;
      this.pronunciationScore = scoreData.pronunciationScore || 90;
      this.fluencyScore = scoreData.fluencyScore || 80;
      this.toneScore = scoreData.toneScore || 85;
      this.aiFeedback = scoreData.feedback || "发音不错，继续加油！";
      this.showScore = true;
      this.recordingHint = "评分完成";
    },
    // 显示模拟评分结果
    showMockScore() {
      this.totalScore = Math.floor(Math.random() * 20) + 80;
      this.pronunciationScore = Math.floor(Math.random() * 20) + 80;
      this.fluencyScore = Math.floor(Math.random() * 20) + 75;
      this.toneScore = Math.floor(Math.random() * 20) + 75;
      const feedbacks = [
        "发音很标准，继续保持！",
        "语调可以更自然一些",
        "流利度不错，发音还需要练习",
        "整体表现很好，加油！"
      ];
      this.aiFeedback = feedbacks[Math.floor(Math.random() * feedbacks.length)];
      this.showScore = true;
      this.recordingHint = "评分完成";
    },
    showAIScore() {
      this.showScore = true;
      this.recordingHint = "评分完成";
    },
    retryRecording() {
      this.isRecording = false;
      this.hasRecorded = false;
      this.showScore = false;
      this.recordingInstruction = "点击按钮开始录音";
      this.recordingHint = "长按录音，松开结束";
    },
    nextSentence() {
      if (this.currentSentence < this.totalSentences) {
        this.currentSentence++;
        this.retryRecording();
        if (this.sentences[this.currentSentence - 1]) {
          this.currentSentenceData = this.sentences[this.currentSentence - 1];
        }
      } else {
        this.completePractice();
      }
    },
    completePractice() {
      common_vendor.index.showModal({
        title: "练习完成",
        content: "恭喜完成本课练习！",
        showCancel: false,
        success: () => {
          common_vendor.index.navigateBack();
        }
      });
    },
    getRecordIcon() {
      if (this.hasRecorded) {
        return "icon-check";
      } else if (this.isRecording) {
        return "icon-stop";
      } else {
        return "icon-microphone";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.t($data.currentSentence),
    d: common_vendor.t($data.totalSentences),
    e: common_vendor.o((...args) => $options.exitPractice && $options.exitPractice(...args)),
    f: common_vendor.t($data.currentSentence),
    g: common_vendor.t($data.totalSentences),
    h: common_vendor.t($data.currentSentenceData.japanese),
    i: common_vendor.t($data.currentSentenceData.chinese),
    j: common_vendor.o((...args) => $options.playOriginal && $options.playOriginal(...args)),
    k: common_vendor.o((...args) => $options.repeatOriginal && $options.repeatOriginal(...args)),
    l: common_vendor.o((...args) => $options.adjustVolume && $options.adjustVolume(...args)),
    m: common_vendor.t($data.recordingInstruction),
    n: common_vendor.n($options.getRecordIcon()),
    o: $data.isRecording ? 1 : "",
    p: $data.hasRecorded ? 1 : "",
    q: common_vendor.o((...args) => $options.toggleRecording && $options.toggleRecording(...args)),
    r: common_vendor.t($data.recordingHint),
    s: $data.showScore
  }, $data.showScore ? common_vendor.e({
    t: common_vendor.t($data.totalScore),
    v: $data.pronunciationScore + "%",
    w: common_vendor.t($data.pronunciationScore),
    x: $data.fluencyScore + "%",
    y: common_vendor.t($data.fluencyScore),
    z: $data.toneScore + "%",
    A: common_vendor.t($data.toneScore),
    B: $data.aiFeedback
  }, $data.aiFeedback ? {
    C: common_vendor.t($data.aiFeedback)
  } : {}) : {}, {
    D: $data.showScore
  }, $data.showScore ? {
    E: common_vendor.o((...args) => $options.retryRecording && $options.retryRecording(...args)),
    F: common_vendor.o((...args) => $options.nextSentence && $options.nextSentence(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-338bd53f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/practice/practice.js.map
