"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_index = require("./index.js");
const utils_auth = require("../utils/auth.js");
function login(loginData) {
  return __async(this, null, function* () {
    try {
      common_vendor.index.__f__("log", "at api/jeecg-user.js:19", "🔐 jeecg-boot用户登录:", loginData);
      const params = {
        username: loginData.username,
        password: loginData.password
      };
      if (loginData.captcha && loginData.checkKey) {
        params.captcha = loginData.captcha;
        params.checkKey = loginData.checkKey;
        common_vendor.index.__f__("log", "at api/jeecg-user.js:31", "✅ 登录包含验证码参数:", {
          captcha: params.captcha,
          checkKey: params.checkKey
        });
      } else {
        common_vendor.index.__f__("warn", "at api/jeecg-user.js:36", "⚠️ 缺少验证码参数，登录可能失败");
      }
      const result = yield utils_auth.login(params);
      if (result.success) {
        common_vendor.index.__f__("log", "at api/jeecg-user.js:43", "✅ 登录成功:", result.data);
        return {
          success: true,
          data: {
            userInfo: result.data,
            token: result.token
          },
          message: "登录成功"
        };
      } else {
        common_vendor.index.__f__("error", "at api/jeecg-user.js:55", "❌ 登录失败:", result.message);
        return {
          success: false,
          message: result.message || "登录失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at api/jeecg-user.js:62", "❌ 登录异常:", error);
      return {
        success: false,
        message: error.message || "登录异常，请重试"
      };
    }
  });
}
function logout() {
  return __async(this, null, function* () {
    try {
      common_vendor.index.__f__("log", "at api/jeecg-user.js:75", "🚪 jeecg-boot用户登出");
      const result = yield utils_auth.logout();
      if (result.success) {
        common_vendor.index.__f__("log", "at api/jeecg-user.js:80", "✅ 登出成功");
        return {
          success: true,
          message: "登出成功"
        };
      } else {
        return {
          success: false,
          message: result.message || "登出失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at api/jeecg-user.js:92", "❌ 登出异常:", error);
      return {
        success: false,
        message: error.message || "登出异常"
      };
    }
  });
}
function getUserInfo() {
  return __async(this, null, function* () {
    try {
      common_vendor.index.__f__("log", "at api/jeecg-user.js:105", "👤 获取jeecg-boot用户信息");
      const response = yield api_index.userApi.getUserInfo();
      if (response.success) {
        const userInfo = response.data;
        const adaptedUserInfo = {
          id: userInfo.id,
          username: userInfo.username,
          realname: userInfo.realname || userInfo.username,
          nickname: userInfo.realname || userInfo.username,
          avatar: userInfo.avatar || "/static/images/default-avatar.png",
          phone: userInfo.phone,
          email: userInfo.email,
          sex: userInfo.sex,
          birthday: userInfo.birthday,
          orgCode: userInfo.orgCode,
          status: userInfo.status,
          delFlag: userInfo.delFlag,
          workNo: userInfo.workNo,
          post: userInfo.post,
          telephone: userInfo.telephone,
          createBy: userInfo.createBy,
          createTime: userInfo.createTime,
          updateBy: userInfo.updateBy,
          updateTime: userInfo.updateTime,
          // 扩展字段（如果有）
          level: userInfo.level || "beginner",
          totalStudyDays: userInfo.totalStudyDays || 0,
          continuousDays: userInfo.continuousDays || 0,
          totalScore: userInfo.totalScore || 0,
          vipStatus: userInfo.vipStatus || 0
        };
        common_vendor.index.__f__("log", "at api/jeecg-user.js:142", "✅ 用户信息获取成功:", adaptedUserInfo);
        return {
          success: true,
          data: adaptedUserInfo,
          message: "获取成功"
        };
      } else {
        common_vendor.index.__f__("error", "at api/jeecg-user.js:150", "❌ 用户信息获取失败:", response.message);
        return {
          success: false,
          message: response.message || "获取用户信息失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at api/jeecg-user.js:157", "❌ 用户信息获取异常:", error);
      return {
        success: false,
        message: error.message || "获取用户信息异常"
      };
    }
  });
}
exports.getUserInfo = getUserInfo;
exports.login = login;
exports.logout = logout;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/jeecg-user.js.map
