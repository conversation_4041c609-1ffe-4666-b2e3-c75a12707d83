<template>
	<view class="voice-recorder">
		<view class="recorder-content">
			<text class="recorder-instruction">{{ instruction }}</text>
			
			<view 
				class="record-button" 
				:class="{ recording: isRecording, completed: hasRecorded }"
				@touchstart="startRecord"
				@touchend="stopRecord"
				@touchcancel="cancelRecord"
			>
				<text class="iconfont" :class="getRecordIcon()"></text>
			</view>
			
			<text class="recorder-hint">{{ hint }}</text>
			
			<!-- 录音时长显示 -->
			<view v-if="isRecording" class="recording-time">
				<text>{{ formatTime(recordingTime) }}</text>
			</view>
			
			<!-- 录音结果 -->
			<view v-if="hasRecorded && audioPath" class="record-result">
				<view class="result-actions">
					<button class="btn-secondary" @click="playRecord">
						<text class="iconfont icon-play"></text>
						播放录音
					</button>
					<button class="btn-primary" @click="submitRecord">
						<text class="iconfont icon-upload"></text>
						提交录音
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'VoiceRecorder',
	props: {
		// 最大录音时长(秒)
		maxDuration: {
			type: Number,
			default: 60
		},
		// 最小录音时长(秒)
		minDuration: {
			type: Number,
			default: 1
		},
		// 录音格式
		format: {
			type: String,
			default: 'mp3'
		},
		// 采样率
		sampleRate: {
			type: Number,
			default: 16000
		}
	},
	
	data() {
		return {
			recorderManager: null,
			audioContext: null,
			isRecording: false,
			hasRecorded: false,
			recordingTime: 0,
			audioPath: '',
			timer: null,
			instruction: '长按按钮开始录音',
			hint: '松开结束录音'
		}
	},
	
	mounted() {
		this.initRecorder()
	},
	
	beforeUnmount() {
		this.destroyRecorder()
	},
	
	methods: {
		async initRecorder() {
			// 检查录音权限
			try {
				await this.checkRecordPermission()
				this.setupRecorder()
			} catch (error) {
				console.error('录音权限检查失败:', error)
				this.instruction = '需要录音权限'
				this.hint = '请在设置中开启录音权限'
			}
		},
		
		checkRecordPermission() {
			return new Promise((resolve, reject) => {
				uni.getSetting({
					success: (res) => {
						if (res.authSetting['scope.record']) {
							resolve(true)
						} else {
							uni.authorize({
								scope: 'scope.record',
								success: () => resolve(true),
								fail: () => reject(false)
							})
						}
					},
					fail: () => reject(false)
				})
			})
		},
		
		setupRecorder() {
			// 创建录音管理器
			this.recorderManager = uni.getRecorderManager()
			
			// 监听录音开始
			this.recorderManager.onStart(() => {
				console.log('录音开始')
				this.isRecording = true
				this.instruction = '正在录音...'
				this.hint = '松开结束录音'
				this.startTimer()
				this.$emit('recordStart')
			})
			
			// 监听录音结束
			this.recorderManager.onStop((res) => {
				console.log('录音结束:', res)
				this.isRecording = false
				this.hasRecorded = true
				this.audioPath = res.tempFilePath
				this.instruction = '录音完成'
				this.hint = '可以播放或重新录音'
				this.stopTimer()
				this.$emit('recordStop', {
					path: res.tempFilePath,
					duration: res.duration,
					fileSize: res.fileSize
				})
			})
			
			// 监听录音错误
			this.recorderManager.onError((err) => {
				console.error('录音错误:', err)
				this.isRecording = false
				this.instruction = '录音失败'
				this.hint = '请重试'
				this.stopTimer()
				this.$emit('recordError', err)
			})
			
			// 创建音频播放器
			this.audioContext = uni.createInnerAudioContext()
		},
		
		destroyRecorder() {
			if (this.recorderManager) {
				this.recorderManager.onStart(() => {})
				this.recorderManager.onStop(() => {})
				this.recorderManager.onError(() => {})
			}
			
			if (this.audioContext) {
				this.audioContext.destroy()
			}
			
			this.stopTimer()
		},
		
		startRecord() {
			if (this.isRecording) return
			
			// 重置状态
			this.hasRecorded = false
			this.audioPath = ''
			this.recordingTime = 0
			
			// 开始录音
			const options = {
				duration: this.maxDuration * 1000,
				sampleRate: this.sampleRate,
				numberOfChannels: 1,
				encodeBitRate: 96000,
				format: this.format,
				frameSize: 50
			}
			
			this.recorderManager.start(options)
		},
		
		stopRecord() {
			if (!this.isRecording) return
			
			// 检查最小录音时长
			if (this.recordingTime < this.minDuration) {
				this.cancelRecord()
				uni.showToast({
					title: `录音时长不能少于${this.minDuration}秒`,
					icon: 'none'
				})
				return
			}
			
			this.recorderManager.stop()
		},
		
		cancelRecord() {
			if (this.isRecording) {
				this.recorderManager.stop()
				this.isRecording = false
				this.instruction = '录音已取消'
				this.hint = '长按按钮开始录音'
				this.stopTimer()
			}
		},
		
		playRecord() {
			if (!this.audioPath) return
			
			this.audioContext.src = this.audioPath
			this.audioContext.play()
			
			this.audioContext.onPlay(() => {
				uni.showToast({
					title: '播放录音',
					icon: 'none'
				})
			})
			
			this.audioContext.onError((err) => {
				console.error('播放错误:', err)
				uni.showToast({
					title: '播放失败',
					icon: 'none'
				})
			})
		},
		
		submitRecord() {
			if (!this.audioPath) return
			
			this.$emit('submit', {
				path: this.audioPath,
				duration: this.recordingTime
			})
		},
		
		resetRecord() {
			this.isRecording = false
			this.hasRecorded = false
			this.audioPath = ''
			this.recordingTime = 0
			this.instruction = '长按按钮开始录音'
			this.hint = '松开结束录音'
			this.stopTimer()
		},
		
		startTimer() {
			this.stopTimer()
			this.timer = setInterval(() => {
				this.recordingTime++
				if (this.recordingTime >= this.maxDuration) {
					this.stopRecord()
				}
			}, 1000)
		},
		
		stopTimer() {
			if (this.timer) {
				clearInterval(this.timer)
				this.timer = null
			}
		},
		
		formatTime(seconds) {
			const mins = Math.floor(seconds / 60)
			const secs = seconds % 60
			return `${mins}:${secs.toString().padStart(2, '0')}`
		},
		
		getRecordIcon() {
			if (this.hasRecorded) {
				return 'icon-check'
			} else if (this.isRecording) {
				return 'icon-stop'
			} else {
				return 'icon-microphone'
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.voice-recorder {
	text-align: center;
}

.recorder-content {
	padding: 40rpx;
}

.recorder-instruction {
	display: block;
	color: #666;
	font-size: 28rpx;
	margin-bottom: 32rpx;
}

.record-button {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #ff6b6b, #ee5a24);
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 32rpx;
	box-shadow: 0 16rpx 64rpx rgba(255, 107, 107, 0.4);
	transition: all 0.3s ease;
	
	&.recording {
		background: linear-gradient(135deg, #ff4757, #c44569);
		animation: pulse 1.5s infinite;
		transform: scale(1.1);
	}
	
	&.completed {
		background: linear-gradient(135deg, #2ed573, #1e90ff);
	}
	
	.iconfont {
		color: white;
		font-size: 64rpx;
	}
}

@keyframes pulse {
	0% { box-shadow: 0 16rpx 64rpx rgba(255, 107, 107, 0.4); }
	50% { box-shadow: 0 16rpx 64rpx rgba(255, 107, 107, 0.8); }
	100% { box-shadow: 0 16rpx 64rpx rgba(255, 107, 107, 0.4); }
}

.recorder-hint {
	color: #666;
	font-size: 24rpx;
	margin-bottom: 24rpx;
}

.recording-time {
	background: rgba(255, 107, 107, 0.1);
	color: #ff6b6b;
	padding: 16rpx 32rpx;
	border-radius: 32rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: inline-block;
	margin-bottom: 32rpx;
}

.record-result {
	margin-top: 32rpx;
}

.result-actions {
	display: flex;
	gap: 24rpx;
	justify-content: center;
	
	.btn-secondary,
	.btn-primary {
		padding: 24rpx 32rpx;
		border-radius: 32rpx;
		font-size: 28rpx;
		font-weight: 500;
		border: none;
		display: flex;
		align-items: center;
		
		.iconfont {
			margin-right: 8rpx;
			font-size: 32rpx;
		}
	}
	
	.btn-secondary {
		background: #f1f5f9;
		color: #64748b;
	}
	
	.btn-primary {
		background: linear-gradient(135deg, #16a34a, #22c55e);
		color: white;
		box-shadow: 0 8rpx 32rpx rgba(34, 197, 94, 0.3);
	}
}

/* 字体图标 */
.icon-microphone::before { content: '\e017'; }
.icon-stop::before { content: '\e022'; }
.icon-check::before { content: '\e021'; }
.icon-upload::before { content: '\e036'; }
</style>
