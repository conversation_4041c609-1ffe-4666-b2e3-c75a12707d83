<template>
	<view class="page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 页面内容 -->
		<view class="content">
			<!-- 头部标题 -->
			<view class="header">
				<text class="title">每日打卡</text>
				<text class="subtitle">坚持学习，成就更好的自己</text>
			</view>

			<!-- 今日打卡卡片 -->
			<view class="checkin-card card">
				<text class="card-title">今日学习</text>
				<view class="checkin-button" :class="{ completed: isCheckedIn }" @click="handleCheckin">
					<text class="iconfont" :class="isCheckedIn ? 'icon-check' : 'icon-calendar'"></text>
				</view>
				<text class="checkin-status" :class="{ completed: isCheckedIn }">
					{{ isCheckedIn ? '今日已打卡！' : '点击完成打卡' }}
				</text>
				<text class="checkin-desc">{{ checkinDesc }}</text>
				
				<view v-if="isCheckedIn" class="streak-info">
					<view class="streak-badge">
						<text class="iconfont icon-fire"></text>
						<text>连续打卡 {{ continuousDays }} 天</text>
					</view>
					<text class="milestone-text">距离下一个里程碑还有 {{ nextMilestone }} 天</text>
				</view>
			</view>

			<!-- 本月打卡日历 -->
			<view class="calendar-card card">
				<view class="calendar-header">
					<text class="calendar-title">{{ currentYear }}年{{ currentMonth }}月</text>
					<text class="calendar-stats">
						<text class="completed-days">{{ completedDays }}</text>/{{ totalDays }} 天
					</text>
				</view>
				
				<view class="calendar-grid">
					<!-- 星期标题 -->
					<view class="calendar-day header" v-for="day in weekDays" :key="day">{{ day }}</view>
					
					<!-- 日期 -->
					<view 
						class="calendar-day" 
						v-for="(day, index) in calendarDays" 
						:key="index"
						:class="getDayClass(day)"
					>
						{{ day.date || '' }}
					</view>
				</view>
				
				<view class="calendar-legend">
					<view class="legend-item">
						<view class="legend-dot checked"></view>
						<text>已打卡</text>
					</view>
					<view class="legend-item">
						<view class="legend-dot makeup"></view>
						<text>补打卡</text>
					</view>
					<view class="legend-item">
						<view class="legend-dot today"></view>
						<text>今天</text>
					</view>
				</view>
			</view>

			<!-- 打卡统计 -->
			<view class="stats-card card">
				<text class="stats-title">打卡统计</text>
				
				<view class="stat-item" v-for="stat in statistics" :key="stat.key">
					<view class="stat-left">
						<text class="iconfont" :class="stat.icon" :style="{ color: stat.color }"></text>
						<text class="stat-label">{{ stat.label }}</text>
					</view>
					<text class="stat-value">{{ stat.value }}</text>
				</view>
			</view>

			<!-- 补打卡按钮 -->
			<view class="makeup-section">
				<button class="makeup-btn" @click="showMakeupDialog">
					<text class="iconfont icon-redo"></text>
					使用补打卡机会
				</button>
				<text class="makeup-hint">本月还可补打卡 {{ remainingMakeups }} 次</text>
			</view>
		</view>
	</view>
</template>

<script>
import { userApi } from '@/api/index.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			loading: true,
			isCheckedIn: false,
			continuousDays: 0,
			nextMilestone: 7,
			checkinDesc: '今天还没有学习记录',
			currentYear: new Date().getFullYear(),
			currentMonth: new Date().getMonth() + 1,
			completedDays: 0,
			totalDays: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate(),
			remainingMakeups: 5,
			weekDays: ['日', '一', '二', '三', '四', '五', '六'],
			calendarDays: [],
			checkinHistory: [], // 打卡历史记录
			statistics: [
				{
					key: 'total',
					label: '总打卡天数',
					value: '156 天',
					icon: 'icon-calendar-check',
					color: '#16a34a'
				},
				{
					key: 'max_continuous',
					label: '最长连续',
					value: '28 天',
					icon: 'icon-fire',
					color: '#ea580c'
				},
				{
					key: 'remaining_makeup',
					label: '剩余补打卡',
					value: '4 次',
					icon: 'icon-redo',
					color: '#2563eb'
				},
				{
					key: 'completion_rate',
					label: '本月完成率',
					value: '71%',
					icon: 'icon-percentage',
					color: '#9333ea'
				}
			]
		}
	},
	
	onLoad() {
		this.initPage()
	},

	onShow() {
		this.loadCheckinData()
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.loadCheckinData().finally(() => {
			uni.stopPullDownRefresh()
		})
	},

	methods: {
		initPage() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight
		},

		// 加载打卡数据
		async loadCheckinData() {
			this.loading = true

			try {
				// 并行加载今日打卡状态和历史记录
				const [todayResponse, historyResponse] = await Promise.all([
					this.checkTodayCheckin(),
					this.getCheckinHistory()
				])

				// 生成日历
				this.generateCalendar()

			} catch (error) {
				console.error('加载打卡数据失败:', error)

				// 使用默认数据
				this.setDefaultData()
				this.generateCalendar()

				uni.showToast({
					title: '加载失败，显示离线数据',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		// 检查今日打卡状态
		async checkTodayCheckin() {
			try {
				const today = new Date().toISOString().split('T')[0]
				const response = await userApi.getCheckinHistory({
					date: today
				})

				if (response.success) {
					this.isCheckedIn = response.data.isCheckedToday || false
					this.continuousDays = response.data.continuousDays || 0
					this.checkinDesc = response.data.todayStudyDesc || '今天还没有学习记录'
					this.calculateNextMilestone()
				}

				return response
			} catch (error) {
				console.error('检查今日打卡状态失败:', error)
				return { success: false }
			}
		},

		// 获取打卡历史记录
		async getCheckinHistory() {
			try {
				const response = await userApi.getCheckinHistory({
					year: this.currentYear,
					month: this.currentMonth
				})

				if (response.success && response.data) {
					this.checkinHistory = response.data.checkinDays || []
					this.completedDays = this.checkinHistory.length
					this.remainingMakeups = response.data.remainingMakeups || 5
					this.updateStatistics(response.data.statistics)
				}

				return response
			} catch (error) {
				console.error('获取打卡历史失败:', error)
				return { success: false }
			}
		},

		// 设置默认数据
		setDefaultData() {
			this.isCheckedIn = false
			this.continuousDays = 0
			this.checkinHistory = []
			this.completedDays = 0
		},

		// 计算下一个里程碑
		calculateNextMilestone() {
			const milestones = [7, 15, 30, 50, 100, 200, 365]
			for (let milestone of milestones) {
				if (this.continuousDays < milestone) {
					this.nextMilestone = milestone - this.continuousDays
					break
				}
			}
		},
		
		generateCalendar() {
			const year = this.currentYear
			const month = this.currentMonth
			const firstDay = new Date(year, month - 1, 1).getDay()
			const daysInMonth = new Date(year, month, 0).getDate()
			const today = new Date().getDate()
			
			this.calendarDays = []
			
			// 添加空白日期
			for (let i = 0; i < firstDay; i++) {
				this.calendarDays.push({ date: null })
			}
			
			// 添加本月日期
			for (let date = 1; date <= daysInMonth; date++) {
				const dayData = {
					date,
					isToday: date === today,
					isChecked: this.isDateChecked(date),
					isMakeup: this.isDateMakeup(date)
				}
				this.calendarDays.push(dayData)
			}
		},
		
		isDateChecked(date) {
			// 检查指定日期是否已打卡
			return this.checkinHistory.some(record => {
				const recordDate = new Date(record.checkinDate).getDate()
				return recordDate === date && !record.isMakeup
			})
		},

		isDateMakeup(date) {
			// 检查指定日期是否为补打卡
			return this.checkinHistory.some(record => {
				const recordDate = new Date(record.checkinDate).getDate()
				return recordDate === date && record.isMakeup
			})
		},
		
		getDayClass(day) {
			const classes = []
			if (!day.date) return 'empty'
			if (day.isToday) classes.push('today')
			if (day.isChecked) classes.push('checked')
			if (day.isMakeup) classes.push('makeup')
			return classes.join(' ')
		},
		
		handleCheckin() {
			if (this.isCheckedIn) {
				uni.showToast({
					title: '今日已打卡',
					icon: 'success'
				})
				return
			}
			
			// 执行打卡
			this.performCheckin()
		},
		
		async performCheckin() {
			try {
				uni.showLoading({ title: '打卡中...' })

				// 调用打卡API
				const response = await userApi.checkin()

				if (response.success) {
					this.isCheckedIn = true
					this.continuousDays = response.data.continuousDays || this.continuousDays + 1
					this.checkinDesc = response.data.studyDesc || '完成今日学习任务'

					// 更新打卡历史
					const today = new Date()
					this.checkinHistory.push({
						checkinDate: today.toISOString(),
						isMakeup: false,
						studyDesc: this.checkinDesc
					})

					this.completedDays++
					this.calculateNextMilestone()

					uni.showToast({
						title: '打卡成功！',
						icon: 'success'
					})

					// 更新日历
					this.generateCalendar()
				} else {
					throw new Error(response.message || '打卡失败')
				}

			} catch (error) {
				console.error('打卡失败:', error)

				uni.showToast({
					title: error.message || '打卡失败，请重试',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		
		showMakeupDialog() {
			if (this.remainingMakeups <= 0) {
				uni.showToast({
					title: '本月补打卡次数已用完',
					icon: 'none'
				})
				return
			}
			
			uni.showActionSheet({
				itemList: ['昨天', '前天', '3天前', '4天前', '5天前'],
				success: (res) => {
					this.performMakeup(res.tapIndex + 1)
				}
			})
		},
		
		performMakeup(daysAgo) {
			uni.showModal({
				title: '确认补打卡',
				content: `确定要为${daysAgo}天前补打卡吗？将消耗1次补打卡机会。`,
				success: (res) => {
					if (res.confirm) {
						this.executeMakeup(daysAgo)
					}
				}
			})
		},
		
		async executeMakeup(daysAgo) {
			try {
				uni.showLoading({ title: '补打卡中...' })

				// 计算补打卡日期
				const makeupDate = new Date()
				makeupDate.setDate(makeupDate.getDate() - daysAgo)

				// 调用补打卡API
				const response = await userApi.checkin({
					isMakeup: true,
					makeupDate: makeupDate.toISOString().split('T')[0]
				})

				if (response.success) {
					this.remainingMakeups--

					// 更新打卡历史
					this.checkinHistory.push({
						checkinDate: makeupDate.toISOString(),
						isMakeup: true,
						studyDesc: '补打卡'
					})

					this.completedDays++

					uni.showToast({
						title: '补打卡成功！',
						icon: 'success'
					})

					// 更新统计数据和日历
					this.updateStatistics()
					this.generateCalendar()
				} else {
					throw new Error(response.message || '补打卡失败')
				}

			} catch (error) {
				console.error('补打卡失败:', error)

				uni.showToast({
					title: error.message || '补打卡失败，请重试',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 更新统计数据
		updateStatistics(apiStats) {
			if (apiStats) {
				// 使用API返回的统计数据
				this.statistics = [
					{
						key: 'total',
						label: '总打卡天数',
						value: `${apiStats.totalCheckinDays || 0} 天`,
						icon: 'icon-calendar-check',
						color: '#16a34a'
					},
					{
						key: 'max_continuous',
						label: '最长连续',
						value: `${apiStats.maxContinuousDays || 0} 天`,
						icon: 'icon-fire',
						color: '#ea580c'
					},
					{
						key: 'remaining_makeup',
						label: '剩余补打卡',
						value: `${this.remainingMakeups} 次`,
						icon: 'icon-redo',
						color: '#2563eb'
					},
					{
						key: 'completion_rate',
						label: '本月完成率',
						value: `${Math.round((this.completedDays / this.totalDays) * 100)}%`,
						icon: 'icon-percentage',
						color: '#9333ea'
					}
				]
			} else {
				// 只更新剩余补打卡次数
				this.statistics.forEach(stat => {
					if (stat.key === 'remaining_makeup') {
						stat.value = `${this.remainingMakeups} 次`
					} else if (stat.key === 'completion_rate') {
						stat.value = `${Math.round((this.completedDays / this.totalDays) * 100)}%`
					}
				})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.status-bar {
	background: transparent;
}

.content {
	padding: 48rpx 40rpx 200rpx;
}

.header {
	text-align: center;
	margin-bottom: 48rpx;
	
	.title {
		display: block;
		color: white;
		font-size: 48rpx;
		font-weight: bold;
		margin-bottom: 16rpx;
	}
	
	.subtitle {
		color: rgba(255, 255, 255, 0.8);
		font-size: 32rpx;
	}
}

.checkin-card {
	text-align: center;
	margin-bottom: 32rpx;
	
	.card-title {
		display: block;
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 32rpx;
	}
	
	.checkin-button {
		width: 240rpx;
		height: 240rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, #ff9a9e, #fecfef);
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 32rpx;
		box-shadow: 0 16rpx 64rpx rgba(255, 154, 158, 0.4);
		transition: all 0.3s ease;
		
		&.completed {
			background: linear-gradient(135deg, #a8e6cf, #88d8a3);
		}
		
		.iconfont {
			color: white;
			font-size: 80rpx;
		}
	}
	
	.checkin-status {
		display: block;
		font-size: 36rpx;
		font-weight: 500;
		color: #ff9a9e;
		margin-bottom: 16rpx;
		
		&.completed {
			color: #16a34a;
		}
	}
	
	.checkin-desc {
		display: block;
		color: #666;
		font-size: 28rpx;
		margin-bottom: 48rpx;
	}
	
	.streak-info {
		.streak-badge {
			background: linear-gradient(135deg, #667eea, #764ba2);
			color: white;
			padding: 16rpx 32rpx;
			border-radius: 40rpx;
			font-size: 28rpx;
			font-weight: bold;
			display: inline-flex;
			align-items: center;
			margin-bottom: 16rpx;
			
			.iconfont {
				margin-right: 16rpx;
				font-size: 32rpx;
			}
		}
		
		.milestone-text {
			display: block;
			color: #666;
			font-size: 24rpx;
		}
	}
}

.calendar-card {
	margin-bottom: 32rpx;
	
	.calendar-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 32rpx;
		
		.calendar-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
		
		.calendar-stats {
			color: #666;
			font-size: 28rpx;
			
			.completed-days {
				color: #16a34a;
				font-weight: 500;
			}
		}
	}
	
	.calendar-grid {
		display: grid;
		grid-template-columns: repeat(7, 1fr);
		gap: 16rpx;
		margin-bottom: 32rpx;
		
		.calendar-day {
			aspect-ratio: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 24rpx;
			font-size: 28rpx;
			font-weight: 500;
			background: rgba(255,255,255,0.7);
			color: #666;
			
			&.header {
				background: transparent;
				color: #333;
				font-weight: bold;
			}
			
			&.empty {
				background: transparent;
			}
			
			&.checked {
				background: linear-gradient(135deg, #a8e6cf, #88d8a3);
				color: white;
			}
			
			&.today {
				background: linear-gradient(135deg, #ff9a9e, #fecfef);
				color: white;
				border: 4rpx solid white;
			}
			
			&.makeup {
				background: linear-gradient(135deg, #ffd93d, #ff6b6b);
				color: white;
			}
		}
	}
	
	.calendar-legend {
		display: flex;
		justify-content: center;
		gap: 32rpx;
		
		.legend-item {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #666;
			
			.legend-dot {
				width: 24rpx;
				height: 24rpx;
				border-radius: 50%;
				margin-right: 8rpx;
				
				&.checked {
					background: linear-gradient(135deg, #a8e6cf, #88d8a3);
				}
				
				&.makeup {
					background: linear-gradient(135deg, #ffd93d, #ff6b6b);
				}
				
				&.today {
					background: linear-gradient(135deg, #ff9a9e, #fecfef);
				}
			}
		}
	}
}

.stats-card {
	margin-bottom: 32rpx;
	
	.stats-title {
		display: block;
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 32rpx;
	}
	
	.stat-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #f1f5f9;
		
		&:last-child {
			border-bottom: none;
		}
		
		.stat-left {
			display: flex;
			align-items: center;
			
			.iconfont {
				font-size: 32rpx;
				margin-right: 24rpx;
			}
			
			.stat-label {
				color: #333;
				font-size: 28rpx;
			}
		}
		
		.stat-value {
			font-weight: bold;
			color: #333;
			font-size: 28rpx;
		}
	}
}

.makeup-section {
	text-align: center;
	
	.makeup-btn {
		width: 100%;
		background: linear-gradient(135deg, #f59e0b, #ea580c);
		color: white;
		padding: 32rpx;
		border-radius: 40rpx;
		font-weight: bold;
		font-size: 32rpx;
		box-shadow: 0 16rpx 64rpx rgba(245, 158, 11, 0.3);
		border: none;
		margin-bottom: 16rpx;
		
		.iconfont {
			margin-right: 16rpx;
			font-size: 36rpx;
		}
	}
	
	.makeup-hint {
		color: rgba(255, 255, 255, 0.8);
		font-size: 24rpx;
	}
}

/* 字体图标 */
.icon-calendar::before { content: '\e024'; }
.icon-calendar-check::before { content: '\e025'; }
.icon-percentage::before { content: '\e026'; }
</style>
