<view class="debug-container data-v-12bbb3dc"><view class="header data-v-12bbb3dc"><text class="title data-v-12bbb3dc">🐛 调试面板</text><text class="subtitle data-v-12bbb3dc">应用状态和错误信息</text></view><view class="section data-v-12bbb3dc"><view class="section-header data-v-12bbb3dc"><text class="section-title data-v-12bbb3dc">📱 应用信息</text><button class="refresh-btn data-v-12bbb3dc" bindtap="{{a}}">刷新</button></view><view class="info-grid data-v-12bbb3dc"><view class="info-item data-v-12bbb3dc"><text class="label data-v-12bbb3dc">版本号</text><text class="value data-v-12bbb3dc">{{b}}</text></view><view class="info-item data-v-12bbb3dc"><text class="label data-v-12bbb3dc">平台</text><text class="value data-v-12bbb3dc">{{c}}</text></view><view class="info-item data-v-12bbb3dc"><text class="label data-v-12bbb3dc">系统版本</text><text class="value data-v-12bbb3dc">{{d}}</text></view><view class="info-item data-v-12bbb3dc"><text class="label data-v-12bbb3dc">屏幕尺寸</text><text class="value data-v-12bbb3dc">{{e}}</text></view></view></view><view class="section data-v-12bbb3dc"><view class="section-header data-v-12bbb3dc"><text class="section-title data-v-12bbb3dc">🌐 全局数据</text><button class="refresh-btn data-v-12bbb3dc" bindtap="{{f}}">刷新</button></view><view class="json-viewer data-v-12bbb3dc"><text class="json-content data-v-12bbb3dc">{{g}}</text></view></view><view class="section data-v-12bbb3dc"><view class="section-header data-v-12bbb3dc"><text class="section-title data-v-12bbb3dc">🔐 登录状态</text><view class="{{['status-indicator', 'data-v-12bbb3dc', h && 'active']}}"></view></view><view class="info-grid data-v-12bbb3dc"><view class="info-item data-v-12bbb3dc"><text class="label data-v-12bbb3dc">登录状态</text><text class="{{['value', 'data-v-12bbb3dc', j && 'success', k && 'error']}}">{{i}}</text></view><view wx:if="{{l}}" class="info-item data-v-12bbb3dc"><text class="label data-v-12bbb3dc">Token</text><text class="value token data-v-12bbb3dc">{{m}}</text></view><view wx:if="{{n}}" class="info-item data-v-12bbb3dc"><text class="label data-v-12bbb3dc">用户信息</text><text class="value data-v-12bbb3dc">{{o}}</text></view></view></view><view class="section data-v-12bbb3dc"><view class="section-header data-v-12bbb3dc"><text class="section-title data-v-12bbb3dc">📊 错误统计</text><button class="clear-btn data-v-12bbb3dc" bindtap="{{p}}">清空</button></view><view class="error-stats data-v-12bbb3dc"><view class="stat-item data-v-12bbb3dc"><text class="stat-number data-v-12bbb3dc">{{q}}</text><text class="stat-label data-v-12bbb3dc">总错误数</text></view><view wx:for="{{r}}" wx:for-item="count" wx:key="d" class="stat-item data-v-12bbb3dc"><text class="{{['stat-number', 'data-v-12bbb3dc', count.b]}}">{{count.a}}</text><text class="stat-label data-v-12bbb3dc">{{count.c}}</text></view></view></view><view class="section data-v-12bbb3dc"><view class="section-header data-v-12bbb3dc"><text class="section-title data-v-12bbb3dc">🚨 最近错误</text><text class="count-badge data-v-12bbb3dc">{{s}}</text></view><view class="error-list data-v-12bbb3dc"><view wx:for="{{t}}" wx:for-item="error" wx:key="h" class="error-item data-v-12bbb3dc"><view class="error-header data-v-12bbb3dc"><text class="error-type data-v-12bbb3dc">{{error.a}}</text><text class="{{['error-level', 'data-v-12bbb3dc', error.c]}}">{{error.b}}</text><text class="error-time data-v-12bbb3dc">{{error.d}}</text></view><text class="error-message data-v-12bbb3dc">{{error.e}}</text><text wx:if="{{error.f}}" class="error-page data-v-12bbb3dc">页面: {{error.g}}</text></view><view wx:if="{{v}}" class="empty-state data-v-12bbb3dc"><text class="data-v-12bbb3dc">🎉 暂无错误记录</text></view></view></view><view class="actions data-v-12bbb3dc"><button class="action-btn primary data-v-12bbb3dc" bindtap="{{w}}">导出调试信息</button><button class="action-btn data-v-12bbb3dc" bindtap="{{x}}">测试错误</button><button class="action-btn data-v-12bbb3dc" bindtap="{{y}}">测试请求</button><button class="action-btn data-v-12bbb3dc" bindtap="{{z}}">API接口测试</button></view></view>