"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const API_CONFIG = {
  // 后台接口基础URL - 统一配置
  BASE_URL: "http://localhost:8080/jeecg-boot",
  // 请求超时时间
  TIMEOUT: 1e4,
  // 重试次数
  RETRY_COUNT: 3,
  // 重试延迟（毫秒）
  RETRY_DELAY: 1e3
};
const getApiBaseUrl = () => {
  return API_CONFIG.BASE_URL;
};
const getApiUrl = (endpoint = "") => {
  const baseUrl = getApiBaseUrl();
  const cleanEndpoint = endpoint.replace(/^\/+/, "");
  if (!cleanEndpoint) {
    return baseUrl;
  }
  return `${baseUrl}/${cleanEndpoint}`;
};
const getRequestHeaders = (customHeaders = {}) => {
  const headers = __spreadValues({
    "Content-Type": "application/json",
    "Accept": "application/json"
  }, customHeaders);
  const token = common_vendor.index.getStorageSync("X-Access-Token") || common_vendor.index.getStorageSync("token");
  if (token) {
    headers["X-Access-Token"] = token;
    headers["X-Access-Client"] = "miniApp";
    headers["X-Tenant-Id"] = "";
  }
  return headers;
};
const API_ENDPOINTS = {
  // 用户相关 - 使用jeecg-boot标准接口
  USER: {
    LOGIN: "/sys/login",
    // jeecg-boot标准登录接口
    LOGOUT: "/sys/logout",
    // jeecg-boot标准登出接口
    INFO: "/sys/user/userInfo",
    // jeecg-boot获取用户信息
    DEPART_INFO: "/sys/user/departInfo",
    // jeecg-boot获取部门信息
    UPDATE_PASSWORD: "/sys/user/updatePassword",
    // jeecg-boot修改密码
    UPLOAD_AVATAR: "/sys/common/upload",
    // jeecg-boot文件上传
    // 验证码相关接口
    CAPTCHA: "/sys/randomImage/{key}",
    // jeecg-boot获取验证码（带path参数）
    CHECK_CAPTCHA: "/sys/checkCaptcha",
    // jeecg-boot验证验证码
    // 自定义扩展接口（基于jeecg-boot）
    PROFILE: "/app/user/profile",
    // 获取用户扩展信息
    UPDATE_PROFILE: "/app/user/profile",
    // 更新用户扩展信息
    CHECKIN: "/app/user/checkin",
    // 用户签到
    CHECKIN_HISTORY: "/app/user/checkin/history",
    // 签到历史
    STATISTICS: "/app/user/statistics"
    // 学习统计
  },
  // 课程相关
  COURSE: {
    LIST: "/app/course/list",
    DETAIL: "/app/course/{id}",
    LESSONS: "/app/course/{id}/lessons",
    FAVORITE: "/app/course/{id}/favorite"
  },
  // 课时相关
  LESSON: {
    DETAIL: "/app/lesson/{id}",
    START: "/app/lesson/{id}/start",
    COMPLETE: "/app/lesson/{id}/complete"
  },
  // 练习相关
  PRACTICE: {
    SUBMIT: "/app/practice/submit",
    SUBMIT_BASE64: "/app/practice/submitBase64",
    HISTORY: "/app/practice/history",
    STATISTICS: "/app/practice/statistics",
    LANGUAGES: "/app/practice/supportedLanguages",
    CATEGORIES: "/app/practice/supportedCategories",
    CONFIG: "/app/practice/evaluationConfig"
  },
  // 排行榜相关
  LEADERBOARD: {
    POINTS: "/app/leaderboard/points",
    STUDY_TIME: "/app/leaderboard/studytime",
    CHECKIN: "/app/leaderboard/checkin"
  },
  // 支付相关
  PAYMENT: {
    CREATE: "/api/v1/payment/create",
    STATUS: "/api/v1/payment/status/{id}",
    METHODS: "/api/v1/payment/methods"
  },
  // AI相关
  AI: {
    EVALUATE: "/api/v1/ai/evaluate",
    HISTORY: "/api/v1/ai/history"
  },
  // 系统相关
  SYSTEM: {
    CONFIG: "/api/v1/system/config",
    VERSION: "/api/v1/system/version",
    FEEDBACK: "/api/v1/system/feedback",
    HELP: "/api/v1/system/help"
  }
};
const replaceUrlParams = (url, params = {}) => {
  let result = url;
  Object.keys(params).forEach((key) => {
    result = result.replace(`{${key}}`, params[key]);
  });
  return result;
};
const buildQueryString = (params = {}) => {
  const query = Object.keys(params).filter((key) => params[key] !== void 0 && params[key] !== null && params[key] !== "").map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join("&");
  return query ? `?${query}` : "";
};
const buildApiUrl = (endpoint, urlParams = {}, queryParams = {}) => {
  const url = replaceUrlParams(endpoint, urlParams);
  const queryString = buildQueryString(queryParams);
  return getApiUrl(url) + queryString;
};
const DEBUG_CONFIG = {
  // 是否启用调试
  enabled: false,
  // 是否显示控制台日志
  console: false,
  // 是否显示请求详情
  request: false,
  // 是否显示响应详情
  response: false
};
const api = {
  API_CONFIG,
  API_ENDPOINTS,
  DEBUG_CONFIG,
  getApiBaseUrl,
  getApiUrl,
  getRequestHeaders,
  replaceUrlParams,
  buildQueryString,
  buildApiUrl
};
exports.API_CONFIG = API_CONFIG;
exports.API_ENDPOINTS = API_ENDPOINTS;
exports.DEBUG_CONFIG = DEBUG_CONFIG;
exports.buildApiUrl = buildApiUrl;
exports.buildQueryString = buildQueryString;
exports.default = api;
exports.getApiBaseUrl = getApiBaseUrl;
exports.getApiUrl = getApiUrl;
exports.getRequestHeaders = getRequestHeaders;
exports.replaceUrlParams = replaceUrlParams;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/api.js.map
