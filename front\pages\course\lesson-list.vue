<template>
	<view class="lesson-list-container">
		<!-- 页面头部 -->
		<view class="page-header">
			<view class="header-content">
				<text class="course-title">{{ courseInfo.title || '课程' }}</text>
				<text class="page-subtitle">课时一览</text>
			</view>
		</view>

		<!-- 课程信息卡片 -->
		<view class="course-info-card">
			<view class="course-meta">
				<view class="meta-item">
					<text class="iconfont icon-clock"></text>
					<text class="meta-text">总时长 {{ totalDuration }}分钟</text>
				</view>
				<view class="meta-item">
					<text class="iconfont icon-list"></text>
					<text class="meta-text">共 {{ lessons.length }} 课时</text>
				</view>
				<view class="meta-item">
					<text class="iconfont icon-check"></text>
					<text class="meta-text">已完成 {{ completedCount }} 课时</text>
				</view>
			</view>
			<view class="progress-section">
				<view class="progress-bar">
					<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
				</view>
				<text class="progress-text">{{ progressPercent }}% 完成</text>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-item" v-for="i in 5" :key="i">
				<view class="timeline-skeleton">
					<view class="node-skeleton"></view>
					<view class="card-skeleton"></view>
				</view>
			</view>
		</view>

		<!-- 课时时间轴列表 -->
		<view v-else-if="lessons.length > 0" class="timeline-container">
			<view 
				class="timeline-item" 
				v-for="(lesson, index) in lessons" 
				:key="lesson.id"
				:class="{ 'last-item': index === lessons.length - 1 }"
			>
				<!-- 时间轴节点 -->
				<view class="timeline-node" :class="getNodeClass(lesson)">
					<view class="node-circle">
						<text v-if="lesson.isCompleted" class="iconfont icon-check node-icon"></text>
						<text v-else-if="lesson.isCurrent" class="iconfont icon-play node-icon"></text>
						<text v-else-if="lesson.isLocked" class="iconfont icon-lock node-icon"></text>
					</view>
					<view v-if="index < lessons.length - 1" class="node-line"></view>
				</view>

				<!-- 课时卡片 -->
				<view 
					class="lesson-card" 
					:class="getLessonCardClass(lesson)"
					@click="goToLessonDetail(lesson)"
				>
					<!-- 完成状态标记 -->
					<view v-if="lesson.isCompleted" class="completed-badge">
						<text class="iconfont icon-check"></text>
					</view>

					<!-- 锁定状态标记 -->
					<view v-if="lesson.isLocked" class="locked-badge">
						<text class="iconfont icon-lock"></text>
					</view>

					<!-- 课时内容 -->
					<view class="lesson-content">
						<view class="lesson-header">
							<text class="lesson-number">第{{ lesson.order }}课时</text>
							<text class="lesson-duration">{{ lesson.duration }}分钟</text>
						</view>
						<text class="lesson-title">{{ lesson.title }}</text>
						<text class="lesson-description">{{ lesson.description }}</text>
						
						<!-- 课时状态 -->
						<view class="lesson-status">
							<view v-if="lesson.isCompleted" class="status-tag completed">
								<text class="iconfont icon-check"></text>
								<text>已完成</text>
							</view>
							<view v-else-if="lesson.isCurrent" class="status-tag current">
								<text class="iconfont icon-play"></text>
								<text>正在学习</text>
							</view>
							<view v-else-if="lesson.isLocked" class="status-tag locked">
								<text class="iconfont icon-lock"></text>
								<text>未解锁</text>
							</view>
							<view v-else class="status-tag available">
								<text class="iconfont icon-play"></text>
								<text>可学习</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-else class="empty-state">
			<text class="iconfont icon-book empty-icon"></text>
			<text class="empty-text">暂无课时内容</text>
			<text class="empty-desc">课程内容正在准备中</text>
		</view>
	</view>
</template>

<script>
import { courseApi } from '@/api/index.js'
import authMixin from '@/mixins/auth-mixin.js'

export default {
	mixins: [authMixin],
	
	data() {
		return {
			loading: false,
			courseId: '',
			courseInfo: {},
			lessons: []
		}
	},
	
	computed: {
		// 总时长
		totalDuration() {
			return this.lessons.reduce((total, lesson) => total + (lesson.duration || 0), 0)
		},
		
		// 已完成课时数
		completedCount() {
			return this.lessons.filter(lesson => lesson.isCompleted).length
		},
		
		// 完成百分比
		progressPercent() {
			if (this.lessons.length === 0) return 0
			return Math.round((this.completedCount / this.lessons.length) * 100)
		}
	},
	
	onLoad(options) {
		console.log('📚 课时一览页面加载:', options)
		
		this.courseId = options.id || options.courseId
		
		if (!this.courseId) {
			console.error('❌ 缺少课程ID参数')
			uni.showToast({
				title: '参数错误',
				icon: 'none'
			})
			return
		}
		
		// 设置页面标题
		if (options.title) {
			const courseTitle = decodeURIComponent(options.title)
			uni.setNavigationBarTitle({
				title: `${courseTitle} - 课时一览`
			})
			this.courseInfo.title = courseTitle
		}
		
		this.loadLessonList()
	},
	
	methods: {
		// 加载课时列表
		async loadLessonList() {
			this.loading = true
			
			try {
				console.log('📖 加载课时列表:', this.courseId)

				const response = await courseApi.getLessonsByCourseId(this.courseId)

				if (response.success && response.data) {
					this.lessons = this.formatLessonData(response.data.records || response.data)
					this.courseInfo = response.data.courseInfo || this.courseInfo
					console.log('✅ 课时列表加载成功:', this.lessons.length, '个课时')
				} else {
					console.log('⚠️ 暂无课时数据')
					this.lessons = []
				}
			} catch (error) {
				console.error('❌ 加载课时列表失败:', error)
				this.lessons = []

				uni.showToast({
					title: '课时加载失败，请重试',
					icon: 'none',
					duration: 2000
				})
			} finally {
				this.loading = false
			}
		},
		
		// 格式化课时数据
		formatLessonData(lessons) {
			return lessons.map((lesson, index) => ({
				id: lesson.id || `lesson_${index + 1}`,
				title: lesson.title || lesson.name,
				description: lesson.description || lesson.desc,
				duration: lesson.duration || 30,
				isCompleted: lesson.isCompleted || false,
				isCurrent: lesson.isCurrent || false,
				isLocked: lesson.isLocked || false,
				order: lesson.order || index + 1
			}))
		},
		

		
		// 获取时间轴节点样式类
		getNodeClass(lesson) {
			if (lesson.isCompleted) return 'completed'
			if (lesson.isCurrent) return 'current'
			if (lesson.isLocked) return 'locked'
			return 'available'
		},
		
		// 获取课时卡片样式类
		getLessonCardClass(lesson) {
			const classes = []
			if (lesson.isCompleted) classes.push('completed')
			if (lesson.isCurrent) classes.push('current')
			if (lesson.isLocked) classes.push('locked')
			return classes.join(' ')
		},
		
		// 跳转到课时详情
		goToLessonDetail(lesson) {
			console.log('📖 跳转到课时详情:', lesson.title)
			
			if (lesson.isLocked) {
				uni.showModal({
					title: '课时未解锁',
					content: '请先完成前面的课时学习',
					showCancel: false
				})
				return
			}
			
			// 检查VIP权限（如果需要）
			if (lesson.requireVip && !this.isAuthenticated) {
				uni.showModal({
					title: '需要登录',
					content: '请先登录后再学习课程',
					confirmText: '去登录',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login/login'
							})
						}
					}
				})
				return
			}
			
			// 跳转到课程详情页面
			uni.navigateTo({
				url: `/pages/course/detail?id=${this.courseId}&lessonId=${lesson.id}&title=${encodeURIComponent(lesson.title)}`,
				success: () => {
					console.log('✅ 成功跳转到课时详情')
				},
				fail: (error) => {
					console.error('❌ 跳转课时详情失败:', error)
					uni.showToast({
						title: '跳转失败',
						icon: 'none'
					})
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.lesson-list-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20rpx;
}

.page-header {
	margin-bottom: 32rpx;
	
	.header-content {
		text-align: center;
		
		.course-title {
			display: block;
			font-size: 40rpx;
			font-weight: bold;
			color: white;
			margin-bottom: 8rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
		}
		
		.page-subtitle {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}

.course-info-card {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 40rpx;
	backdrop-filter: blur(20rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	
	.course-meta {
		display: flex;
		justify-content: space-between;
		margin-bottom: 24rpx;
		
		.meta-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			flex: 1;
			
			.iconfont {
				font-size: 32rpx;
				color: white;
				margin-bottom: 8rpx;
			}
			
			.meta-text {
				font-size: 24rpx;
				color: rgba(255, 255, 255, 0.9);
				text-align: center;
			}
		}
	}
	
	.progress-section {
		.progress-bar {
			width: 100%;
			height: 12rpx;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 6rpx;
			overflow: hidden;
			margin-bottom: 12rpx;
			
			.progress-fill {
				height: 100%;
				background: linear-gradient(90deg, #10b981, #059669);
				border-radius: 6rpx;
				transition: width 0.3s ease;
			}
		}
		
		.progress-text {
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.8);
			text-align: center;
			display: block;
		}
	}
}

.loading-container {
	.loading-item {
		margin-bottom: 40rpx;
		
		.timeline-skeleton {
			display: flex;
			align-items: flex-start;
			
			.node-skeleton {
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.2);
				margin-right: 32rpx;
				margin-top: 20rpx;
				animation: skeleton-loading 1.5s ease-in-out infinite;
			}
			
			.card-skeleton {
				flex: 1;
				height: 160rpx;
				background: rgba(255, 255, 255, 0.1);
				border-radius: 16rpx;
				animation: skeleton-loading 1.5s ease-in-out infinite;
			}
		}
	}
}

.timeline-container {
	.timeline-item {
		display: flex;
		margin-bottom: 40rpx;
		
		&.last-item {
			margin-bottom: 0;
			
			.timeline-node .node-line {
				display: none;
			}
		}
		
		.timeline-node {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-right: 32rpx;
			
			.node-circle {
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				z-index: 2;
				
				.node-icon {
					font-size: 20rpx;
					color: white;
				}
			}
			
			.node-line {
				width: 4rpx;
				flex: 1;
				min-height: 80rpx;
				margin-top: 8rpx;
			}
			
			&.completed {
				.node-circle {
					background: linear-gradient(135deg, #10b981, #059669);
					border: 3rpx solid white;
				}
				
				.node-line {
					background: #10b981;
				}
			}
			
			&.current {
				.node-circle {
					background: linear-gradient(135deg, #3b82f6, #1d4ed8);
					border: 3rpx solid white;
					animation: pulse 2s infinite;
				}
				
				.node-line {
					background: rgba(255, 255, 255, 0.3);
				}
			}
			
			&.available {
				.node-circle {
					background: rgba(255, 255, 255, 0.8);
					border: 3rpx solid white;
				}
				
				.node-line {
					background: rgba(255, 255, 255, 0.3);
				}
			}
			
			&.locked {
				.node-circle {
					background: rgba(255, 255, 255, 0.3);
					border: 3rpx solid rgba(255, 255, 255, 0.5);
				}
				
				.node-line {
					background: rgba(255, 255, 255, 0.2);
				}
			}
		}
		
		.lesson-card {
			flex: 1;
			background: white;
			border-radius: 16rpx;
			padding: 24rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
			position: relative;
			transition: all 0.3s ease;
			
			&:active {
				transform: scale(0.98);
			}
			
			&.completed {
				border-left: 6rpx solid #10b981;
			}
			
			&.current {
				border-left: 6rpx solid #3b82f6;
				background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(29, 78, 216, 0.05));
			}
			
			&.locked {
				opacity: 0.6;
				background: #f5f5f5;
			}
			
			.completed-badge {
				position: absolute;
				top: -8rpx;
				right: -8rpx;
				width: 32rpx;
				height: 32rpx;
				background: linear-gradient(135deg, #10b981, #059669);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 3rpx solid white;
				
				.iconfont {
					font-size: 16rpx;
					color: white;
				}
			}
			
			.locked-badge {
				position: absolute;
				top: -8rpx;
				right: -8rpx;
				width: 32rpx;
				height: 32rpx;
				background: #9ca3af;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 3rpx solid white;
				
				.iconfont {
					font-size: 16rpx;
					color: white;
				}
			}
			
			.lesson-content {
				.lesson-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 12rpx;
					
					.lesson-number {
						font-size: 24rpx;
						color: #666;
						font-weight: bold;
					}
					
					.lesson-duration {
						font-size: 22rpx;
						color: #999;
					}
				}
				
				.lesson-title {
					display: block;
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 8rpx;
				}
				
				.lesson-description {
					display: block;
					font-size: 26rpx;
					color: #666;
					line-height: 1.5;
					margin-bottom: 16rpx;
				}
				
				.lesson-status {
					.status-tag {
						display: inline-flex;
						align-items: center;
						padding: 6rpx 12rpx;
						border-radius: 12rpx;
						font-size: 22rpx;
						
						.iconfont {
							font-size: 18rpx;
							margin-right: 6rpx;
						}
						
						&.completed {
							background: linear-gradient(135deg, #dcfce7, #bbf7d0);
							color: #059669;
						}
						
						&.current {
							background: linear-gradient(135deg, #dbeafe, #bfdbfe);
							color: #1d4ed8;
						}
						
						&.available {
							background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
							color: #374151;
						}
						
						&.locked {
							background: linear-gradient(135deg, #f9fafb, #f3f4f6);
							color: #9ca3af;
						}
					}
				}
			}
		}
	}
}

.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
	
	.empty-icon {
		font-size: 120rpx;
		color: rgba(255, 255, 255, 0.3);
		margin-bottom: 24rpx;
	}
	
	.empty-text {
		display: block;
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 12rpx;
	}
	
	.empty-desc {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.6);
	}
}

@keyframes skeleton-loading {
	0%, 100% {
		opacity: 0.3;
	}
	50% {
		opacity: 0.7;
	}
}

@keyframes pulse {
	0%, 100% {
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
	}
	50% {
		transform: scale(1.05);
		box-shadow: 0 0 0 10rpx rgba(59, 130, 246, 0);
	}
}
</style>
