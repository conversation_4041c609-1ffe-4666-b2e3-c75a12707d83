<template>
	<view class="debug-container">
		<view class="header">
			<text class="title">🐛 调试面板</text>
			<text class="subtitle">应用状态和错误信息</text>
		</view>
		
		<!-- 应用信息 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">📱 应用信息</text>
				<button class="refresh-btn" @tap="refreshAppInfo">刷新</button>
			</view>
			<view class="info-grid">
				<view class="info-item">
					<text class="label">版本号</text>
					<text class="value">{{ appInfo.version }}</text>
				</view>
				<view class="info-item">
					<text class="label">平台</text>
					<text class="value">{{ appInfo.platform }}</text>
				</view>
				<view class="info-item">
					<text class="label">系统版本</text>
					<text class="value">{{ appInfo.system }}</text>
				</view>
				<view class="info-item">
					<text class="label">屏幕尺寸</text>
					<text class="value">{{ appInfo.screenSize }}</text>
				</view>
			</view>
		</view>
		
		<!-- 全局数据 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">🌐 全局数据</text>
				<button class="refresh-btn" @tap="refreshGlobalData">刷新</button>
			</view>
			<view class="json-viewer">
				<text class="json-content">{{ globalDataStr }}</text>
			</view>
		</view>
		
		<!-- 登录状态 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">🔐 登录状态</text>
				<view class="status-indicator" :class="{ active: loginStatus.isLogin }"></view>
			</view>
			<view class="info-grid">
				<view class="info-item">
					<text class="label">登录状态</text>
					<text class="value" :class="{ success: loginStatus.isLogin, error: !loginStatus.isLogin }">
						{{ loginStatus.isLogin ? '已登录' : '未登录' }}
					</text>
				</view>
				<view class="info-item" v-if="loginStatus.token">
					<text class="label">Token</text>
					<text class="value token">{{ loginStatus.tokenPreview }}</text>
				</view>
				<view class="info-item" v-if="loginStatus.userInfo">
					<text class="label">用户信息</text>
					<text class="value">{{ loginStatus.userInfo.nickname || '未设置' }}</text>
				</view>
			</view>
		</view>
		
		<!-- 错误统计 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">📊 错误统计</text>
				<button class="clear-btn" @tap="clearErrors">清空</button>
			</view>
			<view class="error-stats">
				<view class="stat-item">
					<text class="stat-number">{{ errorStats.total }}</text>
					<text class="stat-label">总错误数</text>
				</view>
				<view class="stat-item" v-for="(count, level) in errorStats.byLevel" :key="level">
					<text class="stat-number" :class="level.toLowerCase()">{{ count }}</text>
					<text class="stat-label">{{ getLevelName(level) }}</text>
				</view>
			</view>
		</view>
		
		<!-- 最近错误 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">🚨 最近错误</text>
				<text class="count-badge">{{ errorStats.recent.length }}</text>
			</view>
			<view class="error-list">
				<view class="error-item" v-for="error in errorStats.recent" :key="error.id">
					<view class="error-header">
						<text class="error-type">{{ error.type }}</text>
						<text class="error-level" :class="error.level.toLowerCase()">{{ error.level }}</text>
						<text class="error-time">{{ formatTime(error.timestamp) }}</text>
					</view>
					<text class="error-message">{{ error.message }}</text>
					<text class="error-page" v-if="error.context.page">页面: {{ error.context.page }}</text>
				</view>
				<view class="empty-state" v-if="errorStats.recent.length === 0">
					<text>🎉 暂无错误记录</text>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="actions">
			<button class="action-btn primary" @tap="exportDebugInfo">导出调试信息</button>
			<button class="action-btn" @tap="testError">测试错误</button>
			<button class="action-btn" @tap="testRequest">测试请求</button>
			<button class="action-btn" @tap="goToApiTest">API接口测试</button>
		</view>
	</view>
</template>

<script>
import { getGlobalData, getUserInfo, getToken, isLoggedIn } from '@/utils/common.js'

export default {
	data() {
		return {
			appInfo: {},
			globalData: {},
			loginStatus: {},
			errorStats: {
				total: 0,
				byLevel: {},
				byType: {},
				recent: []
			}
		}
	},
	
	computed: {
		globalDataStr() {
			return JSON.stringify(this.globalData, null, 2)
		}
	},
	
	onLoad() {
		this.loadDebugInfo()
	},
	
	onShow() {
		this.refreshErrorStats()
	},
	
	methods: {
		loadDebugInfo() {
			this.refreshAppInfo()
			this.refreshGlobalData()
			this.refreshLoginStatus()
			this.refreshErrorStats()
		},
		
		refreshAppInfo() {
			try {
				const systemInfo = uni.getSystemInfoSync()
				this.appInfo = {
					version: getGlobalData('version') || '1.0.0',
					platform: systemInfo.platform || 'unknown',
					system: `${systemInfo.system} ${systemInfo.version}`,
					screenSize: `${systemInfo.screenWidth}x${systemInfo.screenHeight}`
				}
			} catch (error) {
				console.error('获取应用信息失败:', error)
			}
		},
		
		refreshGlobalData() {
			try {
				this.globalData = getGlobalData()
			} catch (error) {
				console.error('获取全局数据失败:', error)
				this.globalData = { error: '获取失败' }
			}
		},

		refreshLoginStatus() {
			try {
				const token = getToken()
				const userInfo = getUserInfo()
				const isLogin = isLoggedIn()

				this.loginStatus = {
					isLogin,
					token,
					tokenPreview: token ? `${token.substring(0, 10)}...` : null,
					userInfo
				}
			} catch (error) {
				console.error('获取登录状态失败:', error)
				this.loginStatus = { isLogin: false }
			}
		},

		refreshErrorStats() {
			// 简化错误统计，只显示基本信息
			this.errorStats = {
				total: 0,
				byLevel: {},
				byType: {},
				recent: []
			}
		},

		clearErrors() {
			uni.showToast({
				title: '错误记录已清空',
				icon: 'success'
			})
			this.refreshErrorStats()
		},
		
		exportDebugInfo() {
			try {
				const debugInfo = {
					timestamp: new Date().toISOString(),
					appInfo: this.appInfo,
					globalData: this.globalData,
					loginStatus: this.loginStatus,
					errorStats: this.errorStats
				}
				
				const debugStr = JSON.stringify(debugInfo, null, 2)
				
				// 复制到剪贴板
				uni.setClipboardData({
					data: debugStr,
					success: () => {
						uni.showToast({
							title: '调试信息已复制到剪贴板',
							icon: 'success'
						})
					}
				})
			} catch (error) {
				handleError(error, ErrorTypes.SYSTEM_ERROR, ErrorLevels.MEDIUM)
			}
		},
		
		testError() {
			// 简单的错误测试
			console.error('这是一个测试错误')
			uni.showToast({
				title: '测试错误已触发',
				icon: 'none'
			})
		},
		
		async testRequest() {
			try {
				uni.showLoading({ title: '测试中...' })
				
				// 测试一个不存在的接口
				await this.$request({
					url: '/api/test/debug',
					method: 'GET'
				})
				
			} catch (error) {
				console.log('测试请求完成（预期会失败）')
			} finally {
				uni.hideLoading()
				setTimeout(() => {
					this.refreshErrorStats()
				}, 100)
			}
		},
		
		getLevelName(level) {
			const names = {
				LOW: '低',
				MEDIUM: '中',
				HIGH: '高',
				CRITICAL: '严重'
			}
			return names[level] || level
		},
		
		formatTime(timestamp) {
			const date = new Date(timestamp)
			return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
		},

		goToApiTest() {
			uni.navigateTo({
				url: '/pages/debug/api-test'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.debug-container {
	padding: 20px;
	background-color: #f8f9fa;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30px;
	
	.title {
		display: block;
		font-size: 24px;
		font-weight: bold;
		color: #333;
		margin-bottom: 8px;
	}
	
	.subtitle {
		display: block;
		font-size: 14px;
		color: #666;
	}
}

.section {
	background: white;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
		
		.section-title {
			font-size: 16px;
			font-weight: bold;
			color: #333;
		}
		
		.refresh-btn, .clear-btn {
			padding: 4px 12px;
			font-size: 12px;
			border-radius: 6px;
			border: 1px solid #ddd;
			background: #f8f9fa;
			color: #666;
		}
		
		.count-badge {
			background: #667eea;
			color: white;
			padding: 2px 8px;
			border-radius: 10px;
			font-size: 12px;
		}
		
		.status-indicator {
			width: 12px;
			height: 12px;
			border-radius: 50%;
			background: #ccc;
			
			&.active {
				background: #10b981;
			}
		}
	}
}

.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15px;
	
	.info-item {
		.label {
			display: block;
			font-size: 12px;
			color: #666;
			margin-bottom: 4px;
		}
		
		.value {
			display: block;
			font-size: 14px;
			color: #333;
			font-weight: 500;
			
			&.success {
				color: #10b981;
			}
			
			&.error {
				color: #ef4444;
			}
			
			&.token {
				font-family: monospace;
				font-size: 12px;
			}
		}
	}
}

.json-viewer {
	background: #f8f9fa;
	border-radius: 8px;
	padding: 15px;
	border: 1px solid #e9ecef;
	
	.json-content {
		font-family: monospace;
		font-size: 12px;
		color: #333;
		line-height: 1.5;
		white-space: pre-wrap;
		word-break: break-all;
	}
}

.error-stats {
	display: flex;
	gap: 20px;
	
	.stat-item {
		text-align: center;
		
		.stat-number {
			display: block;
			font-size: 24px;
			font-weight: bold;
			color: #333;
			
			&.low { color: #10b981; }
			&.medium { color: #f59e0b; }
			&.high { color: #ef4444; }
			&.critical { color: #dc2626; }
		}
		
		.stat-label {
			display: block;
			font-size: 12px;
			color: #666;
			margin-top: 4px;
		}
	}
}

.error-list {
	.error-item {
		padding: 12px;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		margin-bottom: 10px;
		
		.error-header {
			display: flex;
			gap: 10px;
			align-items: center;
			margin-bottom: 8px;
			
			.error-type {
				font-size: 12px;
				color: #666;
				background: #f8f9fa;
				padding: 2px 6px;
				border-radius: 4px;
			}
			
			.error-level {
				font-size: 10px;
				padding: 2px 6px;
				border-radius: 4px;
				color: white;
				
				&.low { background: #10b981; }
				&.medium { background: #f59e0b; }
				&.high { background: #ef4444; }
				&.critical { background: #dc2626; }
			}
			
			.error-time {
				font-size: 10px;
				color: #999;
				margin-left: auto;
			}
		}
		
		.error-message {
			display: block;
			font-size: 14px;
			color: #333;
			margin-bottom: 4px;
		}
		
		.error-page {
			display: block;
			font-size: 12px;
			color: #666;
		}
	}
	
	.empty-state {
		text-align: center;
		padding: 40px 20px;
		color: #666;
	}
}

.actions {
	display: flex;
	gap: 10px;
	margin-top: 20px;
	
	.action-btn {
		flex: 1;
		padding: 12px;
		border-radius: 8px;
		border: 1px solid #ddd;
		background: white;
		color: #666;
		font-size: 14px;
		
		&.primary {
			background: #667eea;
			color: white;
			border-color: #667eea;
		}
	}
}
</style>
