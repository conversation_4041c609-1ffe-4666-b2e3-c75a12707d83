"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const store_index = require("./store/index.js");
const config_api = require("./config/api.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/login/login.js";
  "./pages/course/list.js";
  "./pages/course/detail.js";
  "./pages/practice/practice.js";
  "./pages/checkin/checkin.js";
  "./pages/leaderboard/leaderboard.js";
  "./pages/profile/profile.js";
  "./pages/debug/debug.js";
  "./pages/debug/api-test.js";
  "./pages/debug/login-test.js";
  "./pages/debug/api-config-test.js";
  "./pages/debug/config-verify.js";
  "./pages/debug/captcha-structure-test.js";
  "./pages/debug/tabbar-navigation-test.js";
  "./pages/debug/login-success-test.js";
  "./pages/debug/user-info-display-test.js";
  "./pages/debug/json-parse-error-test.js";
}
const _sfc_main = {
  // 定义全局数据
  globalData: {
    systemInfo: null,
    statusBarHeight: 0,
    navBarHeight: 44,
    windowHeight: 0,
    windowWidth: 0,
    userInfo: null,
    token: null,
    version: "1.0.0",
    isLogin: false
  },
  onLaunch: function(options) {
    common_vendor.index.__f__("log", "at App.vue:17", "🚀 App Launch - 口语便利店启动");
    common_vendor.index.__f__("log", "at App.vue:18", "启动参数:", options);
    this.globalData.scene = options.scene || 0;
    this.globalData.path = options.path || "";
    this.globalData.query = options.query || {};
    this.initApp();
  },
  onShow: function(options) {
    common_vendor.index.__f__("log", "at App.vue:29", "👁️ App Show");
    common_vendor.index.__f__("log", "at App.vue:30", "显示参数:", options);
    this.checkNetworkStatus();
    if (options && options.scene) {
      this.globalData.scene = options.scene;
    }
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:42", "🙈 App Hide");
  },
  onError: function(err) {
    common_vendor.index.__f__("error", "at App.vue:46", "💥 App Error:", err);
    this.addError({
      type: "app_error",
      message: err,
      timestamp: Date.now()
    });
  },
  methods: {
    initApp() {
      try {
        common_vendor.index.__f__("log", "at App.vue:57", "🔧 开始初始化应用...");
        const systemInfo = common_vendor.index.getSystemInfoSync();
        common_vendor.index.__f__("log", "at App.vue:61", "📱 系统信息:", {
          platform: systemInfo.platform,
          version: systemInfo.version,
          model: systemInfo.model,
          pixelRatio: systemInfo.pixelRatio
        });
        this.globalData.systemInfo = systemInfo;
        this.globalData.statusBarHeight = systemInfo.statusBarHeight || 0;
        this.globalData.navBarHeight = (systemInfo.statusBarHeight || 0) + 44;
        this.globalData.windowHeight = systemInfo.windowHeight || 0;
        this.globalData.windowWidth = systemInfo.windowWidth || 0;
        this.globalData.platform = systemInfo.platform;
        this.checkLoginStatus();
        this.initNetworkListener();
        this.checkForUpdates();
        common_vendor.index.__f__("log", "at App.vue:85", "✅ 应用初始化完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:88", "❌ 应用初始化失败:", error);
        this.addError({
          type: "init_error",
          message: error.message,
          timestamp: Date.now()
        });
      }
    },
    checkLoginStatus() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (token) {
          this.globalData.token = token;
          this.globalData.isLogin = true;
          common_vendor.index.__f__("log", "at App.vue:105", "发现本地token");
        }
        if (userInfo) {
          this.globalData.userInfo = userInfo;
          common_vendor.index.__f__("log", "at App.vue:110", "发现本地用户信息");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:113", "检查登录状态失败:", error);
      }
    },
    // 全局方法：设置用户信息
    setUserInfo(userInfo) {
      this.globalData.userInfo = userInfo;
      this.globalData.isLogin = !!userInfo;
      if (userInfo) {
        common_vendor.index.setStorageSync("userInfo", userInfo);
      } else {
        common_vendor.index.removeStorageSync("userInfo");
      }
    },
    // 全局方法：设置token
    setToken(token) {
      this.globalData.token = token;
      this.globalData.isLogin = !!token;
      if (token) {
        common_vendor.index.setStorageSync("token", token);
      } else {
        common_vendor.index.removeStorageSync("token");
      }
    },
    // 全局方法：清除登录信息
    clearLoginInfo() {
      this.globalData.userInfo = null;
      this.globalData.token = null;
      this.globalData.isLogin = false;
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.removeStorageSync("token");
    },
    // 初始化网络监听
    initNetworkListener() {
      common_vendor.index.onNetworkStatusChange((res) => {
        common_vendor.index.__f__("log", "at App.vue:152", "🌐 网络状态变化:", res);
        if (!res.isConnected) {
          common_vendor.index.showToast({
            title: "网络连接已断开",
            icon: "none"
          });
        } else {
          common_vendor.index.__f__("log", "at App.vue:160", "✅ 网络已连接:", res.networkType);
        }
      });
    },
    // 检查网络状态
    checkNetworkStatus() {
      common_vendor.index.getNetworkType({
        success: (res) => {
          common_vendor.index.__f__("log", "at App.vue:169", "🌐 当前网络类型:", res.networkType);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at App.vue:172", "❌ 获取网络状态失败:", err);
        }
      });
    },
    // 检查小程序更新
    checkForUpdates() {
      if (common_vendor.index.getUpdateManager) {
        const updateManager = common_vendor.index.getUpdateManager();
        updateManager.onCheckForUpdate((res) => {
          common_vendor.index.__f__("log", "at App.vue:184", "🔄 检查更新结果:", res.hasUpdate);
        });
        updateManager.onUpdateReady(() => {
          common_vendor.index.showModal({
            title: "更新提示",
            content: "新版本已经准备好，是否重启应用？",
            success: (res) => {
              if (res.confirm) {
                updateManager.applyUpdate();
              }
            }
          });
        });
        updateManager.onUpdateFailed(() => {
          common_vendor.index.__f__("error", "at App.vue:200", "❌ 新版本下载失败");
        });
      }
    },
    // 添加错误记录
    addError(error) {
      if (!this.globalData.errors) {
        this.globalData.errors = [];
      }
      this.globalData.errors.unshift(__spreadProps(__spreadValues({}, error), {
        id: Date.now()
      }));
      if (this.globalData.errors.length > 50) {
        this.globalData.errors = this.globalData.errors.slice(0, 50);
      }
    },
    // 清除错误记录
    clearErrors() {
      this.globalData.errors = [];
    },
    // 获取场景值描述
    getSceneDesc(scene) {
      const sceneMap = {
        1001: "发现栏小程序主入口",
        1005: "顶部搜索框的搜索结果页",
        1006: "发现栏小程序主入口搜索框的搜索结果页",
        1007: "单人聊天会话中的小程序消息卡片",
        1008: "群聊会话中的小程序消息卡片",
        1011: "扫描二维码",
        1012: "长按图片识别二维码",
        1013: "手机相册选取二维码",
        1014: "小程序模版消息",
        1017: "前往体验版的入口页",
        1019: "微信钱包",
        1020: "公众号 profile 页相关小程序列表",
        1022: "聊天顶部置顶小程序入口",
        1023: "安卓系统桌面图标",
        1024: "小程序 profile 页",
        1025: "扫描一维码",
        1026: "附近小程序列表",
        1027: '顶部搜索框搜索结果页"使用过的小程序"列表',
        1028: "我的卡包",
        1029: "卡券详情页",
        1030: "自动化测试下打开小程序",
        1031: "长按图片识别一维码",
        1032: "手机相册选取一维码",
        1034: "微信支付完成页",
        1035: "公众号自定义菜单",
        1036: "App 分享消息卡片",
        1037: "小程序打开小程序",
        1038: "从另一个小程序返回",
        1039: "摇电视",
        1042: "添加好友搜索框的搜索结果页",
        1043: "公众号模板消息",
        1044: "带 shareTicket 的小程序消息卡片",
        1045: "朋友圈广告",
        1046: "朋友圈广告详情页",
        1047: "扫描小程序码",
        1048: "长按图片识别小程序码",
        1049: "手机相册选取小程序码",
        1052: "卡券的适用门店列表",
        1053: "搜一搜的结果页",
        1054: "顶部搜索框小程序快捷入口",
        1056: "音乐播放器菜单",
        1057: "钱包中的银行卡详情页",
        1058: "公众号文章",
        1059: "体验版小程序绑定邀请页",
        1064: "微信连Wi-Fi状态栏",
        1067: "公众号文章广告",
        1068: "附近小程序列表广告",
        1069: "移动应用",
        1071: "钱包中的银行卡详情页",
        1072: "二维码收款页面",
        1073: "客服消息列表下拉刷新",
        1074: "公众号会话下拉刷新",
        1077: "摇周边",
        1078: "连Wi-Fi成功页",
        1079: "微信游戏中心",
        1081: "客服会话菜单小程序入口",
        1082: "从微信聊天主界面下拉，搜索框内搜索小程序",
        1084: "公众号阅读完毕页",
        1089: '微信聊天主界面下拉，"最近使用"栏（聊天主界面下拉）',
        1090: "长按小程序右上角菜单唤出最近使用历史",
        1091: "公众号文章商品广告",
        1092: "城市服务入口",
        1095: "小程序广告组件",
        1096: "聊天记录",
        1097: "微信支付签约页",
        1099: "页面内嵌插件",
        1102: "公众号 profile 页服务预览",
        1103: "发现栏小游戏",
        1104: '微信聊天主界面下拉，"我的小程序"栏',
        1106: "小程序 profile 页",
        1107: "系统桌面图标",
        1108: "小程序 profile 页",
        1109: "微信聊天主界面下拉，搜索框内搜索小程序",
        1110: "小程序 profile 页",
        1111: "扫描二维码",
        1112: "长按图片识别二维码",
        1113: "手机相册选取二维码",
        1114: "小程序模版消息",
        1117: "前往体验版的入口页"
      };
      return sceneMap[scene] || `未知场景(${scene})`;
    }
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(store_index.store);
  app.config.errorHandler = (error, instance, info) => {
    common_vendor.index.__f__("error", "at main.js:13", "Vue全局错误:", error, info);
  };
  const baseUrl = config_api.getApiBaseUrl();
  const isDev = baseUrl.includes("localhost");
  app.config.globalProperties.$baseUrl = baseUrl;
  app.config.globalProperties.$isDev = isDev;
  app.config.globalProperties.$request = (options) => {
    return new Promise((resolve, reject) => {
      const token = common_vendor.index.getStorageSync("token");
      common_vendor.index.request({
        url: app.config.globalProperties.$baseUrl + options.url,
        method: options.method || "GET",
        data: options.data || {},
        header: __spreadValues({
          "Content-Type": "application/json",
          "Authorization": token ? `Bearer ${token}` : ""
        }, options.header),
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.success !== false) {
              resolve(res.data);
            } else {
              common_vendor.index.showToast({
                title: res.data.message || "请求失败",
                icon: "none"
              });
              reject(res.data);
            }
          } else if (res.statusCode === 401) {
            common_vendor.index.removeStorageSync("token");
            common_vendor.index.removeStorageSync("userInfo");
            common_vendor.index.showToast({
              title: "登录已过期",
              icon: "none"
            });
            setTimeout(() => {
              common_vendor.index.reLaunch({
                url: "/pages/login/login"
              });
            }, 1e3);
            reject(res);
          } else {
            common_vendor.index.showToast({
              title: "网络错误",
              icon: "none"
            });
            reject(res);
          }
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "网络连接失败",
            icon: "none"
          });
          reject(err);
        }
      });
    });
  };
  app.config.globalProperties.$utils = {
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hour = date.getHours().toString().padStart(2, "0");
      const minute = date.getMinutes().toString().padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}`;
    },
    // 格式化日期
    formatDate(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 防抖函数
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },
    // 节流函数
    throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },
    // 深拷贝
    deepClone(obj) {
      if (obj === null || typeof obj !== "object")
        return obj;
      if (obj instanceof Date)
        return new Date(obj.getTime());
      if (obj instanceof Array)
        return obj.map((item) => this.deepClone(item));
      if (typeof obj === "object") {
        const clonedObj = {};
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            clonedObj[key] = this.deepClone(obj[key]);
          }
        }
        return clonedObj;
      }
    },
    // 存储管理
    setStorage(key, value) {
      try {
        common_vendor.index.setStorageSync(key, JSON.stringify(value));
      } catch (e) {
        common_vendor.index.__f__("error", "at main.js:152", "存储失败:", e);
      }
    },
    getStorage(key) {
      try {
        const value = common_vendor.index.getStorageSync(key);
        return value ? JSON.parse(value) : null;
      } catch (e) {
        common_vendor.index.__f__("error", "at main.js:161", "读取存储失败:", e);
        return null;
      }
    },
    removeStorage(key) {
      try {
        common_vendor.index.removeStorageSync(key);
      } catch (e) {
        common_vendor.index.__f__("error", "at main.js:170", "删除存储失败:", e);
      }
    },
    // 音频播放管理
    createAudioContext() {
      return common_vendor.index.createInnerAudioContext();
    },
    // 录音管理
    createRecorderManager() {
      return common_vendor.index.getRecorderManager();
    },
    // 权限检查
    checkPermission(scope) {
      return new Promise((resolve, reject) => {
        common_vendor.index.getSetting({
          success: (res) => {
            if (res.authSetting[scope]) {
              resolve(true);
            } else {
              common_vendor.index.authorize({
                scope,
                success: () => resolve(true),
                fail: () => reject(false)
              });
            }
          },
          fail: () => reject(false)
        });
      });
    }
  };
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
