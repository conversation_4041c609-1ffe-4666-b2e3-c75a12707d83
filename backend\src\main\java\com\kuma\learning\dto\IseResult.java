package com.kuma.learning.dto;

import lombok.Data;

/**
 * 语音评测解析后的结果
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
public class IseResult {
    
    /**
     * 总分
     */
    private Double totalScore;
    
    /**
     * 发音得分(声韵分)
     */
    private Double pronunciationScore;
    
    /**
     * 流利度得分
     */
    private Double fluencyScore;
    
    /**
     * 语调得分(调型分)
     */
    private Double toneScore;
    
    /**
     * 准确度得分
     */
    private Double accuracyScore;
    
    /**
     * 完整度得分
     */
    private Double integrityScore;
    
    /**
     * 标准度得分
     */
    private Double standardScore;
    
    /**
     * 是否被拒识(乱读检测)
     */
    private Boolean isRejected;
    
    /**
     * 异常信息
     */
    private String exceptInfo;
    
    /**
     * AI反馈建议
     */
    private String feedback;
    
    /**
     * 详细评测数据(XML格式)
     */
    private String detailData;
    
    /**
     * 评测等级：A(90-100) B(80-89) C(70-79) D(60-69) F(0-59)
     */
    private String grade;
    
    /**
     * 评测建议
     */
    private String suggestion;
}
