<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tabbar图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-item h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .icon-preview {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 15px;
        }
        .icon-canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .download-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #5a6fd8;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        .color-info {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
        }
        .color-box {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .color-sample {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 口语便利店 Tabbar 图标生成器</h1>
        
        <div class="color-info">
            <div class="color-box">
                <div class="color-sample" style="background-color: #999999;"></div>
                <span>未选中: #999999</span>
            </div>
            <div class="color-box">
                <div class="color-sample" style="background-color: #667eea;"></div>
                <span>选中: #667eea</span>
            </div>
        </div>
        
        <div class="icon-grid" id="iconGrid">
            <!-- 图标将通过JavaScript生成 -->
        </div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>点击下载按钮保存对应的PNG图标文件</li>
                <li>将下载的图标文件放置到 <code>front/static/tabbar/</code> 目录下</li>
                <li>确保文件名与 pages.json 中配置的路径一致</li>
                <li>建议图标尺寸为 48x48px，适配不同设备显示</li>
            </ol>
            
            <h3>🎯 图标说明</h3>
            <ul>
                <li><strong>首页</strong>：房屋图标，代表主页和起始点</li>
                <li><strong>课程</strong>：书本图标，代表学习内容和教材</li>
                <li><strong>打卡</strong>：日历打勾图标，代表每日学习打卡</li>
                <li><strong>排行榜</strong>：柱状图图标，代表成绩排名和竞争</li>
                <li><strong>个人中心</strong>：用户图标，代表个人信息和设置</li>
            </ul>
        </div>
    </div>

    <script>
        // 图标配置
        const icons = [
            {
                name: 'home',
                title: '首页',
                draw: (ctx, color, size) => {
                    const s = size / 24; // 缩放比例
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2 * s;
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';
                    
                    // 房屋外框
                    ctx.beginPath();
                    ctx.moveTo(3 * s, 9 * s);
                    ctx.lineTo(12 * s, 2 * s);
                    ctx.lineTo(21 * s, 9 * s);
                    ctx.lineTo(21 * s, 20 * s);
                    ctx.lineTo(19 * s, 20 * s);
                    ctx.lineTo(19 * s, 22 * s);
                    ctx.lineTo(5 * s, 22 * s);
                    ctx.lineTo(5 * s, 20 * s);
                    ctx.lineTo(3 * s, 20 * s);
                    ctx.closePath();
                    ctx.stroke();
                    
                    // 门
                    ctx.beginPath();
                    ctx.moveTo(9 * s, 22 * s);
                    ctx.lineTo(9 * s, 12 * s);
                    ctx.lineTo(15 * s, 12 * s);
                    ctx.lineTo(15 * s, 22 * s);
                    ctx.stroke();
                }
            },
            {
                name: 'course',
                title: '课程',
                draw: (ctx, color, size) => {
                    const s = size / 24;
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2 * s;
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';
                    
                    // 书本外框
                    ctx.beginPath();
                    ctx.roundRect(6.5 * s, 2 * s, 13.5 * s, 20 * s, 2 * s);
                    ctx.stroke();
                    
                    // 书脊
                    ctx.beginPath();
                    ctx.moveTo(4 * s, 19.5 * s);
                    ctx.lineTo(6.5 * s, 17 * s);
                    ctx.lineTo(20 * s, 17 * s);
                    ctx.stroke();
                    
                    // 文字线条
                    ctx.beginPath();
                    ctx.moveTo(8 * s, 7 * s);
                    ctx.lineTo(16 * s, 7 * s);
                    ctx.moveTo(8 * s, 11 * s);
                    ctx.lineTo(16 * s, 11 * s);
                    ctx.stroke();
                }
            },
            {
                name: 'checkin',
                title: '打卡',
                draw: (ctx, color, size) => {
                    const s = size / 24;
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2 * s;
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';
                    
                    // 日历框
                    ctx.beginPath();
                    ctx.roundRect(3 * s, 4 * s, 18 * s, 18 * s, 2 * s);
                    ctx.stroke();
                    
                    // 日历顶部装订环
                    ctx.beginPath();
                    ctx.moveTo(16 * s, 2 * s);
                    ctx.lineTo(16 * s, 6 * s);
                    ctx.moveTo(8 * s, 2 * s);
                    ctx.lineTo(8 * s, 6 * s);
                    ctx.stroke();
                    
                    // 分隔线
                    ctx.beginPath();
                    ctx.moveTo(3 * s, 10 * s);
                    ctx.lineTo(21 * s, 10 * s);
                    ctx.stroke();
                    
                    // 打勾
                    ctx.beginPath();
                    ctx.moveTo(8 * s, 14 * s);
                    ctx.lineTo(10 * s, 16 * s);
                    ctx.lineTo(16 * s, 10 * s);
                    ctx.stroke();
                }
            },
            {
                name: 'leaderboard',
                title: '排行榜',
                draw: (ctx, color, size) => {
                    const s = size / 24;
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2 * s;
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';
                    
                    // 第一个柱子（最矮）
                    ctx.beginPath();
                    ctx.roundRect(2 * s, 9 * s, 6 * s, 13 * s, 1 * s);
                    ctx.stroke();
                    
                    // 第二个柱子（最高）
                    ctx.beginPath();
                    ctx.roundRect(10 * s, 5 * s, 6 * s, 17 * s, 1 * s);
                    ctx.stroke();
                    
                    // 第三个柱子（中等）
                    ctx.beginPath();
                    ctx.roundRect(18 * s, 13 * s, 6 * s, 9 * s, 1 * s);
                    ctx.stroke();
                    
                    // 皇冠（在最高柱子上）
                    ctx.beginPath();
                    ctx.moveTo(13 * s, 2 * s);
                    ctx.lineTo(15 * s, 4 * s);
                    ctx.lineTo(13 * s, 6 * s);
                    ctx.lineTo(11 * s, 4 * s);
                    ctx.closePath();
                    ctx.stroke();
                }
            },
            {
                name: 'profile',
                title: '个人中心',
                draw: (ctx, color, size) => {
                    const s = size / 24;
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 2 * s;
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';
                    
                    // 头部圆圈
                    ctx.beginPath();
                    ctx.arc(12 * s, 7 * s, 4 * s, 0, 2 * Math.PI);
                    ctx.stroke();
                    
                    // 身体轮廓
                    ctx.beginPath();
                    ctx.moveTo(20 * s, 21 * s);
                    ctx.lineTo(20 * s, 19 * s);
                    ctx.arc(12 * s, 19 * s, 8 * s, 0, Math.PI, true);
                    ctx.lineTo(4 * s, 21 * s);
                    ctx.stroke();
                }
            }
        ];

        // 生成图标
        function generateIcons() {
            const iconGrid = document.getElementById('iconGrid');
            
            icons.forEach(icon => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                
                iconItem.innerHTML = `
                    <h3>${icon.title} (${icon.name})</h3>
                    <div class="icon-preview">
                        <div>
                            <canvas id="${icon.name}-normal" class="icon-canvas" width="48" height="48"></canvas>
                            <div>未选中</div>
                        </div>
                        <div>
                            <canvas id="${icon.name}-active" class="icon-canvas" width="48" height="48"></canvas>
                            <div>选中</div>
                        </div>
                    </div>
                    <div>
                        <button class="download-btn" onclick="downloadIcon('${icon.name}', false)">下载未选中</button>
                        <button class="download-btn" onclick="downloadIcon('${icon.name}', true)">下载选中</button>
                    </div>
                `;
                
                iconGrid.appendChild(iconItem);
                
                // 绘制图标
                drawIcon(icon, false); // 未选中状态
                drawIcon(icon, true);  // 选中状态
            });
        }

        // 绘制图标
        function drawIcon(icon, isActive) {
            const canvasId = `${icon.name}-${isActive ? 'active' : 'normal'}`;
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 48, 48);
            
            // 设置颜色
            const color = isActive ? '#667eea' : '#999999';
            
            // 移动到画布中心
            ctx.save();
            ctx.translate(24, 24);
            ctx.translate(-12, -12);
            
            // 绘制图标
            icon.draw(ctx, color, 48);
            
            ctx.restore();
        }

        // 下载图标
        function downloadIcon(iconName, isActive) {
            const canvasId = `${iconName}-${isActive ? 'active' : 'normal'}`;
            const canvas = document.getElementById(canvasId);
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = `${iconName}${isActive ? '-active' : ''}.png`;
            link.href = canvas.toDataURL('image/png');
            
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 页面加载完成后生成图标
        document.addEventListener('DOMContentLoaded', generateIcons);
    </script>
</body>
</html>
