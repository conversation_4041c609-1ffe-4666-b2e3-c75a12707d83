# 语音评测API文档

## 概述

本文档描述了口语便利店小程序中集成的科大讯飞语音评测功能的API接口。

## 接口列表

### 1. 提交录音练习（文件上传）

**接口地址：** `POST /app/practice/submit`

**请求方式：** `multipart/form-data`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| audioFile | File | 是 | 音频文件（支持wav、mp3、m4a、aac格式，最大10MB） |
| sentenceText | String | 是 | 待评测的句子文本 |
| courseId | String | 是 | 课程ID |
| lessonId | String | 否 | 课时ID |
| language | String | 否 | 语言类型（cn=中文，en=英文），默认cn |
| category | String | 否 | 题型，默认read_sentence |

**支持的题型：**
- 中文：read_syllable（单字朗读）、read_word（词语朗读）、read_sentence（句子朗读）、read_chapter（篇章朗读）
- 英文：read_word（词语朗读）、read_sentence（句子朗读）、read_chapter（篇章朗读）

**响应示例：**
```json
{
  "success": true,
  "message": "练习提交成功",
  "code": 200,
  "result": {
    "practiceRecord": {
      "id": "1234567890",
      "userId": "user123",
      "courseId": "course123",
      "lessonId": "lesson123",
      "sentenceText": "こんにちは",
      "audioUrl": "http://localhost:8080/files/audio/user123/2024-03-22/uuid.wav",
      "audioDuration": 30,
      "totalScore": 85,
      "pronunciationScore": 80,
      "fluencyScore": 88,
      "toneScore": 87,
      "aiFeedback": "发音清晰，语调自然，建议在语速上稍作调整。",
      "createTime": "2024-03-22 10:30:00"
    },
    "evaluationResult": {
      "totalScore": 85.0,
      "pronunciationScore": 80.0,
      "fluencyScore": 88.0,
      "toneScore": 87.0,
      "accuracyScore": 85.0,
      "integrityScore": 85.0,
      "standardScore": 85.0,
      "grade": "B",
      "feedback": "发音清晰，语调自然，建议在语速上稍作调整。",
      "suggestion": "表现不错，继续练习可以达到更高水平。"
    }
  }
}
```

### 2. 提交录音练习（Base64）

**接口地址：** `POST /app/practice/submitBase64`

**请求方式：** `application/json`

**请求参数：**
```json
{
  "audioData": "base64编码的音频数据",
  "sentenceText": "待评测的句子文本",
  "courseId": "课程ID",
  "lessonId": "课时ID（可选）",
  "language": "语言类型（cn/en）",
  "category": "题型",
  "audioFormat": "音频格式（wav/mp3等）",
  "audioDuration": 30
}
```

**响应格式：** 与文件上传接口相同

### 3. 获取练习历史

**接口地址：** `GET /app/practice/history`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNo | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |
| courseId | String | 否 | 课程ID筛选 |
| lessonId | String | 否 | 课时ID筛选 |

### 4. 获取练习统计

**接口地址：** `GET /app/practice/statistics`

**响应示例：**
```json
{
  "success": true,
  "result": {
    "totalPractices": 50,
    "averageScore": 82,
    "bestScore": 95,
    "improvementRate": 15
  }
}
```

### 5. 获取支持的语言类型

**接口地址：** `GET /app/practice/supportedLanguages`

**响应示例：**
```json
{
  "success": true,
  "result": ["cn", "en"]
}
```

### 6. 获取支持的题型

**接口地址：** `GET /app/practice/supportedCategories`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| language | String | 是 | 语言类型 |

### 7. 获取评测配置

**接口地址：** `GET /app/practice/evaluationConfig`

**响应示例：**
```json
{
  "success": true,
  "result": {
    "languages": {
      "cn": "中文",
      "en": "英文"
    },
    "categories": {
      "cn": {
        "read_syllable": "单字朗读",
        "read_word": "词语朗读",
        "read_sentence": "句子朗读",
        "read_chapter": "篇章朗读"
      },
      "en": {
        "read_word": "词语朗读",
        "read_sentence": "句子朗读",
        "read_chapter": "篇章朗读"
      }
    },
    "grades": {
      "A": "优秀(90-100分)",
      "B": "良好(80-89分)",
      "C": "中等(70-79分)",
      "D": "及格(60-69分)",
      "F": "不及格(0-59分)"
    }
  }
}
```

## 评分说明

### 评分维度

1. **总分（totalScore）**：综合评分，0-100分
2. **发音得分（pronunciationScore）**：声韵准确性评分
3. **流利度得分（fluencyScore）**：语音流畅度评分
4. **语调得分（toneScore）**：语调准确性评分
5. **准确度得分（accuracyScore）**：整体准确度评分
6. **完整度得分（integrityScore）**：朗读完整度评分
7. **标准度得分（standardScore）**：发音标准度评分

### 评分等级

- **A级（90-100分）**：优秀，发音标准，语调自然
- **B级（80-89分）**：良好，发音较准确，有小幅提升空间
- **C级（70-79分）**：中等，发音基本准确，需要练习
- **D级（60-69分）**：及格，发音有明显问题，需要加强
- **F级（0-59分）**：不及格，发音问题较多，需要重点练习

### 积分奖励

根据评分给予不同的积分奖励：
- 90分以上：10积分
- 80-89分：8积分
- 70-79分：5积分
- 60-69分：3积分
- 60分以下：1积分（参与奖励）

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 用户未登录 |
| 413 | 文件过大（超过10MB） |
| 415 | 不支持的文件格式 |
| 500 | 服务器内部错误 |

## 注意事项

1. **文件格式**：支持wav、mp3、m4a、aac、pcm格式
2. **文件大小**：最大10MB
3. **音频质量**：建议使用16kHz采样率，16位深度
4. **网络环境**：评测需要稳定的网络连接
5. **并发限制**：避免同时提交多个评测请求
6. **费用控制**：科大讯飞API可能产生费用，请注意用量

## 前端集成示例

### JavaScript示例

```javascript
// 文件上传方式
const formData = new FormData();
formData.append('audioFile', audioFile);
formData.append('sentenceText', '你好世界');
formData.append('courseId', 'course123');
formData.append('language', 'cn');
formData.append('category', 'read_sentence');

fetch('/app/practice/submit', {
  method: 'POST',
  body: formData,
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => {
  console.log('评测结果:', data.result);
});

// Base64方式
const requestData = {
  audioData: base64AudioData,
  sentenceText: '你好世界',
  courseId: 'course123',
  language: 'cn',
  category: 'read_sentence',
  audioFormat: 'wav',
  audioDuration: 30
};

fetch('/app/practice/submitBase64', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(data => {
  console.log('评测结果:', data.result);
});
```

### uni-app示例

```javascript
// 在uni-app中使用
uni.uploadFile({
  url: '/app/practice/submit',
  filePath: audioFilePath,
  name: 'audioFile',
  formData: {
    sentenceText: '你好世界',
    courseId: 'course123',
    language: 'cn',
    category: 'read_sentence'
  },
  header: {
    'Authorization': 'Bearer ' + token
  },
  success: (res) => {
    const data = JSON.parse(res.data);
    console.log('评测结果:', data.result);
  }
});
```
