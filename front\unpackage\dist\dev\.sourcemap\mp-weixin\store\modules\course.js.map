{"version": 3, "file": "course.js", "sources": ["store/modules/course.js"], "sourcesContent": ["const course = {\n  namespaced: true,\n  \n  state: {\n    // 课程列表\n    courseList: [],\n    \n    // 当前课程详情\n    currentCourse: null,\n    \n    // 课程级别\n    levels: [\n      {\n        id: 1,\n        key: 'beginner',\n        name: '初级 N5-N4',\n        description: '基础对话练习',\n        icon: 'icon-seedling',\n        iconBg: '#dcfce7',\n        iconColor: '#16a34a'\n      },\n      {\n        id: 2,\n        key: 'intermediate',\n        name: '中级 N3-N2',\n        description: '日常会话提升',\n        icon: 'icon-mountain',\n        iconBg: '#dbeafe',\n        iconColor: '#2563eb'\n      },\n      {\n        id: 3,\n        key: 'advanced',\n        name: '高级 N1',\n        description: '商务日语精进',\n        icon: 'icon-crown',\n        iconBg: '#f3e8ff',\n        iconColor: '#9333ea'\n      },\n      {\n        id: 4,\n        key: 'professional',\n        name: '专业级',\n        description: '新闻听力训练',\n        icon: 'icon-star',\n        iconBg: '#fed7aa',\n        iconColor: '#ea580c',\n        isVip: true\n      }\n    ],\n    \n    // 当前选择的级别\n    currentLevel: 'beginner',\n    \n    // 加载状态\n    loading: false,\n    \n    // 用户学习记录\n    userStudyRecords: {},\n    \n    // 收藏的课程\n    favoriteCourses: []\n  },\n  \n  mutations: {\n    // 设置课程列表\n    SET_COURSE_LIST(state, courseList) {\n      state.courseList = courseList\n    },\n    \n    // 设置当前课程\n    SET_CURRENT_COURSE(state, course) {\n      state.currentCourse = course\n    },\n    \n    // 设置当前级别\n    SET_CURRENT_LEVEL(state, level) {\n      state.currentLevel = level\n    },\n    \n    // 设置加载状态\n    SET_LOADING(state, loading) {\n      state.loading = loading\n    },\n    \n    // 设置用户学习记录\n    SET_USER_STUDY_RECORDS(state, records) {\n      state.userStudyRecords = records\n    },\n    \n    // 更新单个课程的学习记录\n    UPDATE_COURSE_RECORD(state, { courseId, record }) {\n      state.userStudyRecords[courseId] = record\n    },\n    \n    // 设置收藏课程\n    SET_FAVORITE_COURSES(state, courses) {\n      state.favoriteCourses = courses\n    },\n    \n    // 添加收藏\n    ADD_FAVORITE(state, courseId) {\n      if (!state.favoriteCourses.includes(courseId)) {\n        state.favoriteCourses.push(courseId)\n      }\n    },\n    \n    // 移除收藏\n    REMOVE_FAVORITE(state, courseId) {\n      const index = state.favoriteCourses.indexOf(courseId)\n      if (index > -1) {\n        state.favoriteCourses.splice(index, 1)\n      }\n    }\n  },\n  \n  actions: {\n    // 加载课程列表\n    async loadCourseList({ commit, rootState }, { level, page = 1, size = 20 }) {\n      commit('SET_LOADING', true)\n      \n      try {\n        const response = await uni.request({\n          url: '/api/v1/courses',\n          method: 'GET',\n          data: { level, page, size },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          commit('SET_COURSE_LIST', response.data.data.list)\n          commit('SET_CURRENT_LEVEL', level)\n          return { success: true, data: response.data.data }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('加载课程列表失败:', error)\n        return { success: false, message: '加载失败，请重试' }\n      } finally {\n        commit('SET_LOADING', false)\n      }\n    },\n    \n    // 加载课程详情\n    async loadCourseDetail({ commit, rootState }, courseId) {\n      try {\n        const response = await uni.request({\n          url: `/api/v1/courses/${courseId}`,\n          method: 'GET',\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          commit('SET_CURRENT_COURSE', response.data.data)\n          return { success: true, data: response.data.data }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('加载课程详情失败:', error)\n        return { success: false, message: '加载失败，请重试' }\n      }\n    },\n    \n    // 更新学习进度\n    async updateStudyProgress({ commit, rootState }, { courseId, progress, studyTime }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/study/progress',\n          method: 'POST',\n          data: { courseId, progress, studyTime },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          commit('UPDATE_COURSE_RECORD', {\n            courseId,\n            record: response.data.data\n          })\n          return { success: true }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('更新学习进度失败:', error)\n        return { success: false, message: '更新失败，请重试' }\n      }\n    },\n    \n    // 加载用户学习记录\n    async loadUserStudyRecords({ commit, rootState }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/study/records',\n          method: 'GET',\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          const records = {}\n          response.data.data.forEach(record => {\n            records[record.courseId] = record\n          })\n          commit('SET_USER_STUDY_RECORDS', records)\n        }\n      } catch (error) {\n        console.error('加载学习记录失败:', error)\n      }\n    },\n    \n    // 购买课程\n    async purchaseCourse({ rootState }, { courseId, paymentMethod }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/payment/create',\n          method: 'POST',\n          data: { courseId, paymentMethod },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          // 这里应该调用支付接口\n          return { success: true, data: response.data.data }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('创建订单失败:', error)\n        return { success: false, message: '创建订单失败，请重试' }\n      }\n    },\n    \n    // 收藏/取消收藏课程\n    async toggleFavorite({ commit, state, rootState }, courseId) {\n      const isFavorite = state.favoriteCourses.includes(courseId)\n      \n      try {\n        const response = await uni.request({\n          url: `/api/v1/courses/${courseId}/favorite`,\n          method: isFavorite ? 'DELETE' : 'POST',\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          if (isFavorite) {\n            commit('REMOVE_FAVORITE', courseId)\n          } else {\n            commit('ADD_FAVORITE', courseId)\n          }\n          return { success: true, isFavorite: !isFavorite }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('收藏操作失败:', error)\n        return { success: false, message: '操作失败，请重试' }\n      }\n    },\n    \n    // 加载收藏课程\n    async loadFavoriteCourses({ commit, rootState }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/user/favorites',\n          method: 'GET',\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          const courseIds = response.data.data.map(item => item.courseId)\n          commit('SET_FAVORITE_COURSES', courseIds)\n        }\n      } catch (error) {\n        console.error('加载收藏课程失败:', error)\n      }\n    }\n  },\n  \n  getters: {\n    // 根据级别获取课程列表\n    getCoursesByLevel: (state) => (level) => {\n      return state.courseList.filter(course => course.level === level)\n    },\n    \n    // 获取课程学习记录\n    getCourseRecord: (state) => (courseId) => {\n      return state.userStudyRecords[courseId] || null\n    },\n    \n    // 检查课程是否已收藏\n    isFavorite: (state) => (courseId) => {\n      return state.favoriteCourses.includes(courseId)\n    },\n    \n    // 获取当前级别信息\n    currentLevelInfo: (state) => {\n      return state.levels.find(level => level.key === state.currentLevel)\n    },\n    \n    // 获取已完成的课程数量\n    completedCoursesCount: (state) => {\n      return Object.values(state.userStudyRecords).filter(record => record.isCompleted).length\n    },\n    \n    // 获取学习进度统计\n    studyProgressStats: (state) => {\n      const records = Object.values(state.userStudyRecords)\n      const totalCourses = state.courseList.length\n      const completedCourses = records.filter(record => record.isCompleted).length\n      const totalStudyTime = records.reduce((total, record) => total + (record.studyTime || 0), 0)\n      \n      return {\n        totalCourses,\n        completedCourses,\n        totalStudyTime,\n        completionRate: totalCourses > 0 ? Math.round((completedCourses / totalCourses) * 100) : 0\n      }\n    }\n  }\n}\n\nexport default course\n"], "names": ["course", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAK,MAAC,SAAS;AAAA,EACb,YAAY;AAAA,EAEZ,OAAO;AAAA;AAAA,IAEL,YAAY,CAAE;AAAA;AAAA,IAGd,eAAe;AAAA;AAAA,IAGf,QAAQ;AAAA,MACN;AAAA,QACE,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,OAAO;AAAA,MACR;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AAAA;AAAA,IAGd,SAAS;AAAA;AAAA,IAGT,kBAAkB,CAAE;AAAA;AAAA,IAGpB,iBAAiB,CAAE;AAAA,EACpB;AAAA,EAED,WAAW;AAAA;AAAA,IAET,gBAAgB,OAAO,YAAY;AACjC,YAAM,aAAa;AAAA,IACpB;AAAA;AAAA,IAGD,mBAAmB,OAAOA,SAAQ;AAChC,YAAM,gBAAgBA;AAAA,IACvB;AAAA;AAAA,IAGD,kBAAkB,OAAO,OAAO;AAC9B,YAAM,eAAe;AAAA,IACtB;AAAA;AAAA,IAGD,YAAY,OAAO,SAAS;AAC1B,YAAM,UAAU;AAAA,IACjB;AAAA;AAAA,IAGD,uBAAuB,OAAO,SAAS;AACrC,YAAM,mBAAmB;AAAA,IAC1B;AAAA;AAAA,IAGD,qBAAqB,OAAO,EAAE,UAAU,OAAM,GAAI;AAChD,YAAM,iBAAiB,QAAQ,IAAI;AAAA,IACpC;AAAA;AAAA,IAGD,qBAAqB,OAAO,SAAS;AACnC,YAAM,kBAAkB;AAAA,IACzB;AAAA;AAAA,IAGD,aAAa,OAAO,UAAU;AAC5B,UAAI,CAAC,MAAM,gBAAgB,SAAS,QAAQ,GAAG;AAC7C,cAAM,gBAAgB,KAAK,QAAQ;AAAA,MACpC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,OAAO,UAAU;AAC/B,YAAM,QAAQ,MAAM,gBAAgB,QAAQ,QAAQ;AACpD,UAAI,QAAQ,IAAI;AACd,cAAM,gBAAgB,OAAO,OAAO,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAED,eAAe,IAAuB,IAAgC;AAAA,iDAAvD,EAAE,QAAQ,aAAa,EAAE,OAAO,OAAO,GAAG,OAAO,MAAM;AAC1E,eAAO,eAAe,IAAI;AAE1B,YAAI;AACF,gBAAM,WAAW,MAAMC,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM,EAAE,OAAO,MAAM,KAAM;AAAA,YAC3B,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,mBAAO,mBAAmB,SAAS,KAAK,KAAK,IAAI;AACjD,mBAAO,qBAAqB,KAAK;AACjC,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,kCAAA,aAAa,KAAK;AAChC,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QACtD,UAAgB;AACR,iBAAO,eAAe,KAAK;AAAA,QAC5B;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,iBAAiB,IAAuB,IAAU;AAAA,iDAAjC,EAAE,QAAQ,UAAS,GAAI,UAAU;AACtD,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK,mBAAmB,QAAQ;AAAA,YAChC,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,mBAAO,sBAAsB,SAAS,KAAK,IAAI;AAC/C,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,kCAAA,aAAa,KAAK;AAChC,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,oBAAoB,IAAuB,IAAmC;AAAA,iDAA1D,EAAE,QAAQ,UAAS,GAAI,EAAE,UAAU,UAAU,aAAa;AAClF,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM,EAAE,UAAU,UAAU,UAAW;AAAA,YACvC,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,mBAAO,wBAAwB;AAAA,cAC7B;AAAA,cACA,QAAQ,SAAS,KAAK;AAAA,YAClC,CAAW;AACD,mBAAO,EAAE,SAAS,KAAM;AAAA,UAClC,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,kCAAA,aAAa,KAAK;AAChC,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,qBAAqB,IAAuB;AAAA,iDAAvB,EAAE,QAAQ,aAAa;AAChD,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,kBAAM,UAAU,CAAE;AAClB,qBAAS,KAAK,KAAK,QAAQ,YAAU;AACnC,sBAAQ,OAAO,QAAQ,IAAI;AAAA,YACvC,CAAW;AACD,mBAAO,0BAA0B,OAAO;AAAA,UACzC;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,kCAAA,aAAa,KAAK;AAAA,QACjC;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,eAAe,IAAe,IAA6B;AAAA,iDAA5C,EAAE,UAAS,GAAI,EAAE,UAAU,cAAa,GAAI;AAC/D,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM,EAAE,UAAU,cAAe;AAAA,YACjC,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAE9B,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9B,iBAAO,EAAE,SAAS,OAAO,SAAS,aAAc;AAAA,QACjD;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,eAAe,IAA8B,IAAU;AAAA,iDAAxC,EAAE,QAAQ,OAAO,UAAS,GAAI,UAAU;AAC3D,cAAM,aAAa,MAAM,gBAAgB,SAAS,QAAQ;AAE1D,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK,mBAAmB,QAAQ;AAAA,YAChC,QAAQ,aAAa,WAAW;AAAA,YAChC,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,gBAAI,YAAY;AACd,qBAAO,mBAAmB,QAAQ;AAAA,YAC9C,OAAiB;AACL,qBAAO,gBAAgB,QAAQ;AAAA,YAChC;AACD,mBAAO,EAAE,SAAS,MAAM,YAAY,CAAC,WAAY;AAAA,UAC3D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9B,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,oBAAoB,IAAuB;AAAA,iDAAvB,EAAE,QAAQ,aAAa;AAC/C,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,kBAAM,YAAY,SAAS,KAAK,KAAK,IAAI,UAAQ,KAAK,QAAQ;AAC9D,mBAAO,wBAAwB,SAAS;AAAA,UACzC;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,kCAAA,aAAa,KAAK;AAAA,QACjC;AAAA,MACF;AAAA;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,mBAAmB,CAAC,UAAU,CAAC,UAAU;AACvC,aAAO,MAAM,WAAW,OAAO,CAAAD,YAAUA,QAAO,UAAU,KAAK;AAAA,IAChE;AAAA;AAAA,IAGD,iBAAiB,CAAC,UAAU,CAAC,aAAa;AACxC,aAAO,MAAM,iBAAiB,QAAQ,KAAK;AAAA,IAC5C;AAAA;AAAA,IAGD,YAAY,CAAC,UAAU,CAAC,aAAa;AACnC,aAAO,MAAM,gBAAgB,SAAS,QAAQ;AAAA,IAC/C;AAAA;AAAA,IAGD,kBAAkB,CAAC,UAAU;AAC3B,aAAO,MAAM,OAAO,KAAK,WAAS,MAAM,QAAQ,MAAM,YAAY;AAAA,IACnE;AAAA;AAAA,IAGD,uBAAuB,CAAC,UAAU;AAChC,aAAO,OAAO,OAAO,MAAM,gBAAgB,EAAE,OAAO,YAAU,OAAO,WAAW,EAAE;AAAA,IACnF;AAAA;AAAA,IAGD,oBAAoB,CAAC,UAAU;AAC7B,YAAM,UAAU,OAAO,OAAO,MAAM,gBAAgB;AACpD,YAAM,eAAe,MAAM,WAAW;AACtC,YAAM,mBAAmB,QAAQ,OAAO,YAAU,OAAO,WAAW,EAAE;AACtE,YAAM,iBAAiB,QAAQ,OAAO,CAAC,OAAO,WAAW,SAAS,OAAO,aAAa,IAAI,CAAC;AAE3F,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,gBAAgB,eAAe,IAAI,KAAK,MAAO,mBAAmB,eAAgB,GAAG,IAAI;AAAA,MAC1F;AAAA,IACF;AAAA,EACF;AACH;;"}