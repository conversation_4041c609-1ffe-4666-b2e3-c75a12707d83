/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.login-container.data-v-e4e4508d {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}
.status-bar.data-v-e4e4508d {
  background: transparent;
}
.login-header.data-v-e4e4508d {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx 40rpx;
}
.login-header .logo-section.data-v-e4e4508d {
  text-align: center;
  color: white;
}
.login-header .logo-section .logo.data-v-e4e4508d {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 32rpx;
}
.login-header .logo-section .app-name.data-v-e4e4508d {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.login-header .logo-section .app-desc.data-v-e4e4508d {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}
.login-form.data-v-e4e4508d {
  background: white;
  border-radius: 48rpx 48rpx 0 0;
  padding: 80rpx 40rpx 40rpx;
}
.login-form .form-item.data-v-e4e4508d {
  margin-bottom: 32rpx;
}
.login-form .form-item .input-wrapper.data-v-e4e4508d {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 0 24rpx;
  height: 96rpx;
}
.login-form .form-item .input-wrapper .input-icon.data-v-e4e4508d {
  color: #999;
  font-size: 32rpx;
  margin-right: 16rpx;
}
.login-form .form-item .input-wrapper .form-input.data-v-e4e4508d {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}
.login-form .form-item .input-wrapper .form-input.data-v-e4e4508d::-webkit-input-placeholder {
  color: #999;
}
.login-form .form-item .input-wrapper .form-input.data-v-e4e4508d::placeholder {
  color: #999;
}
.login-form .form-item .input-wrapper .toggle-password.data-v-e4e4508d {
  margin-left: 16rpx;
  margin-right: 0;
  cursor: pointer;
}
.login-form .form-item .captcha-wrapper.data-v-e4e4508d {
  display: flex;
  gap: 16rpx;
}
.login-form .form-item .captcha-wrapper .captcha-input.data-v-e4e4508d {
  flex: 1;
}
.login-form .form-item .captcha-wrapper .captcha-image.data-v-e4e4508d {
  width: 200rpx;
  height: 96rpx;
  background: #f8f9fa;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.login-form .form-item .captcha-wrapper .captcha-image.data-v-e4e4508d:active {
  opacity: 0.8;
}
.login-form .form-item .captcha-wrapper .captcha-image .captcha-loading.data-v-e4e4508d {
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-form .form-item .captcha-wrapper .captcha-image .captcha-loading .icon-loading.data-v-e4e4508d {
  font-size: 32rpx;
  color: #667eea;
  animation: rotate-e4e4508d 1s linear infinite;
}
.login-form .form-item .captcha-wrapper .captcha-image .captcha-img.data-v-e4e4508d {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
}
.login-form .form-item .captcha-wrapper .captcha-image .captcha-placeholder.data-v-e4e4508d {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  padding: 0 8rpx;
}
.login-form .login-btn.data-v-e4e4508d {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin: 40rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-form .login-btn.loading.data-v-e4e4508d {
  opacity: 0.7;
}
.login-form .login-btn.data-v-e4e4508d:disabled {
  opacity: 0.5;
}
.login-form .login-btn .icon-loading.data-v-e4e4508d {
  margin-right: 16rpx;
  animation: rotate-e4e4508d 1s linear infinite;
}
.login-form .login-actions.data-v-e4e4508d {
  display: flex;
  justify-content: space-between;
}
.login-form .login-actions .action-link.data-v-e4e4508d {
  color: #667eea;
  font-size: 28rpx;
}
.third-party-login.data-v-e4e4508d {
  padding: 40rpx;
}
.third-party-login .divider.data-v-e4e4508d {
  text-align: center;
  margin-bottom: 40rpx;
}
.third-party-login .divider .divider-text.data-v-e4e4508d {
  color: white;
  font-size: 28rpx;
  opacity: 0.8;
}
.third-party-login .third-party-buttons.data-v-e4e4508d {
  display: flex;
  gap: 24rpx;
}
.third-party-login .third-party-buttons .third-party-btn.data-v-e4e4508d {
  flex: 1;
  height: 80rpx;
  border-radius: 20rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}
.third-party-login .third-party-buttons .third-party-btn.wechat.data-v-e4e4508d {
  background: #07c160;
  color: white;
}
.third-party-login .third-party-buttons .third-party-btn.dingtalk.data-v-e4e4508d {
  background: #0089ff;
  color: white;
}
.third-party-login .third-party-buttons .third-party-btn .iconfont.data-v-e4e4508d {
  margin-right: 12rpx;
  font-size: 32rpx;
}
@keyframes rotate-e4e4508d {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
/* 字体图标 */
.icon-user.data-v-e4e4508d::before {
  content: "\e001";
}
.icon-lock.data-v-e4e4508d::before {
  content: "\e002";
}
.icon-eye.data-v-e4e4508d::before {
  content: "\e003";
}
.icon-eye-close.data-v-e4e4508d::before {
  content: "\e004";
}
.icon-shield.data-v-e4e4508d::before {
  content: "\e005";
}
.icon-loading.data-v-e4e4508d::before {
  content: "\e006";
}
.icon-wechat.data-v-e4e4508d::before {
  content: "\e007";
}
.icon-dingtalk.data-v-e4e4508d::before {
  content: "\e008";
}