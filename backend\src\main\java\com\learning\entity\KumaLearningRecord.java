package com.learning.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: kuma_learning_record
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Data
@TableName("kuma_learning_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="kuma_learning_record对象", description="kuma_learning_record")
public class KumaLearningRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**系统标识*/
	@Excel(name = "系统标识", width = 15)
    @ApiModelProperty(value = "系统标识")
    private java.lang.String sysSign;
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private java.lang.String userId;
	/**课程ID*/
	@Excel(name = "课程ID", width = 15)
    @ApiModelProperty(value = "课程ID")
    private java.lang.String courseId;
	/**课时ID*/
	@Excel(name = "课时ID", width = 15)
    @ApiModelProperty(value = "课时ID")
    private java.lang.String lessonId;
	/**学习进度(0-100)*/
	@Excel(name = "学习进度(0-100)", width = 15)
    @ApiModelProperty(value = "学习进度(0-100)")
    private java.lang.Integer progress;
	/**学习时长(秒)*/
	@Excel(name = "学习时长(秒)", width = 15)
    @ApiModelProperty(value = "学习时长(秒)")
    private java.lang.Integer duration;
	/**状态：0-学习中 1-已完成*/
	@Excel(name = "状态：0-学习中 1-已完成", width = 15)
    @ApiModelProperty(value = "状态：0-学习中 1-已完成")
    private java.lang.Integer status;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**删除标志（0-未删除，1-已删除）*/
	@Excel(name = "删除标志（0-未删除，1-已删除）", width = 15)
    @ApiModelProperty(value = "删除标志（0-未删除，1-已删除）")
    @TableLogic
    private java.lang.Integer delFlag;
}
