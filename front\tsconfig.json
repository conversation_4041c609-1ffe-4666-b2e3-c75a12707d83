{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"allowJs": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/utils/*": ["./src/utils/*"], "@/api/*": ["./src/api/*"], "@/store/*": ["./src/store/*"], "@/pages/*": ["./src/pages/*"], "@/static/*": ["./src/static/*"]}, "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "@types/wechat-miniprogram", "@uni-helper/uni-app-types"]}, "vueCompilerOptions": {"nativeTags": ["block", "component", "template", "slot"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.js", "src/**/*.jsx"], "exclude": ["node_modules", "unpackage", "src/**/*.nvue"]}