#!/usr/bin/env node

/**
 * API集成测试脚本
 * 测试jeecg-boot后台接口集成是否正确
 */

const fs = require('fs')
const path = require('path')

console.log('🔗 测试API集成...\n')

let allPassed = true
let warnings = []

/**
 * 检查配置文件
 */
function checkConfigFiles() {
  console.log('📋 检查配置文件...')
  
  const configFiles = [
    {
      name: '.env',
      path: '.env',
      checks: [
        { key: 'VITE_API_BASE_URL', expected: 'http://localhost:8080' },
        { key: 'VITE_API_PREFIX', expected: '/jeecg-boot' }
      ]
    },
    {
      name: '.env.production',
      path: '.env.production',
      checks: [
        { key: 'VITE_API_BASE_URL', expected: 'https://localhost:8080' },
        { key: 'VITE_API_PREFIX', expected: '/jeecg-boot' }
      ]
    },
    {
      name: 'config/api.js',
      path: 'config/api.js',
      checks: [
        { pattern: /jeecg-boot/, description: 'jeecg-boot路径配置' },
        { pattern: /jeecg-boot/, description: 'kuma前缀配置' }
      ]
    }
  ]
  
  let configOk = true
  
  configFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file.path)
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ ${file.name} - 文件不存在`)
      configOk = false
      return
    }
    
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      
      if (file.checks) {
        file.checks.forEach(check => {
          if (check.key && check.expected) {
            // 检查环境变量
            const regex = new RegExp(`${check.key}\\s*=\\s*(.+)`)
            const match = content.match(regex)
            
            if (match && match[1].trim() === check.expected) {
              console.log(`✅ ${file.name} - ${check.key}: ${check.expected}`)
            } else {
              console.log(`❌ ${file.name} - ${check.key}: 期望 ${check.expected}, 实际 ${match ? match[1].trim() : '未找到'}`)
              configOk = false
            }
          } else if (check.pattern && check.description) {
            // 检查模式匹配
            if (check.pattern.test(content)) {
              console.log(`✅ ${file.name} - ${check.description}`)
            } else {
              console.log(`❌ ${file.name} - ${check.description} 未找到`)
              configOk = false
            }
          }
        })
      }
      
    } catch (error) {
      console.log(`❌ ${file.name} - 读取失败: ${error.message}`)
      configOk = false
    }
  })
  
  return configOk
}

/**
 * 检查请求管理器配置
 */
function checkRequestManager() {
  console.log('\n📋 检查请求管理器...')
  
  const requestPath = path.join(process.cwd(), 'utils/request.js')
  
  if (!fs.existsSync(requestPath)) {
    console.log('❌ utils/request.js - 文件不存在')
    return false
  }
  
  try {
    const content = fs.readFileSync(requestPath, 'utf8')
    
    const checks = [
      { pattern: /jeecg-boot/, description: 'jeecg-boot路径引用' },
      { pattern: /config\/api\.js/, description: 'API配置文件导入' },
      { pattern: /getApiUrl/, description: 'API URL获取方法' },
      { pattern: /getRequestHeaders/, description: '请求头获取方法' }
    ]
    
    let requestOk = true
    
    checks.forEach(check => {
      if (check.pattern.test(content)) {
        console.log(`✅ ${check.description}`)
      } else {
        console.log(`❌ ${check.description} - 未找到`)
        requestOk = false
      }
    })
    
    return requestOk
    
  } catch (error) {
    console.log(`❌ 请求管理器检查失败: ${error.message}`)
    return false
  }
}

/**
 * 检查API接口配置
 */
function checkApiEndpoints() {
  console.log('\n📋 检查API接口配置...')
  
  const apiPath = path.join(process.cwd(), 'api/index.js')
  
  if (!fs.existsSync(apiPath)) {
    console.log('❌ api/index.js - 文件不存在')
    return false
  }
  
  try {
    const content = fs.readFileSync(apiPath, 'utf8')
    
    const checks = [
      { pattern: /API_ENDPOINTS/, description: 'API端点配置导入' },
      { pattern: /API_ENDPOINTS\.USER\.LOGIN/, description: '用户登录端点' },
      { pattern: /API_ENDPOINTS\.COURSE\.LIST/, description: '课程列表端点' },
      { pattern: /API_ENDPOINTS\.PRACTICE\.SUBMIT/, description: '练习提交端点' }
    ]
    
    let apiOk = true
    
    checks.forEach(check => {
      if (check.pattern.test(content)) {
        console.log(`✅ ${check.description}`)
      } else {
        console.log(`⚠️  ${check.description} - 未找到`)
        warnings.push(check.description)
      }
    })
    
    return apiOk
    
  } catch (error) {
    console.log(`❌ API接口检查失败: ${error.message}`)
    return false
  }
}

/**
 * 检查Vite代理配置
 */
function checkViteProxy() {
  console.log('\n📋 检查Vite代理配置...')
  
  const vitePath = path.join(process.cwd(), 'vite.config.js')
  
  if (!fs.existsSync(vitePath)) {
    console.log('❌ vite.config.js - 文件不存在')
    return false
  }
  
  try {
    const content = fs.readFileSync(vitePath, 'utf8')
    
    const checks = [
      { pattern: /target:\s*['"]http:\/\/localhost:8080\/jeecg-boot['"]/, description: '代理目标地址' },
      { pattern: /rewrite:.*\/jeecg-boot/, description: '路径重写配置' }
    ]
    
    let viteOk = true
    
    checks.forEach(check => {
      if (check.pattern.test(content)) {
        console.log(`✅ ${check.description}`)
      } else {
        console.log(`❌ ${check.description} - 配置错误`)
        viteOk = false
      }
    })
    
    return viteOk
    
  } catch (error) {
    console.log(`❌ Vite配置检查失败: ${error.message}`)
    return false
  }
}

/**
 * 检查主入口文件
 */
function checkMainFiles() {
  console.log('\n📋 检查主入口文件...')
  
  const mainFiles = [
    { name: 'main.js', path: 'main.js' },
    { name: 'main-mp.js', path: 'main-mp.js' }
  ]
  
  let mainOk = true
  
  mainFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file.path)
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  ${file.name} - 文件不存在`)
      warnings.push(`${file.name} 文件`)
      return
    }
    
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      
      if (content.includes('jeecg-boot')) {
        console.log(`✅ ${file.name} - jeecg-boot配置正确`)
      } else {
        console.log(`❌ ${file.name} - 缺少jeecg-boot配置`)
        mainOk = false
      }
      
    } catch (error) {
      console.log(`❌ ${file.name} - 读取失败: ${error.message}`)
      mainOk = false
    }
  })
  
  return mainOk
}

/**
 * 生成集成报告
 */
function generateReport() {
  console.log('\n📊 生成集成报告...')
  
  const report = {
    timestamp: new Date().toISOString(),
    status: allPassed ? 'SUCCESS' : 'FAILED',
    backend: {
      framework: 'jeecg-boot',
      baseUrl: 'http://localhost:8080/jeecg-boot',
      apiPrefix: '/jeecg-boot'
    },
    frontend: {
      framework: 'uni-app + Vue3',
      platforms: ['H5', 'MP-WEIXIN', 'MP-DINGTALK']
    },
    warnings: warnings
  }
  
  const reportPath = path.join(process.cwd(), 'docs/api-integration-report.json')
  
  try {
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`✅ 集成报告已生成: ${reportPath}`)
  } catch (error) {
    console.log(`⚠️  集成报告生成失败: ${error.message}`)
  }
  
  return report
}

/**
 * 主函数
 */
function main() {
  try {
    const configOk = checkConfigFiles()
    const requestOk = checkRequestManager()
    const apiOk = checkApiEndpoints()
    const viteOk = checkViteProxy()
    const mainOk = checkMainFiles()
    
    allPassed = configOk && requestOk && apiOk && viteOk && mainOk
    
    console.log('\n' + '='.repeat(60))
    
    if (allPassed) {
      console.log('🎉 API集成测试通过！')
      console.log('✅ jeecg-boot后台接口集成成功')
      console.log('✅ 所有配置文件正确')
      console.log('✅ 请求管理器配置完整')
      console.log('✅ API端点配置正确')
      
      if (warnings.length > 0) {
        console.log('\n⚠️  注意事项:')
        warnings.forEach(warning => {
          console.log(`   - ${warning}`)
        })
      }
      
      console.log('\n🚀 可以开始与jeecg-boot后台联调！')
      console.log('\n📡 API地址信息:')
      console.log('   开发环境: http://localhost:8080/jeecg-boot/kuma')
      console.log('   生产环境: https://api.example.com/jeecg-boot/kuma')
      console.log('   代理地址: http://localhost:3000/api -> http://localhost:8080/jeecg-boot/kuma')
      
    } else {
      console.log('❌ API集成测试失败！')
      console.log('请根据上述错误信息修复配置')
      console.log('\n💡 常见问题:')
      console.log('   - 检查环境变量配置是否正确')
      console.log('   - 确认API配置文件是否存在')
      console.log('   - 验证Vite代理配置是否正确')
      console.log('   - 检查请求管理器是否正确导入配置')
      
      process.exit(1)
    }
    
    // 生成集成报告
    generateReport()
    
    console.log('\n' + '='.repeat(60))
    
  } catch (error) {
    console.error('❌ API集成测试过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main()
