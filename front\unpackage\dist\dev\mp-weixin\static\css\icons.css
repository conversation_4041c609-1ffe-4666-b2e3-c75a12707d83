/**
 * 图标样式库
 * 口语便利店小程序图标系统
 * Version: 1.0.0
 */

/* 基础图标类 */
.icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
}

/* 图标尺寸变体 */
.icon-xs {
  width: 16px;
  height: 16px;
}

.icon-sm {
  width: 20px;
  height: 20px;
}

.icon-md {
  width: 24px;
  height: 24px;
}

.icon-lg {
  width: 32px;
  height: 32px;
}

.icon-xl {
  width: 48px;
  height: 48px;
}

/* Tabbar 图标 */
.icon-home {
  background-image: url('/static/tabbar/home.png');
}

.icon-home-active {
  background-image: url('/static/tabbar/home-active.png');
}

.icon-course {
  background-image: url('/static/tabbar/course.png');
}

.icon-course-active {
  background-image: url('/static/tabbar/course-active.png');
}

.icon-checkin {
  background-image: url('/static/tabbar/checkin.png');
}

.icon-checkin-active {
  background-image: url('/static/tabbar/checkin-active.png');
}

.icon-leaderboard {
  background-image: url('/static/tabbar/leaderboard.png');
}

.icon-leaderboard-active {
  background-image: url('/static/tabbar/leaderboard-active.png');
}

.icon-profile {
  background-image: url('/static/tabbar/profile.png');
}

.icon-profile-active {
  background-image: url('/static/tabbar/profile-active.png');
}

/* SVG 图标（用于支持SVG的场景） */
.icon-svg {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-home-svg {
  background-image: url('/static/icons/home.svg');
}

.icon-home-active-svg {
  background-image: url('/static/icons/home-active.svg');
}

.icon-course-svg {
  background-image: url('/static/icons/course.svg');
}

.icon-course-active-svg {
  background-image: url('/static/icons/course-active.svg');
}

.icon-checkin-svg {
  background-image: url('/static/icons/checkin.svg');
}

.icon-checkin-active-svg {
  background-image: url('/static/icons/checkin-active.svg');
}

.icon-leaderboard-svg {
  background-image: url('/static/icons/leaderboard.svg');
}

.icon-leaderboard-active-svg {
  background-image: url('/static/icons/leaderboard-active.svg');
}

.icon-profile-svg {
  background-image: url('/static/icons/profile.svg');
}

.icon-profile-active-svg {
  background-image: url('/static/icons/profile-active.svg');
}

/* 图标状态类 */
.icon-normal {
  opacity: 0.6;
}

.icon-active {
  opacity: 1;
}

.icon-disabled {
  opacity: 0.3;
  pointer-events: none;
}

/* 图标动画效果 */
.icon-hover {
  transition: all 0.2s ease;
}

.icon-hover:hover {
  transform: scale(1.1);
}

.icon-bounce {
  animation: iconBounce 0.6s ease;
}

@keyframes iconBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.icon-pulse {
  animation: iconPulse 1.5s infinite;
}

@keyframes iconPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 图标组合样式 */
.icon-with-text {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-with-text .icon {
  flex-shrink: 0;
}

.icon-badge {
  position: relative;
}

.icon-badge::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #ff4757;
  border-radius: 50%;
  border: 1px solid #fff;
}

/* 响应式图标 */
@media (max-width: 768px) {
  .icon-responsive {
    width: 20px;
    height: 20px;
  }
}

@media (min-width: 769px) {
  .icon-responsive {
    width: 24px;
    height: 24px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .icon-auto {
    filter: invert(1);
  }
}

/* 工具类 */
.icon-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-left {
  float: left;
  margin-right: 8px;
}

.icon-right {
  float: right;
  margin-left: 8px;
}

.icon-block {
  display: block;
  margin: 0 auto;
}

/* 图标按钮样式 */
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.icon-button:hover {
  background-color: rgba(102, 126, 234, 0.1);
}

.icon-button:active {
  background-color: rgba(102, 126, 234, 0.2);
}

.icon-button.active {
  background-color: rgba(102, 126, 234, 0.15);
}

/* 图标列表样式 */
.icon-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.icon-list-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.icon-list-item .icon {
  margin-right: 12px;
}

.icon-list-item:last-child {
  border-bottom: none;
}

/* 图标网格样式 */
.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 16px;
  padding: 16px;
}

.icon-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.icon-grid-item:hover {
  background-color: #f8f9fa;
}

.icon-grid-item .icon {
  margin-bottom: 8px;
}

.icon-grid-item .label {
  font-size: 12px;
  color: #666;
}
