<template>
	<view class="page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 页面内容 -->
		<view class="content">
			<!-- 个人信息头部 -->
			<view class="profile-header">
				<view class="profile-avatar">
					<image :src="getCurrentUserAvatar()" mode="aspectFill" @click="changeAvatar"></image>
				</view>
				<text class="profile-name">{{ getCurrentUserName() }}</text>
				<text class="profile-desc">学习日语 {{ userInfo.studyDays }} 天</text>
				<view class="level-badge">
					<text class="iconfont icon-star"></text>
					<text>{{ userInfo.level }}</text>
				</view>
			</view>

			<!-- 学习统计 -->
			<view class="stats-grid">
				<view class="stat-card" v-for="stat in statistics" :key="stat.key">
					<text class="stat-number">{{ stat.value }}</text>
					<text class="stat-label">{{ stat.label }}</text>
				</view>
			</view>

			<!-- VIP会员卡 -->
			<view v-if="!isVipUser()" class="vip-card" @click="goToVip">
				<view class="vip-content">
					<view class="vip-left">
						<text class="vip-title">
							<text class="iconfont icon-crown"></text>
							升级VIP会员
						</text>
						<text class="vip-desc">解锁全部高级课程</text>
					</view>
					<view class="vip-right">
						<text class="vip-price">¥99/年</text>
						<text class="vip-discount">限时优惠</text>
					</view>
				</view>
			</view>

			<!-- 菜单区域 -->
			<view class="menu-section">
				<!-- 学习管理 -->
				<view class="menu-group">
					<text class="menu-title">学习管理</text>
					
					<view class="menu-item" v-for="item in studyMenus" :key="item.key" @click="handleMenuClick(item)">
						<view class="menu-left">
							<view class="menu-icon" :style="{ background: item.iconBg }">
								<text class="iconfont" :class="item.icon" :style="{ color: item.iconColor }"></text>
							</view>
							<view class="menu-text">
								<text class="menu-label">{{ item.label }}</text>
								<text class="menu-desc">{{ item.description }}</text>
							</view>
						</view>
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</view>

				<!-- 设置选项 -->
				<view class="menu-group">
					<text class="menu-title">设置</text>
					
					<view class="menu-item" v-for="item in settingMenus" :key="item.key" @click="handleMenuClick(item)">
						<view class="menu-left">
							<view class="menu-icon" :style="{ background: item.iconBg }">
								<text class="iconfont" :class="item.icon" :style="{ color: item.iconColor }"></text>
							</view>
							<view class="menu-text">
								<text class="menu-label">{{ item.label }}</text>
								<text class="menu-desc">{{ item.description }}</text>
							</view>
						</view>
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { userApi } from '@/api/index.js'
import * as jeecgUser from '@/api/jeecg-user.js'
import authMixin from '@/mixins/auth-mixin.js'

export default {
	mixins: [authMixin],

	data() {
		return {
			statusBarHeight: 0,
			loading: true,
			userInfo: {
				name: '加载中...',
				avatar: '/static/images/default-avatar.png',
				studyDays: 0,
				level: '新手学员',
				isVip: false,
				nickname: '',
				username: ''
			},
			statistics: [
				{
					key: 'studyDays',
					value: '0',
					label: '学习天数'
				},
				{
					key: 'continuousDays',
					value: '0',
					label: '连续打卡'
				},
				{
					key: 'averageScore',
					value: '0',
					label: '平均分数'
				}
			],
			studyMenus: [
				{
					key: 'report',
					label: '学习报告',
					description: '查看详细学习数据',
					icon: 'icon-chart',
					iconBg: 'linear-gradient(135deg, #667eea, #764ba2)',
					iconColor: 'white'
				},
				{
					key: 'favorites',
					label: '我的收藏',
					description: '收藏的课程和词汇',
					icon: 'icon-bookmark',
					iconBg: 'linear-gradient(135deg, #56ab2f, #a8e6cf)',
					iconColor: 'white'
				// },
				// {
				// 	key: 'download',
				// 	label: '离线下载',
				// 	description: '下载课程离线学习',
				// 	icon: 'icon-download',
				// 	iconBg: 'linear-gradient(135deg, #ff9a9e, #fecfef)',
				// 	iconColor: 'white'
				}
			],
			settingMenus: [
				{
					key: 'notification',
					label: '消息通知',
					description: '学习提醒设置',
					icon: 'icon-bell',
					iconBg: 'linear-gradient(135deg, #a8edea, #fed6e3)',
					iconColor: 'white'
				},
				{
					key: 'settings',
					label: '通用设置',
					description: '语言、主题等设置',
					icon: 'icon-settings',
					iconBg: 'linear-gradient(135deg, #667eea, #764ba2)',
					iconColor: 'white'
				},
				{
					key: 'help',
					label: '帮助中心',
					description: '常见问题与反馈',
					icon: 'icon-help',
					iconBg: 'linear-gradient(135deg, #56ab2f, #a8e6cf)',
					iconColor: 'white'
				},
				{
					key: 'logout',
					label: '退出登录',
					description: '安全退出当前账号',
					icon: 'icon-logout',
					iconBg: 'linear-gradient(135deg, #ff6b6b, #ee5a24)',
					iconColor: 'white'
				}
			]
		}
	},
	
	onLoad() {
		this.initPage()
	},

	onShow() {
		this.loadUserProfile()
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.loadUserProfile().finally(() => {
			uni.stopPullDownRefresh()
		})
	},

	methods: {
		// 获取当前用户头像
		getCurrentUserAvatar() {
			if (this.currentUser && this.currentUser.avatar) {
				return this.currentUser.avatar
			}
			return this.userInfo.avatar || '/static/images/default-avatar.png'
		},

		// 检查是否VIP用户
		isVipUser() {
			if (this.currentUser && this.currentUser.vipStatus) {
				return this.currentUser.vipStatus.isVip || false
			}
			//return this.userInfo.isVip || false
			return true
		},

		initPage() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight

			// 刷新认证状态
			this.checkAuthStatus()

			// 从本地存储获取用户信息（用于扩展信息）
			const localUserInfo = uni.getStorageSync('userInfo')
			if (localUserInfo) {
				this.updateUserInfo(localUserInfo)
			}
		},

		// 加载用户资料 - 使用jeecg-boot接口
		async loadUserProfile() {
			this.loading = true

			try {
				// 并行加载用户信息和统计数据
				const [userResponse, statsResponse] = await Promise.all([
					jeecgUser.getUserInfo(),
					userApi.getStudyStats()
				])

				if (userResponse.success) {
					this.updateUserInfo(userResponse.data)
					// 更新本地存储
					uni.setStorageSync('userInfo', userResponse.data)
				}

				if (statsResponse.success) {
					this.updateStatistics(statsResponse.data)
				}

			} catch (error) {
				console.error('加载用户资料失败:', error)

				// 如果没有本地数据，显示错误提示
				if (!this.userInfo.username) {
					uni.showToast({
						title: '加载失败，请下拉刷新',
						icon: 'none'
					})
				}
			} finally {
				this.loading = false
			}
		},

		// 更新用户信息
		updateUserInfo(userData) {
			this.userInfo = {
				...this.userInfo,
				name: userData.nickname || userData.username || '用户',
				avatar: userData.avatar || '/static/images/default-avatar.png',
				studyDays: userData.studyDays || 0,
				level: userData.level || '新手学员',
				isVip: userData.isVip || false,
				nickname: userData.nickname || '',
				username: userData.username || ''
			}
		},

		// 更新统计数据
		updateStatistics(statsData) {
			this.statistics = [
				{
					key: 'studyDays',
					value: String(statsData.totalStudyDays || 0),
					label: '学习天数'
				},
				{
					key: 'continuousDays',
					value: String(statsData.continuousCheckinDays || 0),
					label: '连续打卡'
				},
				{
					key: 'averageScore',
					value: String(Math.round(statsData.averageScore || 0)),
					label: '平均分数'
				}
			]
		},
		
		changeAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					// 上传头像
					this.uploadAvatar(res.tempFilePaths[0])
				}
			})
		},
		
		async uploadAvatar(filePath) {
			try {
				uni.showLoading({ title: '上传中...' })

				// 调用头像上传API
				const response = await userApi.uploadAvatar(filePath)

				if (response.success) {
					this.userInfo.avatar = response.data.avatarUrl || filePath

					// 更新本地存储
					const localUserInfo = uni.getStorageSync('userInfo')
					if (localUserInfo) {
						localUserInfo.avatar = this.userInfo.avatar
						uni.setStorageSync('userInfo', localUserInfo)
					}

					uni.showToast({
						title: '头像更新成功',
						icon: 'success'
					})
				} else {
					throw new Error(response.message || '上传失败')
				}

			} catch (error) {
				console.error('上传头像失败:', error)

				// 临时使用本地图片
				this.userInfo.avatar = filePath

				uni.showToast({
					title: '上传失败，使用本地图片',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		
		goToVip() {
			uni.navigateTo({
				url: '/pages/vip/vip'
			})
		},
		
		handleMenuClick(item) {
			switch (item.key) {
				case 'report':
					this.goToReport()
					break
				case 'favorites':
					this.goToFavorites()
					break
				case 'download':
					this.goToDownload()
					break
				case 'notification':
					this.goToNotification()
					break
				case 'settings':
					this.goToSettings()
					break
				case 'help':
					this.goToHelp()
					break
				case 'logout':
					this.handleLogout()
					break
				default:
					uni.showToast({
						title: '功能开发中',
						icon: 'none'
					})
			}
		},
		
		goToReport() {
			uni.navigateTo({
				url: '/pages/report/report'
			})
		},
		
		goToFavorites() {
			uni.navigateTo({
				url: '/pages/favorites/favorites'
			})
		},
		
		goToDownload() {
			uni.navigateTo({
				url: '/pages/download/download'
			})
		},
		
		goToNotification() {
			uni.navigateTo({
				url: '/pages/settings/notification'
			})
		},
		
		goToSettings() {
			uni.navigateTo({
				url: '/pages/settings/settings'
			})
		},
		
		goToHelp() {
			uni.navigateTo({
				url: '/pages/help/help'
			})
		},
		
		handleLogout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						this.performLogout()
					}
				}
			})
		},
		
		async performLogout() {
			try {
				uni.showLoading({ title: '退出中...' })

				// 调用jeecg-boot退出登录API
				const logoutResult = await jeecgUser.logout()

				if (logoutResult.success) {
					uni.showToast({
						title: '退出成功',
						icon: 'success'
					})
				} else {
					// 即使API调用失败，也清除本地数据
					uni.showToast({
						title: '已退出登录',
						icon: 'success'
					})
				}

				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}, 1000)

			} catch (error) {
				console.error('退出登录失败:', error)

				// 即使API调用失败，也清除本地数据
				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				})

				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}, 1000)
			} finally {
				uni.hideLoading()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-bar {
	background: transparent;
}

.content {
	padding-bottom: 200rpx;
}

.profile-header {
	text-align: center;
	padding: 60rpx 40rpx;
	color: white;
	
	.profile-avatar {
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
		margin: 0 auto 32rpx;
		border: 8rpx solid white;
		overflow: hidden;
		box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
		
		image {
			width: 100%;
			height: 100%;
		}
	}
	
	.profile-name {
		display: block;
		font-size: 40rpx;
		font-weight: bold;
		margin-bottom: 16rpx;
	}
	
	.profile-desc {
		display: block;
		font-size: 28rpx;
		opacity: 0.8;
		margin-bottom: 24rpx;
	}
	
	.level-badge {
		background: linear-gradient(135deg, #ffd700, #ffed4e);
		color: #333;
		padding: 12rpx 24rpx;
		border-radius: 40rpx;
		font-size: 24rpx;
		font-weight: bold;
		display: inline-flex;
		align-items: center;
		
		.iconfont {
			margin-right: 8rpx;
			font-size: 28rpx;
		}
	}
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 32rpx;
	margin: 0 40rpx 40rpx;
	
	.stat-card {
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20rpx);
		border-radius: 32rpx;
		padding: 40rpx;
		text-align: center;
		box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);
		
		.stat-number {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			color: #667eea;
			margin-bottom: 8rpx;
		}
		
		.stat-label {
			color: #666;
			font-size: 24rpx;
		}
	}
}

.vip-card {
	background: linear-gradient(135deg, #ffd700, #ffed4e);
	border-radius: 32rpx;
	margin: 0 40rpx 40rpx;
	padding: 40rpx;
	color: #333;
	position: relative;
	overflow: hidden;
	
	&::before {
		content: '';
		position: absolute;
		top: -50%;
		right: -50%;
		width: 100%;
		height: 100%;
		background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
	}
	
	.vip-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		position: relative;
		z-index: 1;
		
		.vip-left {
			flex: 1;
			
			.vip-title {
				display: block;
				font-size: 36rpx;
				font-weight: bold;
				margin-bottom: 8rpx;
				
				.iconfont {
					margin-right: 16rpx;
					font-size: 40rpx;
				}
			}
			
			.vip-desc {
				font-size: 28rpx;
				opacity: 0.8;
			}
		}
		
		.vip-right {
			text-align: right;
			
			.vip-price {
				display: block;
				font-size: 36rpx;
				font-weight: bold;
				line-height: 1;
			}
			
			.vip-discount {
				font-size: 24rpx;
				opacity: 0.7;
			}
		}
	}
}

.menu-section {
	background: #f8fafc;
	border-radius: 48rpx 48rpx 0 0;
	padding: 40rpx 0;
	min-height: 800rpx;
	
	.menu-group {
		margin: 0 32rpx 48rpx;
		
		.menu-title {
			display: block;
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 24rpx;
			padding: 0 8rpx;
		}
	}
}

.menu-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 40rpx;
	background: white;
	border-radius: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	transition: all 0.2s;
	
	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	}
	
	.menu-left {
		display: flex;
		align-items: center;
		flex: 1;
		
		.menu-icon {
			width: 80rpx;
			height: 80rpx;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 24rpx;
			
			.iconfont {
				font-size: 36rpx;
			}
		}
		
		.menu-text {
			flex: 1;
			
			.menu-label {
				display: block;
				font-weight: 500;
				color: #333;
				font-size: 32rpx;
				margin-bottom: 4rpx;
			}
			
			.menu-desc {
				color: #666;
				font-size: 24rpx;
			}
		}
	}
	
	.icon-arrow-right {
		color: #ccc;
		font-size: 32rpx;
	}
}

/* 字体图标 */
.icon-chart::before { content: '\e029'; }
.icon-bookmark::before { content: '\e030'; }
.icon-download::before { content: '\e031'; }
.icon-bell::before { content: '\e032'; }
.icon-settings::before { content: '\e033'; }
.icon-help::before { content: '\e034'; }
.icon-logout::before { content: '\e035'; }
</style>
