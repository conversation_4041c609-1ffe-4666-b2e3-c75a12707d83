"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_auth = require("../../utils/auth.js");
const _sfc_main = {
  data() {
    return {
      userInfoType: "",
      permissionsType: "",
      rolesType: "",
      userInfoResult: { success: false, message: "" },
      permissionsResult: { success: false, message: "" },
      rolesResult: { success: false, message: "" },
      rawUserInfo: "",
      rawPermissions: "",
      rawRoles: "",
      testLogs: []
    };
  },
  onLoad() {
    this.addLog("JSON解析错误修复测试页面加载", "info");
    this.checkCurrentState();
  },
  onShow() {
    this.checkCurrentState();
  },
  methods: {
    // 添加日志
    addLog(message, type = "info") {
      const now = /* @__PURE__ */ new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;
      this.testLogs.unshift({
        time,
        message,
        type
      });
      if (this.testLogs.length > 20) {
        this.testLogs = this.testLogs.slice(0, 20);
      }
    },
    // 检查当前状态
    checkCurrentState() {
      this.checkStorageTypes();
      this.checkRawData();
      this.testSafeGetters();
    },
    // 检查存储数据类型
    checkStorageTypes() {
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        const permissions = common_vendor.index.getStorageSync("permissions");
        const roles = common_vendor.index.getStorageSync("roles");
        this.userInfoType = userInfo ? typeof userInfo : "null";
        this.permissionsType = permissions ? typeof permissions : "null";
        this.rolesType = roles ? typeof roles : "null";
      } catch (error) {
        this.addLog(`检查存储类型失败: ${error.message}`, "error");
      }
    },
    // 检查原始数据
    checkRawData() {
      try {
        this.rawUserInfo = JSON.stringify(common_vendor.index.getStorageSync("userInfo")) || "null";
        this.rawPermissions = JSON.stringify(common_vendor.index.getStorageSync("permissions")) || "null";
        this.rawRoles = JSON.stringify(common_vendor.index.getStorageSync("roles")) || "null";
      } catch (error) {
        this.addLog(`检查原始数据失败: ${error.message}`, "error");
      }
    },
    // 测试安全获取函数
    testSafeGetters() {
      try {
        const userInfo = utils_auth.getUserInfo();
        this.userInfoResult = {
          success: true,
          message: userInfo ? `成功获取用户信息: ${userInfo.username || "unknown"}` : "用户信息为空"
        };
      } catch (error) {
        this.userInfoResult = {
          success: false,
          message: `获取失败: ${error.message}`
        };
      }
      try {
        const permissions = utils_auth.getPermissions();
        this.permissionsResult = {
          success: true,
          message: `成功获取权限: ${permissions.length} 项`
        };
      } catch (error) {
        this.permissionsResult = {
          success: false,
          message: `获取失败: ${error.message}`
        };
      }
      try {
        const roles = utils_auth.getRoles();
        this.rolesResult = {
          success: true,
          message: `成功获取角色: ${roles.length} 项`
        };
      } catch (error) {
        this.rolesResult = {
          success: false,
          message: `获取失败: ${error.message}`
        };
      }
    },
    // 模拟无效用户信息
    testInvalidUserInfo() {
      this.addLog("模拟无效用户信息数据", "warning");
      common_vendor.index.setStorageSync("userInfo", { username: "test", realname: "测试用户" });
      this.checkCurrentState();
    },
    // 模拟无效权限信息
    testInvalidPermissions() {
      this.addLog("模拟无效权限信息数据", "warning");
      common_vendor.index.setStorageSync("permissions", "invalid_permissions_data");
      this.checkCurrentState();
    },
    // 模拟无效角色信息
    testInvalidRoles() {
      this.addLog("模拟无效角色信息数据", "warning");
      common_vendor.index.setStorageSync("roles", { admin: true });
      this.checkCurrentState();
    },
    // 模拟损坏数据
    testCorruptedData() {
      this.addLog("模拟损坏的JSON数据", "warning");
      common_vendor.index.setStorageSync("userInfo", '{"username":"test","invalid":}');
      common_vendor.index.setStorageSync("permissions", "[invalid,json]");
      common_vendor.index.setStorageSync("roles", "{broken:json}");
      this.checkCurrentState();
    },
    // 恢复正常用户信息
    testValidUserInfo() {
      this.addLog("恢复正常用户信息", "success");
      const validUserInfo = {
        id: "1001",
        username: "testuser",
        realname: "测试用户",
        avatar: "/static/images/default-avatar.png",
        phone: "13800138000",
        email: "<EMAIL>"
      };
      utils_auth.setUserInfo(validUserInfo);
      this.checkCurrentState();
    },
    // 恢复正常权限信息
    testValidPermissions() {
      this.addLog("恢复正常权限信息", "success");
      const validPermissions = ["user:read", "user:write", "course:read"];
      utils_auth.setPermissions(validPermissions);
      this.checkCurrentState();
    },
    // 恢复正常角色信息
    testValidRoles() {
      this.addLog("恢复正常角色信息", "success");
      const validRoles = ["user", "student"];
      utils_auth.setRoles(validRoles);
      this.checkCurrentState();
    },
    // 清除所有数据
    clearAllData() {
      this.addLog("清除所有测试数据", "info");
      utils_auth.removeUserInfo();
      common_vendor.index.removeStorageSync("permissions");
      common_vendor.index.removeStorageSync("roles");
      this.checkCurrentState();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.userInfoType),
    b: common_vendor.t($data.permissionsType),
    c: common_vendor.t($data.rolesType),
    d: common_vendor.t($data.userInfoResult.message),
    e: common_vendor.n($data.userInfoResult.success ? "success" : "error"),
    f: common_vendor.t($data.permissionsResult.message),
    g: common_vendor.n($data.permissionsResult.success ? "success" : "error"),
    h: common_vendor.t($data.rolesResult.message),
    i: common_vendor.n($data.rolesResult.success ? "success" : "error"),
    j: common_vendor.o((...args) => $options.testInvalidUserInfo && $options.testInvalidUserInfo(...args)),
    k: common_vendor.o((...args) => $options.testInvalidPermissions && $options.testInvalidPermissions(...args)),
    l: common_vendor.o((...args) => $options.testInvalidRoles && $options.testInvalidRoles(...args)),
    m: common_vendor.o((...args) => $options.testCorruptedData && $options.testCorruptedData(...args)),
    n: common_vendor.o((...args) => $options.testValidUserInfo && $options.testValidUserInfo(...args)),
    o: common_vendor.o((...args) => $options.testValidPermissions && $options.testValidPermissions(...args)),
    p: common_vendor.o((...args) => $options.testValidRoles && $options.testValidRoles(...args)),
    q: common_vendor.o((...args) => $options.clearAllData && $options.clearAllData(...args)),
    r: common_vendor.t($data.rawUserInfo),
    s: common_vendor.t($data.rawPermissions),
    t: common_vendor.t($data.rawRoles),
    v: common_vendor.f($data.testLogs, (log, index, i0) => {
      return {
        a: common_vendor.t(log.time),
        b: common_vendor.t(log.message),
        c: common_vendor.n(log.type),
        d: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f200c6d7"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/json-parse-error-test.js.map
