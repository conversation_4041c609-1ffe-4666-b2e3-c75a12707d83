<view class="login-test-container data-v-6bd25c5c"><view class="test-header data-v-6bd25c5c"><text class="test-title data-v-6bd25c5c">登录功能测试</text></view><view class="test-section data-v-6bd25c5c"><text class="section-title data-v-6bd25c5c">认证状态</text><view class="status-item data-v-6bd25c5c"><text class="status-label data-v-6bd25c5c">登录状态:</text><text class="{{['status-value', 'data-v-6bd25c5c', b]}}">{{a}}</text></view><view class="status-item data-v-6bd25c5c"><text class="status-label data-v-6bd25c5c">用户信息:</text><text class="status-value data-v-6bd25c5c">{{c}}</text></view><view class="status-item data-v-6bd25c5c"><text class="status-label data-v-6bd25c5c">Token:</text><text class="status-value token data-v-6bd25c5c">{{d}}</text></view><view class="status-item data-v-6bd25c5c"><text class="status-label data-v-6bd25c5c">验证码Path参数:</text><text class="status-value data-v-6bd25c5c">{{e}}</text></view></view><view class="test-section data-v-6bd25c5c"><text class="section-title data-v-6bd25c5c">请求头测试</text><view class="headers-display data-v-6bd25c5c"><view class="header-item data-v-6bd25c5c"><text class="header-key data-v-6bd25c5c">X-Access-Token:</text><text class="header-value data-v-6bd25c5c">{{f}}</text></view><view class="header-item data-v-6bd25c5c"><text class="header-key data-v-6bd25c5c">X-Access-Client:</text><text class="header-value data-v-6bd25c5c">{{g}}</text></view><view class="header-item data-v-6bd25c5c"><text class="header-key data-v-6bd25c5c">X-Tenant-Id:</text><text class="header-value data-v-6bd25c5c">{{h}}</text></view></view></view><view class="test-section data-v-6bd25c5c"><text class="section-title data-v-6bd25c5c">验证码测试</text><view class="captcha-test data-v-6bd25c5c"><view class="captcha-display data-v-6bd25c5c"><image wx:if="{{i}}" src="{{j}}" class="test-captcha-img data-v-6bd25c5c"></image><text wx:else class="no-captcha data-v-6bd25c5c">暂无验证码</text></view><view class="captcha-actions data-v-6bd25c5c"><button class="test-btn small data-v-6bd25c5c" bindtap="{{k}}">获取验证码</button><button class="test-btn small data-v-6bd25c5c" bindtap="{{l}}">刷新验证码</button><button class="test-btn small data-v-6bd25c5c" bindtap="{{m}}">生成新Path</button><button class="test-btn small data-v-6bd25c5c" bindtap="{{n}}">重置Path</button></view></view></view><view class="test-section data-v-6bd25c5c"><text class="section-title data-v-6bd25c5c">功能测试</text><view class="test-buttons data-v-6bd25c5c"><button class="test-btn data-v-6bd25c5c" bindtap="{{o}}">测试登录</button><button class="test-btn data-v-6bd25c5c" bindtap="{{p}}">测试登出</button><button class="test-btn data-v-6bd25c5c" bindtap="{{q}}">测试需要登录</button><button class="test-btn data-v-6bd25c5c" bindtap="{{r}}">测试API调用</button><button class="test-btn data-v-6bd25c5c" bindtap="{{s}}">刷新状态</button><button class="test-btn danger data-v-6bd25c5c" bindtap="{{t}}">清除认证</button></view></view><view class="test-section data-v-6bd25c5c"><text class="section-title data-v-6bd25c5c">测试日志</text><scroll-view class="log-container data-v-6bd25c5c" scroll-y><view wx:for="{{v}}" wx:for-item="log" wx:key="d" class="log-item data-v-6bd25c5c"><text class="log-time data-v-6bd25c5c">{{log.a}}</text><text class="{{['log-message', 'data-v-6bd25c5c', log.c]}}">{{log.b}}</text></view></scroll-view></view></view>