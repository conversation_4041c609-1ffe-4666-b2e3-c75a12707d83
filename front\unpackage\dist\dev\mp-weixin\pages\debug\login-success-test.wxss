/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.login-success-test-container.data-v-201a54f3 {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
}
.test-header.data-v-201a54f3 {
  text-align: center;
  margin-bottom: 40rpx;
}
.test-header .test-title.data-v-201a54f3 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-201a54f3 {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.test-section .section-title.data-v-201a54f3 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 16rpx;
}
.auth-status .status-item.data-v-201a54f3, .auth-status .storage-item.data-v-201a54f3, .storage-check .status-item.data-v-201a54f3, .storage-check .storage-item.data-v-201a54f3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.auth-status .status-item.data-v-201a54f3:last-child, .auth-status .storage-item.data-v-201a54f3:last-child, .storage-check .status-item.data-v-201a54f3:last-child, .storage-check .storage-item.data-v-201a54f3:last-child {
  border-bottom: none;
}
.auth-status .status-item .status-label.data-v-201a54f3, .auth-status .status-item .storage-key.data-v-201a54f3, .auth-status .storage-item .status-label.data-v-201a54f3, .auth-status .storage-item .storage-key.data-v-201a54f3, .storage-check .status-item .status-label.data-v-201a54f3, .storage-check .status-item .storage-key.data-v-201a54f3, .storage-check .storage-item .status-label.data-v-201a54f3, .storage-check .storage-item .storage-key.data-v-201a54f3 {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
  flex-shrink: 0;
}
.auth-status .status-item .status-value.data-v-201a54f3, .auth-status .status-item .storage-value.data-v-201a54f3, .auth-status .storage-item .status-value.data-v-201a54f3, .auth-status .storage-item .storage-value.data-v-201a54f3, .storage-check .status-item .status-value.data-v-201a54f3, .storage-check .status-item .storage-value.data-v-201a54f3, .storage-check .storage-item .status-value.data-v-201a54f3, .storage-check .storage-item .storage-value.data-v-201a54f3 {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}
.auth-status .status-item .status-value.success.data-v-201a54f3, .auth-status .status-item .storage-value.success.data-v-201a54f3, .auth-status .storage-item .status-value.success.data-v-201a54f3, .auth-status .storage-item .storage-value.success.data-v-201a54f3, .storage-check .status-item .status-value.success.data-v-201a54f3, .storage-check .status-item .storage-value.success.data-v-201a54f3, .storage-check .storage-item .status-value.success.data-v-201a54f3, .storage-check .storage-item .storage-value.success.data-v-201a54f3 {
  color: #52c41a;
}
.auth-status .status-item .status-value.error.data-v-201a54f3, .auth-status .status-item .storage-value.error.data-v-201a54f3, .auth-status .storage-item .status-value.error.data-v-201a54f3, .auth-status .storage-item .storage-value.error.data-v-201a54f3, .storage-check .status-item .status-value.error.data-v-201a54f3, .storage-check .status-item .storage-value.error.data-v-201a54f3, .storage-check .storage-item .status-value.error.data-v-201a54f3, .storage-check .storage-item .storage-value.error.data-v-201a54f3 {
  color: #ff4d4f;
}
.auth-status .status-item .status-value.token.data-v-201a54f3, .auth-status .status-item .storage-value.token.data-v-201a54f3, .auth-status .storage-item .status-value.token.data-v-201a54f3, .auth-status .storage-item .storage-value.token.data-v-201a54f3, .storage-check .status-item .status-value.token.data-v-201a54f3, .storage-check .status-item .storage-value.token.data-v-201a54f3, .storage-check .storage-item .status-value.token.data-v-201a54f3, .storage-check .storage-item .storage-value.token.data-v-201a54f3 {
  font-family: monospace;
  font-size: 24rpx;
}
.mock-login .mock-data.data-v-201a54f3 {
  margin-bottom: 24rpx;
}
.mock-login .mock-data .mock-title.data-v-201a54f3 {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}
.mock-login .mock-data .mock-code.data-v-201a54f3 {
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 16rpx;
}
.mock-login .mock-data .mock-code .code-text.data-v-201a54f3 {
  font-family: monospace;
  font-size: 22rpx;
  color: #333;
  white-space: pre-wrap;
}
.test-buttons.data-v-201a54f3 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.test-buttons .test-btn.data-v-201a54f3 {
  height: 80rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}
.test-buttons .test-btn.primary.data-v-201a54f3 {
  background: #1890ff;
}
.test-buttons .test-btn.success.data-v-201a54f3 {
  background: #52c41a;
}
.test-buttons .test-btn.warning.data-v-201a54f3 {
  background: #faad14;
}
.test-buttons .test-btn.error.data-v-201a54f3 {
  background: #ff4d4f;
}
.test-buttons .test-btn.data-v-201a54f3:active {
  opacity: 0.8;
}
.log-container.data-v-201a54f3 {
  height: 300rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 16rpx;
}
.log-container .log-item.data-v-201a54f3 {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 24rpx;
}
.log-container .log-item .log-time.data-v-201a54f3 {
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
}
.log-container .log-item .log-message.data-v-201a54f3 {
  color: #333;
  word-break: break-all;
}
.log-container .log-item .log-message.success.data-v-201a54f3 {
  color: #52c41a;
}
.log-container .log-item .log-message.error.data-v-201a54f3 {
  color: #ff4d4f;
}
.log-container .log-item .log-message.warning.data-v-201a54f3 {
  color: #faad14;
}
.log-container .log-item .log-message.info.data-v-201a54f3 {
  color: #1890ff;
}