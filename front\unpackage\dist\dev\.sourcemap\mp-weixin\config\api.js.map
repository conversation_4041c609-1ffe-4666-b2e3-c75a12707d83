{"version": 3, "file": "api.js", "sources": ["config/api.js"], "sourcesContent": ["/**\n * 统一API配置管理\n * 简化配置，统一H5和小程序的接口调用\n */\n\n// 统一API配置 - 不再区分环境，统一使用后台地址\nexport const API_CONFIG = {\n  // 后台接口基础URL - 统一配置\n  BASE_URL: 'http://localhost:8080/jeecg-boot',\n\n  // 请求超时时间\n  TIMEOUT: 10000,\n\n  // 重试次数\n  RETRY_COUNT: 3,\n\n  // 重试延迟（毫秒）\n  RETRY_DELAY: 1000\n}\n\n// 获取API基础URL - 统一返回配置的地址\nexport const getApiBaseUrl = () => {\n  return API_CONFIG.BASE_URL\n}\n\n// 获取完整的API URL\nexport const getApiUrl = (endpoint = '') => {\n  const baseUrl = getApiBaseUrl()\n\n  // 移除endpoint开头的斜杠\n  const cleanEndpoint = endpoint.replace(/^\\/+/, '')\n\n  // 如果endpoint为空，返回基础URL\n  if (!cleanEndpoint) {\n    return baseUrl\n  }\n\n  return `${baseUrl}/${cleanEndpoint}`\n}\n\n// 请求头配置 - 统一jeecg-boot标准\nexport const getRequestHeaders = (customHeaders = {}) => {\n  const headers = {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    ...customHeaders\n  }\n\n  // 添加jeecg-boot认证token\n  const token = uni.getStorageSync('X-Access-Token') || uni.getStorageSync('token')\n  if (token) {\n    headers['X-Access-Token'] = token\n    headers['X-Access-Client'] = 'miniApp'\n    headers['X-Tenant-Id'] = ''\n  }\n\n  return headers\n}\n\n// API端点配置 - jeecg-boot标准接口\nexport const API_ENDPOINTS = {\n  // 用户相关 - 使用jeecg-boot标准接口\n  USER: {\n    LOGIN: '/sys/login',                    // jeecg-boot标准登录接口\n    LOGOUT: '/sys/logout',                  // jeecg-boot标准登出接口\n    INFO: '/sys/user/userInfo',             // jeecg-boot获取用户信息\n    DEPART_INFO: '/sys/user/departInfo',    // jeecg-boot获取部门信息\n    UPDATE_PASSWORD: '/sys/user/updatePassword', // jeecg-boot修改密码\n    UPLOAD_AVATAR: '/sys/common/upload',    // jeecg-boot文件上传\n\n    // 验证码相关接口\n    CAPTCHA: '/sys/randomImage/{key}',      // jeecg-boot获取验证码（带path参数）\n    CHECK_CAPTCHA: '/sys/checkCaptcha',     // jeecg-boot验证验证码\n\n    // 自定义扩展接口（基于jeecg-boot）\n    PROFILE: '/app/user/profile',           // 获取用户扩展信息\n    UPDATE_PROFILE: '/app/user/profile',    // 更新用户扩展信息\n    CHECKIN: '/app/user/checkin',           // 用户签到\n    CHECKIN_HISTORY: '/app/user/checkin/history', // 签到历史\n    STATISTICS: '/app/user/statistics'      // 学习统计\n  },\n  \n  // 课程相关\n  COURSE: {\n    LIST: '/app/course/list',\n    DETAIL: '/app/course/{id}',\n    LESSONS: '/app/course/{id}/lessons',\n    FAVORITE: '/app/course/{id}/favorite'\n  },\n  \n  // 课时相关\n  LESSON: {\n    DETAIL: '/app/lesson/{id}',\n    START: '/app/lesson/{id}/start',\n    COMPLETE: '/app/lesson/{id}/complete'\n  },\n  \n  // 练习相关\n  PRACTICE: {\n    SUBMIT: '/app/practice/submit',\n    SUBMIT_BASE64: '/app/practice/submitBase64',\n    HISTORY: '/app/practice/history',\n    STATISTICS: '/app/practice/statistics',\n    LANGUAGES: '/app/practice/supportedLanguages',\n    CATEGORIES: '/app/practice/supportedCategories',\n    CONFIG: '/app/practice/evaluationConfig'\n  },\n  \n  // 排行榜相关\n  LEADERBOARD: {\n    POINTS: '/app/leaderboard/points',\n    STUDY_TIME: '/app/leaderboard/studytime',\n    CHECKIN: '/app/leaderboard/checkin'\n  },\n  \n  // 支付相关\n  PAYMENT: {\n    CREATE: '/api/v1/payment/create',\n    STATUS: '/api/v1/payment/status/{id}',\n    METHODS: '/api/v1/payment/methods'\n  },\n  \n  // AI相关\n  AI: {\n    EVALUATE: '/api/v1/ai/evaluate',\n    HISTORY: '/api/v1/ai/history'\n  },\n  \n  // 系统相关\n  SYSTEM: {\n    CONFIG: '/api/v1/system/config',\n    VERSION: '/api/v1/system/version',\n    FEEDBACK: '/api/v1/system/feedback',\n    HELP: '/api/v1/system/help'\n  }\n}\n\n// 替换URL中的参数\nexport const replaceUrlParams = (url, params = {}) => {\n  let result = url\n  \n  Object.keys(params).forEach(key => {\n    result = result.replace(`{${key}}`, params[key])\n  })\n  \n  return result\n}\n\n// 构建查询字符串\nexport const buildQueryString = (params = {}) => {\n  const query = Object.keys(params)\n    .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')\n    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)\n    .join('&')\n  \n  return query ? `?${query}` : ''\n}\n\n// 获取完整的API URL（包含参数）\nexport const buildApiUrl = (endpoint, urlParams = {}, queryParams = {}) => {\n  // 替换URL参数\n  const url = replaceUrlParams(endpoint, urlParams)\n  \n  // 构建查询字符串\n  const queryString = buildQueryString(queryParams)\n  \n  // 返回完整URL\n  return getApiUrl(url) + queryString\n}\n\n// 调试配置 - 简化配置，默认关闭调试\nexport const DEBUG_CONFIG = {\n  // 是否启用调试\n  enabled: false,\n\n  // 是否显示控制台日志\n  console: false,\n\n  // 是否显示请求详情\n  request: false,\n\n  // 是否显示响应详情\n  response: false\n}\n\n// 导出默认配置\nexport default {\n  API_CONFIG,\n  API_ENDPOINTS,\n  DEBUG_CONFIG,\n  getApiBaseUrl,\n  getApiUrl,\n  getRequestHeaders,\n  replaceUrlParams,\n  buildQueryString,\n  buildApiUrl\n}\n"], "names": ["uni"], "mappings": ";;;;;;;;;;;;;;;;;;;AAMY,MAAC,aAAa;AAAA;AAAA,EAExB,UAAU;AAAA;AAAA,EAGV,SAAS;AAAA;AAAA,EAGT,aAAa;AAAA;AAAA,EAGb,aAAa;AACf;AAGY,MAAC,gBAAgB,MAAM;AACjC,SAAO,WAAW;AACpB;AAGY,MAAC,YAAY,CAAC,WAAW,OAAO;AAC1C,QAAM,UAAU,cAAe;AAG/B,QAAM,gBAAgB,SAAS,QAAQ,QAAQ,EAAE;AAGjD,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACR;AAED,SAAO,GAAG,OAAO,IAAI,aAAa;AACpC;AAGY,MAAC,oBAAoB,CAAC,gBAAgB,OAAO;AACvD,QAAM,UAAU;AAAA,IACd,gBAAgB;AAAA,IAChB,UAAU;AAAA,KACP;AAIL,QAAM,QAAQA,cAAAA,MAAI,eAAe,gBAAgB,KAAKA,cAAG,MAAC,eAAe,OAAO;AAChF,MAAI,OAAO;AACT,YAAQ,gBAAgB,IAAI;AAC5B,YAAQ,iBAAiB,IAAI;AAC7B,YAAQ,aAAa,IAAI;AAAA,EAC1B;AAED,SAAO;AACT;AAGY,MAAC,gBAAgB;AAAA;AAAA,EAE3B,MAAM;AAAA,IACJ,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,IACR,MAAM;AAAA;AAAA,IACN,aAAa;AAAA;AAAA,IACb,iBAAiB;AAAA;AAAA,IACjB,eAAe;AAAA;AAAA;AAAA,IAGf,SAAS;AAAA;AAAA,IACT,eAAe;AAAA;AAAA;AAAA,IAGf,SAAS;AAAA;AAAA,IACT,gBAAgB;AAAA;AAAA,IAChB,SAAS;AAAA;AAAA,IACT,iBAAiB;AAAA;AAAA,IACjB,YAAY;AAAA;AAAA,EACb;AAAA;AAAA,EAGD,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,EACX;AAAA;AAAA,EAGD,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,EACX;AAAA;AAAA,EAGD,UAAU;AAAA,IACR,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,QAAQ;AAAA,EACT;AAAA;AAAA,EAGD,aAAa;AAAA,IACX,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,SAAS;AAAA,EACV;AAAA;AAAA,EAGD,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,EACV;AAAA;AAAA,EAGD,IAAI;AAAA,IACF,UAAU;AAAA,IACV,SAAS;AAAA,EACV;AAAA;AAAA,EAGD,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,EACP;AACH;AAGY,MAAC,mBAAmB,CAAC,KAAK,SAAS,OAAO;AACpD,MAAI,SAAS;AAEb,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,aAAS,OAAO,QAAQ,IAAI,GAAG,KAAK,OAAO,GAAG,CAAC;AAAA,EACnD,CAAG;AAED,SAAO;AACT;AAGY,MAAC,mBAAmB,CAAC,SAAS,OAAO;AAC/C,QAAM,QAAQ,OAAO,KAAK,MAAM,EAC7B,OAAO,SAAO,OAAO,GAAG,MAAM,UAAa,OAAO,GAAG,MAAM,QAAQ,OAAO,GAAG,MAAM,EAAE,EACrF,IAAI,SAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EAC1E,KAAK,GAAG;AAEX,SAAO,QAAQ,IAAI,KAAK,KAAK;AAC/B;AAGY,MAAC,cAAc,CAAC,UAAU,YAAY,CAAA,GAAI,cAAc,CAAA,MAAO;AAEzE,QAAM,MAAM,iBAAiB,UAAU,SAAS;AAGhD,QAAM,cAAc,iBAAiB,WAAW;AAGhD,SAAO,UAAU,GAAG,IAAI;AAC1B;AAGY,MAAC,eAAe;AAAA;AAAA,EAE1B,SAAS;AAAA;AAAA,EAGT,SAAS;AAAA;AAAA,EAGT,SAAS;AAAA;AAAA,EAGT,UAAU;AACZ;AAGA,MAAe,MAAA;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;;;;;;;;;"}