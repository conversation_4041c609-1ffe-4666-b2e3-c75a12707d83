<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>口语便利店 - 排行榜</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { 
            overflow: hidden; 
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar { display: none; }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .content {
            height: calc(100% - 44px - 83px);
            overflow-y: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .content::-webkit-scrollbar { display: none; }
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
            font-size: 12px;
        }
        .tab-item.active {
            color: #667eea;
        }
        .podium {
            display: flex;
            justify-content: center;
            align-items: end;
            padding: 20px;
            gap: 10px;
        }
        .podium-item {
            text-align: center;
            color: white;
        }
        .podium-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 8px;
            border: 3px solid white;
            overflow: hidden;
        }
        .podium-item.first .podium-avatar {
            width: 80px;
            height: 80px;
            border-color: #ffd700;
        }
        .podium-base {
            background: rgba(255,255,255,0.2);
            border-radius: 8px 8px 0 0;
            padding: 16px 12px 8px;
            position: relative;
        }
        .podium-item.first .podium-base {
            height: 100px;
            background: rgba(255,215,0,0.3);
        }
        .podium-item.second .podium-base {
            height: 80px;
            background: rgba(192,192,192,0.3);
        }
        .podium-item.third .podium-base {
            height: 60px;
            background: rgba(205,127,50,0.3);
        }
        .crown {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            color: #ffd700;
            font-size: 24px;
        }
        .ranking-list {
            background: #f8fafc;
            border-radius: 24px 24px 0 0;
            margin-top: 20px;
            padding: 20px 0;
            min-height: 400px;
        }
        .ranking-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: white;
            margin: 8px 16px;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .ranking-item.current-user {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        .rank-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #64748b;
            margin-right: 12px;
        }
        .ranking-item.current-user .rank-number {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
        }
        .user-info {
            flex: 1;
        }
        .user-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .user-streak {
            font-size: 12px;
            opacity: 0.7;
        }
        .user-score {
            text-align: right;
            font-weight: bold;
        }
        .tab-buttons {
            display: flex;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            padding: 4px;
            margin: 20px;
        }
        .tab-button {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            color: rgba(255,255,255,0.7);
            cursor: pointer;
            transition: all 0.2s;
        }
        .tab-button.active {
            background: rgba(255,255,255,0.9);
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 头部标题 -->
                <div class="px-6 py-6 text-center">
                    <h1 class="text-white text-2xl font-bold mb-2">学习排行榜</h1>
                    <p class="text-white/80 text-base">与朋友一起进步</p>
                </div>

                <!-- 时间范围切换 -->
                <div class="tab-buttons">
                    <div class="tab-button">本周</div>
                    <div class="tab-button active">本月</div>
                    <div class="tab-button">总榜</div>
                </div>

                <!-- 前三名领奖台 -->
                <div class="podium">
                    <!-- 第二名 -->
                    <div class="podium-item second">
                        <div class="podium-base">
                            <div class="podium-avatar">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face" alt="用户头像" class="w-full h-full object-cover">
                            </div>
                            <div class="text-sm font-bold">小樱</div>
                            <div class="text-xs opacity-80">28天</div>
                        </div>
                    </div>

                    <!-- 第一名 -->
                    <div class="podium-item first">
                        <div class="podium-base">
                            <i class="fas fa-crown crown"></i>
                            <div class="podium-avatar">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face" alt="用户头像" class="w-full h-full object-cover">
                            </div>
                            <div class="text-sm font-bold">田中君</div>
                            <div class="text-xs opacity-80">31天</div>
                        </div>
                    </div>

                    <!-- 第三名 -->
                    <div class="podium-item third">
                        <div class="podium-base">
                            <div class="podium-avatar">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face" alt="用户头像" class="w-full h-full object-cover">
                            </div>
                            <div class="text-sm font-bold">美咲</div>
                            <div class="text-xs opacity-80">25天</div>
                        </div>
                    </div>
                </div>

                <!-- 排行榜列表 -->
                <div class="ranking-list">
                    <div class="px-6 mb-4">
                        <h3 class="text-lg font-bold text-gray-800">完整排行榜</h3>
                        <p class="text-sm text-gray-600">基于本月连续打卡天数</p>
                    </div>

                    <!-- 第4名 -->
                    <div class="ranking-item">
                        <div class="rank-number">4</div>
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face" alt="用户头像" class="w-full h-full object-cover">
                        </div>
                        <div class="user-info">
                            <div class="user-name">佐藤</div>
                            <div class="user-streak">连续打卡 22天</div>
                        </div>
                        <div class="user-score">
                            <div class="text-lg">22</div>
                            <div class="text-xs opacity-70">天</div>
                        </div>
                    </div>

                    <!-- 第5名 -->
                    <div class="ranking-item">
                        <div class="rank-number">5</div>
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=48&h=48&fit=crop&crop=face" alt="用户头像" class="w-full h-full object-cover">
                        </div>
                        <div class="user-info">
                            <div class="user-name">山田</div>
                            <div class="user-streak">连续打卡 20天</div>
                        </div>
                        <div class="user-score">
                            <div class="text-lg">20</div>
                            <div class="text-xs opacity-70">天</div>
                        </div>
                    </div>

                    <!-- 当前用户 -->
                    <div class="ranking-item current-user">
                        <div class="rank-number">8</div>
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=48&h=48&fit=crop&crop=face" alt="用户头像" class="w-full h-full object-cover">
                        </div>
                        <div class="user-info">
                            <div class="user-name">我 (小明)</div>
                            <div class="user-streak">连续打卡 12天</div>
                        </div>
                        <div class="user-score">
                            <div class="text-lg">12</div>
                            <div class="text-xs opacity-70">天</div>
                        </div>
                    </div>

                    <!-- 第9名 -->
                    <div class="ranking-item">
                        <div class="rank-number">9</div>
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=48&h=48&fit=crop&crop=face" alt="用户头像" class="w-full h-full object-cover">
                        </div>
                        <div class="user-info">
                            <div class="user-name">花子</div>
                            <div class="user-streak">连续打卡 11天</div>
                        </div>
                        <div class="user-score">
                            <div class="text-lg">11</div>
                            <div class="text-xs opacity-70">天</div>
                        </div>
                    </div>

                    <!-- 第10名 -->
                    <div class="ranking-item">
                        <div class="rank-number">10</div>
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=48&h=48&fit=crop&crop=face" alt="用户头像" class="w-full h-full object-cover">
                        </div>
                        <div class="user-info">
                            <div class="user-name">太郎</div>
                            <div class="user-streak">连续打卡 10天</div>
                        </div>
                        <div class="user-score">
                            <div class="text-lg">10</div>
                            <div class="text-xs opacity-70">天</div>
                        </div>
                    </div>

                    <!-- 激励信息 -->
                    <div class="mx-6 mt-6 p-4 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg">
                        <div class="text-center">
                            <i class="fas fa-trophy text-yellow-500 text-2xl mb-2"></i>
                            <p class="font-bold text-gray-800 mb-1">继续加油！</p>
                            <p class="text-sm text-gray-600">再坚持 3 天就能超越前一名</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="tab-bar">
                <div class="tab-item">
                    <i class="fas fa-home text-xl mb-1"></i>
                    <span>首页</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-book text-xl mb-1"></i>
                    <span>课程</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-calendar-check text-xl mb-1"></i>
                    <span>打卡</span>
                </div>
                <div class="tab-item active">
                    <i class="fas fa-trophy text-xl mb-1"></i>
                    <span>排行</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
