/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.page.data-v-338bd53f {
  min-height: 100vh;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}
.status-bar.data-v-338bd53f {
  background: transparent;
}
.nav-bar.data-v-338bd53f {
  background: transparent;
}
.nav-bar .nav-content.data-v-338bd53f {
  height: 120rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}
.nav-bar .nav-content .nav-left.data-v-338bd53f, .nav-bar .nav-content .nav-right.data-v-338bd53f {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nav-bar .nav-content .nav-left .iconfont.data-v-338bd53f, .nav-bar .nav-content .nav-right .iconfont.data-v-338bd53f {
  color: white;
  font-size: 40rpx;
}
.nav-bar .nav-content .nav-title.data-v-338bd53f {
  flex: 1;
  text-align: center;
}
.nav-bar .nav-content .nav-title .title.data-v-338bd53f {
  display: block;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.2;
}
.nav-bar .nav-content .nav-title .subtitle.data-v-338bd53f {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}
.content.data-v-338bd53f {
  padding: 40rpx;
}
.progress-section.data-v-338bd53f {
  text-align: center;
  margin-bottom: 60rpx;
}
.progress-section .progress-circle.data-v-338bd53f {
  width: 240rpx;
  height: 240rpx;
  border-radius: 50%;
  background: conic-gradient(#10b981 0deg 108deg, rgba(255, 255, 255, 0.3) 108deg 360deg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
}
.progress-section .progress-circle.data-v-338bd53f::before {
  content: "";
  position: absolute;
  width: 180rpx;
  height: 180rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}
.progress-section .progress-circle .progress-number.data-v-338bd53f {
  position: relative;
  z-index: 1;
  font-size: 48rpx;
  font-weight: bold;
  color: #10b981;
  line-height: 1;
}
.progress-section .progress-circle .progress-total.data-v-338bd53f {
  position: relative;
  z-index: 1;
  font-size: 28rpx;
  color: #666;
}
.sentence-card.data-v-338bd53f {
  text-align: center;
  margin-bottom: 40rpx;
}
.sentence-card .instruction.data-v-338bd53f {
  display: block;
  color: #666;
  font-size: 28rpx;
  margin-bottom: 32rpx;
}
.sentence-card .sentence-content.data-v-338bd53f {
  margin-bottom: 48rpx;
}
.sentence-card .sentence-content .japanese-text.data-v-338bd53f {
  display: block;
  font-size: 48rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 24rpx;
}
.sentence-card .sentence-content .chinese-text.data-v-338bd53f {
  display: block;
  font-size: 32rpx;
  color: #666;
}
.sentence-card .audio-controls.data-v-338bd53f {
  display: flex;
  justify-content: center;
  gap: 40rpx;
}
.sentence-card .audio-controls .control-btn.data-v-338bd53f {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.sentence-card .audio-controls .control-btn .iconfont.data-v-338bd53f {
  color: #666;
  font-size: 36rpx;
}
.recording-section.data-v-338bd53f {
  text-align: center;
  margin-bottom: 40rpx;
}
.recording-section .recording-instruction.data-v-338bd53f {
  display: block;
  color: #666;
  font-size: 28rpx;
  margin-bottom: 32rpx;
}
.recording-section .record-button.data-v-338bd53f {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
}
.recording-section .record-button.recording.data-v-338bd53f {
  background: linear-gradient(135deg, #ff4757, #c44569);
  animation: pulse-338bd53f 1.5s infinite;
}
.recording-section .record-button.completed.data-v-338bd53f {
  background: linear-gradient(135deg, #2ed573, #1e90ff);
}
.recording-section .record-button .iconfont.data-v-338bd53f {
  color: white;
  font-size: 64rpx;
}
.recording-section .record-hint.data-v-338bd53f {
  color: #666;
  font-size: 24rpx;
}
@keyframes pulse-338bd53f {
0% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
100% {
    transform: scale(1);
}
}
.score-section.data-v-338bd53f {
  margin-bottom: 40rpx;
}
.score-section .score-header.data-v-338bd53f {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}
.score-section .score-header .iconfont.data-v-338bd53f {
  color: #f59e0b;
  font-size: 40rpx;
  margin-right: 16rpx;
}
.score-section .score-header .score-title.data-v-338bd53f {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.score-section .total-score.data-v-338bd53f {
  text-align: center;
  font-size: 72rpx;
  font-weight: bold;
  color: #f59e0b;
  margin-bottom: 48rpx;
}
.score-section .score-details .score-item.data-v-338bd53f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.score-section .score-details .score-item .score-label.data-v-338bd53f {
  color: #333;
  font-size: 28rpx;
}
.score-section .score-details .score-item .score-bar-container.data-v-338bd53f {
  display: flex;
  align-items: center;
}
.score-section .score-details .score-item .score-bar-container .score-bar.data-v-338bd53f {
  width: 200rpx;
  height: 16rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 16rpx;
}
.score-section .score-details .score-item .score-bar-container .score-bar .score-fill.data-v-338bd53f {
  height: 100%;
  background: linear-gradient(90deg, #00b894, #00cec9);
  border-radius: 8rpx;
  transition: width 0.5s ease;
}
.score-section .score-details .score-item .score-bar-container .score-value.data-v-338bd53f {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}
.score-section .ai-feedback.data-v-338bd53f {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16rpx;
  margin-top: 32rpx;
}
.score-section .ai-feedback .iconfont.data-v-338bd53f {
  color: #f59e0b;
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}
.score-section .ai-feedback .feedback-text.data-v-338bd53f {
  flex: 1;
  color: #333;
  font-size: 28rpx;
  line-height: 1.5;
}
.action-buttons.data-v-338bd53f {
  display: flex;
  gap: 24rpx;
}
.action-buttons .btn-secondary.data-v-338bd53f, .action-buttons .btn-primary.data-v-338bd53f {
  flex: 1;
  padding: 24rpx;
  border-radius: 32rpx;
  font-weight: bold;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-buttons .btn-secondary .iconfont.data-v-338bd53f, .action-buttons .btn-primary .iconfont.data-v-338bd53f {
  margin-right: 16rpx;
  font-size: 32rpx;
}
.action-buttons .btn-secondary.data-v-338bd53f {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}
.action-buttons .btn-primary.data-v-338bd53f {
  background: linear-gradient(135deg, #16a34a, #22c55e);
  color: white;
  box-shadow: 0 16rpx 64rpx rgba(34, 197, 94, 0.3);
}

/* 字体图标 */
.icon-close.data-v-338bd53f::before {
  content: "\e018";
}
.icon-volume.data-v-338bd53f::before {
  content: "\e019";
}
.icon-robot.data-v-338bd53f::before {
  content: "\e020";
}
.icon-check.data-v-338bd53f::before {
  content: "\e021";
}
.icon-stop.data-v-338bd53f::before {
  content: "\e022";
}
.icon-redo.data-v-338bd53f::before {
  content: "\e023";
}