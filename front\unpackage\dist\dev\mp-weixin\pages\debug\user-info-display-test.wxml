<view class="user-info-test-container data-v-a7929c82"><view class="test-header data-v-a7929c82"><text class="test-title data-v-a7929c82">用户信息显示测试</text></view><view class="test-section data-v-a7929c82"><text class="section-title data-v-a7929c82">认证状态检查</text><view class="auth-status data-v-a7929c82"><view class="status-item data-v-a7929c82"><text class="status-label data-v-a7929c82">登录状态:</text><text class="{{['status-value', 'data-v-a7929c82', b]}}">{{a}}</text></view><view class="status-item data-v-a7929c82"><text class="status-label data-v-a7929c82">Token状态:</text><text class="{{['status-value', 'data-v-a7929c82', d]}}">{{c}}</text></view></view></view><view class="test-section data-v-a7929c82"><text class="section-title data-v-a7929c82">auth-mixin用户信息</text><view class="user-info-display data-v-a7929c82"><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">用户ID:</text><text class="info-value data-v-a7929c82">{{e}}</text></view><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">用户名:</text><text class="info-value data-v-a7929c82">{{f}}</text></view><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">真实姓名:</text><text class="info-value data-v-a7929c82">{{g}}</text></view><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">显示名称:</text><text class="info-value data-v-a7929c82">{{h}}</text></view><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">头像:</text><text class="info-value data-v-a7929c82">{{i}}</text></view><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">手机号:</text><text class="info-value data-v-a7929c82">{{j}}</text></view><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">邮箱:</text><text class="info-value data-v-a7929c82">{{k}}</text></view></view></view><view class="test-section data-v-a7929c82"><text class="section-title data-v-a7929c82">本地存储用户信息</text><view class="storage-info data-v-a7929c82"><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">存储状态:</text><text class="{{['info-value', 'data-v-a7929c82', m]}}">{{l}}</text></view><view wx:if="{{n}}" class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">用户名:</text><text class="info-value data-v-a7929c82">{{o}}</text></view><view wx:if="{{p}}" class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">真实姓名:</text><text class="info-value data-v-a7929c82">{{q}}</text></view></view></view><view class="test-section data-v-a7929c82"><text class="section-title data-v-a7929c82">全局状态检查</text><view class="global-state data-v-a7929c82"><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">globalData.isLogin:</text><text class="{{['info-value', 'data-v-a7929c82', s]}}">{{r}}</text></view><view class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">globalData.userInfo:</text><text class="{{['info-value', 'data-v-a7929c82', v]}}">{{t}}</text></view><view wx:if="{{w}}" class="info-item data-v-a7929c82"><text class="info-label data-v-a7929c82">全局用户名:</text><text class="info-value data-v-a7929c82">{{x}}</text></view></view></view><view class="test-section data-v-a7929c82"><text class="section-title data-v-a7929c82">页面用户信息显示测试</text><view class="page-display-test data-v-a7929c82"><view class="display-item data-v-a7929c82"><text class="display-label data-v-a7929c82">首页样式显示:</text><text class="display-value data-v-a7929c82">{{y}}，加油！</text></view><view class="display-item data-v-a7929c82"><text class="display-label data-v-a7929c82">个人中心样式:</text><view class="profile-style data-v-a7929c82"><image class="avatar data-v-a7929c82" src="{{z}}" mode="aspectFill"></image><text class="name data-v-a7929c82">{{A}}</text></view></view></view></view><view class="test-section data-v-a7929c82"><text class="section-title data-v-a7929c82">操作测试</text><view class="test-buttons data-v-a7929c82"><button class="test-btn primary data-v-a7929c82" bindtap="{{B}}">刷新用户信息</button><button class="test-btn success data-v-a7929c82" bindtap="{{C}}">检查认证状态</button><button class="test-btn warning data-v-a7929c82" bindtap="{{D}}">模拟登录</button><button class="test-btn error data-v-a7929c82" bindtap="{{E}}">清除用户信息</button></view></view><view class="test-section data-v-a7929c82"><text class="section-title data-v-a7929c82">测试日志</text><scroll-view class="log-container data-v-a7929c82" scroll-y><view wx:for="{{F}}" wx:for-item="log" wx:key="d" class="log-item data-v-a7929c82"><text class="log-time data-v-a7929c82">{{log.a}}</text><text class="{{['log-message', 'data-v-a7929c82', log.c]}}">{{log.b}}</text></view></scroll-view></view></view>