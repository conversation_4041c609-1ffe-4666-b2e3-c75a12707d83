# Tabbar 图标说明

本目录包含小程序底部导航栏的图标文件。

## 图标列表

### 首页 (Home)
- `home.png` - 未选中状态的首页图标
- `home-active.png` - 选中状态的首页图标

### 课程 (Course)
- `course.png` - 未选中状态的课程图标
- `course-active.png` - 选中状态的课程图标

### 打卡 (Checkin)
- `checkin.png` - 未选中状态的打卡图标
- `checkin-active.png` - 选中状态的打卡图标

### 排行榜 (Leaderboard)
- `leaderboard.png` - 未选中状态的排行榜图标
- `leaderboard-active.png` - 选中状态的排行榜图标

### 个人中心 (Profile)
- `profile.png` - 未选中状态的个人中心图标
- `profile-active.png` - 选中状态的个人中心图标

## 设计规范

### 尺寸要求
- 推荐尺寸：48x48px
- 支持尺寸：24x24px, 32x32px, 48x48px, 64x64px
- 格式：PNG（支持透明背景）

### 颜色规范
- **未选中状态**：#999999 (浅灰色)
- **选中状态**：#667eea (主题蓝色)
- **背景**：透明

### 设计风格
- 线性图标风格
- 2px 描边粗细
- 圆角处理
- 简洁明了的视觉表达

## 使用方法

### 在 pages.json 中配置
```json
{
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#667eea",
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/tabbar/home.png",
        "selectedIconPath": "static/tabbar/home-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/course/index",
        "iconPath": "static/tabbar/course.png",
        "selectedIconPath": "static/tabbar/course-active.png",
        "text": "课程"
      },
      {
        "pagePath": "pages/checkin/index",
        "iconPath": "static/tabbar/checkin.png",
        "selectedIconPath": "static/tabbar/checkin-active.png",
        "text": "打卡"
      },
      {
        "pagePath": "pages/leaderboard/index",
        "iconPath": "static/tabbar/leaderboard.png",
        "selectedIconPath": "static/tabbar/leaderboard-active.png",
        "text": "排行"
      },
      {
        "pagePath": "pages/profile/index",
        "iconPath": "static/tabbar/profile.png",
        "selectedIconPath": "static/tabbar/profile-active.png",
        "text": "我的"
      }
    ]
  }
}
```

## 图标转换

如需从SVG转换为PNG，可以使用以下工具：

### 1. 在线转换工具
- [CloudConvert](https://cloudconvert.com/svg-to-png)
- [Convertio](https://convertio.co/svg-png/)

### 2. 命令行工具
```bash
# 使用 ImageMagick
convert -background transparent -size 48x48 input.svg output.png

# 使用 Inkscape
inkscape --export-png=output.png --export-width=48 --export-height=48 input.svg
```

### 3. Node.js 脚本
运行项目中的转换脚本：
```bash
cd front
npm install sharp  # 安装依赖
node scripts/convert-icons.js
```

## 注意事项

1. **文件命名**：使用小写字母和连字符，保持一致性
2. **文件大小**：尽量控制在 5KB 以内，优化加载性能
3. **兼容性**：确保在不同设备和平台上显示正常
4. **更新维护**：修改图标时同时更新对应的选中和未选中状态

## 版本历史

- v1.0.0 - 初始版本，包含5个tabbar图标
- 创建时间：2024-03-22
- 设计师：Kuma Team
