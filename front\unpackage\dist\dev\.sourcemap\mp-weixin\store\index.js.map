{"version": 3, "file": "index.js", "sources": ["store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\nimport user from './modules/user'\nimport course from './modules/course'\nimport checkin from './modules/checkin'\nimport practice from './modules/practice'\n\nconst store = createStore({\n  modules: {\n    user,\n    course,\n    checkin,\n    practice\n  },\n  \n  state: {\n    // 全局状态\n    systemInfo: {},\n    networkType: 'unknown',\n    isOnline: true\n  },\n  \n  mutations: {\n    SET_SYSTEM_INFO(state, systemInfo) {\n      state.systemInfo = systemInfo\n    },\n    \n    SET_NETWORK_TYPE(state, networkType) {\n      state.networkType = networkType\n    },\n    \n    SET_ONLINE_STATUS(state, isOnline) {\n      state.isOnline = isOnline\n    }\n  },\n  \n  actions: {\n    // 初始化应用\n    async initApp({ commit, dispatch }) {\n      try {\n        // 获取系统信息\n        const systemInfo = uni.getSystemInfoSync()\n        commit('SET_SYSTEM_INFO', systemInfo)\n        \n        // 监听网络状态\n        uni.onNetworkStatusChange((res) => {\n          commit('SET_NETWORK_TYPE', res.networkType)\n          commit('SET_ONLINE_STATUS', res.isConnected)\n        })\n        \n        // 检查登录状态\n        await dispatch('user/checkLoginStatus')\n        \n        console.log('应用初始化完成')\n      } catch (error) {\n        console.error('应用初始化失败:', error)\n      }\n    }\n  },\n  \n  getters: {\n    // 是否为小程序环境\n    isMiniProgram: (state) => {\n      return state.systemInfo.uniPlatform && state.systemInfo.uniPlatform.startsWith('mp-')\n    },\n    \n    // 是否为App环境\n    isApp: (state) => {\n      return state.systemInfo.uniPlatform === 'app'\n    },\n    \n    // 是否为H5环境\n    isH5: (state) => {\n      return state.systemInfo.uniPlatform === 'h5'\n    },\n    \n    // 状态栏高度\n    statusBarHeight: (state) => {\n      return state.systemInfo.statusBarHeight || 0\n    },\n    \n    // 导航栏高度\n    navBarHeight: (state, getters) => {\n      if (getters.isMiniProgram) {\n        // 小程序需要考虑胶囊按钮\n        return 44\n      }\n      return 44\n    }\n  }\n})\n\nexport default store\n"], "names": ["createStore", "user", "course", "checkin", "practice", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAMK,MAAC,QAAQA,cAAAA,YAAY;AAAA,EACxB,SAAS;AAAA,IACX,MAAIC,mBAAI;AAAA,IACR,QAAIC,qBAAM;AAAA,IACV,SAAIC,sBAAO;AAAA,IACX,UAAIC,uBAAQ;AAAA,EACT;AAAA,EAED,OAAO;AAAA;AAAA,IAEL,YAAY,CAAE;AAAA,IACd,aAAa;AAAA,IACb,UAAU;AAAA,EACX;AAAA,EAED,WAAW;AAAA,IACT,gBAAgB,OAAO,YAAY;AACjC,YAAM,aAAa;AAAA,IACpB;AAAA,IAED,iBAAiB,OAAO,aAAa;AACnC,YAAM,cAAc;AAAA,IACrB;AAAA,IAED,kBAAkB,OAAO,UAAU;AACjC,YAAM,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAED,QAAQ,IAAsB;AAAA,iDAAtB,EAAE,QAAQ,YAAY;AAClC,YAAI;AAEF,gBAAM,aAAaC,cAAG,MAAC,kBAAmB;AAC1C,iBAAO,mBAAmB,UAAU;AAGpCA,8BAAI,sBAAsB,CAAC,QAAQ;AACjC,mBAAO,oBAAoB,IAAI,WAAW;AAC1C,mBAAO,qBAAqB,IAAI,WAAW;AAAA,UACrD,CAAS;AAGD,gBAAM,SAAS,uBAAuB;AAEtCA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,SAAS;AAAA,QACtB,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,wBAAc,YAAY,KAAK;AAAA,QAChC;AAAA,MACF;AAAA;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,eAAe,CAAC,UAAU;AACxB,aAAO,MAAM,WAAW,eAAe,MAAM,WAAW,YAAY,WAAW,KAAK;AAAA,IACrF;AAAA;AAAA,IAGD,OAAO,CAAC,UAAU;AAChB,aAAO,MAAM,WAAW,gBAAgB;AAAA,IACzC;AAAA;AAAA,IAGD,MAAM,CAAC,UAAU;AACf,aAAO,MAAM,WAAW,gBAAgB;AAAA,IACzC;AAAA;AAAA,IAGD,iBAAiB,CAAC,UAAU;AAC1B,aAAO,MAAM,WAAW,mBAAmB;AAAA,IAC5C;AAAA;AAAA,IAGD,cAAc,CAAC,OAAO,YAAY;AAChC,UAAI,QAAQ,eAAe;AAEzB,eAAO;AAAA,MACR;AACD,aAAO;AAAA,IACR;AAAA,EACF;AACH,CAAC;;"}