{"version": 3, "file": "checkin.js", "sources": ["store/modules/checkin.js"], "sourcesContent": ["const checkin = {\n  namespaced: true,\n  \n  state: {\n    // 今日是否已打卡\n    isCheckedInToday: false,\n    \n    // 连续打卡天数\n    continuousDays: 0,\n    \n    // 本月打卡记录\n    monthlyRecords: [],\n    \n    // 打卡统计\n    checkinStats: {\n      totalDays: 0,\n      maxContinuous: 0,\n      thisMonthDays: 0,\n      completionRate: 0\n    },\n    \n    // 补打卡次数\n    makeupChances: {\n      total: 5, // 每月总次数\n      used: 0,  // 已使用次数\n      remaining: 5 // 剩余次数\n    },\n    \n    // 排行榜数据\n    leaderboard: {\n      weekly: [],\n      monthly: [],\n      total: []\n    },\n    \n    // 当前用户排名\n    userRanking: {\n      weekly: { rank: 0, days: 0 },\n      monthly: { rank: 0, days: 0 },\n      total: { rank: 0, days: 0 }\n    }\n  },\n  \n  mutations: {\n    // 设置今日打卡状态\n    SET_CHECKIN_TODAY(state, isChecked) {\n      state.isCheckedInToday = isChecked\n    },\n    \n    // 设置连续打卡天数\n    SET_CONTINUOUS_DAYS(state, days) {\n      state.continuousDays = days\n    },\n    \n    // 设置月度打卡记录\n    SET_MONTHLY_RECORDS(state, records) {\n      state.monthlyRecords = records\n    },\n    \n    // 添加打卡记录\n    ADD_CHECKIN_RECORD(state, record) {\n      const existingIndex = state.monthlyRecords.findIndex(\n        r => r.date === record.date\n      )\n      if (existingIndex > -1) {\n        state.monthlyRecords[existingIndex] = record\n      } else {\n        state.monthlyRecords.push(record)\n      }\n    },\n    \n    // 设置打卡统计\n    SET_CHECKIN_STATS(state, stats) {\n      state.checkinStats = { ...state.checkinStats, ...stats }\n    },\n    \n    // 设置补打卡次数\n    SET_MAKEUP_CHANCES(state, chances) {\n      state.makeupChances = { ...state.makeupChances, ...chances }\n    },\n    \n    // 使用补打卡次数\n    USE_MAKEUP_CHANCE(state) {\n      if (state.makeupChances.remaining > 0) {\n        state.makeupChances.used += 1\n        state.makeupChances.remaining -= 1\n      }\n    },\n    \n    // 设置排行榜数据\n    SET_LEADERBOARD(state, { type, data }) {\n      state.leaderboard[type] = data\n    },\n    \n    // 设置用户排名\n    SET_USER_RANKING(state, { type, ranking }) {\n      state.userRanking[type] = ranking\n    }\n  },\n  \n  actions: {\n    // 执行打卡\n    async performCheckin({ commit, rootState }, { coursesCompleted, studyDuration }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/checkin',\n          method: 'POST',\n          data: { coursesCompleted, studyDuration },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          const { continuousDays, checkinRecord } = response.data.data\n          \n          commit('SET_CHECKIN_TODAY', true)\n          commit('SET_CONTINUOUS_DAYS', continuousDays)\n          commit('ADD_CHECKIN_RECORD', checkinRecord)\n          \n          // 更新用户状态中的连续打卡天数\n          commit('user/UPDATE_CONTINUOUS_DAYS', continuousDays, { root: true })\n          \n          return { success: true, data: response.data.data }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('打卡失败:', error)\n        return { success: false, message: '打卡失败，请重试' }\n      }\n    },\n    \n    // 补打卡\n    async performMakeup({ commit, state, rootState }, date) {\n      if (state.makeupChances.remaining <= 0) {\n        return { success: false, message: '补打卡次数已用完' }\n      }\n      \n      try {\n        const response = await uni.request({\n          url: '/api/v1/checkin/makeup',\n          method: 'POST',\n          data: { date },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          const { checkinRecord } = response.data.data\n          \n          commit('USE_MAKEUP_CHANCE')\n          commit('ADD_CHECKIN_RECORD', { ...checkinRecord, isMakeup: true })\n          \n          return { success: true, data: response.data.data }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('补打卡失败:', error)\n        return { success: false, message: '补打卡失败，请重试' }\n      }\n    },\n    \n    // 加载打卡记录\n    async loadCheckinRecords({ commit, rootState }, { year, month }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/checkin/records',\n          method: 'GET',\n          data: { year, month },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          commit('SET_MONTHLY_RECORDS', response.data.data.records)\n          \n          // 检查今日是否已打卡\n          const today = new Date().toISOString().split('T')[0]\n          const todayRecord = response.data.data.records.find(r => r.date === today)\n          commit('SET_CHECKIN_TODAY', !!todayRecord)\n          \n          return { success: true, data: response.data.data }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('加载打卡记录失败:', error)\n        return { success: false, message: '加载失败，请重试' }\n      }\n    },\n    \n    // 加载打卡统计\n    async loadCheckinStats({ commit, rootState }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/checkin/statistics',\n          method: 'GET',\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          const { stats, makeupChances, continuousDays } = response.data.data\n          \n          commit('SET_CHECKIN_STATS', stats)\n          commit('SET_MAKEUP_CHANCES', makeupChances)\n          commit('SET_CONTINUOUS_DAYS', continuousDays)\n          \n          return { success: true, data: response.data.data }\n        }\n      } catch (error) {\n        console.error('加载打卡统计失败:', error)\n      }\n    },\n    \n    // 加载排行榜\n    async loadLeaderboard({ commit, rootState }, { type = 'monthly', limit = 50 }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/leaderboard',\n          method: 'GET',\n          data: { type, limit },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          const { leaderboard, userRanking } = response.data.data\n          \n          commit('SET_LEADERBOARD', { type, data: leaderboard })\n          commit('SET_USER_RANKING', { type, ranking: userRanking })\n          \n          return { success: true, data: response.data.data }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('加载排行榜失败:', error)\n        return { success: false, message: '加载失败，请重试' }\n      }\n    }\n  },\n  \n  getters: {\n    // 获取指定日期的打卡记录\n    getRecordByDate: (state) => (date) => {\n      return state.monthlyRecords.find(record => record.date === date)\n    },\n    \n    // 检查指定日期是否已打卡\n    isCheckedOnDate: (state) => (date) => {\n      return state.monthlyRecords.some(record => record.date === date)\n    },\n    \n    // 检查指定日期是否为补打卡\n    isMakeupOnDate: (state) => (date) => {\n      const record = state.monthlyRecords.find(record => record.date === date)\n      return record ? record.isMakeup : false\n    },\n    \n    // 获取本月打卡天数\n    thisMonthCheckinDays: (state) => {\n      return state.monthlyRecords.filter(record => !record.isMakeup).length\n    },\n    \n    // 获取本月补打卡天数\n    thisMonthMakeupDays: (state) => {\n      return state.monthlyRecords.filter(record => record.isMakeup).length\n    },\n    \n    // 获取本月完成率\n    thisMonthCompletionRate: (state, getters) => {\n      const today = new Date()\n      const daysInMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate()\n      const currentDay = today.getDate()\n      const checkedDays = getters.thisMonthCheckinDays\n      \n      return currentDay > 0 ? Math.round((checkedDays / currentDay) * 100) : 0\n    },\n    \n    // 获取排行榜前三名\n    getTopThree: (state) => (type = 'monthly') => {\n      return state.leaderboard[type].slice(0, 3)\n    },\n    \n    // 获取用户在排行榜中的位置\n    getUserRankInList: (state) => (type = 'monthly') => {\n      const list = state.leaderboard[type]\n      const userId = state.userRanking[type].userId\n      return list.findIndex(user => user.id === userId)\n    },\n    \n    // 距离下一个里程碑的天数\n    daysToNextMilestone: (state) => {\n      const milestones = [7, 15, 30, 50, 100, 200, 365]\n      const current = state.continuousDays\n      const next = milestones.find(m => m > current)\n      return next ? next - current : 0\n    }\n  }\n}\n\nexport default checkin\n"], "names": ["uni", "record"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAK,MAAC,UAAU;AAAA,EACd,YAAY;AAAA,EAEZ,OAAO;AAAA;AAAA,IAEL,kBAAkB;AAAA;AAAA,IAGlB,gBAAgB;AAAA;AAAA,IAGhB,gBAAgB,CAAE;AAAA;AAAA,IAGlB,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,eAAe;AAAA,MACf,eAAe;AAAA,MACf,gBAAgB;AAAA,IACjB;AAAA;AAAA,IAGD,eAAe;AAAA,MACb,OAAO;AAAA;AAAA,MACP,MAAM;AAAA;AAAA,MACN,WAAW;AAAA;AAAA,IACZ;AAAA;AAAA,IAGD,aAAa;AAAA,MACX,QAAQ,CAAE;AAAA,MACV,SAAS,CAAE;AAAA,MACX,OAAO,CAAE;AAAA,IACV;AAAA;AAAA,IAGD,aAAa;AAAA,MACX,QAAQ,EAAE,MAAM,GAAG,MAAM,EAAG;AAAA,MAC5B,SAAS,EAAE,MAAM,GAAG,MAAM,EAAG;AAAA,MAC7B,OAAO,EAAE,MAAM,GAAG,MAAM,EAAG;AAAA,IAC5B;AAAA,EACF;AAAA,EAED,WAAW;AAAA;AAAA,IAET,kBAAkB,OAAO,WAAW;AAClC,YAAM,mBAAmB;AAAA,IAC1B;AAAA;AAAA,IAGD,oBAAoB,OAAO,MAAM;AAC/B,YAAM,iBAAiB;AAAA,IACxB;AAAA;AAAA,IAGD,oBAAoB,OAAO,SAAS;AAClC,YAAM,iBAAiB;AAAA,IACxB;AAAA;AAAA,IAGD,mBAAmB,OAAO,QAAQ;AAChC,YAAM,gBAAgB,MAAM,eAAe;AAAA,QACzC,OAAK,EAAE,SAAS,OAAO;AAAA,MACxB;AACD,UAAI,gBAAgB,IAAI;AACtB,cAAM,eAAe,aAAa,IAAI;AAAA,MAC9C,OAAa;AACL,cAAM,eAAe,KAAK,MAAM;AAAA,MACjC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,OAAO,OAAO;AAC9B,YAAM,eAAe,kCAAK,MAAM,eAAiB;AAAA,IAClD;AAAA;AAAA,IAGD,mBAAmB,OAAO,SAAS;AACjC,YAAM,gBAAgB,kCAAK,MAAM,gBAAkB;AAAA,IACpD;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACvB,UAAI,MAAM,cAAc,YAAY,GAAG;AACrC,cAAM,cAAc,QAAQ;AAC5B,cAAM,cAAc,aAAa;AAAA,MAClC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,OAAO,EAAE,MAAM,KAAI,GAAI;AACrC,YAAM,YAAY,IAAI,IAAI;AAAA,IAC3B;AAAA;AAAA,IAGD,iBAAiB,OAAO,EAAE,MAAM,QAAO,GAAI;AACzC,YAAM,YAAY,IAAI,IAAI;AAAA,IAC3B;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAED,eAAe,IAAuB,IAAqC;AAAA,iDAA5D,EAAE,QAAQ,UAAW,GAAE,EAAE,kBAAkB,iBAAiB;AAC/E,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM,EAAE,kBAAkB,cAAe;AAAA,YACzC,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,kBAAM,EAAE,gBAAgB,cAAe,IAAG,SAAS,KAAK;AAExD,mBAAO,qBAAqB,IAAI;AAChC,mBAAO,uBAAuB,cAAc;AAC5C,mBAAO,sBAAsB,aAAa;AAG1C,mBAAO,+BAA+B,gBAAgB,EAAE,MAAM,KAAI,CAAE;AAEpE,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,mCAAA,SAAS,KAAK;AAC5B,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,cAAc,IAA8B,IAAM;AAAA,iDAApC,EAAE,QAAQ,OAAO,UAAS,GAAI,MAAM;AACtD,YAAI,MAAM,cAAc,aAAa,GAAG;AACtC,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAED,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM,EAAE,KAAM;AAAA,YACd,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,kBAAM,EAAE,cAAa,IAAK,SAAS,KAAK;AAExC,mBAAO,mBAAmB;AAC1B,mBAAO,sBAAsB,iCAAK,gBAAL,EAAoB,UAAU,KAAI,EAAE;AAEjE,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,mCAAA,UAAU,KAAK;AAC7B,iBAAO,EAAE,SAAS,OAAO,SAAS,YAAa;AAAA,QAChD;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,mBAAmB,IAAuB,IAAiB;AAAA,iDAAxC,EAAE,QAAQ,UAAW,GAAE,EAAE,MAAM,SAAS;AAC/D,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM,EAAE,MAAM,MAAO;AAAA,YACrB,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,mBAAO,uBAAuB,SAAS,KAAK,KAAK,OAAO;AAGxD,kBAAM,SAAQ,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AACnD,kBAAM,cAAc,SAAS,KAAK,KAAK,QAAQ,KAAK,OAAK,EAAE,SAAS,KAAK;AACzE,mBAAO,qBAAqB,CAAC,CAAC,WAAW;AAEzC,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,mCAAA,aAAa,KAAK;AAChC,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,iBAAiB,IAAuB;AAAA,iDAAvB,EAAE,QAAQ,aAAa;AAC5C,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,kBAAM,EAAE,OAAO,eAAe,eAAc,IAAK,SAAS,KAAK;AAE/D,mBAAO,qBAAqB,KAAK;AACjC,mBAAO,sBAAsB,aAAa;AAC1C,mBAAO,uBAAuB,cAAc;AAE5C,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UACnD;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,mCAAA,aAAa,KAAK;AAAA,QACjC;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,gBAAgB,IAAuB,IAAkC;AAAA,iDAAzD,EAAE,QAAQ,UAAW,GAAE,EAAE,OAAO,WAAW,QAAQ,MAAM;AAC7E,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM,EAAE,MAAM,MAAO;AAAA,YACrB,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,kBAAM,EAAE,aAAa,YAAa,IAAG,SAAS,KAAK;AAEnD,mBAAO,mBAAmB,EAAE,MAAM,MAAM,YAAW,CAAE;AACrD,mBAAO,oBAAoB,EAAE,MAAM,SAAS,YAAW,CAAE;AAEzD,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,mCAAc,YAAY,KAAK;AAC/B,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,iBAAiB,CAAC,UAAU,CAAC,SAAS;AACpC,aAAO,MAAM,eAAe,KAAK,YAAU,OAAO,SAAS,IAAI;AAAA,IAChE;AAAA;AAAA,IAGD,iBAAiB,CAAC,UAAU,CAAC,SAAS;AACpC,aAAO,MAAM,eAAe,KAAK,YAAU,OAAO,SAAS,IAAI;AAAA,IAChE;AAAA;AAAA,IAGD,gBAAgB,CAAC,UAAU,CAAC,SAAS;AACnC,YAAM,SAAS,MAAM,eAAe,KAAK,CAAAC,YAAUA,QAAO,SAAS,IAAI;AACvE,aAAO,SAAS,OAAO,WAAW;AAAA,IACnC;AAAA;AAAA,IAGD,sBAAsB,CAAC,UAAU;AAC/B,aAAO,MAAM,eAAe,OAAO,YAAU,CAAC,OAAO,QAAQ,EAAE;AAAA,IAChE;AAAA;AAAA,IAGD,qBAAqB,CAAC,UAAU;AAC9B,aAAO,MAAM,eAAe,OAAO,YAAU,OAAO,QAAQ,EAAE;AAAA,IAC/D;AAAA;AAAA,IAGD,yBAAyB,CAAC,OAAO,YAAY;AAC3C,YAAM,QAAQ,oBAAI,KAAM;AACJ,UAAI,KAAK,MAAM,eAAe,MAAM,SAAQ,IAAK,GAAG,CAAC,EAAE,QAAS;AACpF,YAAM,aAAa,MAAM,QAAS;AAClC,YAAM,cAAc,QAAQ;AAE5B,aAAO,aAAa,IAAI,KAAK,MAAO,cAAc,aAAc,GAAG,IAAI;AAAA,IACxE;AAAA;AAAA,IAGD,aAAa,CAAC,UAAU,CAAC,OAAO,cAAc;AAC5C,aAAO,MAAM,YAAY,IAAI,EAAE,MAAM,GAAG,CAAC;AAAA,IAC1C;AAAA;AAAA,IAGD,mBAAmB,CAAC,UAAU,CAAC,OAAO,cAAc;AAClD,YAAM,OAAO,MAAM,YAAY,IAAI;AACnC,YAAM,SAAS,MAAM,YAAY,IAAI,EAAE;AACvC,aAAO,KAAK,UAAU,UAAQ,KAAK,OAAO,MAAM;AAAA,IACjD;AAAA;AAAA,IAGD,qBAAqB,CAAC,UAAU;AAC9B,YAAM,aAAa,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG;AAChD,YAAM,UAAU,MAAM;AACtB,YAAM,OAAO,WAAW,KAAK,OAAK,IAAI,OAAO;AAC7C,aAAO,OAAO,OAAO,UAAU;AAAA,IAChC;AAAA,EACF;AACH;;"}