/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.page.data-v-dd383ca2 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.status-bar.data-v-dd383ca2 {
  background: transparent;
}
.content.data-v-dd383ca2 {
  padding-bottom: 200rpx;
}
.profile-header.data-v-dd383ca2 {
  text-align: center;
  padding: 60rpx 40rpx;
  color: white;
}
.profile-header .profile-avatar.data-v-dd383ca2 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  margin: 0 auto 32rpx;
  border: 8rpx solid white;
  overflow: hidden;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
}
.profile-header .profile-avatar image.data-v-dd383ca2 {
  width: 100%;
  height: 100%;
}
.profile-header .profile-name.data-v-dd383ca2 {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.profile-header .profile-desc.data-v-dd383ca2 {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 24rpx;
}
.profile-header .level-badge.data-v-dd383ca2 {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
}
.profile-header .level-badge .iconfont.data-v-dd383ca2 {
  margin-right: 8rpx;
  font-size: 28rpx;
}
.stats-grid.data-v-dd383ca2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  margin: 0 40rpx 40rpx;
}
.stats-grid .stat-card.data-v-dd383ca2 {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);
}
.stats-grid .stat-card .stat-number.data-v-dd383ca2 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}
.stats-grid .stat-card .stat-label.data-v-dd383ca2 {
  color: #666;
  font-size: 24rpx;
}
.vip-card.data-v-dd383ca2 {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-radius: 32rpx;
  margin: 0 40rpx 40rpx;
  padding: 40rpx;
  color: #333;
  position: relative;
  overflow: hidden;
}
.vip-card.data-v-dd383ca2::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
}
.vip-card .vip-content.data-v-dd383ca2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}
.vip-card .vip-content .vip-left.data-v-dd383ca2 {
  flex: 1;
}
.vip-card .vip-content .vip-left .vip-title.data-v-dd383ca2 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.vip-card .vip-content .vip-left .vip-title .iconfont.data-v-dd383ca2 {
  margin-right: 16rpx;
  font-size: 40rpx;
}
.vip-card .vip-content .vip-left .vip-desc.data-v-dd383ca2 {
  font-size: 28rpx;
  opacity: 0.8;
}
.vip-card .vip-content .vip-right.data-v-dd383ca2 {
  text-align: right;
}
.vip-card .vip-content .vip-right .vip-price.data-v-dd383ca2 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1;
}
.vip-card .vip-content .vip-right .vip-discount.data-v-dd383ca2 {
  font-size: 24rpx;
  opacity: 0.7;
}
.menu-section.data-v-dd383ca2 {
  background: #f8fafc;
  border-radius: 48rpx 48rpx 0 0;
  padding: 40rpx 0;
  min-height: 800rpx;
}
.menu-section .menu-group.data-v-dd383ca2 {
  margin: 0 32rpx 48rpx;
}
.menu-section .menu-group .menu-title.data-v-dd383ca2 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}
.menu-item.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  background: white;
  border-radius: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}
.menu-item.data-v-dd383ca2:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.menu-item .menu-left.data-v-dd383ca2 {
  display: flex;
  align-items: center;
  flex: 1;
}
.menu-item .menu-left .menu-icon.data-v-dd383ca2 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.menu-item .menu-left .menu-icon .iconfont.data-v-dd383ca2 {
  font-size: 36rpx;
}
.menu-item .menu-left .menu-text.data-v-dd383ca2 {
  flex: 1;
}
.menu-item .menu-left .menu-text .menu-label.data-v-dd383ca2 {
  display: block;
  font-weight: 500;
  color: #333;
  font-size: 32rpx;
  margin-bottom: 4rpx;
}
.menu-item .menu-left .menu-text .menu-desc.data-v-dd383ca2 {
  color: #666;
  font-size: 24rpx;
}
.menu-item .icon-arrow-right.data-v-dd383ca2 {
  color: #ccc;
  font-size: 32rpx;
}

/* 字体图标 */
.icon-chart.data-v-dd383ca2::before {
  content: "\e029";
}
.icon-bookmark.data-v-dd383ca2::before {
  content: "\e030";
}
.icon-download.data-v-dd383ca2::before {
  content: "\e031";
}
.icon-bell.data-v-dd383ca2::before {
  content: "\e032";
}
.icon-settings.data-v-dd383ca2::before {
  content: "\e033";
}
.icon-help.data-v-dd383ca2::before {
  content: "\e034";
}
.icon-logout.data-v-dd383ca2::before {
  content: "\e035";
}