/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.captcha-test-container.data-v-b6f4fe2b {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
}
.test-header.data-v-b6f4fe2b {
  text-align: center;
  margin-bottom: 40rpx;
}
.test-header .test-title.data-v-b6f4fe2b {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-b6f4fe2b {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.test-section .section-title.data-v-b6f4fe2b {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 16rpx;
}
.structure-display .structure-title.data-v-b6f4fe2b {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}
.structure-display .structure-code.data-v-b6f4fe2b {
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 16rpx;
}
.structure-display .structure-code .code-text.data-v-b6f4fe2b {
  font-family: monospace;
  font-size: 24rpx;
  color: #333;
  white-space: pre-wrap;
}
.captcha-display.data-v-b6f4fe2b {
  margin-bottom: 24rpx;
}
.captcha-display .captcha-info.data-v-b6f4fe2b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.captcha-display .captcha-info.data-v-b6f4fe2b:last-child {
  border-bottom: none;
}
.captcha-display .captcha-info .info-label.data-v-b6f4fe2b {
  font-size: 28rpx;
  color: #666;
}
.captcha-display .captcha-info .info-value.data-v-b6f4fe2b {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}
.captcha-display .captcha-info .info-value.success.data-v-b6f4fe2b {
  color: #52c41a;
}
.captcha-display .captcha-info .info-value.error.data-v-b6f4fe2b {
  color: #ff4d4f;
}
.captcha-image-container.data-v-b6f4fe2b {
  text-align: center;
  padding: 24rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
}
.captcha-image-container .captcha-image.data-v-b6f4fe2b {
  max-width: 100%;
  height: 120rpx;
  border-radius: 8rpx;
}
.captcha-image-container .no-image.data-v-b6f4fe2b {
  font-size: 28rpx;
  color: #999;
}
.test-buttons.data-v-b6f4fe2b {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.test-buttons .test-btn.data-v-b6f4fe2b {
  height: 80rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.test-buttons .test-btn.data-v-b6f4fe2b:active {
  opacity: 0.8;
}
.log-container.data-v-b6f4fe2b {
  height: 400rpx;
}
.log-container .log-item.data-v-b6f4fe2b {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 24rpx;
}
.log-container .log-item .log-time.data-v-b6f4fe2b {
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
}
.log-container .log-item .log-message.data-v-b6f4fe2b {
  color: #333;
  word-break: break-all;
}
.log-container .log-item .log-message.success.data-v-b6f4fe2b {
  color: #52c41a;
}
.log-container .log-item .log-message.error.data-v-b6f4fe2b {
  color: #ff4d4f;
}
.log-container .log-item .log-message.warning.data-v-b6f4fe2b {
  color: #faad14;
}
.log-container .log-item .log-message.info.data-v-b6f4fe2b {
  color: #1890ff;
}