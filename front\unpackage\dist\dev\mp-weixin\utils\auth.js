"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_index = require("../api/index.js");
const TOKEN_KEY = "X-Access-Token";
const USER_INFO_KEY = "userInfo";
const PERMISSIONS_KEY = "permissions";
const ROLES_KEY = "roles";
function safeGetStorage(key, defaultValue = null, expectedType = "object") {
  try {
    const data = common_vendor.index.getStorageSync(key);
    if (!data) {
      return defaultValue;
    }
    if (expectedType === "object" && typeof data === "object" && !Array.isArray(data)) {
      return data;
    }
    if (expectedType === "array" && Array.isArray(data)) {
      return data;
    }
    if (expectedType === "string" && typeof data === "string") {
      return data;
    }
    if (typeof data === "string") {
      try {
        const parsed = JSON.parse(data);
        if (expectedType === "object" && typeof parsed === "object" && !Array.isArray(parsed)) {
          return parsed;
        }
        if (expectedType === "array" && Array.isArray(parsed)) {
          return parsed;
        }
        if (expectedType === "string") {
          return parsed;
        }
        common_vendor.index.__f__("warn", "at utils/auth.js:60", `存储数据类型不匹配，期望: ${expectedType}, 实际: ${typeof parsed}`, key, parsed);
        return defaultValue;
      } catch (parseError) {
        common_vendor.index.__f__("error", "at utils/auth.js:64", `JSON解析失败 [${key}]:`, parseError, "原始数据:", data);
        return defaultValue;
      }
    }
    common_vendor.index.__f__("warn", "at utils/auth.js:70", `存储数据类型异常 [${key}]:`, typeof data, data);
    return defaultValue;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:74", `获取存储数据失败 [${key}]:`, error);
    return defaultValue;
  }
}
function safeSetStorage(key, data) {
  try {
    if (data === null || data === void 0) {
      common_vendor.index.removeStorageSync(key);
      return true;
    }
    if (typeof data === "object") {
      common_vendor.index.setStorageSync(key, JSON.stringify(data));
    } else {
      common_vendor.index.setStorageSync(key, data);
    }
    return true;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:100", `设置存储数据失败 [${key}]:`, error);
    return false;
  }
}
function getToken() {
  try {
    return common_vendor.index.getStorageSync(TOKEN_KEY);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:112", "获取Token失败:", error);
    return null;
  }
}
function setToken(token) {
  try {
    common_vendor.index.setStorageSync(TOKEN_KEY, token);
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.token = token;
    }
    return true;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:132", "设置Token失败:", error);
    return false;
  }
}
function removeToken() {
  try {
    common_vendor.index.removeStorageSync(TOKEN_KEY);
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.token = null;
    }
    return true;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:152", "移除Token失败:", error);
    return false;
  }
}
function getUserInfo() {
  return safeGetStorage(USER_INFO_KEY, null, "object");
}
function setUserInfo(userInfo) {
  try {
    if (!userInfo || typeof userInfo !== "object") {
      common_vendor.index.__f__("error", "at utils/auth.js:170", "用户信息格式错误:", userInfo);
      return false;
    }
    const success = safeSetStorage(USER_INFO_KEY, userInfo);
    if (success) {
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.userInfo = userInfo;
        app.globalData.isLogin = true;
        app.globalData.isLoggedIn = true;
      }
      common_vendor.index.__f__("log", "at utils/auth.js:186", "✅ 用户信息保存成功:", {
        username: userInfo.username,
        realname: userInfo.realname,
        id: userInfo.id
      });
    }
    return success;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:195", "设置用户信息失败:", error);
    return false;
  }
}
function removeUserInfo() {
  try {
    common_vendor.index.removeStorageSync(USER_INFO_KEY);
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userInfo = null;
      app.globalData.isLogin = false;
      app.globalData.isLoggedIn = false;
    }
    return true;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:217", "移除用户信息失败:", error);
    return false;
  }
}
function getPermissions() {
  return safeGetStorage(PERMISSIONS_KEY, [], "array");
}
function setPermissions(permissions) {
  if (!Array.isArray(permissions)) {
    common_vendor.index.__f__("error", "at utils/auth.js:234", "权限信息必须是数组:", permissions);
    return false;
  }
  return safeSetStorage(PERMISSIONS_KEY, permissions);
}
function getRoles() {
  return safeGetStorage(ROLES_KEY, [], "array");
}
function setRoles(roles) {
  if (!Array.isArray(roles)) {
    common_vendor.index.__f__("error", "at utils/auth.js:252", "角色信息必须是数组:", roles);
    return false;
  }
  return safeSetStorage(ROLES_KEY, roles);
}
function isLoggedIn() {
  const token = getToken();
  const userInfo = getUserInfo();
  return !!(token && userInfo);
}
function login(loginData) {
  return __async(this, null, function* () {
    try {
      common_vendor.index.__f__("log", "at utils/auth.js:272", "🔐 开始登录:", loginData);
      const response = yield api_index.userApi.login(loginData);
      if (response.success) {
        const { token, userInfo } = response.result || response.data;
        setToken(token);
        setUserInfo(userInfo);
        if (userInfo.permissions) {
          setPermissions(userInfo.permissions);
        }
        if (userInfo.roles) {
          setRoles(userInfo.roles);
        }
        common_vendor.index.__f__("log", "at utils/auth.js:291", "✅ 登录成功:", userInfo);
        return {
          success: true,
          data: userInfo,
          token
        };
      } else {
        common_vendor.index.__f__("error", "at utils/auth.js:299", "❌ 登录失败:", response.message);
        return {
          success: false,
          message: response.message || "登录失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/auth.js:306", "❌ 登录异常:", error);
      return {
        success: false,
        message: error.message || "登录异常，请重试"
      };
    }
  });
}
function logout() {
  return __async(this, null, function* () {
    try {
      common_vendor.index.__f__("log", "at utils/auth.js:319", "🚪 开始登出");
      try {
        yield api_index.userApi.logout();
      } catch (error) {
        common_vendor.index.__f__("warn", "at utils/auth.js:325", "后端登出接口调用失败:", error);
      }
      removeToken();
      removeUserInfo();
      common_vendor.index.removeStorageSync(PERMISSIONS_KEY);
      common_vendor.index.removeStorageSync(ROLES_KEY);
      common_vendor.index.__f__("log", "at utils/auth.js:334", "✅ 登出成功");
      return { success: true };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/auth.js:338", "❌ 登出异常:", error);
      return {
        success: false,
        message: error.message || "登出异常"
      };
    }
  });
}
function refreshUserInfo() {
  return __async(this, null, function* () {
    try {
      common_vendor.index.__f__("log", "at utils/auth.js:351", "🔄 刷新用户信息");
      const response = yield api_index.userApi.getUserInfo();
      if (response.success) {
        const userInfo = response.result || response.data;
        setUserInfo(userInfo);
        common_vendor.index.__f__("log", "at utils/auth.js:359", "✅ 用户信息刷新成功");
        return { success: true, data: userInfo };
      } else {
        common_vendor.index.__f__("error", "at utils/auth.js:362", "❌ 用户信息刷新失败:", response.message);
        return { success: false, message: response.message };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/auth.js:366", "❌ 用户信息刷新异常:", error);
      if (error.statusCode === 401) {
        yield logout();
        return { success: false, message: "登录已过期，请重新登录", needLogin: true };
      }
      return { success: false, message: error.message || "刷新失败" };
    }
  });
}
function requireLogin(currentRoute = "") {
  if (!isLoggedIn()) {
    common_vendor.index.__f__("log", "at utils/auth.js:443", "🔐 需要登录，跳转到登录页面");
    const redirectUrl = currentRoute && currentRoute !== "pages/login/login" ? encodeURIComponent(`/${currentRoute}`) : "";
    common_vendor.index.reLaunch({
      url: `/pages/login/login${redirectUrl ? `?redirect=${redirectUrl}` : ""}`
    });
    return false;
  }
  return true;
}
exports.getPermissions = getPermissions;
exports.getRoles = getRoles;
exports.getToken = getToken;
exports.getUserInfo = getUserInfo;
exports.isLoggedIn = isLoggedIn;
exports.login = login;
exports.logout = logout;
exports.refreshUserInfo = refreshUserInfo;
exports.removeToken = removeToken;
exports.removeUserInfo = removeUserInfo;
exports.requireLogin = requireLogin;
exports.setPermissions = setPermissions;
exports.setRoles = setRoles;
exports.setToken = setToken;
exports.setUserInfo = setUserInfo;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/auth.js.map
