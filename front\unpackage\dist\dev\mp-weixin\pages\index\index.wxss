/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.page.data-v-1cf27b2a {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.status-bar.data-v-1cf27b2a {
  background: transparent;
}
.content.data-v-1cf27b2a {
  min-height: calc(100vh - var(--status-bar-height));
  padding-bottom: 200rpx;
}
.header.data-v-1cf27b2a {
  padding: 96rpx 48rpx;
  text-align: center;
}
.header .greeting.data-v-1cf27b2a {
  display: block;
  color: white;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.header .subtitle.data-v-1cf27b2a {
  color: rgba(255, 255, 255, 0.8);
  font-size: 32rpx;
}
.study-card.data-v-1cf27b2a {
  margin: 32rpx;
}
.study-card .card-title.data-v-1cf27b2a {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.study-card .progress-ring.data-v-1cf27b2a {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: conic-gradient(#667eea 0deg 216deg, #e5e7eb 216deg 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.study-card .progress-ring.data-v-1cf27b2a::before {
  content: "";
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  background: white;
  border-radius: 50%;
}
.study-card .progress-ring .progress-text.data-v-1cf27b2a {
  position: relative;
  z-index: 1;
  font-weight: bold;
  color: #667eea;
  font-size: 28rpx;
}
.study-card .progress-desc.data-v-1cf27b2a {
  color: #666;
  font-size: 28rpx;
}
.study-card .card-subtitle.data-v-1cf27b2a {
  display: block;
  color: #999;
  font-size: 24rpx;
  margin-top: 8rpx;
}
.study-card .checkin-area .checkin-status.data-v-1cf27b2a {
  color: #666;
  font-size: 28rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  cursor: pointer;
  transition: all 0.3s ease;
}
.study-card .checkin-area .checkin-status.checked.data-v-1cf27b2a {
  color: #16a34a;
  background: #dcfce7;
  font-weight: 500;
}
.study-card .checkin-area .checkin-status.data-v-1cf27b2a:not(.checked):hover {
  background: #667eea;
  color: white;
}
.levels-section.data-v-1cf27b2a {
  padding: 0 48rpx;
}
.levels-section .section-title.data-v-1cf27b2a {
  display: block;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
}
.levels-section .loading-container .loading-item.data-v-1cf27b2a {
  margin-bottom: 32rpx;
}
.levels-section .loading-container .loading-item .loading-skeleton.data-v-1cf27b2a {
  height: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  animation: skeleton-loading-1cf27b2a 1.5s ease-in-out infinite;
}
@keyframes skeleton-loading-1cf27b2a {
0%, 100% {
    opacity: 0.3;
}
50% {
    opacity: 0.7;
}
}
.level-item.data-v-1cf27b2a {
  margin-bottom: 32rpx;
}
.level-item .level-icon.data-v-1cf27b2a {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}
.level-item .level-icon .iconfont.data-v-1cf27b2a {
  font-size: 36rpx;
}
.level-item .level-info.data-v-1cf27b2a {
  flex: 1;
}
.level-item .level-info .level-name.data-v-1cf27b2a {
  display: block;
  font-weight: bold;
  color: #333;
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.level-item .level-info .level-desc.data-v-1cf27b2a {
  color: #666;
  font-size: 28rpx;
}
.level-item .level-progress.data-v-1cf27b2a {
  text-align: right;
}
.level-item .level-progress .progress-text.data-v-1cf27b2a {
  display: block;
  color: #666;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}
.level-item .level-progress .progress-bar.data-v-1cf27b2a {
  width: 128rpx;
  height: 8rpx;
  background: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
}
.level-item .level-progress .progress-bar .progress-fill.data-v-1cf27b2a {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 字体图标 */
.iconfont.data-v-1cf27b2a {
  font-family: "iconfont";
}
.icon-seedling.data-v-1cf27b2a::before {
  content: "\e001";
}
.icon-mountain.data-v-1cf27b2a::before {
  content: "\e002";
}
.icon-crown.data-v-1cf27b2a::before {
  content: "\e003";
}
.icon-star.data-v-1cf27b2a::before {
  content: "\e004";
}