{"version": 3, "file": "user.js", "sources": ["store/modules/user.js"], "sourcesContent": ["const user = {\n  namespaced: true,\n  \n  state: {\n    // 用户信息\n    userInfo: {\n      id: null,\n      username: '',\n      nickname: '',\n      avatar: '',\n      phone: '',\n      email: '',\n      level: 'beginner',\n      totalStudyDays: 0,\n      continuousDays: 0,\n      maxContinuousDays: 0,\n      totalScore: 0,\n      vipStatus: 0,\n      vipExpireTime: null\n    },\n    \n    // 登录状态\n    isLogin: false,\n    token: '',\n    \n    // 学习统计\n    studyStats: {\n      todayProgress: 0,\n      completedCourses: 0,\n      totalCourses: 0,\n      averageScore: 0,\n      weeklyProgress: []\n    }\n  },\n  \n  mutations: {\n    // 设置用户信息\n    SET_USER_INFO(state, userInfo) {\n      state.userInfo = { ...state.userInfo, ...userInfo }\n    },\n    \n    // 设置登录状态\n    SET_LOGIN_STATUS(state, isLogin) {\n      state.isLogin = isLogin\n    },\n    \n    // 设置token\n    SET_TOKEN(state, token) {\n      state.token = token\n      if (token) {\n        uni.setStorageSync('token', token)\n      } else {\n        uni.removeStorageSync('token')\n      }\n    },\n    \n    // 设置学习统计\n    SET_STUDY_STATS(state, stats) {\n      state.studyStats = { ...state.studyStats, ...stats }\n    },\n    \n    // 更新连续打卡天数\n    UPDATE_CONTINUOUS_DAYS(state, days) {\n      state.userInfo.continuousDays = days\n      if (days > state.userInfo.maxContinuousDays) {\n        state.userInfo.maxContinuousDays = days\n      }\n    },\n    \n    // 更新总学习天数\n    UPDATE_TOTAL_STUDY_DAYS(state, days) {\n      state.userInfo.totalStudyDays = days\n    },\n    \n    // 更新总积分\n    UPDATE_TOTAL_SCORE(state, score) {\n      state.userInfo.totalScore = score\n    },\n    \n    // 清除用户数据\n    CLEAR_USER_DATA(state) {\n      state.userInfo = {\n        id: null,\n        username: '',\n        nickname: '',\n        avatar: '',\n        phone: '',\n        email: '',\n        level: 'beginner',\n        totalStudyDays: 0,\n        continuousDays: 0,\n        maxContinuousDays: 0,\n        totalScore: 0,\n        vipStatus: 0,\n        vipExpireTime: null\n      }\n      state.isLogin = false\n      state.token = ''\n      state.studyStats = {\n        todayProgress: 0,\n        completedCourses: 0,\n        totalCourses: 0,\n        averageScore: 0,\n        weeklyProgress: []\n      }\n    }\n  },\n  \n  actions: {\n    // 登录\n    async login({ commit }, { username, password }) {\n      try {\n        // 这里应该调用实际的登录API\n        const response = await uni.request({\n          url: '/api/v1/auth/login',\n          method: 'POST',\n          data: { username, password }\n        })\n        \n        if (response.data.code === 200) {\n          const { token, userInfo } = response.data.data\n          \n          commit('SET_TOKEN', token)\n          commit('SET_USER_INFO', userInfo)\n          commit('SET_LOGIN_STATUS', true)\n          \n          // 保存用户信息到本地\n          uni.setStorageSync('userInfo', userInfo)\n          \n          return { success: true, data: userInfo }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('登录失败:', error)\n        return { success: false, message: '登录失败，请重试' }\n      }\n    },\n    \n    // 退出登录\n    async logout({ commit }) {\n      try {\n        // 调用退出登录API\n        await uni.request({\n          url: '/api/v1/auth/logout',\n          method: 'POST'\n        })\n      } catch (error) {\n        console.error('退出登录API调用失败:', error)\n      } finally {\n        // 清除本地数据\n        commit('CLEAR_USER_DATA')\n        commit('SET_TOKEN', '')\n        uni.removeStorageSync('userInfo')\n        uni.removeStorageSync('token')\n      }\n    },\n    \n    // 检查登录状态\n    async checkLoginStatus({ commit, dispatch }) {\n      const token = uni.getStorageSync('token')\n      const userInfo = uni.getStorageSync('userInfo')\n      \n      if (token && userInfo) {\n        try {\n          // 验证token有效性\n          const response = await uni.request({\n            url: '/api/v1/user/profile',\n            method: 'GET',\n            header: {\n              'Authorization': `Bearer ${token}`\n            }\n          })\n          \n          if (response.data.code === 200) {\n            commit('SET_TOKEN', token)\n            commit('SET_USER_INFO', response.data.data)\n            commit('SET_LOGIN_STATUS', true)\n            \n            // 加载学习统计\n            await dispatch('loadStudyStats')\n          } else {\n            // token无效，清除数据\n            await dispatch('logout')\n          }\n        } catch (error) {\n          console.error('验证登录状态失败:', error)\n          await dispatch('logout')\n        }\n      }\n    },\n    \n    // 更新用户信息\n    async updateUserInfo({ commit, state }, userInfo) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/user/profile',\n          method: 'PUT',\n          data: userInfo,\n          header: {\n            'Authorization': `Bearer ${state.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          commit('SET_USER_INFO', response.data.data)\n          uni.setStorageSync('userInfo', response.data.data)\n          return { success: true }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('更新用户信息失败:', error)\n        return { success: false, message: '更新失败，请重试' }\n      }\n    },\n    \n    // 加载学习统计\n    async loadStudyStats({ commit, state }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/study/statistics',\n          method: 'GET',\n          header: {\n            'Authorization': `Bearer ${state.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          commit('SET_STUDY_STATS', response.data.data)\n        }\n      } catch (error) {\n        console.error('加载学习统计失败:', error)\n      }\n    },\n    \n    // 上传头像\n    async uploadAvatar({ commit, state }, filePath) {\n      try {\n        const response = await uni.uploadFile({\n          url: '/api/v1/user/avatar',\n          filePath: filePath,\n          name: 'avatar',\n          header: {\n            'Authorization': `Bearer ${state.token}`\n          }\n        })\n        \n        const data = JSON.parse(response.data)\n        if (data.code === 200) {\n          commit('SET_USER_INFO', { avatar: data.data.avatar })\n          return { success: true, avatar: data.data.avatar }\n        } else {\n          return { success: false, message: data.message }\n        }\n      } catch (error) {\n        console.error('上传头像失败:', error)\n        return { success: false, message: '上传失败，请重试' }\n      }\n    }\n  },\n  \n  getters: {\n    // 是否为VIP用户\n    isVip: (state) => {\n      if (state.userInfo.vipStatus === 0) return false\n      if (!state.userInfo.vipExpireTime) return false\n      return new Date(state.userInfo.vipExpireTime) > new Date()\n    },\n    \n    // 用户级别文本\n    levelText: (state) => {\n      const levelMap = {\n        'beginner': '初级学员',\n        'intermediate': '中级学员',\n        'advanced': '高级学员',\n        'professional': '专业学员'\n      }\n      return levelMap[state.userInfo.level] || '初级学员'\n    },\n    \n    // 学习进度百分比\n    studyProgressPercent: (state) => {\n      if (state.studyStats.totalCourses === 0) return 0\n      return Math.round((state.studyStats.completedCourses / state.studyStats.totalCourses) * 100)\n    }\n  }\n}\n\nexport default user\n"], "names": ["uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAK,MAAC,OAAO;AAAA,EACX,YAAY;AAAA,EAEZ,OAAO;AAAA;AAAA,IAEL,UAAU;AAAA,MACR,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,eAAe;AAAA,IAChB;AAAA;AAAA,IAGD,SAAS;AAAA,IACT,OAAO;AAAA;AAAA,IAGP,YAAY;AAAA,MACV,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB,CAAE;AAAA,IACnB;AAAA,EACF;AAAA,EAED,WAAW;AAAA;AAAA,IAET,cAAc,OAAO,UAAU;AAC7B,YAAM,WAAW,kCAAK,MAAM,WAAa;AAAA,IAC1C;AAAA;AAAA,IAGD,iBAAiB,OAAO,SAAS;AAC/B,YAAM,UAAU;AAAA,IACjB;AAAA;AAAA,IAGD,UAAU,OAAO,OAAO;AACtB,YAAM,QAAQ;AACd,UAAI,OAAO;AACTA,4BAAI,eAAe,SAAS,KAAK;AAAA,MACzC,OAAa;AACLA,sBAAG,MAAC,kBAAkB,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,OAAO,OAAO;AAC5B,YAAM,aAAa,kCAAK,MAAM,aAAe;AAAA,IAC9C;AAAA;AAAA,IAGD,uBAAuB,OAAO,MAAM;AAClC,YAAM,SAAS,iBAAiB;AAChC,UAAI,OAAO,MAAM,SAAS,mBAAmB;AAC3C,cAAM,SAAS,oBAAoB;AAAA,MACpC;AAAA,IACF;AAAA;AAAA,IAGD,wBAAwB,OAAO,MAAM;AACnC,YAAM,SAAS,iBAAiB;AAAA,IACjC;AAAA;AAAA,IAGD,mBAAmB,OAAO,OAAO;AAC/B,YAAM,SAAS,aAAa;AAAA,IAC7B;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACrB,YAAM,WAAW;AAAA,QACf,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,eAAe;AAAA,MAChB;AACD,YAAM,UAAU;AAChB,YAAM,QAAQ;AACd,YAAM,aAAa;AAAA,QACjB,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB,CAAE;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAED,MAAM,IAAY,IAAwB;AAAA,iDAApC,EAAE,OAAM,GAAI,EAAE,UAAU,SAAQ,GAAI;AAC9C,YAAI;AAEF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM,EAAE,UAAU,SAAU;AAAA,UACtC,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,kBAAM,EAAE,OAAO,SAAU,IAAG,SAAS,KAAK;AAE1C,mBAAO,aAAa,KAAK;AACzB,mBAAO,iBAAiB,QAAQ;AAChC,mBAAO,oBAAoB,IAAI;AAG/BA,gCAAI,eAAe,YAAY,QAAQ;AAEvC,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAU;AAAA,UAClD,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,gCAAA,SAAS,KAAK;AAC5B,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,OAAO,IAAY;AAAA,iDAAZ,EAAE,UAAU;AACvB,YAAI;AAEF,gBAAMA,cAAAA,MAAI,QAAQ;AAAA,YAChB,KAAK;AAAA,YACL,QAAQ;AAAA,UAClB,CAAS;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,gCAAc,gBAAgB,KAAK;AAAA,QAC3C,UAAgB;AAER,iBAAO,iBAAiB;AACxB,iBAAO,aAAa,EAAE;AACtBA,wBAAG,MAAC,kBAAkB,UAAU;AAChCA,wBAAG,MAAC,kBAAkB,OAAO;AAAA,QAC9B;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,iBAAiB,IAAsB;AAAA,iDAAtB,EAAE,QAAQ,YAAY;AAC3C,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9C,YAAI,SAAS,UAAU;AACrB,cAAI;AAEF,kBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,cACjC,KAAK;AAAA,cACL,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,iBAAiB,UAAU,KAAK;AAAA,cACjC;AAAA,YACb,CAAW;AAED,gBAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,qBAAO,aAAa,KAAK;AACzB,qBAAO,iBAAiB,SAAS,KAAK,IAAI;AAC1C,qBAAO,oBAAoB,IAAI;AAG/B,oBAAM,SAAS,gBAAgB;AAAA,YAC3C,OAAiB;AAEL,oBAAM,SAAS,QAAQ;AAAA,YACxB;AAAA,UACF,SAAQ,OAAO;AACdA,0BAAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAChC,kBAAM,SAAS,QAAQ;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,eAAe,IAAmB,IAAU;AAAA,iDAA7B,EAAE,QAAQ,MAAK,GAAI,UAAU;AAChD,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,iBAAiB,UAAU,MAAM,KAAK;AAAA,YACvC;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,mBAAO,iBAAiB,SAAS,KAAK,IAAI;AAC1CA,0BAAAA,MAAI,eAAe,YAAY,SAAS,KAAK,IAAI;AACjD,mBAAO,EAAE,SAAS,KAAM;AAAA,UAClC,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,gCAAA,aAAa,KAAK;AAChC,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,eAAe,IAAmB;AAAA,iDAAnB,EAAE,QAAQ,SAAS;AACtC,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,iBAAiB,UAAU,MAAM,KAAK;AAAA,YACvC;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,mBAAO,mBAAmB,SAAS,KAAK,IAAI;AAAA,UAC7C;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,gCAAA,aAAa,KAAK;AAAA,QACjC;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,aAAa,IAAmB,IAAU;AAAA,iDAA7B,EAAE,QAAQ,MAAK,GAAI,UAAU;AAC9C,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,WAAW;AAAA,YACpC,KAAK;AAAA,YACL;AAAA,YACA,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,iBAAiB,UAAU,MAAM,KAAK;AAAA,YACvC;AAAA,UACX,CAAS;AAED,gBAAM,OAAO,KAAK,MAAM,SAAS,IAAI;AACrC,cAAI,KAAK,SAAS,KAAK;AACrB,mBAAO,iBAAiB,EAAE,QAAQ,KAAK,KAAK,QAAQ;AACpD,mBAAO,EAAE,SAAS,MAAM,QAAQ,KAAK,KAAK,OAAQ;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,KAAK,QAAS;AAAA,UACjD;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9B,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,OAAO,CAAC,UAAU;AAChB,UAAI,MAAM,SAAS,cAAc;AAAG,eAAO;AAC3C,UAAI,CAAC,MAAM,SAAS;AAAe,eAAO;AAC1C,aAAO,IAAI,KAAK,MAAM,SAAS,aAAa,IAAI,oBAAI,KAAM;AAAA,IAC3D;AAAA;AAAA,IAGD,WAAW,CAAC,UAAU;AACpB,YAAM,WAAW;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,gBAAgB;AAAA,MACjB;AACD,aAAO,SAAS,MAAM,SAAS,KAAK,KAAK;AAAA,IAC1C;AAAA;AAAA,IAGD,sBAAsB,CAAC,UAAU;AAC/B,UAAI,MAAM,WAAW,iBAAiB;AAAG,eAAO;AAChD,aAAO,KAAK,MAAO,MAAM,WAAW,mBAAmB,MAAM,WAAW,eAAgB,GAAG;AAAA,IAC5F;AAAA,EACF;AACH;;"}