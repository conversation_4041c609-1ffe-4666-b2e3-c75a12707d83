package org.jeecg.modules.kuma.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户学习记录表
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@TableName("kuma_user_study_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "kuma_user_study_record对象", description = "用户学习记录表")
public class KumaUserStudyRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**用户ID*/
    @Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**课程ID*/
    @Excel(name = "课程ID", width = 15)
    @ApiModelProperty(value = "课程ID")
    private String courseId;

    /**学习进度百分比*/
    @Excel(name = "学习进度", width = 15)
    @ApiModelProperty(value = "学习进度百分比")
    private Integer progress;

    /**学习时长(秒)*/
    @Excel(name = "学习时长", width = 15)
    @ApiModelProperty(value = "学习时长(秒)")
    private Integer studyTime;

    /**最后学习时间*/
    @Excel(name = "最后学习时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后学习时间")
    private Date lastStudyTime;

    /**是否完成*/
    @Excel(name = "是否完成", width = 15, dicCode = "kuma_is_completed")
    @Dict(dicCode = "kuma_is_completed")
    @ApiModelProperty(value = "是否完成")
    private Integer isCompleted;

    /**完成时间*/
    @Excel(name = "完成时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private Date completionTime;

    /**系统标识*/
    @Excel(name = "系统标识", width = 15)
    @ApiModelProperty(value = "系统标识")
    private String sysSign;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**删除标志*/
    @ApiModelProperty(value = "删除标志 0-正常 1-删除")
    private Integer delFlag;
}
