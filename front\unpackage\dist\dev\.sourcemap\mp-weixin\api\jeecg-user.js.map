{"version": 3, "file": "jeecg-user.js", "sources": ["api/jeecg-user.js"], "sourcesContent": ["/**\n * jeecg-boot用户接口适配器\n * 将jeecg-boot标准接口适配为小程序使用的格式\n */\n\nimport { userApi } from './index.js'\nimport { login as authLogin, logout as authLogout, refreshUserInfo } from '@/utils/auth.js'\n\n/**\n * 用户登录适配器\n * @param {Object} loginData 登录数据\n * @param {String} loginData.username 用户名\n * @param {String} loginData.password 密码\n * @param {String} loginData.captcha 验证码\n * @param {String} loginData.checkKey 验证码key\n */\nexport async function login(loginData) {\n  try {\n    console.log('🔐 jeecg-boot用户登录:', loginData)\n    \n    // 构建jeecg-boot标准登录参数\n    const params = {\n      username: loginData.username,\n      password: loginData.password\n    }\n\n    // 添加验证码参数（jeecg-boot标准）\n    if (loginData.captcha && loginData.checkKey) {\n      params.captcha = loginData.captcha\n      params.checkKey = loginData.checkKey\n      console.log('✅ 登录包含验证码参数:', {\n        captcha: params.captcha,\n        checkKey: params.checkKey\n      })\n    } else {\n      console.warn('⚠️ 缺少验证码参数，登录可能失败')\n    }\n    \n    // 调用认证管理器登录\n    const result = await authLogin(params)\n    \n    if (result.success) {\n      console.log('✅ 登录成功:', result.data)\n      \n      // 适配返回格式\n      return {\n        success: true,\n        data: {\n          userInfo: result.data,\n          token: result.token\n        },\n        message: '登录成功'\n      }\n    } else {\n      console.error('❌ 登录失败:', result.message)\n      return {\n        success: false,\n        message: result.message || '登录失败'\n      }\n    }\n  } catch (error) {\n    console.error('❌ 登录异常:', error)\n    return {\n      success: false,\n      message: error.message || '登录异常，请重试'\n    }\n  }\n}\n\n/**\n * 用户登出适配器\n */\nexport async function logout() {\n  try {\n    console.log('🚪 jeecg-boot用户登出')\n    \n    const result = await authLogout()\n    \n    if (result.success) {\n      console.log('✅ 登出成功')\n      return {\n        success: true,\n        message: '登出成功'\n      }\n    } else {\n      return {\n        success: false,\n        message: result.message || '登出失败'\n      }\n    }\n  } catch (error) {\n    console.error('❌ 登出异常:', error)\n    return {\n      success: false,\n      message: error.message || '登出异常'\n    }\n  }\n}\n\n/**\n * 获取用户信息适配器\n */\nexport async function getUserInfo() {\n  try {\n    console.log('👤 获取jeecg-boot用户信息')\n    \n    const response = await userApi.getUserInfo()\n    \n    if (response.success) {\n      const userInfo = response.data\n      \n      // 适配用户信息格式\n      const adaptedUserInfo = {\n        id: userInfo.id,\n        username: userInfo.username,\n        realname: userInfo.realname || userInfo.username,\n        nickname: userInfo.realname || userInfo.username,\n        avatar: userInfo.avatar || '/static/images/default-avatar.png',\n        phone: userInfo.phone,\n        email: userInfo.email,\n        sex: userInfo.sex,\n        birthday: userInfo.birthday,\n        orgCode: userInfo.orgCode,\n        status: userInfo.status,\n        delFlag: userInfo.delFlag,\n        workNo: userInfo.workNo,\n        post: userInfo.post,\n        telephone: userInfo.telephone,\n        createBy: userInfo.createBy,\n        createTime: userInfo.createTime,\n        updateBy: userInfo.updateBy,\n        updateTime: userInfo.updateTime,\n        \n        // 扩展字段（如果有）\n        level: userInfo.level || 'beginner',\n        totalStudyDays: userInfo.totalStudyDays || 0,\n        continuousDays: userInfo.continuousDays || 0,\n        totalScore: userInfo.totalScore || 0,\n        vipStatus: userInfo.vipStatus || 0\n      }\n      \n      console.log('✅ 用户信息获取成功:', adaptedUserInfo)\n      \n      return {\n        success: true,\n        data: adaptedUserInfo,\n        message: '获取成功'\n      }\n    } else {\n      console.error('❌ 用户信息获取失败:', response.message)\n      return {\n        success: false,\n        message: response.message || '获取用户信息失败'\n      }\n    }\n  } catch (error) {\n    console.error('❌ 用户信息获取异常:', error)\n    return {\n      success: false,\n      message: error.message || '获取用户信息异常'\n    }\n  }\n}\n\n/**\n * 获取用户扩展信息\n */\nexport async function getUserProfile() {\n  try {\n    console.log('📋 获取用户扩展信息')\n    \n    const response = await userApi.getUserProfile()\n    \n    if (response.success) {\n      console.log('✅ 用户扩展信息获取成功')\n      return response\n    } else {\n      console.error('❌ 用户扩展信息获取失败:', response.message)\n      return response\n    }\n  } catch (error) {\n    console.error('❌ 用户扩展信息获取异常:', error)\n    return {\n      success: false,\n      message: error.message || '获取用户扩展信息异常'\n    }\n  }\n}\n\n/**\n * 更新用户扩展信息\n */\nexport async function updateUserProfile(profileData) {\n  try {\n    console.log('📝 更新用户扩展信息:', profileData)\n    \n    const response = await userApi.updateUserProfile(profileData)\n    \n    if (response.success) {\n      console.log('✅ 用户扩展信息更新成功')\n      return response\n    } else {\n      console.error('❌ 用户扩展信息更新失败:', response.message)\n      return response\n    }\n  } catch (error) {\n    console.error('❌ 用户扩展信息更新异常:', error)\n    return {\n      success: false,\n      message: error.message || '更新用户扩展信息异常'\n    }\n  }\n}\n\n/**\n * 修改密码适配器\n */\nexport async function updatePassword(passwordData) {\n  try {\n    console.log('🔒 修改密码')\n    \n    // 构建jeecg-boot标准密码修改参数\n    const params = {\n      oldpassword: passwordData.oldPassword,\n      password: passwordData.newPassword,\n      confirmpassword: passwordData.confirmPassword\n    }\n    \n    const response = await userApi.updatePassword(params)\n    \n    if (response.success) {\n      console.log('✅ 密码修改成功')\n      return {\n        success: true,\n        message: '密码修改成功'\n      }\n    } else {\n      console.error('❌ 密码修改失败:', response.message)\n      return {\n        success: false,\n        message: response.message || '密码修改失败'\n      }\n    }\n  } catch (error) {\n    console.error('❌ 密码修改异常:', error)\n    return {\n      success: false,\n      message: error.message || '密码修改异常'\n    }\n  }\n}\n\n/**\n * 上传头像适配器\n */\nexport async function uploadAvatar(filePath) {\n  try {\n    console.log('📷 上传头像:', filePath)\n    \n    const response = await userApi.uploadAvatar(filePath)\n    \n    if (response.success) {\n      const avatarUrl = response.data?.url || response.data?.message || response.data\n      \n      console.log('✅ 头像上传成功:', avatarUrl)\n      \n      return {\n        success: true,\n        data: {\n          url: avatarUrl,\n          avatar: avatarUrl\n        },\n        message: '头像上传成功'\n      }\n    } else {\n      console.error('❌ 头像上传失败:', response.message)\n      return {\n        success: false,\n        message: response.message || '头像上传失败'\n      }\n    }\n  } catch (error) {\n    console.error('❌ 头像上传异常:', error)\n    return {\n      success: false,\n      message: error.message || '头像上传异常'\n    }\n  }\n}\n\n/**\n * 刷新用户信息\n */\nexport async function refreshUser() {\n  try {\n    console.log('🔄 刷新用户信息')\n    \n    const result = await refreshUserInfo()\n    \n    if (result.success) {\n      console.log('✅ 用户信息刷新成功')\n      return {\n        success: true,\n        data: result.data,\n        message: '刷新成功'\n      }\n    } else {\n      console.error('❌ 用户信息刷新失败:', result.message)\n      \n      if (result.needLogin) {\n        return {\n          success: false,\n          message: result.message,\n          needLogin: true\n        }\n      }\n      \n      return {\n        success: false,\n        message: result.message || '刷新失败'\n      }\n    }\n  } catch (error) {\n    console.error('❌ 用户信息刷新异常:', error)\n    return {\n      success: false,\n      message: error.message || '刷新异常'\n    }\n  }\n}\n\n// 导出适配器\nexport default {\n  login,\n  logout,\n  getUserInfo,\n  getUserProfile,\n  updateUserProfile,\n  updatePassword,\n  uploadAvatar,\n  refreshUser\n}\n"], "names": ["uni", "auth<PERSON><PERSON><PERSON>", "authLogout", "userApi"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAgBO,SAAe,MAAM,WAAW;AAAA;AACrC,QAAI;AACFA,oBAAAA,MAAA,MAAA,OAAA,2BAAY,sBAAsB,SAAS;AAG3C,YAAM,SAAS;AAAA,QACb,UAAU,UAAU;AAAA,QACpB,UAAU,UAAU;AAAA,MACrB;AAGD,UAAI,UAAU,WAAW,UAAU,UAAU;AAC3C,eAAO,UAAU,UAAU;AAC3B,eAAO,WAAW,UAAU;AAC5BA,sBAAAA,MAAY,MAAA,OAAA,2BAAA,gBAAgB;AAAA,UAC1B,SAAS,OAAO;AAAA,UAChB,UAAU,OAAO;AAAA,QACzB,CAAO;AAAA,MACP,OAAW;AACLA,sBAAAA,+CAAa,mBAAmB;AAAA,MACjC;AAGD,YAAM,SAAS,MAAMC,WAAS,MAAC,MAAM;AAErC,UAAI,OAAO,SAAS;AAClBD,sBAAA,MAAA,MAAA,OAAA,2BAAY,WAAW,OAAO,IAAI;AAGlC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,UAAU,OAAO;AAAA,YACjB,OAAO,OAAO;AAAA,UACf;AAAA,UACD,SAAS;AAAA,QACV;AAAA,MACP,OAAW;AACLA,sBAAA,MAAA,MAAA,SAAA,2BAAc,WAAW,OAAO,OAAO;AACvC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,OAAO,WAAW;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAA,MAAA,SAAA,2BAAc,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAC3B;AAAA,IACF;AAAA,EACH;AAAA;AAKO,SAAe,SAAS;AAAA;AAC7B,QAAI;AACFA,oBAAAA,MAAA,MAAA,OAAA,2BAAY,mBAAmB;AAE/B,YAAM,SAAS,MAAME,kBAAY;AAEjC,UAAI,OAAO,SAAS;AAClBF,sBAAAA,MAAY,MAAA,OAAA,2BAAA,QAAQ;AACpB,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,QACV;AAAA,MACP,OAAW;AACL,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,OAAO,WAAW;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAA,MAAA,SAAA,2BAAc,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAC3B;AAAA,IACF;AAAA,EACH;AAAA;AAKO,SAAe,cAAc;AAAA;AAClC,QAAI;AACFA,oBAAAA,MAAY,MAAA,OAAA,4BAAA,qBAAqB;AAEjC,YAAM,WAAW,MAAMG,UAAO,QAAC,YAAa;AAE5C,UAAI,SAAS,SAAS;AACpB,cAAM,WAAW,SAAS;AAG1B,cAAM,kBAAkB;AAAA,UACtB,IAAI,SAAS;AAAA,UACb,UAAU,SAAS;AAAA,UACnB,UAAU,SAAS,YAAY,SAAS;AAAA,UACxC,UAAU,SAAS,YAAY,SAAS;AAAA,UACxC,QAAQ,SAAS,UAAU;AAAA,UAC3B,OAAO,SAAS;AAAA,UAChB,OAAO,SAAS;AAAA,UAChB,KAAK,SAAS;AAAA,UACd,UAAU,SAAS;AAAA,UACnB,SAAS,SAAS;AAAA,UAClB,QAAQ,SAAS;AAAA,UACjB,SAAS,SAAS;AAAA,UAClB,QAAQ,SAAS;AAAA,UACjB,MAAM,SAAS;AAAA,UACf,WAAW,SAAS;AAAA,UACpB,UAAU,SAAS;AAAA,UACnB,YAAY,SAAS;AAAA,UACrB,UAAU,SAAS;AAAA,UACnB,YAAY,SAAS;AAAA;AAAA,UAGrB,OAAO,SAAS,SAAS;AAAA,UACzB,gBAAgB,SAAS,kBAAkB;AAAA,UAC3C,gBAAgB,SAAS,kBAAkB;AAAA,UAC3C,YAAY,SAAS,cAAc;AAAA,UACnC,WAAW,SAAS,aAAa;AAAA,QAClC;AAEDH,sBAAAA,+CAAY,eAAe,eAAe;AAE1C,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,MACP,OAAW;AACLA,uEAAc,eAAe,SAAS,OAAO;AAC7C,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,SAAS,WAAW;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,4BAAA,eAAe,KAAK;AAClC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAC3B;AAAA,IACF;AAAA,EACH;AAAA;;;;"}