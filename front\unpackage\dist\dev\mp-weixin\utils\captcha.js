"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_index = require("../api/index.js");
class CaptchaManager {
  constructor() {
    this.captchaData = {
      image: "",
      // 验证码图片base64
      key: "",
      // 验证码key
      timestamp: 0
      // 获取时间戳
    };
    this.config = {
      expireTime: 5 * 60 * 1e3,
      // 验证码过期时间（5分钟）
      autoRefresh: true,
      // 是否自动刷新过期验证码
      retryCount: 3,
      // 获取验证码重试次数
      pathKey: "1629428467008"
      // 固定的path参数key
    };
  }
  /**
   * 获取验证码
   * @param {boolean} force 是否强制刷新
   * @returns {Promise<Object>} 验证码数据
   */
  getCaptcha(force = false) {
    return __async(this, null, function* () {
      try {
        if (!force && this.isValid()) {
          common_vendor.index.__f__("log", "at utils/captcha.js:36", "✅ 使用缓存的验证码");
          return {
            success: true,
            data: this.captchaData
          };
        }
        common_vendor.index.__f__("log", "at utils/captcha.js:43", "🔄 获取新的验证码");
        const response = yield api_index.userApi.getCaptcha(this.config.pathKey);
        if (response.success) {
          const imageData = response.result || response.data;
          const timestamp = response.timestamp || Date.now();
          this.captchaData = {
            image: imageData,
            // 直接使用result字段，已经是完整的base64格式
            key: this.config.pathKey,
            // 使用配置的pathKey作为验证码key
            timestamp
          };
          common_vendor.index.__f__("log", "at utils/captcha.js:59", "✅ 验证码获取成功:", {
            hasImage: !!this.captchaData.image,
            imageFormat: this.captchaData.image ? this.captchaData.image.substring(0, 30) + "..." : "",
            key: this.captchaData.key,
            timestamp: this.captchaData.timestamp
          });
          return {
            success: true,
            data: this.captchaData
          };
        } else {
          common_vendor.index.__f__("error", "at utils/captcha.js:71", "❌ 验证码获取失败:", response.message);
          return {
            success: false,
            message: response.message || "获取验证码失败"
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/captcha.js:78", "❌ 验证码获取异常:", error);
        return {
          success: false,
          message: error.message || "获取验证码异常"
        };
      }
    });
  }
  /**
   * 验证验证码
   * @param {string} code 用户输入的验证码
   * @returns {Promise<Object>} 验证结果
   */
  verifyCaptcha(code) {
    return __async(this, null, function* () {
      try {
        if (!code || !code.trim()) {
          return {
            success: false,
            message: "请输入验证码"
          };
        }
        if (!this.captchaData.key) {
          return {
            success: false,
            message: "验证码已过期，请刷新"
          };
        }
        common_vendor.index.__f__("log", "at utils/captcha.js:107", "🔍 验证验证码:", {
          code,
          key: this.captchaData.key
        });
        const response = yield api_index.userApi.checkCaptcha({
          captcha: code.trim(),
          checkKey: this.captchaData.key
        });
        if (response.success) {
          common_vendor.index.__f__("log", "at utils/captcha.js:119", "✅ 验证码验证成功");
          return {
            success: true,
            message: "验证码正确"
          };
        } else {
          common_vendor.index.__f__("error", "at utils/captcha.js:125", "❌ 验证码验证失败:", response.message);
          if (this.config.autoRefresh) {
            setTimeout(() => {
              this.getCaptcha(true);
            }, 1e3);
          }
          return {
            success: false,
            message: response.message || "验证码错误"
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/captcha.js:140", "❌ 验证码验证异常:", error);
        return {
          success: false,
          message: error.message || "验证码验证异常"
        };
      }
    });
  }
  /**
   * 刷新验证码
   * @returns {Promise<Object>} 新的验证码数据
   */
  refreshCaptcha() {
    return __async(this, null, function* () {
      common_vendor.index.__f__("log", "at utils/captcha.js:153", "🔄 刷新验证码");
      return yield this.getCaptcha(true);
    });
  }
  /**
   * 检查验证码是否有效
   * @returns {boolean} 是否有效
   */
  isValid() {
    if (!this.captchaData.image || !this.captchaData.key) {
      return false;
    }
    const now = Date.now();
    const elapsed = now - this.captchaData.timestamp;
    return elapsed < this.config.expireTime;
  }
  /**
   * 获取验证码图片URL
   * @returns {string} 图片URL
   */
  getImageUrl() {
    if (!this.captchaData.image) {
      return "";
    }
    if (this.captchaData.image.startsWith("data:image/")) {
      return this.captchaData.image;
    }
    if (this.captchaData.image.match(/^[A-Za-z0-9+/=]+$/)) {
      return `data:image/png;base64,${this.captchaData.image}`;
    }
    return this.captchaData.image;
  }
  /**
   * 获取验证码key
   * @returns {string} 验证码key
   */
  getKey() {
    return this.captchaData.key;
  }
  /**
   * 清除验证码数据
   */
  clear() {
    common_vendor.index.__f__("log", "at utils/captcha.js:208", "🗑️ 清除验证码数据");
    this.captchaData = {
      image: "",
      key: "",
      timestamp: 0
    };
  }
  /**
   * 获取剩余有效时间（秒）
   * @returns {number} 剩余秒数
   */
  getRemainingTime() {
    if (!this.isValid()) {
      return 0;
    }
    const now = Date.now();
    const elapsed = now - this.captchaData.timestamp;
    const remaining = this.config.expireTime - elapsed;
    return Math.max(0, Math.floor(remaining / 1e3));
  }
  /**
   * 生成随机path参数
   * @returns {string} 随机数字符串
   */
  generatePathKey() {
    return Date.now().toString();
  }
  /**
   * 设置path参数
   * @param {string} key path参数，如果不提供则使用默认值
   */
  setPathKey(key) {
    if (key) {
      this.config.pathKey = key;
    } else {
      this.config.pathKey = this.generatePathKey();
    }
    common_vendor.index.__f__("log", "at utils/captcha.js:251", "🔑 设置验证码path参数:", this.config.pathKey);
  }
  /**
   * 获取当前path参数
   * @returns {string} 当前path参数
   */
  getPathKey() {
    return this.config.pathKey;
  }
  /**
   * 设置配置
   * @param {Object} config 配置对象
   */
  setConfig(config) {
    this.config = __spreadValues(__spreadValues({}, this.config), config);
  }
}
const captchaManager = new CaptchaManager();
const getCaptcha = (force = false) => captchaManager.getCaptcha(force);
const refreshCaptcha = () => captchaManager.refreshCaptcha();
const getCaptchaImageUrl = () => captchaManager.getImageUrl();
const getCaptchaKey = () => captchaManager.getKey();
const clearCaptcha = () => captchaManager.clear();
const setPathKey = (key) => captchaManager.setPathKey(key);
const getPathKey = () => captchaManager.getPathKey();
const generatePathKey = () => captchaManager.generatePathKey();
exports.clearCaptcha = clearCaptcha;
exports.generatePathKey = generatePathKey;
exports.getCaptcha = getCaptcha;
exports.getCaptchaImageUrl = getCaptchaImageUrl;
exports.getCaptchaKey = getCaptchaKey;
exports.getPathKey = getPathKey;
exports.refreshCaptcha = refreshCaptcha;
exports.setPathKey = setPathKey;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/captcha.js.map
