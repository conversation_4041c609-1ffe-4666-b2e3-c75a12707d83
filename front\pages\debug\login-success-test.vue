<template>
	<view class="login-success-test-container">
		<view class="test-header">
			<text class="test-title">登录成功数据处理测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">当前认证状态</text>
			<view class="auth-status">
				<view class="status-item">
					<text class="status-label">Token状态:</text>
					<text class="status-value" :class="hasToken ? 'success' : 'error'">
						{{ hasToken ? '已保存' : '未保存' }}
					</text>
				</view>
				<view class="status-item">
					<text class="status-label">Token值:</text>
					<text class="status-value token">{{ tokenDisplay }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">用户信息:</text>
					<text class="status-value" :class="hasUserInfo ? 'success' : 'error'">
						{{ hasUserInfo ? '已保存' : '未保存' }}
					</text>
				</view>
				<view class="status-item">
					<text class="status-label">用户名:</text>
					<text class="status-value">{{ currentUser ? currentUser.username : '无' }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">真实姓名:</text>
					<text class="status-value">{{ currentUser ? currentUser.realname : '无' }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">全局登录状态:</text>
					<text class="status-value" :class="globalLoginStatus ? 'success' : 'error'">
						{{ globalLoginStatus ? '已登录' : '未登录' }}
					</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">模拟登录数据测试</text>
			<view class="mock-login">
				<view class="mock-data">
					<text class="mock-title">模拟登录响应数据:</text>
					<view class="mock-code">
						<text class="code-text">{{ mockLoginData }}</text>
					</view>
				</view>
				<view class="test-buttons">
					<button class="test-btn primary" @tap="testSaveAuthInfo">测试保存认证信息</button>
					<button class="test-btn success" @tap="testTokenHeaders">测试请求头</button>
					<button class="test-btn warning" @tap="testGlobalState">测试全局状态</button>
					<button class="test-btn error" @tap="testClearAuth">清除认证信息</button>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">本地存储检查</text>
			<view class="storage-check">
				<view class="storage-item" v-for="(item, key) in storageData" :key="key">
					<text class="storage-key">{{ key }}:</text>
					<text class="storage-value">{{ item }}</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试日志</text>
			<scroll-view class="log-container" scroll-y>
				<view class="log-item" v-for="(log, index) in testLogs" :key="index">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message" :class="log.type">{{ log.message }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { getToken, setToken, getUserInfo, setUserInfo, removeToken, removeUserInfo } from '@/utils/auth.js'
import { getRequestHeaders } from '@/config/api.js'

export default {
	data() {
		return {
			hasToken: false,
			hasUserInfo: false,
			currentUser: null,
			globalLoginStatus: false,
			tokenDisplay: '',
			storageData: {},
			testLogs: [],
			mockLoginData: `{
  "success": true,
  "result": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": "1001",
      "username": "admin",
      "realname": "管理员",
      "avatar": "/avatar/admin.jpg",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "orgCode": "A01",
      "orgCodeTxt": "总部",
      "departs": ["技术部"],
      "roles": ["admin"],
      "permissions": ["user:read", "user:write"]
    }
  }
}`
		}
	},
	
	onLoad() {
		this.addLog('登录成功数据处理测试页面加载', 'info')
		this.checkAuthStatus()
	},
	
	onShow() {
		this.checkAuthStatus()
	},
	
	methods: {
		// 添加日志
		addLog(message, type = 'info') {
			const now = new Date()
			const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
			
			this.testLogs.unshift({
				time,
				message,
				type
			})
			
			if (this.testLogs.length > 20) {
				this.testLogs = this.testLogs.slice(0, 20)
			}
		},
		
		// 检查认证状态
		checkAuthStatus() {
			try {
				// 检查Token
				const token = getToken()
				this.hasToken = !!token
				this.tokenDisplay = token ? token.substring(0, 20) + '...' : '无'
				
				// 检查用户信息
				const userInfo = getUserInfo()
				this.hasUserInfo = !!userInfo
				this.currentUser = userInfo
				
				// 检查全局状态
				const app = getApp()
				this.globalLoginStatus = app && app.globalData && app.globalData.isLogin
				
				// 检查本地存储
				this.checkLocalStorage()
				
				this.addLog('认证状态检查完成', 'info')
			} catch (error) {
				this.addLog(`认证状态检查失败: ${error.message}`, 'error')
			}
		},
		
		// 检查本地存储
		checkLocalStorage() {
			try {
				this.storageData = {
					'X-Access-Token': uni.getStorageSync('X-Access-Token') || '无',
					'token': uni.getStorageSync('token') || '无',
					'userInfo': uni.getStorageSync('userInfo') ? '已保存' : '无',
					'permissions': uni.getStorageSync('permissions') || '无',
					'roles': uni.getStorageSync('roles') || '无'
				}
			} catch (error) {
				this.addLog(`本地存储检查失败: ${error.message}`, 'error')
			}
		},
		
		// 测试保存认证信息
		async testSaveAuthInfo() {
			this.addLog('开始测试保存认证信息', 'info')
			
			try {
				// 模拟登录响应数据
				const mockData = {
					token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
					id: '1001',
					username: 'admin',
					realname: '管理员',
					avatar: '/avatar/admin.jpg',
					phone: '13800138000',
					email: '<EMAIL>',
					orgCode: 'A01',
					orgCodeTxt: '总部',
					departs: ['技术部'],
					roles: ['admin'],
					permissions: ['user:read', 'user:write']
				}
				
				// 保存Token
				const tokenSaved = setToken(mockData.token)
				if (tokenSaved) {
					this.addLog('✅ Token保存成功', 'success')
				} else {
					this.addLog('❌ Token保存失败', 'error')
				}
				
				// 保存用户信息
				const userInfoSaved = setUserInfo({
					id: mockData.id,
					username: mockData.username,
					realname: mockData.realname,
					avatar: mockData.avatar,
					phone: mockData.phone,
					email: mockData.email,
					orgCode: mockData.orgCode,
					orgCodeTxt: mockData.orgCodeTxt,
					departs: mockData.departs,
					roles: mockData.roles,
					permissions: mockData.permissions
				})
				
				if (userInfoSaved) {
					this.addLog('✅ 用户信息保存成功', 'success')
				} else {
					this.addLog('❌ 用户信息保存失败', 'error')
				}
				
				// 刷新状态
				this.checkAuthStatus()
				
			} catch (error) {
				this.addLog(`保存认证信息异常: ${error.message}`, 'error')
			}
		},
		
		// 测试请求头
		testTokenHeaders() {
			this.addLog('测试请求头配置', 'info')
			
			try {
				const headers = getRequestHeaders()
				
				this.addLog(`请求头配置: ${JSON.stringify(headers, null, 2)}`, 'info')
				
				if (headers['X-Access-Token']) {
					this.addLog('✅ X-Access-Token 请求头配置正确', 'success')
				} else {
					this.addLog('❌ X-Access-Token 请求头缺失', 'error')
				}
				
				if (headers['X-Access-Client'] === 'miniApp') {
					this.addLog('✅ X-Access-Client 请求头配置正确', 'success')
				} else {
					this.addLog('❌ X-Access-Client 请求头配置错误', 'error')
				}
				
			} catch (error) {
				this.addLog(`请求头测试异常: ${error.message}`, 'error')
			}
		},
		
		// 测试全局状态
		testGlobalState() {
			this.addLog('测试全局状态同步', 'info')
			
			try {
				const app = getApp()
				
				if (app && app.globalData) {
					this.addLog(`全局状态: ${JSON.stringify({
						isLogin: app.globalData.isLogin,
						hasToken: !!app.globalData.token,
						hasUserInfo: !!app.globalData.userInfo
					}, null, 2)}`, 'info')
					
					if (app.globalData.isLogin) {
						this.addLog('✅ 全局登录状态正确', 'success')
					} else {
						this.addLog('❌ 全局登录状态错误', 'error')
					}
				} else {
					this.addLog('❌ 无法获取全局状态', 'error')
				}
				
			} catch (error) {
				this.addLog(`全局状态测试异常: ${error.message}`, 'error')
			}
		},
		
		// 清除认证信息
		testClearAuth() {
			this.addLog('清除认证信息', 'warning')
			
			try {
				removeToken()
				removeUserInfo()
				
				this.addLog('✅ 认证信息清除成功', 'success')
				this.checkAuthStatus()
				
			} catch (error) {
				this.addLog(`清除认证信息异常: ${error.message}`, 'error')
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.login-success-test-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.test-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 24rpx;
		border-bottom: 2rpx solid #eee;
		padding-bottom: 16rpx;
	}
}

.auth-status, .storage-check {
	.status-item, .storage-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		
		&:last-child {
			border-bottom: none;
		}
		
		.status-label, .storage-key {
			font-size: 28rpx;
			color: #666;
			width: 200rpx;
			flex-shrink: 0;
		}
		
		.status-value, .storage-value {
			font-size: 28rpx;
			color: #333;
			word-break: break-all;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
			
			&.token {
				font-family: monospace;
				font-size: 24rpx;
			}
		}
	}
}

.mock-login {
	.mock-data {
		margin-bottom: 24rpx;
		
		.mock-title {
			display: block;
			font-size: 28rpx;
			color: #666;
			margin-bottom: 16rpx;
		}
		
		.mock-code {
			background: #f5f5f5;
			border-radius: 8rpx;
			padding: 16rpx;
			
			.code-text {
				font-family: monospace;
				font-size: 22rpx;
				color: #333;
				white-space: pre-wrap;
			}
		}
	}
}

.test-buttons {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
	
	.test-btn {
		height: 80rpx;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: white;
		
		&.primary {
			background: #1890ff;
		}
		
		&.success {
			background: #52c41a;
		}
		
		&.warning {
			background: #faad14;
		}
		
		&.error {
			background: #ff4d4f;
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}

.log-container {
	height: 300rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	padding: 16rpx;
	
	.log-item {
		display: flex;
		margin-bottom: 12rpx;
		font-size: 24rpx;
		
		.log-time {
			color: #999;
			width: 120rpx;
			flex-shrink: 0;
		}
		
		.log-message {
			color: #333;
			word-break: break-all;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
			
			&.warning {
				color: #faad14;
			}
			
			&.info {
				color: #1890ff;
			}
		}
	}
}
</style>
