package com.kuma.learning.controller.app;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuma.learning.dto.IseRequest;
import com.kuma.learning.dto.IseResult;
import com.kuma.learning.entity.KumaPracticeRecord;
import com.kuma.learning.entity.KumaUserProfile;
import com.kuma.learning.service.IKumaPracticeRecordService;
import com.kuma.learning.service.IKumaUserProfileService;
import com.kuma.learning.service.IVoiceEvaluationService;
import com.kuma.learning.util.FileUploadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "APP-练习管理")
@RestController
@RequestMapping("/app/practice")
public class PracticeController extends JeecgController<KumaPracticeRecord, IKumaPracticeRecordService> {

    @Autowired
    private IKumaPracticeRecordService practiceRecordService;

    @Autowired
    private IKumaUserProfileService userProfileService;

    @Autowired
    private IVoiceEvaluationService voiceEvaluationService;

    @Autowired
    private FileUploadUtil fileUploadUtil;

    @AutoLog(value = "练习-提交录音练习(文件上传)")
    @ApiOperation(value = "练习-提交录音练习(文件上传)", notes = "练习-提交录音练习(文件上传)")
    @PostMapping("/submit")
    public Result<Map<String, Object>> submitPractice(
            @ApiParam("音频文件") @RequestParam("audioFile") MultipartFile audioFile,
            @ApiParam("练习句子") @RequestParam("sentenceText") String sentenceText,
            @ApiParam("课程ID") @RequestParam("courseId") String courseId,
            @ApiParam("课时ID") @RequestParam(value = "lessonId", required = false) String lessonId,
            @ApiParam("语言类型") @RequestParam(value = "language", defaultValue = "cn") String language,
            @ApiParam("题型") @RequestParam(value = "category", defaultValue = "read_sentence") String category,
            HttpServletRequest request) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("用户未登录");
        }

        try {
            // 调用AI语音评测
            IseResult evaluationResult = voiceEvaluationService.evaluate(audioFile, sentenceText, language, category);

            // 创建练习记录
            KumaPracticeRecord practiceRecord = new KumaPracticeRecord();
            practiceRecord.setUserId(loginUser.getId());
            practiceRecord.setCourseId(courseId);
            practiceRecord.setLessonId(lessonId);
            practiceRecord.setSentenceText(sentenceText);
            practiceRecord.setSysSign("KUMA");

            // 设置AI评测结果
            practiceRecord.setTotalScore(evaluationResult.getTotalScore().intValue());
            practiceRecord.setPronunciationScore(evaluationResult.getPronunciationScore().intValue());
            practiceRecord.setFluencyScore(evaluationResult.getFluencyScore().intValue());
            practiceRecord.setToneScore(evaluationResult.getToneScore().intValue());
            practiceRecord.setAiFeedback(evaluationResult.getFeedback());
            practiceRecord.setEvaluationData(JSON.toJSONString(evaluationResult));

            // 上传音频文件
            String audioUrl = fileUploadUtil.uploadAudioFile(audioFile, loginUser.getId());
            practiceRecord.setAudioUrl(audioUrl);

            // 计算音频时长（这里简化处理，实际应该解析音频文件获取时长）
            practiceRecord.setAudioDuration(calculateAudioDuration(audioFile));

            practiceRecordService.save(practiceRecord);

            // 更新用户积分
            updateUserScore(loginUser.getId(), evaluationResult.getTotalScore().intValue());

            Map<String, Object> result = new HashMap<>();
            result.put("practiceRecord", practiceRecord);
            result.put("evaluationResult", evaluationResult);
            result.put("message", "练习提交成功");

            return Result.OK(result);

        } catch (Exception e) {
            log.error("提交练习失败", e);
            return Result.error("提交练习失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "练习-提交录音练习(Base64)")
    @ApiOperation(value = "练习-提交录音练习(Base64)", notes = "练习-提交录音练习(Base64)")
    @PostMapping("/submitBase64")
    public Result<Map<String, Object>> submitPracticeBase64(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("用户未登录");
        }

        try {
            // 构建评测请求
            IseRequest iseRequest = new IseRequest();
            iseRequest.setAudioData((String) params.get("audioData"));
            iseRequest.setText((String) params.get("sentenceText"));
            iseRequest.setLanguage((String) params.getOrDefault("language", "cn"));
            iseRequest.setCategory((String) params.getOrDefault("category", "read_sentence"));

            // 调用AI语音评测
            IseResult evaluationResult = voiceEvaluationService.evaluate(iseRequest);

            // 创建练习记录
            KumaPracticeRecord practiceRecord = new KumaPracticeRecord();
            practiceRecord.setUserId(loginUser.getId());
            practiceRecord.setCourseId((String) params.get("courseId"));
            practiceRecord.setLessonId((String) params.get("lessonId"));
            practiceRecord.setSentenceText((String) params.get("sentenceText"));
            practiceRecord.setSysSign("KUMA");

            // 设置AI评测结果
            practiceRecord.setTotalScore(evaluationResult.getTotalScore().intValue());
            practiceRecord.setPronunciationScore(evaluationResult.getPronunciationScore().intValue());
            practiceRecord.setFluencyScore(evaluationResult.getFluencyScore().intValue());
            practiceRecord.setToneScore(evaluationResult.getToneScore().intValue());
            practiceRecord.setAiFeedback(evaluationResult.getFeedback());
            practiceRecord.setEvaluationData(JSON.toJSONString(evaluationResult));

            // 保存Base64音频数据
            String audioFormat = (String) params.getOrDefault("audioFormat", "wav");
            String audioUrl = fileUploadUtil.saveBase64Audio(iseRequest.getAudioData(), loginUser.getId(), audioFormat);
            practiceRecord.setAudioUrl(audioUrl);
            practiceRecord.setAudioDuration((Integer) params.getOrDefault("audioDuration", 30));

            practiceRecordService.save(practiceRecord);

            // 更新用户积分
            updateUserScore(loginUser.getId(), evaluationResult.getTotalScore().intValue());

            Map<String, Object> result = new HashMap<>();
            result.put("practiceRecord", practiceRecord);
            result.put("evaluationResult", evaluationResult);
            result.put("message", "练习提交成功");

            return Result.OK(result);

        } catch (Exception e) {
            log.error("提交练习失败", e);
            return Result.error("提交练习失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "练习-获取练习历史")
    @ApiOperation(value = "练习-获取练习历史", notes = "练习-获取练习历史")
    @GetMapping("/history")
    public Result<IPage<KumaPracticeRecord>> getPracticeHistory(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "courseId", required = false) String courseId,
            @RequestParam(name = "lessonId", required = false) String lessonId,
            HttpServletRequest request) {
        
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        
        Page<KumaPracticeRecord> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<KumaPracticeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaPracticeRecord::getUserId, loginUser.getId());
        
        if (courseId != null && !courseId.isEmpty()) {
            queryWrapper.eq(KumaPracticeRecord::getCourseId, courseId);
        }
        
        if (lessonId != null && !lessonId.isEmpty()) {
            queryWrapper.eq(KumaPracticeRecord::getLessonId, lessonId);
        }
        
        queryWrapper.orderByDesc(KumaPracticeRecord::getCreateTime);
        
        IPage<KumaPracticeRecord> pageList = practiceRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog(value = "练习-获取练习统计")
    @ApiOperation(value = "练习-获取练习统计", notes = "练习-获取练习统计")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getPracticeStatistics(HttpServletRequest request) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        
        LambdaQueryWrapper<KumaPracticeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaPracticeRecord::getUserId, loginUser.getId());
        
        List<KumaPracticeRecord> allRecords = practiceRecordService.list(queryWrapper);
        
        if (allRecords.isEmpty()) {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalPractices", 0);
            statistics.put("averageScore", 0);
            statistics.put("bestScore", 0);
            statistics.put("improvementRate", 0);
            return Result.OK(statistics);
        }
        
        // 计算统计数据
        int totalPractices = allRecords.size();
        double averageScore = allRecords.stream()
                .mapToInt(KumaPracticeRecord::getTotalScore)
                .average()
                .orElse(0.0);
        int bestScore = allRecords.stream()
                .mapToInt(KumaPracticeRecord::getTotalScore)
                .max()
                .orElse(0);
        
        // 计算进步率（最近10次与之前10次的平均分对比）
        double improvementRate = 0.0;
        if (totalPractices >= 10) {
            List<KumaPracticeRecord> recent10 = allRecords.subList(0, Math.min(10, totalPractices));
            List<KumaPracticeRecord> previous10 = allRecords.subList(Math.min(10, totalPractices), Math.min(20, totalPractices));
            
            if (!previous10.isEmpty()) {
                double recentAvg = recent10.stream().mapToInt(KumaPracticeRecord::getTotalScore).average().orElse(0.0);
                double previousAvg = previous10.stream().mapToInt(KumaPracticeRecord::getTotalScore).average().orElse(0.0);
                
                if (previousAvg > 0) {
                    improvementRate = ((recentAvg - previousAvg) / previousAvg) * 100;
                }
            }
        }
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalPractices", totalPractices);
        statistics.put("averageScore", Math.round(averageScore));
        statistics.put("bestScore", bestScore);
        statistics.put("improvementRate", Math.round(improvementRate));
        
        return Result.OK(statistics);
    }

    @AutoLog(value = "练习-通过id查询")
    @ApiOperation(value = "练习-通过id查询", notes = "练习-通过id查询")
    @GetMapping("/queryById")
    public Result<KumaPracticeRecord> queryById(@RequestParam(name = "id") String id, HttpServletRequest request) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        
        KumaPracticeRecord practiceRecord = practiceRecordService.getById(id);
        if (practiceRecord == null) {
            return Result.error("未找到对应练习记录");
        }
        
        // 验证是否为当前用户的记录
        if (!practiceRecord.getUserId().equals(loginUser.getId())) {
            return Result.error("无权限访问该记录");
        }
        
        return Result.OK(practiceRecord);
    }

    @AutoLog(value = "练习-获取支持的语言类型")
    @ApiOperation(value = "练习-获取支持的语言类型", notes = "练习-获取支持的语言类型")
    @GetMapping("/supportedLanguages")
    public Result<String[]> getSupportedLanguages() {
        String[] languages = voiceEvaluationService.getSupportedLanguages();
        return Result.OK(languages);
    }

    @AutoLog(value = "练习-获取支持的题型")
    @ApiOperation(value = "练习-获取支持的题型", notes = "练习-获取支持的题型")
    @GetMapping("/supportedCategories")
    public Result<String[]> getSupportedCategories(@RequestParam("language") String language) {
        String[] categories = voiceEvaluationService.getSupportedCategories(language);
        return Result.OK(categories);
    }

    @AutoLog(value = "练习-获取评测配置")
    @ApiOperation(value = "练习-获取评测配置", notes = "练习-获取评测配置")
    @GetMapping("/evaluationConfig")
    public Result<Map<String, Object>> getEvaluationConfig() {
        Map<String, Object> config = new HashMap<>();

        // 支持的语言类型
        Map<String, String> languages = new HashMap<>();
        languages.put("cn", "中文");
        languages.put("en", "英文");
        config.put("languages", languages);

        // 支持的题型
        Map<String, Map<String, String>> categories = new HashMap<>();

        Map<String, String> cnCategories = new HashMap<>();
        cnCategories.put("read_syllable", "单字朗读");
        cnCategories.put("read_word", "词语朗读");
        cnCategories.put("read_sentence", "句子朗读");
        cnCategories.put("read_chapter", "篇章朗读");
        categories.put("cn", cnCategories);

        Map<String, String> enCategories = new HashMap<>();
        enCategories.put("read_word", "词语朗读");
        enCategories.put("read_sentence", "句子朗读");
        enCategories.put("read_chapter", "篇章朗读");
        categories.put("en", enCategories);

        config.put("categories", categories);

        // 评测等级说明
        Map<String, String> grades = new HashMap<>();
        grades.put("A", "优秀(90-100分)");
        grades.put("B", "良好(80-89分)");
        grades.put("C", "中等(70-79分)");
        grades.put("D", "及格(60-69分)");
        grades.put("F", "不及格(0-59分)");
        config.put("grades", grades);

        return Result.OK(config);
    }

    /**
     * 更新用户积分
     */
    private void updateUserScore(String userId, Integer score) {
        try {
            LambdaQueryWrapper<KumaUserProfile> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KumaUserProfile::getUserId, userId);

            KumaUserProfile userProfile = userProfileService.getOne(queryWrapper);
            if (userProfile != null) {
                // 根据评分给予积分奖励
                int rewardPoints = calculateRewardPoints(score);
                userProfile.setTotalScore(userProfile.getTotalScore() + rewardPoints);
                userProfileService.updateById(userProfile);
            }
        } catch (Exception e) {
            log.error("更新用户积分失败", e);
        }
    }

    /**
     * 计算积分奖励
     */
    private int calculateRewardPoints(Integer score) {
        if (score >= 90) {
            return 10; // 优秀奖励10积分
        } else if (score >= 80) {
            return 8;  // 良好奖励8积分
        } else if (score >= 70) {
            return 5;  // 中等奖励5积分
        } else if (score >= 60) {
            return 3;  // 及格奖励3积分
        } else {
            return 1;  // 参与奖励1积分
        }
    }

    /**
     * 计算音频时长（简化实现）
     * 实际项目中应该使用音频处理库来获取准确的时长
     */
    private int calculateAudioDuration(MultipartFile audioFile) {
        try {
            // 这里简化处理，根据文件大小估算时长
            // 实际应该使用 FFmpeg 或其他音频处理库
            long fileSize = audioFile.getSize();

            // 假设音频质量为 128kbps，计算大概时长
            // 128kbps = 16KB/s
            int estimatedDuration = (int) (fileSize / (16 * 1024));

            // 限制在合理范围内
            return Math.max(1, Math.min(estimatedDuration, 300)); // 1秒到5分钟

        } catch (Exception e) {
            log.warn("计算音频时长失败，使用默认值", e);
            return 30; // 默认30秒
        }
    }
}
