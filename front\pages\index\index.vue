<template>
	<view class="page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 页面内容 -->
		<view class="content" :style="{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }">
			<!-- 头部问候 -->
			<view class="header">
				<text class="greeting">こんにちは！</text>
				<text class="subtitle">今天也要加油学习日语哦～</text>
			</view>

			<!-- 今日学习卡片 -->
			<view class="study-card card">
				<view class="flex-between mb-4">
					<view>
						<text class="card-title">今日学习</text>
						<text class="card-subtitle" v-if="currentUser">{{ getCurrentUserName() }}，加油！</text>
					</view>
					<view class="progress-ring">
						<text class="progress-text">{{ todayProgress }}%</text>
					</view>
				</view>
				<view class="flex-between">
					<text class="progress-desc">已完成 {{ completedCourses }}/{{ totalCourses }} 课程</text>
					<view class="checkin-area">
						<text class="checkin-status" :class="{ 'checked': isCheckedIn }" @tap="handleCheckin">
							{{ isCheckedIn ? '✓ 已打卡' : '📅 点击打卡' }}
						</text>
					</view>
				</view>
			</view>

			<!-- 课程级别选择 -->
			<view class="levels-section">
				<text class="section-title">选择学习课程</text>

				<!-- 加载状态 -->
				<view v-if="coursesLoading" class="course-loading">
					<view class="course-loading-item" v-for="i in 4" :key="i">
						<view class="loading-skeleton course-skeleton"></view>
					</view>
				</view>

				<!-- 所有级别课程列表 -->
				<view v-else class="all-courses-content">
					<view
						class="level-section"
						v-for="level in levels"
						:key="level.id"
					>
						<!-- 级别标题 -->
						<text class="level-title">{{ level.name }}</text>

						<!-- 该级别的课程列表 -->
						<view v-if="levelCourses[level.level] && levelCourses[level.level].length > 0" class="course-list">
							<view
								class="course-item"
								v-for="course in levelCourses[level.level]"
								:key="course.id"
								@click="goToLessonList(course)"
							>
								<view class="course-card" :class="{ 'premium': !course.isFree, 'current': course.isCurrent }">
									<view class="flex-between mb-3">
										<view class="flex">
											<view class="course-icon" :style="{ backgroundColor: course.cover || '#667eea' }">
												<text class="iconfont icon-play" :style="{ color: course.iconColor || 'white' }"></text>
											</view>
											<view class="course-info">
												<text class="course-title">{{ course.title }}</text>
												<text class="course-desc">{{ course.description }}</text>
											</view>
										</view>
										<view class="course-status">
											<view v-if="course.isFree" class="free-badge">免费</view>
											<view v-else class="price-badge">¥{{ course.price || 0 }}</view>
										</view>
									</view>

									<view class="flex-between course-meta mb-2">
										<text class="meta-item">
											<text class="iconfont icon-clock"></text>
											{{ course.duration || 30 }}分钟
										</text>
										<text class="meta-item">
											<text class="iconfont icon-headphones"></text>
											{{ getCourseStatusText(course) }}
										</text>
									</view>

									<view class="progress-bar">
										<view class="progress-fill" :style="{ width: (course.progress || 0) + '%' }"></view>
									</view>
								</view>
							</view>
						</view>

						<!-- 该级别的空状态 -->
						<view v-else class="level-empty-state">
							<text class="empty-text">暂无{{ level.name }}课程</text>
							<text class="empty-desc">更多精彩课程即将上线</text>
						</view>

						<!-- 查看该级别更多课程按钮 -->
						<view v-if="levelCourses[level.level] && levelCourses[level.level].length > 0" class="view-more-btn" @click="goToLevelCourseList(level)">
							<text class="btn-text">查看{{ level.name }}全部课程</text>
							<text class="iconfont icon-arrow-right"></text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { userApi, courseApi } from '@/api/index.js'
import pageMixin from '@/mixins/page-mixin.js'
import authMixin from '@/mixins/auth-mixin.js'
import MpPageWrapper from '@/components/mp-page-wrapper/mp-page-wrapper.vue'

export default {
	mixins: [pageMixin, authMixin],
	components: {
		MpPageWrapper
	},
	data() {
		return {
			statusBarHeight: 0,
			loading: true,
			// 用户学习数据
			todayProgress: 0,
			completedCourses: 0,
			totalCourses: 0,
			isCheckedIn: false,
			// 课程加载状态
			coursesLoading: false,
			// 各级别的课程数据
			levelCourses: {
				beginner: [],
				intermediate: [],
				advanced: [],
				professional: []
			},
			// 学习统计
			studyStats: null,
			// 课程级别数据
			levels: [
				{
					id: 1,
					name: '初级 N5-N4',
					description: '基础对话练习',
					icon: 'icon-seedling',
					iconBg: '#dcfce7',
					iconColor: '#16a34a',
					completed: 0,
					total: 0,
					progress: 0,
					progressColor: '#16a34a',
					level: 'beginner'
				},
				{
					id: 2,
					name: '中级 N3-N2',
					description: '日常会话提升',
					icon: 'icon-mountain',
					iconBg: '#dbeafe',
					iconColor: '#2563eb',
					completed: 0,
					total: 0,
					progress: 0,
					progressColor: '#2563eb',
					level: 'intermediate'
				},
				{
					id: 3,
					name: '高级 N1',
					description: '商务日语精进',
					icon: 'icon-crown',
					iconBg: '#f3e8ff',
					iconColor: '#9333ea',
					completed: 0,
					total: 0,
					progress: 0,
					progressColor: '#9333ea',
					level: 'advanced'
				},
				{
					id: 4,
					name: '专业级',
					description: '新闻听力训练',
					icon: 'icon-star',
					iconBg: '#fed7aa',
					iconColor: '#ea580c',
					completed: 0,
					total: 0,
					progress: 0,
					progressColor: '#ea580c',
					level: 'professional',
					isVip: true
				}
			]
		}
	},



	// 页面生命周期
	onShow() {
		// 每次显示页面时刷新认证状态
		this.checkAuthStatus()
	},

	// 监听认证状态变化
	onLoad() {
		// 监听登录成功事件
		uni.$on('authStateChanged', this.handleAuthStateChanged)
	},

	onUnload() {
		// 移除事件监听
		uni.$off('authStateChanged', this.handleAuthStateChanged)
	},

	methods: {
		// 处理认证状态变化
		handleAuthStateChanged(data) {
			console.log('🔄 首页收到认证状态变化事件:', data)
			if (data.isAuthenticated) {
				// 登录成功，刷新用户信息和数据
				this.checkAuthStatus()
				this.loadUserData()
			} else {
				// 登出，清除数据
				this.setDefaultStudyData()
			}
		},
		// 页面特定初始化
		pageInit() {
			console.log('🏠 首页初始化')
			this.loadUserData()
		},

		// 刷新数据
		async onRefresh() {
			console.log('🔄 首页刷新')
			await this.loadUserData()
		},

		async loadUserData() {
			this.loading = true

			try {
				// 首先刷新认证状态和用户信息
				this.checkAuthStatus()

				// 并行加载多个数据
				await Promise.all([
					this.getUserStudyData(),
					this.getCourseProgress(),
					this.checkTodayCheckin(),
					this.loadAllLevelCourses()
				])
			} catch (error) {
				console.error('加载用户数据失败:', error)
				this.showErrorToast('数据加载失败，请下拉刷新重试')
			} finally {
				this.loading = false
			}
		},

		// 获取用户学习统计数据
		async getUserStudyData() {
			try {
				const response = await userApi.getStudyStats()

				if (response.success) {
					this.studyStats = response.data

					// 更新今日学习进度
					const stats = response.data
					this.todayProgress = stats.todayProgress || 0
					this.completedCourses = stats.todayCompletedCourses || 0
					this.totalCourses = stats.todayTotalCourses || 0

					console.log('用户学习统计:', stats)
				}
			} catch (error) {
				// 未登录显示去学习	
				console.error('获取学习统计失败:', error)
				// 使用默认数据
				this.setDefaultStudyData()
			}
		},

		// 获取各级别课程进度
		async getCourseProgress() {
			try {
				const response = await courseApi.getCourseList({
					includeProgress: true
				})
				if (response.success && response.data.records) {
					this.updateLevelsProgress(response.data.records)
				}
			} catch (error) {
				console.error('获取课程进度失败:', error)
			}
		},

		// 检查今日签到状态
		async checkTodayCheckin() {
			try {
				const response = await userApi.getCheckinHistory({
					date: new Date().toISOString().split('T')[0]
				})

				if (response.success) {
					this.isCheckedIn = response.data.isCheckedToday || false
				}
			} catch (error) {
				console.error('检查签到状态失败:', error)
				this.isCheckedIn = false
			}
		},
		
		// 更新级别进度数据
		updateLevelsProgress(courseData) {
			// 按级别分组课程数据
			const levelStats = {
				beginner: { completed: 0, total: 0 },
				intermediate: { completed: 0, total: 0 },
				advanced: { completed: 0, total: 0 },
				professional: { completed: 0, total: 0 }
			}

			// 统计各级别的课程完成情况
			courseData.forEach(course => {
				const level = course.level || 'beginner'
				if (levelStats[level]) {
					levelStats[level].total++
					if (course.isCompleted) {
						levelStats[level].completed++
					}
				}
			})

			// 更新levels数组
			this.levels.forEach(level => {
				const stats = levelStats[level.level]
				if (stats) {
					level.completed = stats.completed
					level.total = stats.total
					level.progress = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0
				}
			})
		},

		// 设置默认学习数据
		setDefaultStudyData() {
			this.todayProgress = 0
			this.completedCourses = 0
			this.totalCourses = 0
		},

		// 显示错误提示
		showErrorToast(message) {
			uni.showToast({
				title: message,
				icon: 'none',
				duration: 3000
			})
		},

		// 加载所有级别的课程
		async loadAllLevelCourses() {
			this.coursesLoading = true

			try {
				console.log('� 加载所有级别课程')

				// 并行加载所有级别的课程
				const levelKeys = ['beginner', 'intermediate', 'advanced', 'professional']
				await Promise.all(levelKeys.map(level => this.loadCoursesForLevel(level)))

				console.log('✅ 所有级别课程加载完成')
			} catch (error) {
				console.error('❌ 加载所有级别课程失败:', error)
			} finally {
				this.coursesLoading = false
			}
		},

		// 加载指定级别的课程
		async loadCoursesForLevel(level) {
			try {
				console.log('📚 加载级别课程:', level)

				// 模拟API调用
				const response = await courseApi.getCoursesByLevel(level)

				if (response.success && response.data.records) {
					this.levelCourses[level] = this.formatCourseData(response.data.records)
					console.log(`✅ ${level}级别课程加载成功:`, this.levelCourses[level].length, '门课程')
				} else {
					// 使用模拟数据
					this.levelCourses[level] = this.getMockCoursesForLevel(level)
					console.log(`📝 使用${level}级别模拟数据`)
				}
			} catch (error) {
				console.error(`❌ 加载${level}级别课程失败:`, error)
				// 使用模拟数据
				this.levelCourses[level] = this.getMockCoursesForLevel(level)
			}
		},

		// 获取模拟课程数据
		getMockCoursesForLevel(level) {
			const mockCourses = {
				beginner: [
					{
						id: 1,
						title: '日语五十音图',
						description: '掌握日语基础发音',
						cover: '#16a34a',
						iconColor: 'white',
						duration: 45,
						progress: 80,
						isFree: true,
						isCurrent: true,
						status: 'learning'
					},
					{
						id: 2,
						title: '基础问候语',
						description: '日常打招呼用语',
						cover: '#059669',
						iconColor: 'white',
						duration: 30,
						progress: 60,
						isFree: true,
						isCurrent: false,
						status: 'learning'
					},
					{
						id: 3,
						title: '数字与时间',
						description: '学会用日语表达数字和时间',
						cover: '#0d9488',
						iconColor: 'white',
						duration: 35,
						progress: 0,
						isFree: false,
						price: 29,
						isCurrent: false,
						status: 'locked'
					}
				],
				intermediate: [
					{
						id: 4,
						title: '日常会话进阶',
						description: '提升日常对话能力',
						cover: '#2563eb',
						iconColor: 'white',
						duration: 50,
						progress: 40,
						isFree: false,
						price: 49,
						isCurrent: false,
						status: 'learning'
					},
					{
						id: 5,
						title: '商务日语入门',
						description: '职场日语基础',
						cover: '#1d4ed8',
						iconColor: 'white',
						duration: 60,
						progress: 0,
						isFree: false,
						price: 69,
						isCurrent: false,
						status: 'locked'
					}
				],
				advanced: [
					{
						id: 6,
						title: '高级语法精讲',
						description: '掌握复杂语法结构',
						cover: '#9333ea',
						iconColor: 'white',
						duration: 75,
						progress: 20,
						isFree: false,
						price: 99,
						isCurrent: false,
						status: 'learning'
					}
				],
				professional: [
					{
						id: 7,
						title: '新闻听力训练',
						description: '提升日语新闻理解能力',
						cover: '#ea580c',
						iconColor: 'white',
						duration: 90,
						progress: 0,
						isFree: false,
						price: 129,
						isCurrent: false,
						status: 'locked'
					}
				]
			}

			return mockCourses[level] || []
		},

		// 格式化课程数据
		formatCourseData(courses) {
			return courses.map(course => ({
				id: course.id,
				title: course.title || course.name,
				description: course.description || course.desc,
				cover: course.cover || course.thumbnail,
				iconColor: course.iconColor || 'white',
				duration: course.duration || 30,
				progress: course.progress || 0,
				isFree: course.isFree !== false,
				price: course.price || 0,
				isCurrent: course.isCurrent || false,
				status: course.status || 'available'
			}))
		},

		// 获取课程状态文本
		getCourseStatusText(course) {
			switch (course.status) {
				case 'learning':
					return '学习中'
				case 'completed':
					return '已完成'
				case 'locked':
					return '未解锁'
				default:
					return '可学习'
			}
		},

		// 跳转到课时一览
		goToLessonList(course) {
			console.log('📚 跳转到课时一览:', course.title)

			if (!course.isFree && !this.checkVipStatus()) {
				this.showVipDialog()
				return
			}

			uni.navigateTo({
				url: `/pages/course/lesson-list?id=${course.id}&title=${encodeURIComponent(course.title)}`,
				success: () => {
					console.log('✅ 成功跳转到课时一览')
				},
				fail: (error) => {
					console.error('❌ 跳转课时一览失败:', error)
					uni.showToast({
						title: '跳转失败',
						icon: 'none'
					})
				}
			})
		},

		// 跳转到课程详情（保留原方法，供其他地方使用）
		goToCourseDetail(course) {
			console.log('📖 跳转到课程详情:', course.title)

			if (!course.isFree && !this.checkVipStatus()) {
				this.showVipDialog()
				return
			}

			uni.navigateTo({
				url: `/pages/course/detail?id=${course.id}&title=${encodeURIComponent(course.title)}`,
				success: () => {
					console.log('✅ 成功跳转到课程详情')
				},
				fail: (error) => {
					console.error('❌ 跳转课程详情失败:', error)
					uni.showToast({
						title: '跳转失败',
						icon: 'none'
					})
				}
			})
		},

		// 跳转到级别课程列表
		goToLevelCourseList(level) {
			console.log('📋 跳转到级别课程列表:', level.name)

			// 存储级别信息
			uni.setStorageSync('selectedCourseLevel', {
				level: level.level,
				title: level.name,
				timestamp: Date.now()
			})

			// 跳转到课程列表页面
			uni.switchTab({
				url: '/pages/course/list',
				success: () => {
					console.log('✅ 成功跳转到课程列表页面')
				},
				fail: (error) => {
					console.error('❌ 跳转课程列表失败:', error)
					uni.showToast({
						title: '跳转失败',
						icon: 'none'
					})
				}
			})
		},

		// 跳转到课程列表（保留原方法兼容性）
		goToCourseList(level) {
			if (level.isVip && !this.checkVipStatus()) {
				this.showVipDialog()
				return
			}

			// 存储级别信息
			uni.setStorageSync('selectedCourseLevel', {
				level: level.level,
				title: level.name,
				timestamp: Date.now()
			})

			// 跳转到课程列表页面
			uni.switchTab({
				url: '/pages/course/list',
				success: () => {
					console.log('✅ 成功跳转到课程列表页面')
				},
				fail: (error) => {
					console.error('❌ 跳转课程列表失败:', error)
					uni.showToast({
						title: '跳转失败',
						icon: 'none'
					})
				}
			})
		},

		// 检查VIP状态
		checkVipStatus() {
			if (this.currentUser && this.currentUser.vipStatus) {
				return this.currentUser.vipStatus.isVip || false
			}
			return false
		},

		// 显示VIP对话框
		showVipDialog() {
			uni.showModal({
				title: '提示',
				content: '该课程需要VIP会员才能学习，是否立即开通？',
				confirmText: '开通VIP',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.goToVipPage()
					}
				}
			})
		},

		// 跳转到VIP页面
		goToVipPage() {
			uni.navigateTo({
				url: '/pages/vip/vip'
			})
		},

		// 手动签到
		async handleCheckin() {
			if (this.isCheckedIn) {
				uni.showToast({
					title: '今日已签到',
					icon: 'none'
				})
				return
			}

			try {
				uni.showLoading({ title: '签到中...' })

				const response = await userApi.checkin()

				if (response.success) {
					this.isCheckedIn = true
					uni.showToast({
						title: '签到成功！',
						icon: 'success'
					})

					// 刷新学习数据
					this.getUserStudyData()
				} else {
					uni.showToast({
						title: response.message || '签到失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('签到失败:', error)
				uni.showToast({
					title: '签到失败，请重试',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-bar {
	background: transparent;
}

.content {
	min-height: calc(100vh - var(--status-bar-height));
	padding-bottom: 200rpx;
}

.header {
	padding: 96rpx 48rpx;
	text-align: center;
	
	.greeting {
		display: block;
		color: white;
		font-size: 48rpx;
		font-weight: bold;
		margin-bottom: 16rpx;
	}
	
	.subtitle {
		color: rgba(255, 255, 255, 0.8);
		font-size: 32rpx;
	}
}

.study-card {
	margin: 32rpx;
	
	.card-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.progress-ring {
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		background: conic-gradient(#667eea 0deg 216deg, #e5e7eb 216deg 360deg);
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		
		&::before {
			content: '';
			position: absolute;
			width: 120rpx;
			height: 120rpx;
			background: white;
			border-radius: 50%;
		}
		
		.progress-text {
			position: relative;
			z-index: 1;
			font-weight: bold;
			color: #667eea;
			font-size: 28rpx;
		}
	}
	
	.progress-desc {
		color: #666;
		font-size: 28rpx;
	}
	
	.card-subtitle {
		display: block;
		color: #999;
		font-size: 24rpx;
		margin-top: 8rpx;
	}

	.checkin-area {
		.checkin-status {
			color: #666;
			font-size: 28rpx;
			padding: 8rpx 16rpx;
			border-radius: 20rpx;
			background: #f5f5f5;
			cursor: pointer;
			transition: all 0.3s ease;

			&.checked {
				color: #16a34a;
				background: #dcfce7;
				font-weight: 500;
			}

			&:not(.checked):hover {
				background: #667eea;
				color: white;
			}
		}
	}
}

.levels-section {
	padding: 0 48rpx;

	.section-title {
		display: block;
		color: white;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 32rpx;
	}

	.all-courses-content {
		.level-section {
			margin-bottom: 48rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.level-title {
				display: block;
				font-size: 36rpx;
				font-weight: bold;
				color: white;
				margin-bottom: 24rpx;
				text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
			}

			.level-empty-state {
				text-align: center;
				padding: 60rpx 40rpx;

				.empty-text {
					display: block;
					font-size: 28rpx;
					color: rgba(255, 255, 255, 0.7);
					margin-bottom: 8rpx;
				}

				.empty-desc {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.5);
				}
			}
		}
	}

	.course-content, .all-courses-content {
		.course-loading {
			.course-loading-item {
				margin-bottom: 24rpx;

				.course-skeleton {
					height: 160rpx;
					background: rgba(255, 255, 255, 0.1);
					border-radius: 16rpx;
					animation: skeleton-loading 1.5s ease-in-out infinite;
				}
			}
		}

		.course-list {
			.course-item {
				margin-bottom: 24rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.course-card {
					background: white;
					border-radius: 16rpx;
					padding: 24rpx;
					box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
					transition: transform 0.2s ease;
					position: relative;

					&:active {
						transform: scale(0.98);
					}

					&.premium {
						border: 2rpx solid #fbbf24;

						&::before {
							content: 'VIP';
							position: absolute;
							top: -2rpx;
							right: -2rpx;
							background: linear-gradient(135deg, #fbbf24, #f59e0b);
							color: white;
							font-size: 20rpx;
							padding: 4rpx 8rpx;
							border-radius: 0 14rpx 0 8rpx;
							font-weight: bold;
						}
					}

					&.current {
						border: 2rpx solid #667eea;
						background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
					}

					.course-icon {
						width: 80rpx;
						height: 80rpx;
						border-radius: 20rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 24rpx;

						.iconfont {
							font-size: 36rpx;
						}
					}

					.course-info {
						flex: 1;

						.course-title {
							display: block;
							font-size: 32rpx;
							font-weight: bold;
							color: #333;
							margin-bottom: 8rpx;
						}

						.course-desc {
							font-size: 26rpx;
							color: #666;
						}
					}

					.course-status {
						.free-badge {
							background: linear-gradient(135deg, #10b981, #059669);
							color: white;
							font-size: 22rpx;
							padding: 6rpx 12rpx;
							border-radius: 12rpx;
							font-weight: bold;
						}

						.price-badge {
							background: linear-gradient(135deg, #f59e0b, #d97706);
							color: white;
							font-size: 22rpx;
							padding: 6rpx 12rpx;
							border-radius: 12rpx;
							font-weight: bold;
						}
					}

					.course-meta {
						.meta-item {
							font-size: 24rpx;
							color: #666;
							display: flex;
							align-items: center;

							.iconfont {
								font-size: 20rpx;
								margin-right: 6rpx;
							}
						}
					}

					.progress-bar {
						width: 100%;
						height: 8rpx;
						background: #f0f0f0;
						border-radius: 4rpx;
						overflow: hidden;

						.progress-fill {
							height: 100%;
							background: linear-gradient(135deg, #667eea, #764ba2);
							border-radius: 4rpx;
							transition: width 0.3s ease;
						}
					}
				}
			}
		}

		.empty-state {
			text-align: center;
			padding: 80rpx 40rpx;

			.empty-icon {
				font-size: 120rpx;
				color: rgba(255, 255, 255, 0.3);
				margin-bottom: 24rpx;
			}

			.empty-text {
				display: block;
				font-size: 32rpx;
				color: rgba(255, 255, 255, 0.8);
				margin-bottom: 12rpx;
			}

			.empty-desc {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.6);
			}
		}

		.view-more-btn {
			margin-top: 24rpx;
			background: rgba(255, 255, 255, 0.15);
			border-radius: 16rpx;
			padding: 24rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			backdrop-filter: blur(10rpx);
			border: 1rpx solid rgba(255, 255, 255, 0.2);

			.btn-text {
				color: white;
				font-size: 28rpx;
				font-weight: bold;
				margin-right: 12rpx;
			}

			.iconfont {
				color: white;
				font-size: 24rpx;
			}
		}
	}

	.loading-container {
		.loading-item {
			margin-bottom: 32rpx;

			.loading-skeleton {
				height: 120rpx;
				background: rgba(255, 255, 255, 0.1);
				border-radius: 20rpx;
				animation: skeleton-loading 1.5s ease-in-out infinite;
			}
		}
	}
}

@keyframes skeleton-loading {
	0%, 100% {
		opacity: 0.3;
	}
	50% {
		opacity: 0.7;
	}
}

.level-item {
	margin-bottom: 32rpx;
	
	.level-icon {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 32rpx;
		
		.iconfont {
			font-size: 36rpx;
		}
	}
	
	.level-info {
		flex: 1;
		
		.level-name {
			display: block;
			font-weight: bold;
			color: #333;
			font-size: 32rpx;
			margin-bottom: 8rpx;
		}
		
		.level-desc {
			color: #666;
			font-size: 28rpx;
		}
	}
	
	.level-progress {
		text-align: right;
		
		.progress-text {
			display: block;
			color: #666;
			font-size: 24rpx;
			margin-bottom: 8rpx;
		}
		
		.progress-bar {
			width: 128rpx;
			height: 8rpx;
			background: #e5e7eb;
			border-radius: 4rpx;
			overflow: hidden;
			
			.progress-fill {
				height: 100%;
				border-radius: 4rpx;
				transition: width 0.3s ease;
			}
		}
	}
}

/* 字体图标 */
.iconfont {
	font-family: 'iconfont';
}

.icon-seedling::before { content: '\e001'; }
.icon-mountain::before { content: '\e002'; }
.icon-crown::before { content: '\e003'; }
.icon-star::before { content: '\e004'; }
</style>
