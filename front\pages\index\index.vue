<template>
	<view class="page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 页面内容 -->
		<view class="content" :style="{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }">
			<!-- 头部问候 -->
			<view class="header">
				<text class="greeting">こんにちは！</text>
				<text class="subtitle">今天也要加油学习日语哦～</text>
			</view>

			<!-- 今日学习卡片 -->
			<view class="study-card card">
				<view class="flex-between mb-4">
					<view>
						<text class="card-title">今日学习</text>
						<text class="card-subtitle" v-if="currentUser">{{ getCurrentUserName() }}，加油！</text>
					</view>
					<view class="progress-ring">
						<text class="progress-text">{{ todayProgress }}%</text>
					</view>
				</view>
				<view class="flex-between">
					<text class="progress-desc">已完成 {{ completedCourses }}/{{ totalCourses }} 课程</text>
					<view class="checkin-area">
						<text class="checkin-status" :class="{ 'checked': isCheckedIn }" @tap="handleCheckin">
							{{ isCheckedIn ? '✓ 已打卡' : '📅 点击打卡' }}
						</text>
					</view>
				</view>
			</view>

			<!-- 课程级别选择 -->
			<view class="levels-section">
				<text class="section-title">选择学习级别</text>

				<!-- 加载状态 -->
				<view v-if="loading" class="loading-container">
					<view class="loading-item" v-for="i in 4" :key="i">
						<view class="loading-skeleton"></view>
					</view>
				</view>

				<!-- 级别列表 -->
				<view v-else class="level-item card" v-for="level in levels" :key="level.id" @click="goToCourseList(level)">
					<view class="flex-between">
						<view class="flex">
							<view class="level-icon" :style="{ backgroundColor: level.iconBg }">
								<text class="iconfont" :class="level.icon" :style="{ color: level.iconColor }"></text>
							</view>
							<view class="level-info">
								<text class="level-name">{{ level.name }}</text>
								<text class="level-desc">{{ level.description }}</text>
							</view>
						</view>
						<view class="level-progress">
							<text class="progress-text">{{ level.completed }}/{{ level.total }} 课程</text>
							<view class="progress-bar">
								<view class="progress-fill" :style="{ width: level.progress + '%', backgroundColor: level.progressColor }"></view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { userApi, courseApi } from '@/api/index.js'
import pageMixin from '@/mixins/page-mixin.js'
import authMixin from '@/mixins/auth-mixin.js'
import MpPageWrapper from '@/components/mp-page-wrapper/mp-page-wrapper.vue'

export default {
	mixins: [pageMixin, authMixin],
	components: {
		MpPageWrapper
	},
	data() {
		return {
			statusBarHeight: 0,
			loading: true,
			// 用户学习数据
			todayProgress: 0,
			completedCourses: 0,
			totalCourses: 0,
			isCheckedIn: false,
			// 学习统计
			studyStats: null,
			// 课程级别数据
			levels: [
				{
					id: 1,
					name: '初级 N5-N4',
					description: '基础对话练习',
					icon: 'icon-seedling',
					iconBg: '#dcfce7',
					iconColor: '#16a34a',
					completed: 0,
					total: 0,
					progress: 0,
					progressColor: '#16a34a',
					level: 'beginner'
				},
				{
					id: 2,
					name: '中级 N3-N2',
					description: '日常会话提升',
					icon: 'icon-mountain',
					iconBg: '#dbeafe',
					iconColor: '#2563eb',
					completed: 0,
					total: 0,
					progress: 0,
					progressColor: '#2563eb',
					level: 'intermediate'
				},
				{
					id: 3,
					name: '高级 N1',
					description: '商务日语精进',
					icon: 'icon-crown',
					iconBg: '#f3e8ff',
					iconColor: '#9333ea',
					completed: 0,
					total: 0,
					progress: 0,
					progressColor: '#9333ea',
					level: 'advanced'
				},
				{
					id: 4,
					name: '专业级',
					description: '新闻听力训练',
					icon: 'icon-star',
					iconBg: '#fed7aa',
					iconColor: '#ea580c',
					completed: 0,
					total: 0,
					progress: 0,
					progressColor: '#ea580c',
					level: 'professional',
					isVip: true
				}
			]
		}
	},

	// 页面生命周期
	onShow() {
		// 每次显示页面时刷新认证状态
		this.checkAuthStatus()
	},

	// 监听认证状态变化
	onLoad() {
		// 监听登录成功事件
		uni.$on('authStateChanged', this.handleAuthStateChanged)
	},

	onUnload() {
		// 移除事件监听
		uni.$off('authStateChanged', this.handleAuthStateChanged)
	},

	methods: {
		// 处理认证状态变化
		handleAuthStateChanged(data) {
			console.log('🔄 首页收到认证状态变化事件:', data)
			if (data.isAuthenticated) {
				// 登录成功，刷新用户信息和数据
				this.checkAuthStatus()
				this.loadUserData()
			} else {
				// 登出，清除数据
				this.setDefaultStudyData()
			}
		},
		// 页面特定初始化
		pageInit() {
			console.log('🏠 首页初始化')
			this.loadUserData()
		},

		// 刷新数据
		async onRefresh() {
			console.log('🔄 首页刷新')
			await this.loadUserData()
		},

		async loadUserData() {
			this.loading = true

			try {
				// 首先刷新认证状态和用户信息
				this.checkAuthStatus()

				// 并行加载多个数据
				await Promise.all([
					this.getUserStudyData(),
					this.getCourseProgress(),
					this.checkTodayCheckin()
				])
			} catch (error) {
				console.error('加载用户数据失败:', error)
				this.showErrorToast('数据加载失败，请下拉刷新重试')
			} finally {
				this.loading = false
			}
		},

		// 获取用户学习统计数据
		async getUserStudyData() {
			try {
				const response = await userApi.getStudyStats()

				if (response.success) {
					this.studyStats = response.data

					// 更新今日学习进度
					const stats = response.data
					this.todayProgress = stats.todayProgress || 0
					this.completedCourses = stats.todayCompletedCourses || 0
					this.totalCourses = stats.todayTotalCourses || 0

					console.log('用户学习统计:', stats)
				}
			} catch (error) {
				// 未登录显示去学习	
				console.error('获取学习统计失败:', error)
				// 使用默认数据
				this.setDefaultStudyData()
			}
		},

		// 获取各级别课程进度
		async getCourseProgress() {
			try {
				const response = await courseApi.getCourseList({
					includeProgress: true
				})
				console.log('XXXXXXXXXXXXX:', response)
				if (response.success && response.data.records) {
					this.updateLevelsProgress(response.data.records)
				}
			} catch (error) {
				console.error('获取课程进度失败:', error)
			}
		},

		// 检查今日签到状态
		async checkTodayCheckin() {
			try {
				const response = await userApi.getCheckinHistory({
					date: new Date().toISOString().split('T')[0]
				})

				if (response.success) {
					this.isCheckedIn = response.data.isCheckedToday || false
				}
			} catch (error) {
				console.error('检查签到状态失败:', error)
				this.isCheckedIn = false
			}
		},
		
		// 更新级别进度数据
		updateLevelsProgress(courseData) {
			// 按级别分组课程数据
			const levelStats = {
				beginner: { completed: 0, total: 0 },
				intermediate: { completed: 0, total: 0 },
				advanced: { completed: 0, total: 0 },
				professional: { completed: 0, total: 0 }
			}

			// 统计各级别的课程完成情况
			courseData.forEach(course => {
				const level = course.level || 'beginner'
				if (levelStats[level]) {
					levelStats[level].total++
					if (course.isCompleted) {
						levelStats[level].completed++
					}
				}
			})

			// 更新levels数组
			this.levels.forEach(level => {
				const stats = levelStats[level.level]
				if (stats) {
					level.completed = stats.completed
					level.total = stats.total
					level.progress = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0
				}
			})
		},

		// 设置默认学习数据
		setDefaultStudyData() {
			this.todayProgress = 0
			this.completedCourses = 0
			this.totalCourses = 0
		},

		// 显示错误提示
		showErrorToast(message) {
			uni.showToast({
				title: message,
				icon: 'none',
				duration: 3000
			})
		},

		// 跳转到课程列表
		goToCourseList(level) {
			if (level.isVip && !this.checkVipStatus()) {
				this.showVipDialog()
				return
			}

			// 由于course/list是tabBar页面，需要使用switchTab
			// 先将级别信息存储到全局状态或本地存储
			uni.setStorageSync('selectedCourseLevel', {
				level: level.level,
				title: level.name,
				timestamp: Date.now()
			})

			// 使用switchTab跳转到tabBar页面
			uni.switchTab({
				url: '/pages/course/list',
				success: () => {
					console.log('✅ 成功跳转到课程列表页面')
				},
				fail: (error) => {
					console.error('❌ 跳转课程列表失败:', error)
					uni.showToast({
						title: '跳转失败',
						icon: 'none'
					})
				}
			})
		},

		// 检查VIP状态
		checkVipStatus() {
			if (this.currentUser && this.currentUser.vipStatus) {
				return this.currentUser.vipStatus.isVip || false
			}
			return false
		},

		// 显示VIP对话框
		showVipDialog() {
			uni.showModal({
				title: '提示',
				content: '该课程需要VIP会员才能学习，是否立即开通？',
				confirmText: '开通VIP',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.goToVipPage()
					}
				}
			})
		},

		// 跳转到VIP页面
		goToVipPage() {
			uni.navigateTo({
				url: '/pages/vip/vip'
			})
		},

		// 手动签到
		async handleCheckin() {
			if (this.isCheckedIn) {
				uni.showToast({
					title: '今日已签到',
					icon: 'none'
				})
				return
			}

			try {
				uni.showLoading({ title: '签到中...' })

				const response = await userApi.checkin()

				if (response.success) {
					this.isCheckedIn = true
					uni.showToast({
						title: '签到成功！',
						icon: 'success'
					})

					// 刷新学习数据
					this.getUserStudyData()
				} else {
					uni.showToast({
						title: response.message || '签到失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('签到失败:', error)
				uni.showToast({
					title: '签到失败，请重试',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-bar {
	background: transparent;
}

.content {
	min-height: calc(100vh - var(--status-bar-height));
	padding-bottom: 200rpx;
}

.header {
	padding: 96rpx 48rpx;
	text-align: center;
	
	.greeting {
		display: block;
		color: white;
		font-size: 48rpx;
		font-weight: bold;
		margin-bottom: 16rpx;
	}
	
	.subtitle {
		color: rgba(255, 255, 255, 0.8);
		font-size: 32rpx;
	}
}

.study-card {
	margin: 32rpx;
	
	.card-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.progress-ring {
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		background: conic-gradient(#667eea 0deg 216deg, #e5e7eb 216deg 360deg);
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		
		&::before {
			content: '';
			position: absolute;
			width: 120rpx;
			height: 120rpx;
			background: white;
			border-radius: 50%;
		}
		
		.progress-text {
			position: relative;
			z-index: 1;
			font-weight: bold;
			color: #667eea;
			font-size: 28rpx;
		}
	}
	
	.progress-desc {
		color: #666;
		font-size: 28rpx;
	}
	
	.card-subtitle {
		display: block;
		color: #999;
		font-size: 24rpx;
		margin-top: 8rpx;
	}

	.checkin-area {
		.checkin-status {
			color: #666;
			font-size: 28rpx;
			padding: 8rpx 16rpx;
			border-radius: 20rpx;
			background: #f5f5f5;
			cursor: pointer;
			transition: all 0.3s ease;

			&.checked {
				color: #16a34a;
				background: #dcfce7;
				font-weight: 500;
			}

			&:not(.checked):hover {
				background: #667eea;
				color: white;
			}
		}
	}
}

.levels-section {
	padding: 0 48rpx;

	.section-title {
		display: block;
		color: white;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 32rpx;
	}

	.loading-container {
		.loading-item {
			margin-bottom: 32rpx;

			.loading-skeleton {
				height: 120rpx;
				background: rgba(255, 255, 255, 0.1);
				border-radius: 20rpx;
				animation: skeleton-loading 1.5s ease-in-out infinite;
			}
		}
	}
}

@keyframes skeleton-loading {
	0%, 100% {
		opacity: 0.3;
	}
	50% {
		opacity: 0.7;
	}
}

.level-item {
	margin-bottom: 32rpx;
	
	.level-icon {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 32rpx;
		
		.iconfont {
			font-size: 36rpx;
		}
	}
	
	.level-info {
		flex: 1;
		
		.level-name {
			display: block;
			font-weight: bold;
			color: #333;
			font-size: 32rpx;
			margin-bottom: 8rpx;
		}
		
		.level-desc {
			color: #666;
			font-size: 28rpx;
		}
	}
	
	.level-progress {
		text-align: right;
		
		.progress-text {
			display: block;
			color: #666;
			font-size: 24rpx;
			margin-bottom: 8rpx;
		}
		
		.progress-bar {
			width: 128rpx;
			height: 8rpx;
			background: #e5e7eb;
			border-radius: 4rpx;
			overflow: hidden;
			
			.progress-fill {
				height: 100%;
				border-radius: 4rpx;
				transition: width 0.3s ease;
			}
		}
	}
}

/* 字体图标 */
.iconfont {
	font-family: 'iconfont';
}

.icon-seedling::before { content: '\e001'; }
.icon-mountain::before { content: '\e002'; }
.icon-crown::before { content: '\e003'; }
.icon-star::before { content: '\e004'; }
</style>
