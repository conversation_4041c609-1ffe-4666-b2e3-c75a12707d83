<template>
	<view class="container">
		<view class="header">
			<text class="title">📱 Tabbar 图标预览</text>
			<text class="subtitle">口语便利店小程序图标系统</text>
		</view>
		
		<view class="color-guide">
			<view class="color-item">
				<view class="color-box normal"></view>
				<text>未选中 #999999</text>
			</view>
			<view class="color-item">
				<view class="color-box active"></view>
				<text>选中 #667eea</text>
			</view>
		</view>
		
		<view class="icons-grid">
			<view class="icon-group" v-for="icon in icons" :key="icon.name">
				<view class="icon-header">
					<text class="icon-title">{{ icon.title }}</text>
					<text class="icon-name">{{ icon.name }}</text>
				</view>
				
				<view class="icon-states">
					<view class="icon-state">
						<image 
							:src="icon.normalPath" 
							class="icon-image"
							mode="aspectFit"
							@error="onImageError"
						/>
						<text class="state-label">未选中</text>
					</view>
					
					<view class="icon-state">
						<image 
							:src="icon.activePath" 
							class="icon-image"
							mode="aspectFit"
							@error="onImageError"
						/>
						<text class="state-label">选中</text>
					</view>
				</view>
				
				<view class="icon-description">
					<text>{{ icon.description }}</text>
				</view>
			</view>
		</view>
		
		<view class="usage-guide">
			<text class="guide-title">🔧 使用说明</text>
			<view class="guide-content">
				<text class="guide-item">1. 图标文件位于 static/tabbar/ 目录</text>
				<text class="guide-item">2. 在 pages.json 中配置 tabBar</text>
				<text class="guide-item">3. 推荐尺寸：48x48px</text>
				<text class="guide-item">4. 支持格式：PNG（透明背景）</text>
			</view>
		</view>
		
		<view class="footer">
			<text>设计规范 v1.0.0 | Kuma Team</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			icons: [
				{
					name: 'home',
					title: '首页',
					description: '主页入口，用户的起始点和导航中心',
					normalPath: '/static/tabbar/home.png',
					activePath: '/static/tabbar/home-active.png'
				},
				{
					name: 'course',
					title: '课程',
					description: '学习内容展示，包含各类日语课程',
					normalPath: '/static/tabbar/course.png',
					activePath: '/static/tabbar/course-active.png'
				},
				{
					name: 'checkin',
					title: '打卡',
					description: '每日学习打卡，培养学习习惯',
					normalPath: '/static/tabbar/checkin.png',
					activePath: '/static/tabbar/checkin-active.png'
				},
				{
					name: 'leaderboard',
					title: '排行榜',
					description: '学习成绩排名，激发学习动力',
					normalPath: '/static/tabbar/leaderboard.png',
					activePath: '/static/tabbar/leaderboard-active.png'
				},
				{
					name: 'profile',
					title: '个人中心',
					description: '用户信息管理和个人设置',
					normalPath: '/static/tabbar/profile.png',
					activePath: '/static/tabbar/profile-active.png'
				}
			]
		}
	},
	
	methods: {
		onImageError(e) {
			console.warn('图标加载失败:', e);
			// 可以在这里设置默认图标或显示占位符
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	padding: 20px;
	background-color: #f8f9fa;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30px;
	
	.title {
		display: block;
		font-size: 24px;
		font-weight: bold;
		color: #333;
		margin-bottom: 8px;
	}
	
	.subtitle {
		display: block;
		font-size: 14px;
		color: #666;
	}
}

.color-guide {
	display: flex;
	justify-content: center;
	gap: 30px;
	margin-bottom: 30px;
	padding: 15px;
	background: white;
	border-radius: 12px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	
	.color-item {
		display: flex;
		align-items: center;
		gap: 8px;
		
		.color-box {
			width: 20px;
			height: 20px;
			border-radius: 4px;
			border: 1px solid #ddd;
			
			&.normal {
				background-color: #999999;
			}
			
			&.active {
				background-color: #667eea;
			}
		}
		
		text {
			font-size: 12px;
			color: #666;
		}
	}
}

.icons-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
	gap: 20px;
	margin-bottom: 30px;
}

.icon-group {
	background: white;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	
	.icon-header {
		text-align: center;
		margin-bottom: 20px;
		
		.icon-title {
			display: block;
			font-size: 18px;
			font-weight: bold;
			color: #333;
			margin-bottom: 4px;
		}
		
		.icon-name {
			display: block;
			font-size: 12px;
			color: #999;
			font-family: 'Monaco', 'Menlo', monospace;
		}
	}
	
	.icon-states {
		display: flex;
		justify-content: center;
		gap: 30px;
		margin-bottom: 15px;
		
		.icon-state {
			text-align: center;
			
			.icon-image {
				width: 48px;
				height: 48px;
				margin-bottom: 8px;
				border: 1px solid #f0f0f0;
				border-radius: 8px;
				background: #fafafa;
			}
			
			.state-label {
				display: block;
				font-size: 12px;
				color: #666;
			}
		}
	}
	
	.icon-description {
		text-align: center;
		
		text {
			font-size: 13px;
			color: #666;
			line-height: 1.4;
		}
	}
}

.usage-guide {
	background: #e3f2fd;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 30px;
	
	.guide-title {
		display: block;
		font-size: 16px;
		font-weight: bold;
		color: #1976d2;
		margin-bottom: 15px;
	}
	
	.guide-content {
		.guide-item {
			display: block;
			font-size: 14px;
			color: #333;
			margin-bottom: 8px;
			padding-left: 10px;
			position: relative;
			
			&:before {
				content: '•';
				position: absolute;
				left: 0;
				color: #1976d2;
			}
		}
	}
}

.footer {
	text-align: center;
	padding: 20px;
	
	text {
		font-size: 12px;
		color: #999;
	}
}

/* 响应式设计 */
@media (max-width: 768px) {
	.icons-grid {
		grid-template-columns: 1fr;
	}
	
	.color-guide {
		flex-direction: column;
		gap: 15px;
		
		.color-item {
			justify-content: center;
		}
	}
}
</style>
