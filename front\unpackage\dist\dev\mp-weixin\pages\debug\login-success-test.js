"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const utils_auth = require("../../utils/auth.js");
const config_api = require("../../config/api.js");
const _sfc_main = {
  data() {
    return {
      hasToken: false,
      hasUserInfo: false,
      currentUser: null,
      globalLoginStatus: false,
      tokenDisplay: "",
      storageData: {},
      testLogs: [],
      mockLoginData: `{
  "success": true,
  "result": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": "1001",
      "username": "admin",
      "realname": "管理员",
      "avatar": "/avatar/admin.jpg",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "orgCode": "A01",
      "orgCodeTxt": "总部",
      "departs": ["技术部"],
      "roles": ["admin"],
      "permissions": ["user:read", "user:write"]
    }
  }
}`
    };
  },
  onLoad() {
    this.addLog("登录成功数据处理测试页面加载", "info");
    this.checkAuthStatus();
  },
  onShow() {
    this.checkAuthStatus();
  },
  methods: {
    // 添加日志
    addLog(message, type = "info") {
      const now = /* @__PURE__ */ new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;
      this.testLogs.unshift({
        time,
        message,
        type
      });
      if (this.testLogs.length > 20) {
        this.testLogs = this.testLogs.slice(0, 20);
      }
    },
    // 检查认证状态
    checkAuthStatus() {
      try {
        const token = utils_auth.getToken();
        this.hasToken = !!token;
        this.tokenDisplay = token ? token.substring(0, 20) + "..." : "无";
        const userInfo = utils_auth.getUserInfo();
        this.hasUserInfo = !!userInfo;
        this.currentUser = userInfo;
        const app = getApp();
        this.globalLoginStatus = app && app.globalData && app.globalData.isLogin;
        this.checkLocalStorage();
        this.addLog("认证状态检查完成", "info");
      } catch (error) {
        this.addLog(`认证状态检查失败: ${error.message}`, "error");
      }
    },
    // 检查本地存储
    checkLocalStorage() {
      try {
        this.storageData = {
          "X-Access-Token": common_vendor.index.getStorageSync("X-Access-Token") || "无",
          "token": common_vendor.index.getStorageSync("token") || "无",
          "userInfo": common_vendor.index.getStorageSync("userInfo") ? "已保存" : "无",
          "permissions": common_vendor.index.getStorageSync("permissions") || "无",
          "roles": common_vendor.index.getStorageSync("roles") || "无"
        };
      } catch (error) {
        this.addLog(`本地存储检查失败: ${error.message}`, "error");
      }
    },
    // 测试保存认证信息
    testSaveAuthInfo() {
      return __async(this, null, function* () {
        this.addLog("开始测试保存认证信息", "info");
        try {
          const mockData = {
            token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
            id: "1001",
            username: "admin",
            realname: "管理员",
            avatar: "/avatar/admin.jpg",
            phone: "13800138000",
            email: "<EMAIL>",
            orgCode: "A01",
            orgCodeTxt: "总部",
            departs: ["技术部"],
            roles: ["admin"],
            permissions: ["user:read", "user:write"]
          };
          const tokenSaved = utils_auth.setToken(mockData.token);
          if (tokenSaved) {
            this.addLog("✅ Token保存成功", "success");
          } else {
            this.addLog("❌ Token保存失败", "error");
          }
          const userInfoSaved = utils_auth.setUserInfo({
            id: mockData.id,
            username: mockData.username,
            realname: mockData.realname,
            avatar: mockData.avatar,
            phone: mockData.phone,
            email: mockData.email,
            orgCode: mockData.orgCode,
            orgCodeTxt: mockData.orgCodeTxt,
            departs: mockData.departs,
            roles: mockData.roles,
            permissions: mockData.permissions
          });
          if (userInfoSaved) {
            this.addLog("✅ 用户信息保存成功", "success");
          } else {
            this.addLog("❌ 用户信息保存失败", "error");
          }
          this.checkAuthStatus();
        } catch (error) {
          this.addLog(`保存认证信息异常: ${error.message}`, "error");
        }
      });
    },
    // 测试请求头
    testTokenHeaders() {
      this.addLog("测试请求头配置", "info");
      try {
        const headers = config_api.getRequestHeaders();
        this.addLog(`请求头配置: ${JSON.stringify(headers, null, 2)}`, "info");
        if (headers["X-Access-Token"]) {
          this.addLog("✅ X-Access-Token 请求头配置正确", "success");
        } else {
          this.addLog("❌ X-Access-Token 请求头缺失", "error");
        }
        if (headers["X-Access-Client"] === "miniApp") {
          this.addLog("✅ X-Access-Client 请求头配置正确", "success");
        } else {
          this.addLog("❌ X-Access-Client 请求头配置错误", "error");
        }
      } catch (error) {
        this.addLog(`请求头测试异常: ${error.message}`, "error");
      }
    },
    // 测试全局状态
    testGlobalState() {
      this.addLog("测试全局状态同步", "info");
      try {
        const app = getApp();
        if (app && app.globalData) {
          this.addLog(`全局状态: ${JSON.stringify({
            isLogin: app.globalData.isLogin,
            hasToken: !!app.globalData.token,
            hasUserInfo: !!app.globalData.userInfo
          }, null, 2)}`, "info");
          if (app.globalData.isLogin) {
            this.addLog("✅ 全局登录状态正确", "success");
          } else {
            this.addLog("❌ 全局登录状态错误", "error");
          }
        } else {
          this.addLog("❌ 无法获取全局状态", "error");
        }
      } catch (error) {
        this.addLog(`全局状态测试异常: ${error.message}`, "error");
      }
    },
    // 清除认证信息
    testClearAuth() {
      this.addLog("清除认证信息", "warning");
      try {
        utils_auth.removeToken();
        utils_auth.removeUserInfo();
        this.addLog("✅ 认证信息清除成功", "success");
        this.checkAuthStatus();
      } catch (error) {
        this.addLog(`清除认证信息异常: ${error.message}`, "error");
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.hasToken ? "已保存" : "未保存"),
    b: common_vendor.n($data.hasToken ? "success" : "error"),
    c: common_vendor.t($data.tokenDisplay),
    d: common_vendor.t($data.hasUserInfo ? "已保存" : "未保存"),
    e: common_vendor.n($data.hasUserInfo ? "success" : "error"),
    f: common_vendor.t($data.currentUser ? $data.currentUser.username : "无"),
    g: common_vendor.t($data.currentUser ? $data.currentUser.realname : "无"),
    h: common_vendor.t($data.globalLoginStatus ? "已登录" : "未登录"),
    i: common_vendor.n($data.globalLoginStatus ? "success" : "error"),
    j: common_vendor.t($data.mockLoginData),
    k: common_vendor.o((...args) => $options.testSaveAuthInfo && $options.testSaveAuthInfo(...args)),
    l: common_vendor.o((...args) => $options.testTokenHeaders && $options.testTokenHeaders(...args)),
    m: common_vendor.o((...args) => $options.testGlobalState && $options.testGlobalState(...args)),
    n: common_vendor.o((...args) => $options.testClearAuth && $options.testClearAuth(...args)),
    o: common_vendor.f($data.storageData, (item, key, i0) => {
      return {
        a: common_vendor.t(key),
        b: common_vendor.t(item),
        c: key
      };
    }),
    p: common_vendor.f($data.testLogs, (log, index, i0) => {
      return {
        a: common_vendor.t(log.time),
        b: common_vendor.t(log.message),
        c: common_vendor.n(log.type),
        d: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-201a54f3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/login-success-test.js.map
