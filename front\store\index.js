import { createStore } from 'vuex'
import user from './modules/user'
import course from './modules/course'
import checkin from './modules/checkin'
import practice from './modules/practice'

const store = createStore({
  modules: {
    user,
    course,
    checkin,
    practice
  },
  
  state: {
    // 全局状态
    systemInfo: {},
    networkType: 'unknown',
    isOnline: true
  },
  
  mutations: {
    SET_SYSTEM_INFO(state, systemInfo) {
      state.systemInfo = systemInfo
    },
    
    SET_NETWORK_TYPE(state, networkType) {
      state.networkType = networkType
    },
    
    SET_ONLINE_STATUS(state, isOnline) {
      state.isOnline = isOnline
    }
  },
  
  actions: {
    // 初始化应用
    async initApp({ commit, dispatch }) {
      try {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync()
        commit('SET_SYSTEM_INFO', systemInfo)
        
        // 监听网络状态
        uni.onNetworkStatusChange((res) => {
          commit('SET_NETWORK_TYPE', res.networkType)
          commit('SET_ONLINE_STATUS', res.isConnected)
        })
        
        // 检查登录状态
        await dispatch('user/checkLoginStatus')
        
        console.log('应用初始化完成')
      } catch (error) {
        console.error('应用初始化失败:', error)
      }
    }
  },
  
  getters: {
    // 是否为小程序环境
    isMiniProgram: (state) => {
      return state.systemInfo.uniPlatform && state.systemInfo.uniPlatform.startsWith('mp-')
    },
    
    // 是否为App环境
    isApp: (state) => {
      return state.systemInfo.uniPlatform === 'app'
    },
    
    // 是否为H5环境
    isH5: (state) => {
      return state.systemInfo.uniPlatform === 'h5'
    },
    
    // 状态栏高度
    statusBarHeight: (state) => {
      return state.systemInfo.statusBarHeight || 0
    },
    
    // 导航栏高度
    navBarHeight: (state, getters) => {
      if (getters.isMiniProgram) {
        // 小程序需要考虑胶囊按钮
        return 44
      }
      return 44
    }
  }
})

export default store
