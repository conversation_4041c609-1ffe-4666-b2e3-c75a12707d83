"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const utils_captcha = require("../../utils/captcha.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      captchaLoaded: false,
      captchaImage: "",
      captchaKey: "",
      imageFormat: "",
      testLogs: []
    };
  },
  onLoad() {
    this.addLog("页面加载完成", "info");
    this.testGetCaptcha();
  },
  methods: {
    // 添加日志
    addLog(message, type = "info") {
      const now = /* @__PURE__ */ new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;
      this.testLogs.unshift({
        time,
        message,
        type
      });
      if (this.testLogs.length > 30) {
        this.testLogs = this.testLogs.slice(0, 30);
      }
    },
    // 更新验证码信息
    updateCaptchaInfo() {
      this.captchaImage = utils_captcha.getCaptchaImageUrl();
      this.captchaKey = utils_captcha.getCaptchaKey();
      this.captchaLoaded = !!this.captchaImage;
      if (this.captchaImage) {
        if (this.captchaImage.startsWith("data:image/jpg;base64,")) {
          this.imageFormat = "JPG Base64";
        } else if (this.captchaImage.startsWith("data:image/png;base64,")) {
          this.imageFormat = "PNG Base64";
        } else if (this.captchaImage.startsWith("data:image/")) {
          this.imageFormat = "Base64 (其他格式)";
        } else {
          this.imageFormat = "URL或其他格式";
        }
      } else {
        this.imageFormat = "无";
      }
    },
    // 测试获取验证码
    testGetCaptcha() {
      return __async(this, null, function* () {
        this.addLog("开始获取验证码", "info");
        try {
          const result = yield utils_captcha.getCaptcha(true);
          if (result.success) {
            this.updateCaptchaInfo();
            this.addLog("验证码获取成功", "success");
            this.addLog(`返回结构: ${JSON.stringify(result.data)}`, "info");
          } else {
            this.addLog(`验证码获取失败: ${result.message}`, "error");
          }
        } catch (error) {
          this.addLog(`验证码获取异常: ${error.message}`, "error");
        }
      });
    },
    // 测试刷新验证码
    testRefreshCaptcha() {
      return __async(this, null, function* () {
        this.addLog("开始刷新验证码", "info");
        try {
          const result = yield utils_captcha.refreshCaptcha();
          if (result.success) {
            this.updateCaptchaInfo();
            this.addLog("验证码刷新成功", "success");
          } else {
            this.addLog(`验证码刷新失败: ${result.message}`, "error");
          }
        } catch (error) {
          this.addLog(`验证码刷新异常: ${error.message}`, "error");
        }
      });
    },
    // 测试图片格式
    testImageFormat() {
      this.addLog("检查图片格式", "info");
      if (!this.captchaImage) {
        this.addLog("没有验证码图片", "warning");
        return;
      }
      this.addLog(`图片URL长度: ${this.captchaImage.length}`, "info");
      this.addLog(`图片前缀: ${this.captchaImage.substring(0, 50)}...`, "info");
      if (this.captchaImage.startsWith("data:image/")) {
        this.addLog("✅ 图片格式正确 - Base64格式", "success");
      } else {
        this.addLog("❌ 图片格式异常", "error");
      }
    },
    // 清除验证码
    testClearCaptcha() {
      this.addLog("清除验证码", "info");
      utils_captcha.clearCaptcha();
      this.updateCaptchaInfo();
      this.addLog("验证码已清除", "success");
    },
    // 图片加载成功
    onImageLoad() {
      this.addLog("✅ 验证码图片加载成功", "success");
    },
    // 图片加载失败
    onImageError() {
      this.addLog("❌ 验证码图片加载失败", "error");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t(`{
  "success": true,
  "message": "",
  "code": 0,
  "result": "data:image/jpg;base64,/9j/4AAQ...",
  "timestamp": 1749478235718
}`),
    b: common_vendor.t($data.captchaLoaded ? "已加载" : "未加载"),
    c: common_vendor.n($data.captchaLoaded ? "success" : "error"),
    d: common_vendor.t($data.imageFormat),
    e: common_vendor.t($data.captchaKey),
    f: $data.captchaImage
  }, $data.captchaImage ? {
    g: $data.captchaImage,
    h: common_vendor.o((...args) => $options.onImageLoad && $options.onImageLoad(...args)),
    i: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args))
  } : {}, {
    j: common_vendor.o((...args) => $options.testGetCaptcha && $options.testGetCaptcha(...args)),
    k: common_vendor.o((...args) => $options.testRefreshCaptcha && $options.testRefreshCaptcha(...args)),
    l: common_vendor.o((...args) => $options.testImageFormat && $options.testImageFormat(...args)),
    m: common_vendor.o((...args) => _ctx.clearCaptcha && _ctx.clearCaptcha(...args)),
    n: common_vendor.f($data.testLogs, (log, index, i0) => {
      return {
        a: common_vendor.t(log.time),
        b: common_vendor.t(log.message),
        c: common_vendor.n(log.type),
        d: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b6f4fe2b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/captcha-structure-test.js.map
