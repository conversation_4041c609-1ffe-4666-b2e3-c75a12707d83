/**
 * jeecg-boot用户接口适配器
 * 将jeecg-boot标准接口适配为小程序使用的格式
 */

import { userApi } from './index.js'
import { login as authLogin, logout as authLogout, refreshUserInfo } from '@/utils/auth.js'

/**
 * 用户登录适配器
 * @param {Object} loginData 登录数据
 * @param {String} loginData.username 用户名
 * @param {String} loginData.password 密码
 * @param {String} loginData.captcha 验证码
 * @param {String} loginData.checkKey 验证码key
 */
export async function login(loginData) {
  try {
    console.log('🔐 jeecg-boot用户登录:', loginData)
    
    // 构建jeecg-boot标准登录参数
    const params = {
      username: loginData.username,
      password: loginData.password
    }

    // 添加验证码参数（jeecg-boot标准）
    if (loginData.captcha && loginData.checkKey) {
      params.captcha = loginData.captcha
      params.checkKey = loginData.checkKey
      console.log('✅ 登录包含验证码参数:', {
        captcha: params.captcha,
        checkKey: params.checkKey
      })
    } else {
      console.warn('⚠️ 缺少验证码参数，登录可能失败')
    }
    
    // 调用认证管理器登录
    const result = await authLogin(params)
    
    if (result.success) {
      console.log('✅ 登录成功:', result.data)
      
      // 适配返回格式
      return {
        success: true,
        data: {
          userInfo: result.data,
          token: result.token
        },
        message: '登录成功'
      }
    } else {
      console.error('❌ 登录失败:', result.message)
      return {
        success: false,
        message: result.message || '登录失败'
      }
    }
  } catch (error) {
    console.error('❌ 登录异常:', error)
    return {
      success: false,
      message: error.message || '登录异常，请重试'
    }
  }
}

/**
 * 用户登出适配器
 */
export async function logout() {
  try {
    console.log('🚪 jeecg-boot用户登出')
    
    const result = await authLogout()
    
    if (result.success) {
      console.log('✅ 登出成功')
      return {
        success: true,
        message: '登出成功'
      }
    } else {
      return {
        success: false,
        message: result.message || '登出失败'
      }
    }
  } catch (error) {
    console.error('❌ 登出异常:', error)
    return {
      success: false,
      message: error.message || '登出异常'
    }
  }
}

/**
 * 获取用户信息适配器
 */
export async function getUserInfo() {
  try {
    console.log('👤 获取jeecg-boot用户信息')
    
    const response = await userApi.getUserInfo()
    
    if (response.success) {
      const userInfo = response.data
      
      // 适配用户信息格式
      const adaptedUserInfo = {
        id: userInfo.id,
        username: userInfo.username,
        realname: userInfo.realname || userInfo.username,
        nickname: userInfo.realname || userInfo.username,
        avatar: userInfo.avatar || '/static/images/default-avatar.png',
        phone: userInfo.phone,
        email: userInfo.email,
        sex: userInfo.sex,
        birthday: userInfo.birthday,
        orgCode: userInfo.orgCode,
        status: userInfo.status,
        delFlag: userInfo.delFlag,
        workNo: userInfo.workNo,
        post: userInfo.post,
        telephone: userInfo.telephone,
        createBy: userInfo.createBy,
        createTime: userInfo.createTime,
        updateBy: userInfo.updateBy,
        updateTime: userInfo.updateTime,
        
        // 扩展字段（如果有）
        level: userInfo.level || 'beginner',
        totalStudyDays: userInfo.totalStudyDays || 0,
        continuousDays: userInfo.continuousDays || 0,
        totalScore: userInfo.totalScore || 0,
        vipStatus: userInfo.vipStatus || 0
      }
      
      console.log('✅ 用户信息获取成功:', adaptedUserInfo)
      
      return {
        success: true,
        data: adaptedUserInfo,
        message: '获取成功'
      }
    } else {
      console.error('❌ 用户信息获取失败:', response.message)
      return {
        success: false,
        message: response.message || '获取用户信息失败'
      }
    }
  } catch (error) {
    console.error('❌ 用户信息获取异常:', error)
    return {
      success: false,
      message: error.message || '获取用户信息异常'
    }
  }
}

/**
 * 获取用户扩展信息
 */
export async function getUserProfile() {
  try {
    console.log('📋 获取用户扩展信息')
    
    const response = await userApi.getUserProfile()
    
    if (response.success) {
      console.log('✅ 用户扩展信息获取成功')
      return response
    } else {
      console.error('❌ 用户扩展信息获取失败:', response.message)
      return response
    }
  } catch (error) {
    console.error('❌ 用户扩展信息获取异常:', error)
    return {
      success: false,
      message: error.message || '获取用户扩展信息异常'
    }
  }
}

/**
 * 更新用户扩展信息
 */
export async function updateUserProfile(profileData) {
  try {
    console.log('📝 更新用户扩展信息:', profileData)
    
    const response = await userApi.updateUserProfile(profileData)
    
    if (response.success) {
      console.log('✅ 用户扩展信息更新成功')
      return response
    } else {
      console.error('❌ 用户扩展信息更新失败:', response.message)
      return response
    }
  } catch (error) {
    console.error('❌ 用户扩展信息更新异常:', error)
    return {
      success: false,
      message: error.message || '更新用户扩展信息异常'
    }
  }
}

/**
 * 修改密码适配器
 */
export async function updatePassword(passwordData) {
  try {
    console.log('🔒 修改密码')
    
    // 构建jeecg-boot标准密码修改参数
    const params = {
      oldpassword: passwordData.oldPassword,
      password: passwordData.newPassword,
      confirmpassword: passwordData.confirmPassword
    }
    
    const response = await userApi.updatePassword(params)
    
    if (response.success) {
      console.log('✅ 密码修改成功')
      return {
        success: true,
        message: '密码修改成功'
      }
    } else {
      console.error('❌ 密码修改失败:', response.message)
      return {
        success: false,
        message: response.message || '密码修改失败'
      }
    }
  } catch (error) {
    console.error('❌ 密码修改异常:', error)
    return {
      success: false,
      message: error.message || '密码修改异常'
    }
  }
}

/**
 * 上传头像适配器
 */
export async function uploadAvatar(filePath) {
  try {
    console.log('📷 上传头像:', filePath)
    
    const response = await userApi.uploadAvatar(filePath)
    
    if (response.success) {
      const avatarUrl = response.data?.url || response.data?.message || response.data
      
      console.log('✅ 头像上传成功:', avatarUrl)
      
      return {
        success: true,
        data: {
          url: avatarUrl,
          avatar: avatarUrl
        },
        message: '头像上传成功'
      }
    } else {
      console.error('❌ 头像上传失败:', response.message)
      return {
        success: false,
        message: response.message || '头像上传失败'
      }
    }
  } catch (error) {
    console.error('❌ 头像上传异常:', error)
    return {
      success: false,
      message: error.message || '头像上传异常'
    }
  }
}

/**
 * 刷新用户信息
 */
export async function refreshUser() {
  try {
    console.log('🔄 刷新用户信息')
    
    const result = await refreshUserInfo()
    
    if (result.success) {
      console.log('✅ 用户信息刷新成功')
      return {
        success: true,
        data: result.data,
        message: '刷新成功'
      }
    } else {
      console.error('❌ 用户信息刷新失败:', result.message)
      
      if (result.needLogin) {
        return {
          success: false,
          message: result.message,
          needLogin: true
        }
      }
      
      return {
        success: false,
        message: result.message || '刷新失败'
      }
    }
  } catch (error) {
    console.error('❌ 用户信息刷新异常:', error)
    return {
      success: false,
      message: error.message || '刷新异常'
    }
  }
}

// 导出适配器
export default {
  login,
  logout,
  getUserInfo,
  getUserProfile,
  updateUserProfile,
  updatePassword,
  uploadAvatar,
  refreshUser
}
