<template>
	<view class="api-test-container">
		<view class="test-header">
			<text class="test-title">API配置测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">配置信息</text>
			<view class="config-item">
				<text class="config-label">API基础URL:</text>
				<text class="config-value">{{ apiBaseUrl }}</text>
			</view>
			<view class="config-item">
				<text class="config-label">当前平台:</text>
				<text class="config-value">{{ currentPlatform }}</text>
			</view>
			<view class="config-item">
				<text class="config-label">请求头配置:</text>
				<view class="headers-display">
					<view class="header-item" v-for="(value, key) in testHeaders" :key="key">
						<text class="header-key">{{ key }}:</text>
						<text class="header-value">{{ value || '(空)' }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">接口测试</text>
			<view class="test-buttons">
				<button class="test-btn" @tap="testBaseConnection">测试基础连接</button>
				<button class="test-btn" @tap="testCaptchaApi">测试验证码接口</button>
				<button class="test-btn" @tap="testLoginApi">测试登录接口</button>
				<button class="test-btn" @tap="testUserInfoApi">测试用户信息</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试结果</text>
			<scroll-view class="result-container" scroll-y>
				<view class="result-item" v-for="(result, index) in testResults" :key="index">
					<view class="result-header">
						<text class="result-time">{{ result.time }}</text>
						<text class="result-status" :class="result.success ? 'success' : 'error'">
							{{ result.success ? '成功' : '失败' }}
						</text>
					</view>
					<text class="result-title">{{ result.title }}</text>
					<text class="result-message">{{ result.message }}</text>
					<view v-if="result.details" class="result-details">
						<text class="details-title">详细信息:</text>
						<text class="details-content">{{ JSON.stringify(result.details, null, 2) }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { getApiBaseUrl, getRequestHeaders } from '@/config/api.js'
import { request } from '@/utils/request.js'
import { getCaptcha } from '@/utils/captcha.js'
import * as jeecgUser from '@/api/jeecg-user.js'

export default {
	data() {
		return {
			apiBaseUrl: '',
			currentPlatform: '',
			testHeaders: {},
			testResults: []
		}
	},
	
	onLoad() {
		this.initTestInfo()
	},
	
	methods: {
		// 初始化测试信息
		initTestInfo() {
			this.apiBaseUrl = getApiBaseUrl()
			this.testHeaders = getRequestHeaders()
			
			// 获取当前平台
			// #ifdef H5
			this.currentPlatform = 'H5'
			// #endif
			
			// #ifdef MP-WEIXIN
			this.currentPlatform = '微信小程序'
			// #endif
			
			// #ifdef MP-DINGTALK
			this.currentPlatform = '钉钉小程序'
			// #endif
			
			this.addResult('初始化', '配置信息加载完成', true, {
				baseUrl: this.apiBaseUrl,
				platform: this.currentPlatform,
				headers: this.testHeaders
			})
		},
		
		// 添加测试结果
		addResult(title, message, success, details = null) {
			const now = new Date()
			const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
			
			this.testResults.unshift({
				time,
				title,
				message,
				success,
				details
			})
			
			// 限制结果数量
			if (this.testResults.length > 20) {
				this.testResults = this.testResults.slice(0, 20)
			}
		},
		
		// 测试基础连接
		async testBaseConnection() {
			this.addResult('基础连接测试', '开始测试...', true)
			
			try {
				const response = await request({
					url: '/sys/common/403',
					method: 'GET',
					showLoading: false
				})
				
				this.addResult('基础连接测试', '连接成功', true, response)
			} catch (error) {
				// 403错误是正常的，说明连接成功
				if (error.message.includes('403') || error.message.includes('权限')) {
					this.addResult('基础连接测试', '连接成功（403权限错误是正常的）', true, {
						error: error.message
					})
				} else {
					this.addResult('基础连接测试', `连接失败: ${error.message}`, false, {
						error: error.message
					})
				}
			}
		},
		
		// 测试验证码接口
		async testCaptchaApi() {
			this.addResult('验证码接口测试', '开始测试...', true)
			
			try {
				const result = await getCaptcha(true)
				
				if (result.success) {
					this.addResult('验证码接口测试', '验证码获取成功', true, {
						hasImage: !!result.data.image,
						hasKey: !!result.data.key
					})
				} else {
					this.addResult('验证码接口测试', `验证码获取失败: ${result.message}`, false, result)
				}
			} catch (error) {
				this.addResult('验证码接口测试', `验证码接口异常: ${error.message}`, false, {
					error: error.message
				})
			}
		},
		
		// 测试登录接口
		async testLoginApi() {
			this.addResult('登录接口测试', '开始测试...', true)
			
			try {
				// 先获取验证码
				const captchaResult = await getCaptcha(true)
				
				if (!captchaResult.success) {
					this.addResult('登录接口测试', '获取验证码失败，无法测试登录', false)
					return
				}
				
				// 测试登录（使用错误的密码，预期失败）
				const loginResult = await jeecgUser.login({
					username: 'test',
					password: 'wrongpassword',
					captcha: '1234',
					checkKey: captchaResult.data.key
				})
				
				// 登录应该失败，这是正常的
				this.addResult('登录接口测试', '登录接口调用成功（预期登录失败）', true, {
					loginResult: loginResult
				})
			} catch (error) {
				// 登录失败是预期的
				if (error.message.includes('用户名') || error.message.includes('密码') || error.message.includes('验证码')) {
					this.addResult('登录接口测试', '登录接口调用成功（预期登录失败）', true, {
						error: error.message
					})
				} else {
					this.addResult('登录接口测试', `登录接口异常: ${error.message}`, false, {
						error: error.message
					})
				}
			}
		},
		
		// 测试用户信息接口
		async testUserInfoApi() {
			this.addResult('用户信息接口测试', '开始测试...', true)
			
			try {
				const response = await request({
					url: '/sys/user/userInfo',
					method: 'GET',
					showLoading: false
				})
				
				this.addResult('用户信息接口测试', '用户信息获取成功', true, response)
			} catch (error) {
				// 401错误是正常的，说明接口存在但需要登录
				if (error.message.includes('401') || error.message.includes('登录')) {
					this.addResult('用户信息接口测试', '接口存在（需要登录）', true, {
						error: error.message
					})
				} else {
					this.addResult('用户信息接口测试', `接口异常: ${error.message}`, false, {
						error: error.message
					})
				}
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.api-test-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.test-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 24rpx;
		border-bottom: 2rpx solid #eee;
		padding-bottom: 16rpx;
	}
}

.config-item {
	margin-bottom: 24rpx;
	
	.config-label {
		display: block;
		font-size: 28rpx;
		color: #666;
		margin-bottom: 8rpx;
	}
	
	.config-value {
		font-size: 28rpx;
		color: #333;
		word-break: break-all;
	}
}

.headers-display {
	.header-item {
		display: flex;
		margin-bottom: 12rpx;
		
		.header-key {
			font-size: 24rpx;
			color: #666;
			width: 200rpx;
			flex-shrink: 0;
		}
		
		.header-value {
			font-size: 24rpx;
			color: #333;
			word-break: break-all;
		}
	}
}

.test-buttons {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
	
	.test-btn {
		height: 80rpx;
		background: #1890ff;
		color: white;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		
		&:active {
			opacity: 0.8;
		}
	}
}

.result-container {
	height: 600rpx;
	
	.result-item {
		border: 1rpx solid #eee;
		border-radius: 8rpx;
		padding: 16rpx;
		margin-bottom: 16rpx;
		
		.result-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 8rpx;
			
			.result-time {
				font-size: 24rpx;
				color: #999;
			}
			
			.result-status {
				font-size: 24rpx;
				padding: 4rpx 12rpx;
				border-radius: 4rpx;
				
				&.success {
					background: #f6ffed;
					color: #52c41a;
				}
				
				&.error {
					background: #fff2f0;
					color: #ff4d4f;
				}
			}
		}
		
		.result-title {
			display: block;
			font-size: 28rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 8rpx;
		}
		
		.result-message {
			display: block;
			font-size: 26rpx;
			color: #666;
			margin-bottom: 8rpx;
		}
		
		.result-details {
			.details-title {
				display: block;
				font-size: 24rpx;
				color: #999;
				margin-bottom: 4rpx;
			}
			
			.details-content {
				display: block;
				font-size: 22rpx;
				color: #666;
				background: #f5f5f5;
				padding: 8rpx;
				border-radius: 4rpx;
				font-family: monospace;
				white-space: pre-wrap;
				word-break: break-all;
			}
		}
	}
}
</style>
