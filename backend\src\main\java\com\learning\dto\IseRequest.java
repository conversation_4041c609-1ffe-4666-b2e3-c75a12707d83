package com.learning.dto;

import lombok.Data;

/**
 * 语音评测请求参数
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
public class IseRequest {
    
    /**
     * 音频文件Base64编码
     */
    private String audioData;
    
    /**
     * 待评测文本
     */
    private String text;
    
    /**
     * 语言类型：cn(中文) en(英文)
     */
    private String language = "cn";
    
    /**
     * 题型：
     * 中文：read_syllable(单字朗读)、read_word(词语朗读)、read_sentence(句子朗读)、read_chapter(篇章朗读)
     * 英文：read_word(词语朗读)、read_sentence(句子朗读)、read_chapter(篇章朗读)
     */
    private String category = "read_sentence";
    
    /**
     * 音频格式：raw(pcm)、wav、mp3、speex-wb
     */
    private String audioFormat = "wav";
    
    /**
     * 音频采样率
     */
    private String audioSampleRate = "audio/L16;rate=16000";
    
    /**
     * 群体类型：adult(成人)、youth(中学)、pupil(小学)
     */
    private String group = "adult";
    
    /**
     * 评测松严门限：easy(容易)、common(普通)、hard(困难)
     */
    private String checkType = "common";
    
    /**
     * 返回结果格式：utf8、gbk
     */
    private String resultEncoding = "utf8";
    
    /**
     * 拓展能力：multi_dimension(多维度分信息)、pitch(基频信息)、syll_phone_err_msg(音素错误信息)
     */
    private String extraAbility = "multi_dimension";
}
