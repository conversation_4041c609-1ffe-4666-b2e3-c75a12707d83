package com.kuma.learning.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件上传工具类
 * <AUTHOR>
 * @since 2024-03-22
 */
@Slf4j
@Component
public class FileUploadUtil {
    
    @Value("${kuma.file.upload-path:/data/uploads/kuma/}")
    private String uploadPath;
    
    @Value("${kuma.file.domain:http://localhost:8080}")
    private String domain;
    
    /**
     * 上传音频文件
     * @param file 音频文件
     * @param userId 用户ID
     * @return 文件访问URL
     */
    public String uploadAudioFile(MultipartFile file, String userId) {
        try {
            // 验证文件
            validateAudioFile(file);
            
            // 生成文件路径
            String relativePath = generateAudioFilePath(file, userId);
            String fullPath = uploadPath + relativePath;
            
            // 创建目录
            Path directory = Paths.get(fullPath).getParent();
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }
            
            // 保存文件
            file.transferTo(new File(fullPath));
            
            // 返回访问URL
            String fileUrl = domain + "/files/" + relativePath;
            log.info("音频文件上传成功: {}", fileUrl);
            
            return fileUrl;
            
        } catch (IOException e) {
            log.error("音频文件上传失败", e);
            throw new RuntimeException("音频文件上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存Base64音频数据
     * @param audioData Base64编码的音频数据
     * @param userId 用户ID
     * @param fileExtension 文件扩展名
     * @return 文件访问URL
     */
    public String saveBase64Audio(String audioData, String userId, String fileExtension) {
        try {
            // 解码Base64数据
            byte[] audioBytes = java.util.Base64.getDecoder().decode(audioData);
            
            // 生成文件路径
            String relativePath = generateAudioFilePathForBase64(userId, fileExtension);
            String fullPath = uploadPath + relativePath;
            
            // 创建目录
            Path directory = Paths.get(fullPath).getParent();
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }
            
            // 保存文件
            Files.write(Paths.get(fullPath), audioBytes);
            
            // 返回访问URL
            String fileUrl = domain + "/files/" + relativePath;
            log.info("Base64音频文件保存成功: {}", fileUrl);
            
            return fileUrl;
            
        } catch (Exception e) {
            log.error("Base64音频文件保存失败", e);
            throw new RuntimeException("Base64音频文件保存失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证音频文件
     */
    private void validateAudioFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("音频文件不能为空");
        }
        
        // 检查文件大小（限制10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("音频文件大小不能超过10MB");
        }
        
        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isValidAudioType(contentType)) {
            throw new IllegalArgumentException("不支持的音频格式");
        }
        
        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !hasValidAudioExtension(originalFilename)) {
            throw new IllegalArgumentException("不支持的音频文件扩展名");
        }
    }
    
    /**
     * 检查是否为有效的音频类型
     */
    private boolean isValidAudioType(String contentType) {
        return contentType.startsWith("audio/") ||
               contentType.contains("wav") ||
               contentType.contains("mp3") ||
               contentType.contains("m4a") ||
               contentType.contains("aac") ||
               contentType.contains("pcm");
    }
    
    /**
     * 检查是否为有效的音频文件扩展名
     */
    private boolean hasValidAudioExtension(String filename) {
        String lowerFilename = filename.toLowerCase();
        return lowerFilename.endsWith(".wav") ||
               lowerFilename.endsWith(".mp3") ||
               lowerFilename.endsWith(".m4a") ||
               lowerFilename.endsWith(".aac") ||
               lowerFilename.endsWith(".pcm");
    }
    
    /**
     * 生成音频文件路径
     */
    private String generateAudioFilePath(MultipartFile file, String userId) {
        // 获取文件扩展名
        String originalFilename = file.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        
        // 生成路径：audio/userId/yyyy-MM-dd/uuid.ext
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String filename = UUID.randomUUID().toString() + extension;
        
        return String.format("audio/%s/%s/%s", userId, dateStr, filename);
    }
    
    /**
     * 生成Base64音频文件路径
     */
    private String generateAudioFilePathForBase64(String userId, String extension) {
        // 确保扩展名以点开头
        if (!extension.startsWith(".")) {
            extension = "." + extension;
        }
        
        // 生成路径：audio/userId/yyyy-MM-dd/uuid.ext
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String filename = UUID.randomUUID().toString() + extension;
        
        return String.format("audio/%s/%s/%s", userId, dateStr, filename);
    }
    
    /**
     * 删除文件
     * @param fileUrl 文件URL
     * @return 是否删除成功
     */
    public boolean deleteFile(String fileUrl) {
        try {
            if (fileUrl == null || !fileUrl.startsWith(domain)) {
                return false;
            }
            
            // 提取相对路径
            String relativePath = fileUrl.substring(domain.length() + "/files/".length());
            String fullPath = uploadPath + relativePath;
            
            Path filePath = Paths.get(fullPath);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("文件删除成功: {}", fileUrl);
                return true;
            }
            
        } catch (Exception e) {
            log.error("文件删除失败: " + fileUrl, e);
        }
        
        return false;
    }
    
    /**
     * 获取文件大小（字节）
     * @param fileUrl 文件URL
     * @return 文件大小
     */
    public long getFileSize(String fileUrl) {
        try {
            if (fileUrl == null || !fileUrl.startsWith(domain)) {
                return 0;
            }
            
            String relativePath = fileUrl.substring(domain.length() + "/files/".length());
            String fullPath = uploadPath + relativePath;
            
            Path filePath = Paths.get(fullPath);
            if (Files.exists(filePath)) {
                return Files.size(filePath);
            }
            
        } catch (Exception e) {
            log.error("获取文件大小失败: " + fileUrl, e);
        }
        
        return 0;
    }
}
