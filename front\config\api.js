/**
 * 统一API配置管理
 * 简化配置，统一H5和小程序的接口调用
 */

// 统一API配置 - 不再区分环境，统一使用后台地址
export const API_CONFIG = {
  // 后台接口基础URL - 统一配置
  BASE_URL: 'http://localhost:8080/jeecg-boot',

  // 请求超时时间
  TIMEOUT: 10000,

  // 重试次数
  RETRY_COUNT: 3,

  // 重试延迟（毫秒）
  RETRY_DELAY: 1000
}

// 获取API基础URL - 统一返回配置的地址
export const getApiBaseUrl = () => {
  return API_CONFIG.BASE_URL
}

// 获取完整的API URL
export const getApiUrl = (endpoint = '') => {
  const baseUrl = getApiBaseUrl()

  // 移除endpoint开头的斜杠
  const cleanEndpoint = endpoint.replace(/^\/+/, '')

  // 如果endpoint为空，返回基础URL
  if (!cleanEndpoint) {
    return baseUrl
  }

  return `${baseUrl}/${cleanEndpoint}`
}

// 请求头配置 - 统一jeecg-boot标准
export const getRequestHeaders = (customHeaders = {}) => {
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...customHeaders
  }

  // 添加jeecg-boot认证token
  const token = uni.getStorageSync('X-Access-Token') || uni.getStorageSync('token')
  if (token) {
    headers['X-Access-Token'] = token
    headers['X-Access-Client'] = 'miniApp'
    headers['X-Tenant-Id'] = ''
  }

  return headers
}

// API端点配置 - jeecg-boot标准接口
export const API_ENDPOINTS = {
  // 用户相关 - 使用jeecg-boot标准接口
  USER: {
    LOGIN: '/sys/login',                    // jeecg-boot标准登录接口
    LOGOUT: '/sys/logout',                  // jeecg-boot标准登出接口
    INFO: '/sys/user/userInfo',             // jeecg-boot获取用户信息
    DEPART_INFO: '/sys/user/departInfo',    // jeecg-boot获取部门信息
    UPDATE_PASSWORD: '/sys/user/updatePassword', // jeecg-boot修改密码
    UPLOAD_AVATAR: '/sys/common/upload',    // jeecg-boot文件上传

    // 验证码相关接口
    CAPTCHA: '/sys/randomImage/{key}',      // jeecg-boot获取验证码（带path参数）
    CHECK_CAPTCHA: '/sys/checkCaptcha',     // jeecg-boot验证验证码

    // 自定义扩展接口（基于jeecg-boot）
    PROFILE: '/app/user/profile',           // 获取用户扩展信息
    UPDATE_PROFILE: '/app/user/profile',    // 更新用户扩展信息
    CHECKIN: '/app/user/checkin',           // 用户签到
    CHECKIN_HISTORY: '/app/user/checkin/history', // 签到历史
    STATISTICS: '/app/user/statistics'      // 学习统计
  },
  
  // 课程相关
  COURSE: {
    LIST: '/app/course/list',
    DETAIL: '/app/course/{id}',
    LESSONS: '/app/course/{id}/lessons',
    FAVORITE: '/app/course/{id}/favorite'
  },
  
  // 课时相关
  LESSON: {
    DETAIL: '/app/lesson/{id}',
    START: '/app/lesson/{id}/start',
    COMPLETE: '/app/lesson/{id}/complete'
  },
  
  // 练习相关
  PRACTICE: {
    SUBMIT: '/app/practice/submit',
    SUBMIT_BASE64: '/app/practice/submitBase64',
    HISTORY: '/app/practice/history',
    STATISTICS: '/app/practice/statistics',
    LANGUAGES: '/app/practice/supportedLanguages',
    CATEGORIES: '/app/practice/supportedCategories',
    CONFIG: '/app/practice/evaluationConfig'
  },
  
  // 排行榜相关
  LEADERBOARD: {
    POINTS: '/app/leaderboard/points',
    STUDY_TIME: '/app/leaderboard/studytime',
    CHECKIN: '/app/leaderboard/checkin'
  },
  
  // 支付相关
  PAYMENT: {
    CREATE: '/api/v1/payment/create',
    STATUS: '/api/v1/payment/status/{id}',
    METHODS: '/api/v1/payment/methods'
  },
  
  // AI相关
  AI: {
    EVALUATE: '/api/v1/ai/evaluate',
    HISTORY: '/api/v1/ai/history'
  },
  
  // 系统相关
  SYSTEM: {
    CONFIG: '/api/v1/system/config',
    VERSION: '/api/v1/system/version',
    FEEDBACK: '/api/v1/system/feedback',
    HELP: '/api/v1/system/help'
  }
}

// 替换URL中的参数
export const replaceUrlParams = (url, params = {}) => {
  let result = url
  
  Object.keys(params).forEach(key => {
    result = result.replace(`{${key}}`, params[key])
  })
  
  return result
}

// 构建查询字符串
export const buildQueryString = (params = {}) => {
  const query = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&')
  
  return query ? `?${query}` : ''
}

// 获取完整的API URL（包含参数）
export const buildApiUrl = (endpoint, urlParams = {}, queryParams = {}) => {
  // 替换URL参数
  const url = replaceUrlParams(endpoint, urlParams)
  
  // 构建查询字符串
  const queryString = buildQueryString(queryParams)
  
  // 返回完整URL
  return getApiUrl(url) + queryString
}

// 调试配置 - 简化配置，默认关闭调试
export const DEBUG_CONFIG = {
  // 是否启用调试
  enabled: false,

  // 是否显示控制台日志
  console: false,

  // 是否显示请求详情
  request: false,

  // 是否显示响应详情
  response: false
}

// 导出默认配置
export default {
  API_CONFIG,
  API_ENDPOINTS,
  DEBUG_CONFIG,
  getApiBaseUrl,
  getApiUrl,
  getRequestHeaders,
  replaceUrlParams,
  buildQueryString,
  buildApiUrl
}
