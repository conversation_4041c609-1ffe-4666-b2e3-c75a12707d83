package com.kuma.learning.dto;

import lombok.Data;

/**
 * 语音评测响应结果
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
public class IseResponse {
    
    /**
     * 响应码：0-成功，其他-失败
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 会话ID
     */
    private String sid;
    
    /**
     * 评测结果数据
     */
    private IseResultData data;
    
    @Data
    public static class IseResultData {
        /**
         * 状态：2-最终结果
         */
        private Integer status;
        
        /**
         * 评测结果XML数据(Base64编码)
         */
        private String data;
    }
}
