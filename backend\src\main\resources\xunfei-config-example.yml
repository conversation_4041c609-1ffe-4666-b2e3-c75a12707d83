# 科大讯飞语音评测配置示例
# 请将此文件重命名为 application-xunfei.yml 并填入真实的配置信息

xunfei:
  ise:
    # 从科大讯飞控制台获取的应用ID
    app-id: your_app_id_here
    
    # 从科大讯飞控制台获取的API Key
    api-key: your_api_key_here
    
    # 从科大讯飞控制台获取的API Secret
    api-secret: your_api_secret_here
    
    # WebSocket连接地址（通常不需要修改）
    host-url: wss://ise-api.xfyun.cn/v2/open-ise
    
    # 连接超时时间（毫秒）
    connect-timeout: 30000
    
    # 读取超时时间（毫秒）
    read-timeout: 30000

# 使用说明：
# 1. 注册科大讯飞开发者账号：https://www.xfyun.cn/
# 2. 创建语音评测应用，获取 APPID、API Key、API Secret
# 3. 将上述信息填入对应配置项
# 4. 重命名此文件为 application-xunfei.yml
# 5. 在主配置文件中激活此配置：spring.profiles.include=xunfei

# 注意事项：
# - 请妥善保管您的密钥信息，不要提交到版本控制系统
# - 建议使用环境变量或外部配置文件管理敏感信息
# - 科大讯飞语音评测服务可能产生费用，请注意用量控制
