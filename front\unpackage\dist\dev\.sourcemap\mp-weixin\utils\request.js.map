{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["/**\n * 统一请求管理器\n * 支持H5和小程序，统一调用jeecg-boot后台API\n */\n\nimport { getApiBaseUrl, getRequestHeaders } from '@/config/api.js'\nimport { getToken, removeToken, removeUserInfo } from '@/utils/auth.js'\n\n// 获取全局应用实例\nfunction getGlobalApp() {\n  try {\n    return getApp()\n  } catch (e) {\n    console.warn('获取全局应用实例失败:', e)\n    return null\n  }\n}\n\n// 获取基础URL - 统一使用配置\nfunction getBaseUrl() {\n  return getApiBaseUrl()\n}\n\n// 获取请求头 - 使用统一配置\nfunction getHeaders(customHeaders = {}) {\n  return getRequestHeaders(customHeaders)\n}\n\n// 处理响应数据 - jeecg-boot标准格式\nfunction handleResponse(res, resolve, reject) {\n  console.log('📥 响应数据:', res)\n\n  if (res.statusCode === 200) {\n    // jeecg-boot标准响应格式: { success: true/false, result: {}, message: '', code: 200 }\n    if (res.data) {\n      if (res.data.success === true || res.data.code === 200) {\n        // 成功响应\n        resolve({\n          success: true,\n          data: res.data.result || res.data.data || res.data,\n          message: res.data.message || 'success',\n          code: res.data.code || 200\n        })\n      } else {\n        // 业务逻辑错误\n        const errorMsg = res.data.message || '请求失败'\n        console.error('❌ 业务错误:', errorMsg)\n\n        uni.showToast({\n          title: errorMsg,\n          icon: 'none',\n          duration: 2000\n        })\n\n        reject(new Error(errorMsg))\n      }\n    } else {\n      // 无数据响应\n      resolve({\n        success: true,\n        data: null,\n        message: 'success',\n        code: 200\n      })\n    }\n  } else if (res.statusCode === 401) {\n    // 认证失败 - 自动跳转到登录页面\n    console.error('🔐 认证失败，清除登录状态')\n\n    // 使用认证管理器清除登录状态\n    removeToken()\n    removeUserInfo()\n\n    // 清除全局状态\n    const app = getGlobalApp()\n    if (app && app.clearLoginInfo) {\n      app.clearLoginInfo()\n    }\n\n    uni.showToast({\n      title: '登录已过期，请重新登录',\n      icon: 'none',\n      duration: 2000\n    })\n\n    // 安全获取当前页面路径，用于登录后跳转回来\n    let currentRoute = ''\n    try {\n      const pages = getCurrentPages()\n      if (pages && pages.length > 0) {\n        const currentPage = pages[pages.length - 1]\n        currentRoute = currentPage && currentPage.route ? currentPage.route : ''\n      }\n    } catch (error) {\n      console.warn('获取当前页面路径失败:', error)\n    }\n\n    // 延迟跳转到登录页面\n    setTimeout(() => {\n      const redirectUrl = currentRoute && currentRoute !== 'pages/login/login'\n        ? encodeURIComponent(`/${currentRoute}`)\n        : ''\n\n      uni.reLaunch({\n        url: `/pages/login/login${redirectUrl ? `?redirect=${redirectUrl}` : ''}`\n      })\n    }, 2000)\n\n    reject(new Error('登录已过期'))\n  } else if (res.statusCode === 403) {\n    // 权限不足\n    uni.showToast({\n      title: '权限不足',\n      icon: 'none'\n    })\n    reject(new Error('权限不足'))\n  } else if (res.statusCode >= 500) {\n    // 服务器错误\n    uni.showToast({\n      title: '服务器错误，请稍后重试',\n      icon: 'none'\n    })\n    reject(new Error(`服务器错误: ${res.statusCode}`))\n  } else {\n    // 其他错误\n    const errorMsg = res.data?.message || `请求失败: ${res.statusCode}`\n    uni.showToast({\n      title: errorMsg,\n      icon: 'none'\n    })\n    reject(new Error(errorMsg))\n  }\n}\n\n// 处理请求失败\nfunction handleRequestFail(err, reject) {\n  console.error('📡 请求失败:', err)\n  \n  let errorMessage = '网络请求失败'\n  \n  if (err.errMsg) {\n    if (err.errMsg.includes('timeout')) {\n      errorMessage = '请求超时，请检查网络连接'\n    } else if (err.errMsg.includes('fail')) {\n      errorMessage = '网络连接失败，请检查网络设置'\n    } else if (err.errMsg.includes('abort')) {\n      errorMessage = '请求被取消'\n    }\n  }\n  \n  // 记录错误到全局状态\n  const app = getGlobalApp()\n  if (app && app.addError) {\n    app.addError({\n      type: 'network_error',\n      message: errorMessage,\n      details: err,\n      timestamp: Date.now()\n    })\n  }\n  \n  uni.showToast({\n    title: errorMessage,\n    icon: 'none',\n    duration: 2000\n  })\n  \n  reject(new Error(errorMessage))\n}\n\n/**\n * 统一请求方法\n * @param {Object} options 请求配置\n * @param {String} options.url 请求地址\n * @param {String} options.method 请求方法\n * @param {Object} options.data 请求数据\n * @param {Object} options.header 请求头\n * @param {Number} options.timeout 超时时间\n * @param {Boolean} options.showLoading 是否显示加载提示\n * @param {String} options.loadingText 加载提示文字\n */\nexport function request(options = {}) {\n  return new Promise((resolve, reject) => {\n    // 构建完整的请求URL\n    const baseUrl = getBaseUrl()\n    const url = options.url.startsWith('http') ? options.url : baseUrl + options.url\n    \n    // 获取请求头\n    const headers = getHeaders(options.header)\n    \n    // 请求配置\n    const requestConfig = {\n      url,\n      method: options.method || 'GET',\n      data: options.data || {},\n      header: headers,\n      timeout: options.timeout || 10000\n    }\n    \n    console.log('📤 发起请求:', {\n      url: requestConfig.url,\n      method: requestConfig.method,\n      data: requestConfig.data,\n      header: requestConfig.header\n    })\n    \n    // 显示加载提示\n    if (options.showLoading !== false) {\n      uni.showLoading({\n        title: options.loadingText || '加载中...',\n        mask: true\n      })\n    }\n    \n    // 发起请求\n    uni.request({\n      ...requestConfig,\n      success: (res) => {\n        if (options.showLoading !== false) {\n          uni.hideLoading()\n        }\n        handleResponse(res, resolve, reject)\n      },\n      fail: (err) => {\n        if (options.showLoading !== false) {\n          uni.hideLoading()\n        }\n        handleRequestFail(err, reject)\n      }\n    })\n  })\n}\n\n/**\n * GET请求\n */\nexport function get(url, params = {}, options = {}) {\n  return request({\n    url,\n    method: 'GET',\n    data: params,\n    ...options\n  })\n}\n\n/**\n * POST请求\n */\nexport function post(url, data = {}, options = {}) {\n  return request({\n    url,\n    method: 'POST',\n    data,\n    ...options\n  })\n}\n\n/**\n * PUT请求\n */\nexport function put(url, data = {}, options = {}) {\n  return request({\n    url,\n    method: 'PUT',\n    data,\n    ...options\n  })\n}\n\n/**\n * DELETE请求\n */\nexport function del(url, data = {}, options = {}) {\n  return request({\n    url,\n    method: 'DELETE',\n    data,\n    ...options\n  })\n}\n\n/**\n * 文件上传\n */\nexport function upload(url, filePath, name = 'file', formData = {}) {\n  return new Promise((resolve, reject) => {\n    const baseUrl = getBaseUrl()\n    const uploadUrl = url.startsWith('http') ? url : baseUrl + url\n    const headers = getHeaders()\n    \n    console.log('📤 上传文件:', {\n      url: uploadUrl,\n      filePath,\n      name,\n      formData\n    })\n    \n    uni.showLoading({\n      title: '上传中...',\n      mask: true\n    })\n    \n    uni.uploadFile({\n      url: uploadUrl,\n      filePath,\n      name,\n      formData,\n      header: headers,\n      success: (res) => {\n        uni.hideLoading()\n        \n        try {\n          const data = JSON.parse(res.data)\n          if (data.success !== false) {\n            resolve(data)\n          } else {\n            reject(new Error(data.message || '上传失败'))\n          }\n        } catch (e) {\n          reject(new Error('响应数据格式错误'))\n        }\n      },\n      fail: (err) => {\n        uni.hideLoading()\n        handleRequestFail(err, reject)\n      }\n    })\n  })\n}\n\n// 默认导出\nexport default {\n  request,\n  get,\n  post,\n  put,\n  del,\n  upload\n}\n"], "names": ["uni", "getApiBaseUrl", "getRequestHeaders", "removeToken", "removeUserInfo"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AASA,SAAS,eAAe;AACtB,MAAI;AACF,WAAO,OAAQ;AAAA,EAChB,SAAQ,GAAG;AACVA,kBAAAA,MAAA,MAAA,QAAA,0BAAa,eAAe,CAAC;AAC7B,WAAO;AAAA,EACR;AACH;AAGA,SAAS,aAAa;AACpB,SAAOC,yBAAe;AACxB;AAGA,SAAS,WAAW,gBAAgB,IAAI;AACtC,SAAOC,WAAAA,kBAAkB,aAAa;AACxC;AAGA,SAAS,eAAe,KAAK,SAAS,QAAQ;;AAC5CF,gBAAAA,MAAY,MAAA,OAAA,0BAAA,YAAY,GAAG;AAE3B,MAAI,IAAI,eAAe,KAAK;AAE1B,QAAI,IAAI,MAAM;AACZ,UAAI,IAAI,KAAK,YAAY,QAAQ,IAAI,KAAK,SAAS,KAAK;AAEtD,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,MAAM,IAAI,KAAK,UAAU,IAAI,KAAK,QAAQ,IAAI;AAAA,UAC9C,SAAS,IAAI,KAAK,WAAW;AAAA,UAC7B,MAAM,IAAI,KAAK,QAAQ;AAAA,QACjC,CAAS;AAAA,MACT,OAAa;AAEL,cAAM,WAAW,IAAI,KAAK,WAAW;AACrCA,sBAAAA,MAAc,MAAA,SAAA,0BAAA,WAAW,QAAQ;AAEjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACpB,CAAS;AAED,eAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,MAC3B;AAAA,IACP,OAAW;AAEL,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA,EACL,WAAa,IAAI,eAAe,KAAK;AAEjCA,kBAAAA,MAAA,MAAA,SAAA,0BAAc,gBAAgB;AAG9BG,2BAAa;AACbC,8BAAgB;AAGhB,UAAM,MAAM,aAAc;AAC1B,QAAI,OAAO,IAAI,gBAAgB;AAC7B,UAAI,eAAgB;AAAA,IACrB;AAEDJ,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAChB,CAAK;AAGD,QAAI,eAAe;AACnB,QAAI;AACF,YAAM,QAAQ,gBAAiB;AAC/B,UAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,cAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,uBAAe,eAAe,YAAY,QAAQ,YAAY,QAAQ;AAAA,MACvE;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAa,MAAA,QAAA,0BAAA,eAAe,KAAK;AAAA,IAClC;AAGD,eAAW,MAAM;AACf,YAAM,cAAc,gBAAgB,iBAAiB,sBACjD,mBAAmB,IAAI,YAAY,EAAE,IACrC;AAEJA,oBAAAA,MAAI,SAAS;AAAA,QACX,KAAK,qBAAqB,cAAc,aAAa,WAAW,KAAK,EAAE;AAAA,MAC/E,CAAO;AAAA,IACF,GAAE,GAAI;AAEP,WAAO,IAAI,MAAM,OAAO,CAAC;AAAA,EAC7B,WAAa,IAAI,eAAe,KAAK;AAEjCA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,IACZ,CAAK;AACD,WAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EAC5B,WAAa,IAAI,cAAc,KAAK;AAEhCA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,IACZ,CAAK;AACD,WAAO,IAAI,MAAM,UAAU,IAAI,UAAU,EAAE,CAAC;AAAA,EAChD,OAAS;AAEL,UAAM,aAAW,SAAI,SAAJ,mBAAU,YAAW,SAAS,IAAI,UAAU;AAC7DA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,IACZ,CAAK;AACD,WAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,EAC3B;AACH;AAGA,SAAS,kBAAkB,KAAK,QAAQ;AACtCA,gBAAAA,MAAc,MAAA,SAAA,2BAAA,YAAY,GAAG;AAE7B,MAAI,eAAe;AAEnB,MAAI,IAAI,QAAQ;AACd,QAAI,IAAI,OAAO,SAAS,SAAS,GAAG;AAClC,qBAAe;AAAA,IAChB,WAAU,IAAI,OAAO,SAAS,MAAM,GAAG;AACtC,qBAAe;AAAA,IAChB,WAAU,IAAI,OAAO,SAAS,OAAO,GAAG;AACvC,qBAAe;AAAA,IAChB;AAAA,EACF;AAGD,QAAM,MAAM,aAAc;AAC1B,MAAI,OAAO,IAAI,UAAU;AACvB,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW,KAAK,IAAK;AAAA,IAC3B,CAAK;AAAA,EACF;AAEDA,gBAAAA,MAAI,UAAU;AAAA,IACZ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACd,CAAG;AAED,SAAO,IAAI,MAAM,YAAY,CAAC;AAChC;AAaO,SAAS,QAAQ,UAAU,IAAI;AACpC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,UAAM,UAAU,WAAY;AAC5B,UAAM,MAAM,QAAQ,IAAI,WAAW,MAAM,IAAI,QAAQ,MAAM,UAAU,QAAQ;AAG7E,UAAM,UAAU,WAAW,QAAQ,MAAM;AAGzC,UAAM,gBAAgB;AAAA,MACpB;AAAA,MACA,QAAQ,QAAQ,UAAU;AAAA,MAC1B,MAAM,QAAQ,QAAQ,CAAE;AAAA,MACxB,QAAQ;AAAA,MACR,SAAS,QAAQ,WAAW;AAAA,IAC7B;AAEDA,kBAAAA,MAAY,MAAA,OAAA,2BAAA,YAAY;AAAA,MACtB,KAAK,cAAc;AAAA,MACnB,QAAQ,cAAc;AAAA,MACtB,MAAM,cAAc;AAAA,MACpB,QAAQ,cAAc;AAAA,IAC5B,CAAK;AAGD,QAAI,QAAQ,gBAAgB,OAAO;AACjCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO,QAAQ,eAAe;AAAA,QAC9B,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAGDA,kBAAAA,MAAI,QAAQ,iCACP,gBADO;AAAA,MAEV,SAAS,CAAC,QAAQ;AAChB,YAAI,QAAQ,gBAAgB,OAAO;AACjCA,wBAAAA,MAAI,YAAa;AAAA,QAClB;AACD,uBAAe,KAAK,SAAS,MAAM;AAAA,MACpC;AAAA,MACD,MAAM,CAAC,QAAQ;AACb,YAAI,QAAQ,gBAAgB,OAAO;AACjCA,wBAAAA,MAAI,YAAa;AAAA,QAClB;AACD,0BAAkB,KAAK,MAAM;AAAA,MAC9B;AAAA,IACP,EAAK;AAAA,EACL,CAAG;AACH;AAKO,SAAS,IAAI,KAAK,SAAS,CAAA,GAAI,UAAU,CAAA,GAAI;AAClD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR,MAAM;AAAA,KACH,QACJ;AACH;AAKO,SAAS,KAAK,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,GAAI;AACjD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,KACG,QACJ;AACH;AAKO,SAAS,IAAI,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,GAAI;AAChD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,KACG,QACJ;AACH;AAKO,SAAS,IAAI,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,GAAI;AAChD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,KACG,QACJ;AACH;AAKO,SAAS,OAAO,KAAK,UAAU,OAAO,QAAQ,WAAW,IAAI;AAClE,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,UAAU,WAAY;AAC5B,UAAM,YAAY,IAAI,WAAW,MAAM,IAAI,MAAM,UAAU;AAC3D,UAAM,UAAU,WAAY;AAE5BA,kBAAAA,MAAY,MAAA,OAAA,2BAAA,YAAY;AAAA,MACtB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACN,CAAK;AAEDA,kBAAAA,MAAI,YAAY;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,IACZ,CAAK;AAEDA,kBAAAA,MAAI,WAAW;AAAA,MACb,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,SAAS,CAAC,QAAQ;AAChBA,sBAAAA,MAAI,YAAa;AAEjB,YAAI;AACF,gBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,cAAI,KAAK,YAAY,OAAO;AAC1B,oBAAQ,IAAI;AAAA,UACxB,OAAiB;AACL,mBAAO,IAAI,MAAM,KAAK,WAAW,MAAM,CAAC;AAAA,UACzC;AAAA,QACF,SAAQ,GAAG;AACV,iBAAO,IAAI,MAAM,UAAU,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAAA,MAAI,YAAa;AACjB,0BAAkB,KAAK,MAAM;AAAA,MAC9B;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;;;;;;;"}