"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const mixins_authMixin = require("../../mixins/auth-mixin.js");
const api_jeecgUser = require("../../api/jeecg-user.js");
const api_index = require("../../api/index.js");
const utils_auth = require("../../utils/auth.js");
const utils_captcha = require("../../utils/captcha.js");
const _sfc_main = {
  mixins: [mixins_authMixin.authMixin],
  data() {
    return {
      authRequired: false,
      // 此页面不需要登录
      testHeaders: {},
      testLogs: [],
      testCaptchaImage: "",
      captchaPathKey: "1629428467008"
    };
  },
  onLoad() {
    this.addLog("页面加载", "info");
    this.updateTestHeaders();
  },
  methods: {
    // 认证检查完成回调
    onAuthReady() {
      this.addLog("认证检查完成", "success");
      this.updateTestHeaders();
      this.updateCaptchaInfo();
    },
    // 更新测试请求头
    updateTestHeaders() {
      const token = utils_auth.getToken();
      this.testHeaders = {
        "X-Access-Token": token || "",
        "X-Access-Client": "miniApp",
        "X-Tenant-Id": ""
      };
    },
    // 更新验证码信息
    updateCaptchaInfo() {
      this.captchaPathKey = utils_captcha.getPathKey();
    },
    // 添加日志
    addLog(message, type = "info") {
      const now = /* @__PURE__ */ new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;
      this.testLogs.unshift({
        time,
        message,
        type
      });
      if (this.testLogs.length > 50) {
        this.testLogs = this.testLogs.slice(0, 50);
      }
    },
    // 测试获取验证码
    testGetCaptcha() {
      return __async(this, null, function* () {
        this.addLog("开始测试获取验证码", "info");
        try {
          const result = yield utils_captcha.getCaptcha(true);
          if (result.success) {
            this.testCaptchaImage = utils_captcha.getCaptchaImageUrl();
            this.addLog("验证码获取成功", "success");
            this.addLog(`验证码Key: ${utils_captcha.getCaptchaKey()}`, "info");
            this.addLog(`图片格式: ${this.testCaptchaImage ? this.testCaptchaImage.substring(0, 30) + "..." : "无"}`, "info");
          } else {
            this.addLog(`验证码获取失败: ${result.message}`, "error");
          }
        } catch (error) {
          this.addLog(`验证码获取异常: ${error.message}`, "error");
        }
      });
    },
    // 测试刷新验证码
    testRefreshCaptcha() {
      return __async(this, null, function* () {
        this.addLog("开始测试刷新验证码", "info");
        try {
          const result = yield utils_captcha.refreshCaptcha();
          if (result.success) {
            this.testCaptchaImage = utils_captcha.getCaptchaImageUrl();
            this.updateCaptchaInfo();
            this.addLog("验证码刷新成功", "success");
          } else {
            this.addLog(`验证码刷新失败: ${result.message}`, "error");
          }
        } catch (error) {
          this.addLog(`验证码刷新异常: ${error.message}`, "error");
        }
      });
    },
    // 测试生成新的Path参数
    testGeneratePathKey() {
      this.addLog("生成新的Path参数", "info");
      const newKey = utils_captcha.generatePathKey();
      utils_captcha.setPathKey(newKey);
      this.updateCaptchaInfo();
      this.addLog(`新Path参数: ${newKey}`, "success");
    },
    // 测试重置Path参数
    testResetPathKey() {
      this.addLog("重置Path参数为默认值", "info");
      utils_captcha.setPathKey("1629428467008");
      this.updateCaptchaInfo();
      this.addLog("Path参数已重置为: 1629428467008", "success");
    },
    // 测试登录
    testLogin() {
      return __async(this, null, function* () {
        this.addLog("开始测试登录", "info");
        try {
          yield this.testGetCaptcha();
          const result = yield api_jeecgUser.login({
            username: "admin",
            password: "123456",
            captcha: "1234",
            // 测试验证码
            checkKey: utils_captcha.getCaptchaKey()
          });
          if (result.success) {
            this.addLog("登录测试成功", "success");
            this.refreshAuthStatus();
            this.updateTestHeaders();
          } else {
            this.addLog(`登录测试失败: ${result.message}`, "error");
          }
        } catch (error) {
          this.addLog(`登录测试异常: ${error.message}`, "error");
        }
      });
    },
    // 测试登出
    testLogout() {
      return __async(this, null, function* () {
        this.addLog("开始测试登出", "info");
        try {
          const result = yield api_jeecgUser.logout();
          if (result.success) {
            this.addLog("登出测试成功", "success");
            this.refreshAuthStatus();
            this.updateTestHeaders();
          } else {
            this.addLog(`登出测试失败: ${result.message}`, "error");
          }
        } catch (error) {
          this.addLog(`登出测试异常: ${error.message}`, "error");
        }
      });
    },
    // 测试需要登录的功能
    testAuthRequired() {
      this.addLog("测试需要登录的功能", "info");
      if (this.checkPermission("test")) {
        this.addLog("权限检查通过", "success");
      } else {
        this.addLog("权限检查失败，需要登录", "warning");
      }
    },
    // 测试API调用
    testApiCall() {
      return __async(this, null, function* () {
        this.addLog("开始测试API调用", "info");
        try {
          const response = yield api_index.userApi.getUserInfo();
          if (response.success) {
            this.addLog("API调用成功", "success");
            this.addLog(`用户信息: ${JSON.stringify(response.data)}`, "info");
          } else {
            this.addLog(`API调用失败: ${response.message}`, "error");
          }
        } catch (error) {
          this.addLog(`API调用异常: ${error.message}`, "error");
          if (error.message.includes("登录已过期")) {
            this.addLog("认证失败处理正常", "success");
          }
        }
      });
    },
    // 刷新状态
    refreshStatus() {
      this.addLog("刷新认证状态", "info");
      this.refreshAuthStatus();
      this.updateTestHeaders();
    },
    // 清除认证
    clearAuth() {
      this.addLog("清除认证信息", "warning");
      common_vendor.index.removeStorageSync("X-Access-Token");
      common_vendor.index.removeStorageSync("userInfo");
      this.refreshAuthStatus();
      this.updateTestHeaders();
    },
    // 跳转到登录页面
    goToLogin() {
      common_vendor.index.navigateTo({
        url: "/pages/login/login"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t(_ctx.isAuthenticated ? "已登录" : "未登录"),
    b: common_vendor.n(_ctx.isAuthenticated ? "success" : "error"),
    c: common_vendor.t(_ctx.currentUser ? _ctx.currentUser.username : "无"),
    d: common_vendor.t(_ctx.authToken ? _ctx.authToken.substring(0, 20) + "..." : "无"),
    e: common_vendor.t($data.captchaPathKey),
    f: common_vendor.t($data.testHeaders["X-Access-Token"] || "无"),
    g: common_vendor.t($data.testHeaders["X-Access-Client"] || "无"),
    h: common_vendor.t($data.testHeaders["X-Tenant-Id"] || "无"),
    i: $data.testCaptchaImage
  }, $data.testCaptchaImage ? {
    j: $data.testCaptchaImage
  } : {}, {
    k: common_vendor.o((...args) => $options.testGetCaptcha && $options.testGetCaptcha(...args)),
    l: common_vendor.o((...args) => $options.testRefreshCaptcha && $options.testRefreshCaptcha(...args)),
    m: common_vendor.o((...args) => $options.testGeneratePathKey && $options.testGeneratePathKey(...args)),
    n: common_vendor.o((...args) => $options.testResetPathKey && $options.testResetPathKey(...args)),
    o: common_vendor.o((...args) => $options.testLogin && $options.testLogin(...args)),
    p: common_vendor.o((...args) => $options.testLogout && $options.testLogout(...args)),
    q: common_vendor.o((...args) => $options.testAuthRequired && $options.testAuthRequired(...args)),
    r: common_vendor.o((...args) => $options.testApiCall && $options.testApiCall(...args)),
    s: common_vendor.o((...args) => $options.refreshStatus && $options.refreshStatus(...args)),
    t: common_vendor.o((...args) => $options.clearAuth && $options.clearAuth(...args)),
    v: common_vendor.f($data.testLogs, (log, index, i0) => {
      return {
        a: common_vendor.t(log.time),
        b: common_vendor.t(log.message),
        c: common_vendor.n(log.type),
        d: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6bd25c5c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/login-test.js.map
