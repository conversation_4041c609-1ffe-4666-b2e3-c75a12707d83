/**
 * API接口测试工具
 * 用于前后端联调测试
 */

// 获取基础URL
function getBaseUrl() {
  return import.meta.env.VITE_API_BASE_URL + (import.meta.env.VITE_API_PREFIX || '/jeecg-boot')
}

/**
 * 测试后端连接
 */
export async function testBackendConnection() {
  const baseUrl = getBaseUrl()
  
  try {
    console.log('🔗 测试后端连接:', baseUrl)
    
    const response = await uni.request({
      url: baseUrl + '/app/common/health',
      method: 'GET',
      timeout: 5000
    })
    
    console.log('✅ 后端连接测试结果:', response)
    
    return {
      success: response.statusCode === 200,
      status: response.statusCode,
      data: response.data,
      message: response.statusCode === 200 ? '连接成功' : `HTTP ${response.statusCode}`
    }
  } catch (error) {
    console.error('❌ 后端连接测试失败:', error)
    
    return {
      success: false,
      status: 0,
      data: null,
      message: error.errMsg || error.message || '连接失败'
    }
  }
}

/**
 * 测试用户接口
 */
export async function testUserApis() {
  const baseUrl = getBaseUrl()
  const token = uni.getStorageSync('token')
  
  const tests = [
    {
      name: '获取用户信息',
      url: '/app/user/info',
      method: 'GET',
      needAuth: true
    },
    {
      name: '用户签到',
      url: '/app/user/checkin',
      method: 'POST',
      needAuth: true
    },
    {
      name: '获取签到历史',
      url: '/app/user/checkin/history',
      method: 'GET',
      needAuth: true
    }
  ]
  
  const results = []
  
  for (const test of tests) {
    try {
      console.log(`🧪 测试接口: ${test.name}`)
      
      const headers = {
        'Content-Type': 'application/json'
      }
      
      if (test.needAuth && token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await uni.request({
        url: baseUrl + test.url,
        method: test.method,
        header: headers,
        timeout: 10000
      })
      
      results.push({
        ...test,
        success: response.statusCode === 200,
        status: response.statusCode,
        data: response.data,
        message: response.statusCode === 200 ? '请求成功' : `HTTP ${response.statusCode}`,
        time: new Date()
      })
      
    } catch (error) {
      results.push({
        ...test,
        success: false,
        status: 0,
        data: null,
        message: error.errMsg || error.message || '请求失败',
        time: new Date()
      })
    }
  }
  
  return results
}

/**
 * 测试课程接口
 */
export async function testCourseApis() {
  const baseUrl = getBaseUrl()
  const token = uni.getStorageSync('token')
  
  const tests = [
    {
      name: '获取课程列表',
      url: '/app/course/list',
      method: 'GET',
      needAuth: false
    },
    {
      name: '获取课程详情',
      url: '/app/course/1',
      method: 'GET',
      needAuth: false
    },
    {
      name: '获取课时列表',
      url: '/app/course/1/lessons',
      method: 'GET',
      needAuth: false
    }
  ]
  
  const results = []
  
  for (const test of tests) {
    try {
      console.log(`🧪 测试接口: ${test.name}`)
      
      const headers = {
        'Content-Type': 'application/json'
      }
      
      if (test.needAuth && token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await uni.request({
        url: baseUrl + test.url,
        method: test.method,
        header: headers,
        timeout: 10000
      })
      
      results.push({
        ...test,
        success: response.statusCode === 200,
        status: response.statusCode,
        data: response.data,
        message: response.statusCode === 200 ? '请求成功' : `HTTP ${response.statusCode}`,
        time: new Date()
      })
      
    } catch (error) {
      results.push({
        ...test,
        success: false,
        status: 0,
        data: null,
        message: error.errMsg || error.message || '请求失败',
        time: new Date()
      })
    }
  }
  
  return results
}

/**
 * 测试练习接口
 */
export async function testPracticeApis() {
  const baseUrl = getBaseUrl()
  const token = uni.getStorageSync('token')
  
  const tests = [
    {
      name: '获取支持的语言',
      url: '/app/practice/supportedLanguages',
      method: 'GET',
      needAuth: false
    },
    {
      name: '获取评测配置',
      url: '/app/practice/evaluationConfig',
      method: 'GET',
      needAuth: false
    },
    {
      name: '获取练习统计',
      url: '/app/practice/statistics',
      method: 'GET',
      needAuth: true
    }
  ]
  
  const results = []
  
  for (const test of tests) {
    try {
      console.log(`🧪 测试接口: ${test.name}`)
      
      const headers = {
        'Content-Type': 'application/json'
      }
      
      if (test.needAuth && token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await uni.request({
        url: baseUrl + test.url,
        method: test.method,
        header: headers,
        timeout: 10000
      })
      
      results.push({
        ...test,
        success: response.statusCode === 200,
        status: response.statusCode,
        data: response.data,
        message: response.statusCode === 200 ? '请求成功' : `HTTP ${response.statusCode}`,
        time: new Date()
      })
      
    } catch (error) {
      results.push({
        ...test,
        success: false,
        status: 0,
        data: null,
        message: error.errMsg || error.message || '请求失败',
        time: new Date()
      })
    }
  }
  
  return results
}

/**
 * 运行所有API测试
 */
export async function runAllApiTests() {
  console.log('🚀 开始运行所有API测试...')
  
  const results = {
    connection: null,
    user: [],
    course: [],
    practice: [],
    summary: {
      total: 0,
      success: 0,
      failed: 0,
      startTime: new Date(),
      endTime: null
    }
  }
  
  try {
    // 测试连接
    results.connection = await testBackendConnection()
    
    // 如果连接成功，继续测试其他接口
    if (results.connection.success) {
      results.user = await testUserApis()
      results.course = await testCourseApis()
      results.practice = await testPracticeApis()
    }
    
    // 统计结果
    const allTests = [...results.user, ...results.course, ...results.practice]
    results.summary.total = allTests.length
    results.summary.success = allTests.filter(test => test.success).length
    results.summary.failed = allTests.filter(test => !test.success).length
    results.summary.endTime = new Date()
    
    console.log('✅ API测试完成:', results.summary)
    
  } catch (error) {
    console.error('❌ API测试异常:', error)
    results.summary.endTime = new Date()
  }
  
  return results
}

/**
 * 格式化测试结果
 */
export function formatTestResults(results) {
  const { summary } = results
  const duration = summary.endTime - summary.startTime
  
  return {
    ...results,
    summary: {
      ...summary,
      duration: `${duration}ms`,
      successRate: summary.total > 0 ? ((summary.success / summary.total) * 100).toFixed(1) + '%' : '0%'
    }
  }
}
