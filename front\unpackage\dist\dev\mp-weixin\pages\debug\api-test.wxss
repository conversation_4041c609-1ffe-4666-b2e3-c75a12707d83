/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.api-test-container.data-v-afa62ce9 {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}
.header.data-v-afa62ce9 {
  text-align: center;
  margin-bottom: 30px;
}
.header .title.data-v-afa62ce9 {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}
.header .subtitle.data-v-afa62ce9 {
  display: block;
  font-size: 14px;
  color: #666;
}
.section.data-v-afa62ce9 {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.section .section-header.data-v-afa62ce9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section .section-header .section-title.data-v-afa62ce9 {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.section .section-header .status-indicator.data-v-afa62ce9 {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ccc;
}
.section .section-header .status-indicator.connected.data-v-afa62ce9 {
  background: #10b981;
}
.section .section-header .clear-btn.data-v-afa62ce9 {
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 6px;
  border: 1px solid #ddd;
  background: #f8f9fa;
  color: #666;
}
.connection-info.data-v-afa62ce9 {
  margin-bottom: 15px;
}
.connection-info .info-item.data-v-afa62ce9 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.connection-info .info-item .label.data-v-afa62ce9 {
  font-size: 14px;
  color: #666;
}
.connection-info .info-item .value.data-v-afa62ce9 {
  font-size: 14px;
  color: #333;
}
.connection-info .info-item .value.success.data-v-afa62ce9 {
  color: #10b981;
}
.connection-info .info-item .value.error.data-v-afa62ce9 {
  color: #ef4444;
}
.test-btn.data-v-afa62ce9 {
  width: 100%;
  padding: 12px;
  background: #667eea;
  color: white;
  border-radius: 8px;
  border: none;
  font-size: 14px;
}
.test-btn.data-v-afa62ce9:disabled {
  background: #ccc;
}
.api-group.data-v-afa62ce9 {
  margin-bottom: 20px;
}
.api-group .group-title.data-v-afa62ce9 {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}
.api-list .api-item.data-v-afa62ce9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 8px;
}
.api-list .api-item .api-info.data-v-afa62ce9 {
  flex: 1;
}
.api-list .api-item .api-info .api-name.data-v-afa62ce9 {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}
.api-list .api-item .api-info .api-url.data-v-afa62ce9 {
  display: block;
  font-size: 12px;
  color: #666;
  font-family: monospace;
}
.api-list .api-item .api-test-btn.data-v-afa62ce9 {
  padding: 6px 12px;
  background: #667eea;
  color: white;
  border-radius: 6px;
  border: none;
  font-size: 12px;
}
.api-list .api-item .api-test-btn.data-v-afa62ce9:disabled {
  background: #ccc;
}
.results-list .result-item.data-v-afa62ce9 {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 10px;
  overflow: hidden;
}
.results-list .result-item .result-header.data-v-afa62ce9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
}
.results-list .result-item .result-header .result-name.data-v-afa62ce9 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
.results-list .result-item .result-header .result-status.data-v-afa62ce9 {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  color: white;
}
.results-list .result-item .result-header .result-status.success.data-v-afa62ce9 {
  background: #10b981;
}
.results-list .result-item .result-header .result-status.error.data-v-afa62ce9 {
  background: #ef4444;
}
.results-list .result-item .result-header .result-time.data-v-afa62ce9 {
  font-size: 12px;
  color: #666;
}
.results-list .result-item .result-details.data-v-afa62ce9 {
  padding: 12px;
}
.results-list .result-item .result-details .result-url.data-v-afa62ce9 {
  display: block;
  font-size: 12px;
  color: #666;
  font-family: monospace;
  margin-bottom: 8px;
}
.results-list .result-item .result-details .result-message.data-v-afa62ce9 {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}
.results-list .result-item .result-details .result-data .data-title.data-v-afa62ce9 {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}
.results-list .result-item .result-details .result-data .data-content.data-v-afa62ce9 {
  display: block;
  font-size: 11px;
  color: #333;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}
.results-list .empty-state.data-v-afa62ce9 {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}