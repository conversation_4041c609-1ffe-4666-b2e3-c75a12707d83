#!/usr/bin/env node

/**
 * API定义检查脚本
 * 验证所有API接口是否正确定义
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 检查API定义...\n')

let allPassed = true
let warnings = []

/**
 * 检查API文件语法
 */
function checkApiSyntax() {
  console.log('📋 检查API文件语法...')
  
  try {
    const apiPath = path.join(process.cwd(), 'api/index.js')
    if (!fs.existsSync(apiPath)) {
      console.log('❌ api/index.js - 文件不存在')
      return false
    }
    
    const apiContent = fs.readFileSync(apiPath, 'utf8')
    
    // 检查基本语法结构
    const checks = [
      {
        name: 'userApi导出',
        pattern: /export const userApi\s*=/,
        required: true
      },
      {
        name: 'courseApi导出',
        pattern: /export const courseApi\s*=/,
        required: true
      },
      {
        name: 'checkinApi导出',
        pattern: /export const checkinApi\s*=/,
        required: true
      },
      {
        name: 'leaderboardApi导出',
        pattern: /export const leaderboardApi\s*=/,
        required: true
      },
      {
        name: 'practiceApi导出',
        pattern: /export const practiceApi\s*=/,
        required: true
      },
      {
        name: '默认导出',
        pattern: /export default\s*{/,
        required: true
      },
      {
        name: '请求管理器导入',
        pattern: /from\s+['"]@\/utils\/request\.js['"]/,
        required: true
      }
    ]
    
    let syntaxOk = true
    
    checks.forEach(check => {
      if (check.pattern.test(apiContent)) {
        console.log(`✅ ${check.name}`)
      } else if (check.required) {
        console.log(`❌ ${check.name} - 缺失`)
        syntaxOk = false
      } else {
        console.log(`⚠️  ${check.name} - 可选项缺失`)
        warnings.push(check.name)
      }
    })
    
    return syntaxOk
    
  } catch (error) {
    console.log('❌ API文件语法检查失败:', error.message)
    return false
  }
}

/**
 * 检查API方法定义
 */
function checkApiMethods() {
  console.log('\n📋 检查API方法定义...')
  
  try {
    const apiPath = path.join(process.cwd(), 'api/index.js')
    const apiContent = fs.readFileSync(apiPath, 'utf8')
    
    // 检查关键API方法
    const apiMethods = [
      // 用户API
      { api: 'userApi', method: 'login', pattern: /login:\s*\([^)]*\)\s*=>/},
      { api: 'userApi', method: 'getUserInfo', pattern: /getUserInfo:\s*\([^)]*\)\s*=>/},
      { api: 'userApi', method: 'checkin', pattern: /checkin:\s*\([^)]*\)\s*=>/},
      { api: 'userApi', method: 'getCheckinHistory', pattern: /getCheckinHistory:\s*\([^)]*\)\s*=>/},
      
      // 课程API
      { api: 'courseApi', method: 'getCourseList', pattern: /getCourseList:\s*\([^)]*\)\s*=>/},
      { api: 'courseApi', method: 'getCourseDetail', pattern: /getCourseDetail:\s*\([^)]*\)\s*=>/},
      
      // 签到API
      { api: 'checkinApi', method: 'checkin', pattern: /checkin:\s*\([^)]*\)\s*=>/},
      { api: 'checkinApi', method: 'getHistory', pattern: /getHistory:\s*\([^)]*\)\s*=>/},
      
      // 练习API
      { api: 'practiceApi', method: 'submitPractice', pattern: /submitPractice:\s*\([^)]*\)\s*=>/},
      { api: 'practiceApi', method: 'getStatistics', pattern: /getStatistics:\s*\([^)]*\)\s*=>/},
      
      // 排行榜API
      { api: 'leaderboardApi', method: 'getCheckinRanking', pattern: /getCheckinRanking:\s*\([^)]*\)\s*=>/}
    ]
    
    let methodsOk = true
    
    apiMethods.forEach(({ api, method, pattern }) => {
      if (pattern.test(apiContent)) {
        console.log(`✅ ${api}.${method}`)
      } else {
        console.log(`❌ ${api}.${method} - 方法定义缺失`)
        methodsOk = false
      }
    })
    
    return methodsOk
    
  } catch (error) {
    console.log('❌ API方法检查失败:', error.message)
    return false
  }
}

/**
 * 检查API引用
 */
function checkApiReferences() {
  console.log('\n📋 检查API引用...')
  
  const pageFiles = [
    'pages/index/index.vue',
    'pages/checkin/checkin.vue',
    'pages/course/list.vue',
    'pages/course/detail.vue',
    'pages/practice/practice.vue',
    'pages/profile/profile.vue',
    'pages/leaderboard/leaderboard.vue'
  ]
  
  let referencesOk = true
  
  pageFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file)
    
    if (fs.existsSync(filePath)) {
      try {
        const content = fs.readFileSync(filePath, 'utf8')
        
        // 检查是否正确导入API
        if (content.includes('from \'@/api/index.js\'') || content.includes('from "@/api/index.js"')) {
          console.log(`✅ ${file} - API导入正确`)
        } else {
          console.log(`⚠️  ${file} - 未找到API导入`)
          warnings.push(`${file} API导入`)
        }
        
        // 检查是否有未定义的API引用
        const undefinedRefs = []
        
        // 检查常见的API调用模式
        const apiCallPatterns = [
          /(\w+Api)\.(\w+)/g
        ]
        
        apiCallPatterns.forEach(pattern => {
          let match
          while ((match = pattern.exec(content)) !== null) {
            const apiName = match[1]
            const methodName = match[2]
            
            // 检查是否是已知的API
            const knownApis = ['userApi', 'courseApi', 'checkinApi', 'leaderboardApi', 'practiceApi', 'paymentApi', 'aiApi', 'systemApi']
            
            if (!knownApis.includes(apiName)) {
              undefinedRefs.push(`${apiName}.${methodName}`)
            }
          }
        })
        
        if (undefinedRefs.length > 0) {
          console.log(`❌ ${file} - 未定义的API引用: ${undefinedRefs.join(', ')}`)
          referencesOk = false
        }
        
      } catch (error) {
        console.log(`⚠️  ${file} - 文件读取失败`)
        warnings.push(`${file} 文件读取`)
      }
    } else {
      console.log(`⚠️  ${file} - 文件不存在`)
      warnings.push(`${file} 文件存在性`)
    }
  })
  
  return referencesOk
}

/**
 * 检查请求管理器
 */
function checkRequestManager() {
  console.log('\n📋 检查请求管理器...')
  
  try {
    const requestPath = path.join(process.cwd(), 'utils/request.js')
    
    if (!fs.existsSync(requestPath)) {
      console.log('❌ utils/request.js - 文件不存在')
      return false
    }
    
    const requestContent = fs.readFileSync(requestPath, 'utf8')
    
    // 检查请求管理器的关键功能
    const checks = [
      { name: 'get方法导出', pattern: /export\s+.*\bget\b/ },
      { name: 'post方法导出', pattern: /export\s+.*\bpost\b/ },
      { name: 'put方法导出', pattern: /export\s+.*\bput\b/ },
      { name: 'del方法导出', pattern: /export\s+.*\bdel\b/ },
      { name: 'upload方法导出', pattern: /export\s+.*\bupload\b/ },
      { name: '错误处理', pattern: /handleResponse|handleRequestFail/ },
      { name: '平台识别', pattern: /#ifdef\s+MP-WEIXIN|#ifdef\s+MP-DINGTALK/ }
    ]
    
    let requestOk = true
    
    checks.forEach(check => {
      if (check.pattern.test(requestContent)) {
        console.log(`✅ ${check.name}`)
      } else {
        console.log(`❌ ${check.name} - 缺失`)
        requestOk = false
      }
    })
    
    return requestOk
    
  } catch (error) {
    console.log('❌ 请求管理器检查失败:', error.message)
    return false
  }
}

/**
 * 主函数
 */
function main() {
  try {
    const syntaxOk = checkApiSyntax()
    const methodsOk = checkApiMethods()
    const referencesOk = checkApiReferences()
    const requestOk = checkRequestManager()
    
    allPassed = syntaxOk && methodsOk && referencesOk && requestOk
    
    console.log('\n' + '='.repeat(60))
    
    if (allPassed) {
      console.log('🎉 API定义检查通过！')
      console.log('✅ 所有API接口定义正确')
      console.log('✅ 请求管理器配置完整')
      console.log('✅ API引用检查通过')
      
      if (warnings.length > 0) {
        console.log('\n⚠️  可选项建议:')
        warnings.forEach(warning => {
          console.log(`   - ${warning}`)
        })
      }
      
      console.log('\n🚀 API系统就绪，可以开始开发！')
      
    } else {
      console.log('❌ API定义检查失败！')
      console.log('请根据上述错误信息修复API定义')
      console.log('\n💡 常见问题:')
      console.log('   - 检查API导出语法是否正确')
      console.log('   - 确认所有API方法都已定义')
      console.log('   - 验证请求管理器是否正确导入')
      console.log('   - 检查页面中的API引用是否正确')
      
      process.exit(1)
    }
    
    console.log('\n' + '='.repeat(60))
    
  } catch (error) {
    console.error('❌ API检查过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main()
