#!/usr/bin/env node

/**
 * 前后端联调启动脚本
 * 检查后端服务状态并启动前端开发服务器
 */

const { spawn } = require('child_process')
const http = require('http')

// 配置
const BACKEND_HOST = 'localhost'
const BACKEND_PORT = 8080
const BACKEND_HEALTH_PATH = '/jeecg-boot/app/common/health'
const FRONTEND_PORT = 3000

console.log('🚀 前后端联调启动脚本')
console.log('=' * 50)

/**
 * 检查后端服务状态
 */
function checkBackendStatus() {
  return new Promise((resolve) => {
    const options = {
      hostname: BACKEND_HOST,
      port: BACKEND_PORT,
      path: BACKEND_HEALTH_PATH,
      method: 'GET',
      timeout: 5000
    }

    const req = http.request(options, (res) => {
      console.log(`✅ 后端服务状态: HTTP ${res.statusCode}`)
      resolve(res.statusCode === 200)
    })

    req.on('error', (err) => {
      console.log(`❌ 后端服务连接失败: ${err.message}`)
      resolve(false)
    })

    req.on('timeout', () => {
      console.log('❌ 后端服务连接超时')
      req.destroy()
      resolve(false)
    })

    req.end()
  })
}

/**
 * 启动前端开发服务器
 */
function startFrontend() {
  console.log('🎯 启动前端开发服务器...')
  
  const frontend = spawn('npm', ['run', 'dev:h5'], {
    stdio: 'inherit',
    shell: true
  })

  frontend.on('error', (err) => {
    console.error('❌ 前端启动失败:', err.message)
    process.exit(1)
  })

  frontend.on('close', (code) => {
    console.log(`前端服务器退出，代码: ${code}`)
    process.exit(code)
  })

  // 处理进程退出
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...')
    frontend.kill('SIGINT')
  })

  process.on('SIGTERM', () => {
    console.log('\n🛑 正在关闭服务器...')
    frontend.kill('SIGTERM')
  })
}

/**
 * 显示联调信息
 */
function showDebugInfo(backendRunning) {
  console.log('\n📋 联调信息:')
  console.log(`   后端地址: http://${BACKEND_HOST}:${BACKEND_PORT}`)
  console.log(`   前端地址: http://localhost:${FRONTEND_PORT}`)
  console.log(`   后端状态: ${backendRunning ? '✅ 运行中' : '❌ 未运行'}`)
  
  if (backendRunning) {
    console.log('\n🔗 可用的调试工具:')
    console.log(`   - 调试面板: http://localhost:${FRONTEND_PORT}/#/pages/debug/debug`)
    console.log(`   - API测试: http://localhost:${FRONTEND_PORT}/#/pages/debug/api-test`)
    console.log(`   - 图标预览: http://localhost:${FRONTEND_PORT}/#/pages/demo/icons`)
  } else {
    console.log('\n⚠️  后端服务未运行，请先启动后端服务:')
    console.log('   1. 进入 backend 目录')
    console.log('   2. 运行 mvn spring-boot:run')
    console.log('   3. 等待服务启动完成')
    console.log('   4. 重新运行此脚本')
  }
  
  console.log('\n🛠️  开发建议:')
  console.log('   - 使用调试面板查看应用状态')
  console.log('   - 使用API测试工具验证接口')
  console.log('   - 查看浏览器控制台获取详细日志')
  console.log('   - 修改代码后会自动热重载')
  
  console.log('\n' + '=' * 50)
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🔍 检查后端服务状态...')
    const backendRunning = await checkBackendStatus()
    
    showDebugInfo(backendRunning)
    
    if (!backendRunning) {
      console.log('\n⚠️  建议先启动后端服务，但前端仍可独立运行')
      console.log('是否继续启动前端? (按 Ctrl+C 取消)')
      
      // 等待3秒后自动继续
      await new Promise(resolve => setTimeout(resolve, 3000))
    }
    
    startFrontend()
    
  } catch (error) {
    console.error('❌ 启动失败:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main()
