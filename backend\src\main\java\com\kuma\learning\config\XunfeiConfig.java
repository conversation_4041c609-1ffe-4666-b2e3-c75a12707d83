package com.kuma.learning.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 科大讯飞语音评测配置
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@Component
@ConfigurationProperties(prefix = "xunfei.ise")
public class XunfeiConfig {
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * API Key
     */
    private String apiKey;
    
    /**
     * API Secret
     */
    private String apiSecret;
    
    /**
     * 接口地址
     */
    private String hostUrl = "wss://ise-api.xfyun.cn/v2/open-ise";
    
    /**
     * 连接超时时间(毫秒)
     */
    private Integer connectTimeout = 30000;
    
    /**
     * 读取超时时间(毫秒)
     */
    private Integer readTimeout = 30000;
}
