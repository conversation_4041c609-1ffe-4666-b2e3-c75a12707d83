/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.page.data-v-63c01bdb {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}
.status-bar.data-v-63c01bdb {
  background: transparent;
}
.content.data-v-63c01bdb {
  padding: 48rpx 40rpx 200rpx;
}
.header.data-v-63c01bdb {
  text-align: center;
  margin-bottom: 48rpx;
}
.header .title.data-v-63c01bdb {
  display: block;
  color: white;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.header .subtitle.data-v-63c01bdb {
  color: rgba(255, 255, 255, 0.8);
  font-size: 32rpx;
}
.checkin-card.data-v-63c01bdb {
  text-align: center;
  margin-bottom: 32rpx;
}
.checkin-card .card-title.data-v-63c01bdb {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 32rpx;
}
.checkin-card .checkin-button.data-v-63c01bdb {
  width: 240rpx;
  height: 240rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(255, 154, 158, 0.4);
  transition: all 0.3s ease;
}
.checkin-card .checkin-button.completed.data-v-63c01bdb {
  background: linear-gradient(135deg, #a8e6cf, #88d8a3);
}
.checkin-card .checkin-button .iconfont.data-v-63c01bdb {
  color: white;
  font-size: 80rpx;
}
.checkin-card .checkin-status.data-v-63c01bdb {
  display: block;
  font-size: 36rpx;
  font-weight: 500;
  color: #ff9a9e;
  margin-bottom: 16rpx;
}
.checkin-card .checkin-status.completed.data-v-63c01bdb {
  color: #16a34a;
}
.checkin-card .checkin-desc.data-v-63c01bdb {
  display: block;
  color: #666;
  font-size: 28rpx;
  margin-bottom: 48rpx;
}
.checkin-card .streak-info .streak-badge.data-v-63c01bdb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.checkin-card .streak-info .streak-badge .iconfont.data-v-63c01bdb {
  margin-right: 16rpx;
  font-size: 32rpx;
}
.checkin-card .streak-info .milestone-text.data-v-63c01bdb {
  display: block;
  color: #666;
  font-size: 24rpx;
}
.calendar-card.data-v-63c01bdb {
  margin-bottom: 32rpx;
}
.calendar-card .calendar-header.data-v-63c01bdb {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}
.calendar-card .calendar-header .calendar-title.data-v-63c01bdb {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.calendar-card .calendar-header .calendar-stats.data-v-63c01bdb {
  color: #666;
  font-size: 28rpx;
}
.calendar-card .calendar-header .calendar-stats .completed-days.data-v-63c01bdb {
  color: #16a34a;
  font-weight: 500;
}
.calendar-card .calendar-grid.data-v-63c01bdb {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16rpx;
  margin-bottom: 32rpx;
}
.calendar-card .calendar-grid .calendar-day.data-v-63c01bdb {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.7);
  color: #666;
}
.calendar-card .calendar-grid .calendar-day.header.data-v-63c01bdb {
  background: transparent;
  color: #333;
  font-weight: bold;
}
.calendar-card .calendar-grid .calendar-day.empty.data-v-63c01bdb {
  background: transparent;
}
.calendar-card .calendar-grid .calendar-day.checked.data-v-63c01bdb {
  background: linear-gradient(135deg, #a8e6cf, #88d8a3);
  color: white;
}
.calendar-card .calendar-grid .calendar-day.today.data-v-63c01bdb {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  color: white;
  border: 4rpx solid white;
}
.calendar-card .calendar-grid .calendar-day.makeup.data-v-63c01bdb {
  background: linear-gradient(135deg, #ffd93d, #ff6b6b);
  color: white;
}
.calendar-card .calendar-legend.data-v-63c01bdb {
  display: flex;
  justify-content: center;
  gap: 32rpx;
}
.calendar-card .calendar-legend .legend-item.data-v-63c01bdb {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}
.calendar-card .calendar-legend .legend-item .legend-dot.data-v-63c01bdb {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}
.calendar-card .calendar-legend .legend-item .legend-dot.checked.data-v-63c01bdb {
  background: linear-gradient(135deg, #a8e6cf, #88d8a3);
}
.calendar-card .calendar-legend .legend-item .legend-dot.makeup.data-v-63c01bdb {
  background: linear-gradient(135deg, #ffd93d, #ff6b6b);
}
.calendar-card .calendar-legend .legend-item .legend-dot.today.data-v-63c01bdb {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
}
.stats-card.data-v-63c01bdb {
  margin-bottom: 32rpx;
}
.stats-card .stats-title.data-v-63c01bdb {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 32rpx;
}
.stats-card .stat-item.data-v-63c01bdb {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f1f5f9;
}
.stats-card .stat-item.data-v-63c01bdb:last-child {
  border-bottom: none;
}
.stats-card .stat-item .stat-left.data-v-63c01bdb {
  display: flex;
  align-items: center;
}
.stats-card .stat-item .stat-left .iconfont.data-v-63c01bdb {
  font-size: 32rpx;
  margin-right: 24rpx;
}
.stats-card .stat-item .stat-left .stat-label.data-v-63c01bdb {
  color: #333;
  font-size: 28rpx;
}
.stats-card .stat-item .stat-value.data-v-63c01bdb {
  font-weight: bold;
  color: #333;
  font-size: 28rpx;
}
.makeup-section.data-v-63c01bdb {
  text-align: center;
}
.makeup-section .makeup-btn.data-v-63c01bdb {
  width: 100%;
  background: linear-gradient(135deg, #f59e0b, #ea580c);
  color: white;
  padding: 32rpx;
  border-radius: 40rpx;
  font-weight: bold;
  font-size: 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(245, 158, 11, 0.3);
  border: none;
  margin-bottom: 16rpx;
}
.makeup-section .makeup-btn .iconfont.data-v-63c01bdb {
  margin-right: 16rpx;
  font-size: 36rpx;
}
.makeup-section .makeup-hint.data-v-63c01bdb {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 字体图标 */
.icon-calendar.data-v-63c01bdb::before {
  content: "\e024";
}
.icon-calendar-check.data-v-63c01bdb::before {
  content: "\e025";
}
.icon-percentage.data-v-63c01bdb::before {
  content: "\e026";
}