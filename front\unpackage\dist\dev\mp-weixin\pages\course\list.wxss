/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.page.data-v-80d79958 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.status-bar.data-v-80d79958 {
  background: transparent;
}
.nav-bar.data-v-80d79958 {
  background: transparent;
}
.nav-bar .nav-content.data-v-80d79958 {
  height: 88rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}
.nav-bar .nav-content .nav-left.data-v-80d79958, .nav-bar .nav-content .nav-right.data-v-80d79958 {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nav-bar .nav-content .nav-left .iconfont.data-v-80d79958 {
  color: white;
  font-size: 40rpx;
}
.nav-bar .nav-content .nav-title.data-v-80d79958 {
  flex: 1;
  text-align: center;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}
.content.data-v-80d79958 {
  background: #f8fafc;
  border-radius: 48rpx 48rpx 0 0;
  min-height: calc(100vh - var(--status-bar-height) - 88rpx);
  padding-bottom: 200rpx;
}
.progress-section.data-v-80d79958 {
  padding: 48rpx 32rpx;
}
.progress-section .progress-card .progress-title.data-v-80d79958 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.progress-section .progress-card .progress-desc.data-v-80d79958 {
  color: #666;
  font-size: 28rpx;
}
.progress-section .progress-card .progress-percent.data-v-80d79958 {
  text-align: right;
}
.progress-section .progress-card .progress-percent .percent-text.data-v-80d79958 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #f5576c;
  line-height: 1;
}
.progress-section .progress-card .progress-percent .percent-label.data-v-80d79958 {
  color: #666;
  font-size: 24rpx;
}
.progress-section .progress-card .progress-bar.data-v-80d79958 {
  width: 100%;
  height: 12rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
}
.progress-section .progress-card .progress-bar .progress-fill.data-v-80d79958 {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #34d399);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}
.course-list.data-v-80d79958 {
  padding: 0 16rpx;
}
.course-item.data-v-80d79958 {
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}
.course-card.data-v-80d79958 {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
}
.course-card.premium.data-v-80d79958 {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border-color: #f59e0b;
}
.course-card.current.data-v-80d79958 {
  border-color: #f5576c;
  border-width: 4rpx;
}
.course-card .course-icon.data-v-80d79958 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.course-card .course-icon .iconfont.data-v-80d79958 {
  font-size: 36rpx;
}
.course-card .course-info.data-v-80d79958 {
  flex: 1;
}
.course-card .course-info .course-title.data-v-80d79958 {
  display: block;
  font-weight: bold;
  color: #333;
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.course-card .course-info .course-desc.data-v-80d79958 {
  color: #666;
  font-size: 28rpx;
}
.course-card .course-status.data-v-80d79958 {
  display: flex;
  align-items: center;
}
.course-card .course-status .free-badge.data-v-80d79958 {
  background: #dcfce7;
  color: #16a34a;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 16rpx;
}
.course-card .course-status .price-badge.data-v-80d79958 {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  font-weight: bold;
  margin-right: 16rpx;
}
.course-card .course-status .status-icon.data-v-80d79958 {
  font-size: 40rpx;
}
.course-card .course-status .status-icon.icon-check-circle.data-v-80d79958 {
  color: #16a34a;
}
.course-card .course-status .status-icon.icon-play-circle.data-v-80d79958 {
  color: #f5576c;
}
.course-card .course-status .status-icon.icon-lock.data-v-80d79958 {
  color: #6b7280;
}
.course-card .course-meta .meta-item.data-v-80d79958 {
  color: #666;
  font-size: 24rpx;
}
.course-card .course-meta .meta-item .iconfont.data-v-80d79958 {
  margin-right: 8rpx;
}
.course-card .progress-bar.data-v-80d79958 {
  width: 100%;
  height: 12rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
}
.course-card .progress-bar .progress-fill.data-v-80d79958 {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #34d399);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 字体图标 */
.icon-arrow-left.data-v-80d79958::before {
  content: "\e005";
}
.icon-play.data-v-80d79958::before {
  content: "\e006";
}
.icon-clock.data-v-80d79958::before {
  content: "\e007";
}
.icon-headphones.data-v-80d79958::before {
  content: "\e008";
}
.icon-check-circle.data-v-80d79958::before {
  content: "\e009";
}
.icon-play-circle.data-v-80d79958::before {
  content: "\e010";
}
.icon-lock.data-v-80d79958::before {
  content: "\e011";
}