package com.learning.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: kuma_checkin_record
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Data
@TableName("kuma_checkin_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="kuma_checkin_record对象", description="打卡记录表")
public class KumaCheckinRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**系统标识*/
	@Excel(name = "系统标识", width = 15)
    @ApiModelProperty(value = "系统标识")
    private String sysSign;
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;
	/**打卡日期*/
	@Excel(name = "打卡日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "打卡日期")
    private Date checkinDate;
	/**完成课程数*/
	@Excel(name = "完成课程数", width = 15)
    @ApiModelProperty(value = "完成课程数")
    private Integer coursesCompleted;
	/**学习时长(秒)*/
	@Excel(name = "学习时长", width = 15)
    @ApiModelProperty(value = "学习时长(秒)")
    private Integer studyDuration;
	/**是否补打卡：0-否 1-是*/
	@Excel(name = "是否补打卡", width = 15)
    @ApiModelProperty(value = "是否补打卡：0-否 1-是")
    private Integer isMakeup;
	/**补打卡时间*/
	@Excel(name = "补打卡时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "补打卡时间")
    private Date makeupTime;
	/**当时连续天数*/
	@Excel(name = "当时连续天数", width = 15)
    @ApiModelProperty(value = "当时连续天数")
    private Integer continuousDays;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**删除标志（0-未删除，1-已删除）*/
	@Excel(name = "删除标志", width = 15)
    @ApiModelProperty(value = "删除标志（0-未删除，1-已删除）")
    @TableLogic
    private Integer delFlag;
}
