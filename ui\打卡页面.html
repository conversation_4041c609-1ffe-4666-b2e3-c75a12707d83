<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>口语便利店 - 每日打卡</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { 
            overflow: hidden; 
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar { display: none; }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .content {
            height: calc(100% - 44px - 83px);
            overflow-y: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .content::-webkit-scrollbar { display: none; }
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
            font-size: 12px;
        }
        .tab-item.active {
            color: #fcb69f;
        }
        .checkin-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            margin: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .checkin-button {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 8px 32px rgba(255, 154, 158, 0.4);
        }
        .checkin-button.completed {
            background: linear-gradient(135deg, #a8e6cf, #88d8a3);
        }
        .checkin-button:hover {
            transform: scale(1.05);
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            margin: 20px;
        }
        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            background: rgba(255,255,255,0.7);
            color: #666;
        }
        .calendar-day.header {
            background: transparent;
            color: white;
            font-weight: bold;
        }
        .calendar-day.checked {
            background: linear-gradient(135deg, #a8e6cf, #88d8a3);
            color: white;
        }
        .calendar-day.today {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: white;
            border: 2px solid white;
        }
        .calendar-day.makeup {
            background: linear-gradient(135deg, #ffd93d, #ff6b6b);
            color: white;
        }
        .stats-card {
            background: rgba(255,255,255,0.9);
            border-radius: 16px;
            margin: 16px 20px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        .stat-item:last-child {
            border-bottom: none;
        }
        .streak-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 头部标题 -->
                <div class="px-6 py-6 text-center">
                    <h1 class="text-white text-2xl font-bold mb-2">每日打卡</h1>
                    <p class="text-white/80 text-base">坚持学习，成就更好的自己</p>
                </div>

                <!-- 今日打卡 -->
                <div class="checkin-card">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">今日学习</h2>
                    <div class="checkin-button completed">
                        <i class="fas fa-check text-4xl text-white"></i>
                    </div>
                    <p class="text-lg font-medium text-green-600 mb-2">今日已打卡！</p>
                    <p class="text-sm text-gray-600">完成了 5 个课程的学习</p>
                    
                    <div class="mt-6 p-4 bg-green-50 rounded-lg">
                        <div class="flex items-center justify-center mb-2">
                            <div class="streak-badge">
                                <i class="fas fa-fire mr-2"></i>
                                连续打卡 12 天
                            </div>
                        </div>
                        <p class="text-sm text-gray-600">距离下一个里程碑还有 3 天</p>
                    </div>
                </div>

                <!-- 本月打卡日历 -->
                <div class="stats-card">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-bold text-gray-800">2024年3月</h3>
                        <div class="text-sm text-gray-600">
                            <span class="text-green-600 font-medium">22</span>/31 天
                        </div>
                    </div>
                    
                    <div class="calendar-grid">
                        <!-- 星期标题 -->
                        <div class="calendar-day header">日</div>
                        <div class="calendar-day header">一</div>
                        <div class="calendar-day header">二</div>
                        <div class="calendar-day header">三</div>
                        <div class="calendar-day header">四</div>
                        <div class="calendar-day header">五</div>
                        <div class="calendar-day header">六</div>
                        
                        <!-- 日期 -->
                        <div class="calendar-day"></div>
                        <div class="calendar-day"></div>
                        <div class="calendar-day"></div>
                        <div class="calendar-day"></div>
                        <div class="calendar-day"></div>
                        <div class="calendar-day checked">1</div>
                        <div class="calendar-day checked">2</div>
                        <div class="calendar-day checked">3</div>
                        <div class="calendar-day checked">4</div>
                        <div class="calendar-day checked">5</div>
                        <div class="calendar-day checked">6</div>
                        <div class="calendar-day checked">7</div>
                        <div class="calendar-day checked">8</div>
                        <div class="calendar-day checked">9</div>
                        <div class="calendar-day checked">10</div>
                        <div class="calendar-day checked">11</div>
                        <div class="calendar-day checked">12</div>
                        <div class="calendar-day checked">13</div>
                        <div class="calendar-day checked">14</div>
                        <div class="calendar-day makeup">15</div>
                        <div class="calendar-day checked">16</div>
                        <div class="calendar-day checked">17</div>
                        <div class="calendar-day checked">18</div>
                        <div class="calendar-day checked">19</div>
                        <div class="calendar-day checked">20</div>
                        <div class="calendar-day checked">21</div>
                        <div class="calendar-day today">22</div>
                        <div class="calendar-day">23</div>
                        <div class="calendar-day">24</div>
                        <div class="calendar-day">25</div>
                        <div class="calendar-day">26</div>
                        <div class="calendar-day">27</div>
                        <div class="calendar-day">28</div>
                        <div class="calendar-day">29</div>
                        <div class="calendar-day">30</div>
                        <div class="calendar-day">31</div>
                    </div>
                    
                    <div class="flex justify-center gap-4 mt-4 text-xs">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-green-500 rounded mr-1"></div>
                            <span>已打卡</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gradient-to-r from-yellow-400 to-red-400 rounded mr-1"></div>
                            <span>补打卡</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gradient-to-r from-pink-400 to-pink-500 rounded mr-1"></div>
                            <span>今天</span>
                        </div>
                    </div>
                </div>

                <!-- 打卡统计 -->
                <div class="stats-card">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">打卡统计</h3>
                    
                    <div class="stat-item">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">总打卡天数</span>
                        </div>
                        <span class="font-bold text-gray-800">156 天</span>
                    </div>
                    
                    <div class="stat-item">
                        <div class="flex items-center">
                            <i class="fas fa-fire text-orange-500 mr-3"></i>
                            <span class="text-gray-700">最长连续</span>
                        </div>
                        <span class="font-bold text-gray-800">28 天</span>
                    </div>
                    
                    <div class="stat-item">
                        <div class="flex items-center">
                            <i class="fas fa-redo text-blue-500 mr-3"></i>
                            <span class="text-gray-700">剩余补打卡</span>
                        </div>
                        <span class="font-bold text-gray-800">4 次</span>
                    </div>
                    
                    <div class="stat-item">
                        <div class="flex items-center">
                            <i class="fas fa-percentage text-purple-500 mr-3"></i>
                            <span class="text-gray-700">本月完成率</span>
                        </div>
                        <span class="font-bold text-gray-800">71%</span>
                    </div>
                </div>

                <!-- 补打卡按钮 -->
                <div class="mx-6 mb-6">
                    <button class="w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-white py-4 px-6 rounded-2xl font-bold text-lg shadow-lg">
                        <i class="fas fa-redo mr-2"></i>
                        使用补打卡机会
                    </button>
                    <p class="text-center text-white/80 text-sm mt-2">本月还可补打卡 4 次</p>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="tab-bar">
                <div class="tab-item">
                    <i class="fas fa-home text-xl mb-1"></i>
                    <span>首页</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-book text-xl mb-1"></i>
                    <span>课程</span>
                </div>
                <div class="tab-item active">
                    <i class="fas fa-calendar-check text-xl mb-1"></i>
                    <span>打卡</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-trophy text-xl mb-1"></i>
                    <span>排行</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
