#!/usr/bin/env node

/**
 * Vue3支持检查脚本
 * 验证项目是否正确配置了Vue3环境
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 检查Vue3项目配置...\n')

const checks = [
  {
    name: 'index.html',
    path: 'index.html',
    required: true,
    description: 'H5入口文件'
  },
  {
    name: 'vite.config.js',
    path: 'vite.config.js',
    required: true,
    description: 'Vite构建配置'
  },
  {
    name: 'tsconfig.json',
    path: 'tsconfig.json',
    required: false,
    description: 'TypeScript配置'
  },
  {
    name: 'manifest.json',
    path: 'manifest.json',
    required: true,
    description: '应用配置文件'
  },
  {
    name: 'package.json',
    path: 'package.json',
    required: true,
    description: '依赖配置文件'
  },
  {
    name: '.env',
    path: '.env',
    required: false,
    description: '环境变量配置'
  }
]

let allPassed = true
let warnings = []

// 检查文件存在性
checks.forEach(check => {
  const filePath = path.join(process.cwd(), check.path)
  const exists = fs.existsSync(filePath)
  
  if (exists) {
    console.log(`✅ ${check.name} - ${check.description}`)
  } else if (check.required) {
    console.log(`❌ ${check.name} - ${check.description} (必需文件缺失)`)
    allPassed = false
  } else {
    console.log(`⚠️  ${check.name} - ${check.description} (可选文件缺失)`)
    warnings.push(check.name)
  }
})

console.log('\n📋 详细配置检查...\n')

// 检查manifest.json中的Vue版本
try {
  const manifestPath = path.join(process.cwd(), 'manifest.json')
  if (fs.existsSync(manifestPath)) {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    const vueVersion = manifest.vueVersion
    
    if (vueVersion === '3') {
      console.log('✅ manifest.json - Vue版本设置为3')
    } else {
      console.log(`❌ manifest.json - Vue版本设置为${vueVersion}，应该为3`)
      allPassed = false
    }
  }
} catch (error) {
  console.log('❌ manifest.json - 文件格式错误或无法读取')
  allPassed = false
}

// 检查package.json中的Vue3依赖
try {
  const packagePath = path.join(process.cwd(), 'package.json')
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
    
    // 检查Vue版本
    if (dependencies.vue && dependencies.vue.includes('3')) {
      console.log(`✅ package.json - Vue版本: ${dependencies.vue}`)
    } else {
      console.log(`❌ package.json - Vue版本不正确: ${dependencies.vue || '未找到'}`)
      allPassed = false
    }
    
    // 检查Vite
    if (dependencies.vite) {
      console.log(`✅ package.json - Vite版本: ${dependencies.vite}`)
    } else {
      console.log('⚠️  package.json - 未找到Vite依赖')
      warnings.push('Vite依赖')
    }
    
    // 检查uni-app插件
    if (dependencies['@dcloudio/vite-plugin-uni']) {
      console.log(`✅ package.json - uni-app Vite插件: ${dependencies['@dcloudio/vite-plugin-uni']}`)
    } else {
      console.log('❌ package.json - 缺少uni-app Vite插件')
      allPassed = false
    }
  }
} catch (error) {
  console.log('❌ package.json - 文件格式错误或无法读取')
  allPassed = false
}

// 检查main.js中的Vue3语法
try {
  const mainPath = path.join(process.cwd(), 'main.js')
  if (fs.existsSync(mainPath)) {
    const mainContent = fs.readFileSync(mainPath, 'utf8')
    
    if (mainContent.includes('createSSRApp')) {
      console.log('✅ main.js - 使用Vue3的createSSRApp')
    } else if (mainContent.includes('createApp')) {
      console.log('✅ main.js - 使用Vue3的createApp')
    } else {
      console.log('❌ main.js - 未找到Vue3的应用创建方法')
      allPassed = false
    }
  }
} catch (error) {
  console.log('❌ main.js - 文件读取失败')
  allPassed = false
}

// 输出结果
console.log('\n' + '='.repeat(50))

if (allPassed) {
  console.log('🎉 Vue3配置检查通过！')
  console.log('✅ 项目已正确配置Vue3环境')
  console.log('✅ 所有必需文件都存在')
  console.log('✅ 可以正常开发和构建')
  
  if (warnings.length > 0) {
    console.log('\n⚠️  可选配置建议:')
    warnings.forEach(warning => {
      console.log(`   - ${warning}`)
    })
  }
  
  console.log('\n🚀 可以开始开发了！')
  console.log('运行命令: npm run dev:h5')
  
} else {
  console.log('❌ Vue3配置检查失败！')
  console.log('请根据上述错误信息修复配置')
  console.log('\n📖 参考文档: README.md')
  process.exit(1)
}

console.log('\n' + '='.repeat(50))
