package com.kuma.learning.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.kuma.learning.entity.KumaCourseTag;
import com.kuma.learning.service.IKumaCourseTagService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: kuma_course_tag
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Api(tags="kuma_course_tag")
@RestController
@RequestMapping("/learning/kumaCourseTag")
@Slf4j
public class KumaCourseTagController extends JeecgController<KumaCourseTag, IKumaCourseTagService> {
	@Autowired
	private IKumaCourseTagService kumaCourseTagService;
	
	/**
	 * 分页列表查询
	 *
	 * @param kumaCourseTag
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "kuma_course_tag-分页列表查询")
	@ApiOperation(value="kuma_course_tag-分页列表查询", notes="kuma_course_tag-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<KumaCourseTag>> queryPageList(KumaCourseTag kumaCourseTag,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<KumaCourseTag> queryWrapper = QueryGenerator.initQueryWrapper(kumaCourseTag, req.getParameterMap());
		Page<KumaCourseTag> page = new Page<KumaCourseTag>(pageNo, pageSize);
		IPage<KumaCourseTag> pageList = kumaCourseTagService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param kumaCourseTag
	 * @return
	 */
	@AutoLog(value = "kuma_course_tag-添加")
	@ApiOperation(value="kuma_course_tag-添加", notes="kuma_course_tag-添加")
	@RequiresPermissions("learning:kuma_course_tag:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody KumaCourseTag kumaCourseTag) {
		kumaCourseTagService.save(kumaCourseTag);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param kumaCourseTag
	 * @return
	 */
	@AutoLog(value = "kuma_course_tag-编辑")
	@ApiOperation(value="kuma_course_tag-编辑", notes="kuma_course_tag-编辑")
	@RequiresPermissions("learning:kuma_course_tag:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody KumaCourseTag kumaCourseTag) {
		kumaCourseTagService.updateById(kumaCourseTag);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "kuma_course_tag-通过id删除")
	@ApiOperation(value="kuma_course_tag-通过id删除", notes="kuma_course_tag-通过id删除")
	@RequiresPermissions("learning:kuma_course_tag:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		kumaCourseTagService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "kuma_course_tag-批量删除")
	@ApiOperation(value="kuma_course_tag-批量删除", notes="kuma_course_tag-批量删除")
	@RequiresPermissions("learning:kuma_course_tag:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.kumaCourseTagService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "kuma_course_tag-通过id查询")
	@ApiOperation(value="kuma_course_tag-通过id查询", notes="kuma_course_tag-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<KumaCourseTag> queryById(@RequestParam(name="id",required=true) String id) {
		KumaCourseTag kumaCourseTag = kumaCourseTagService.getById(id);
		if(kumaCourseTag==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(kumaCourseTag);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param kumaCourseTag
    */
    @RequiresPermissions("learning:kuma_course_tag:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, KumaCourseTag kumaCourseTag) {
        return super.exportXls(request, kumaCourseTag, KumaCourseTag.class, "kuma_course_tag");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("learning:kuma_course_tag:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, KumaCourseTag.class);
    }

}
