<template>
	<view class="api-test-container">
		<view class="header">
			<text class="title">🔗 API接口测试</text>
			<text class="subtitle">前后端联调测试工具</text>
		</view>
		
		<!-- 连接状态 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">🌐 连接状态</text>
				<view class="status-indicator" :class="{ connected: isConnected }"></view>
			</view>
			<view class="connection-info">
				<view class="info-item">
					<text class="label">后端地址</text>
					<text class="value">{{ baseUrl }}</text>
				</view>
				<view class="info-item">
					<text class="label">连接状态</text>
					<text class="value" :class="{ success: isConnected, error: !isConnected }">
						{{ isConnected ? '已连接' : '未连接' }}
					</text>
				</view>
				<view class="info-item">
					<text class="label">最后测试</text>
					<text class="value">{{ lastTestTime || '未测试' }}</text>
				</view>
			</view>
			<button class="test-btn" @tap="testConnection" :disabled="testing">
				{{ testing ? '测试中...' : '测试连接' }}
			</button>
		</view>
		
		<!-- API接口测试 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">🔧 接口测试</text>
			</view>
			
			<!-- 用户接口 -->
			<view class="api-group">
				<text class="group-title">👤 用户接口</text>
				<view class="api-list">
					<view class="api-item" v-for="api in userApis" :key="api.name">
						<view class="api-info">
							<text class="api-name">{{ api.name }}</text>
							<text class="api-url">{{ api.method }} {{ api.url }}</text>
						</view>
						<button class="api-test-btn" @tap="testApi(api)" :disabled="api.testing">
							{{ api.testing ? '测试中' : '测试' }}
						</button>
					</view>
				</view>
			</view>
			
			<!-- 课程接口 -->
			<view class="api-group">
				<text class="group-title">📚 课程接口</text>
				<view class="api-list">
					<view class="api-item" v-for="api in courseApis" :key="api.name">
						<view class="api-info">
							<text class="api-name">{{ api.name }}</text>
							<text class="api-url">{{ api.method }} {{ api.url }}</text>
						</view>
						<button class="api-test-btn" @tap="testApi(api)" :disabled="api.testing">
							{{ api.testing ? '测试中' : '测试' }}
						</button>
					</view>
				</view>
			</view>
			
			<!-- 练习接口 -->
			<view class="api-group">
				<text class="group-title">🎯 练习接口</text>
				<view class="api-list">
					<view class="api-item" v-for="api in practiceApis" :key="api.name">
						<view class="api-info">
							<text class="api-name">{{ api.name }}</text>
							<text class="api-url">{{ api.method }} {{ api.url }}</text>
						</view>
						<button class="api-test-btn" @tap="testApi(api)" :disabled="api.testing">
							{{ api.testing ? '测试中' : '测试' }}
						</button>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 测试结果 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">📊 测试结果</text>
				<button class="clear-btn" @tap="clearResults">清空</button>
			</view>
			<view class="results-list">
				<view class="result-item" v-for="result in testResults" :key="result.id">
					<view class="result-header">
						<text class="result-name">{{ result.name }}</text>
						<text class="result-status" :class="result.success ? 'success' : 'error'">
							{{ result.success ? '成功' : '失败' }}
						</text>
						<text class="result-time">{{ formatTime(result.time) }}</text>
					</view>
					<view class="result-details">
						<text class="result-url">{{ result.method }} {{ result.url }}</text>
						<text class="result-message">{{ result.message }}</text>
						<view class="result-data" v-if="result.data">
							<text class="data-title">响应数据:</text>
							<text class="data-content">{{ JSON.stringify(result.data, null, 2) }}</text>
						</view>
					</view>
				</view>
				<view class="empty-state" v-if="testResults.length === 0">
					<text>暂无测试结果</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { userApi, courseApi, practiceApi } from '@/api/index.js'

export default {
	data() {
		return {
			isConnected: false,
			testing: false,
			lastTestTime: '',
			baseUrl: '',
			testResults: [],
			userApis: [
				{ name: '获取用户信息', method: 'GET', url: '/app/user/info', api: () => userApi.getUserInfo() },
				{ name: '用户签到', method: 'POST', url: '/app/user/checkin', api: () => userApi.checkin() },
				{ name: '获取学习统计', method: 'GET', url: '/app/user/statistics', api: () => userApi.getStudyStats() }
			],
			courseApis: [
				{ name: '获取课程列表', method: 'GET', url: '/app/course/list', api: () => courseApi.getCourseList() },
				{ name: '获取课程详情', method: 'GET', url: '/app/course/1', api: () => courseApi.getCourseDetail(1) }
			],
			practiceApis: [
				{ name: '获取支持语言', method: 'GET', url: '/app/practice/supportedLanguages', api: () => practiceApi.getSupportedLanguages() },
				{ name: '获取评测配置', method: 'GET', url: '/app/practice/evaluationConfig', api: () => practiceApi.getEvaluationConfig() },
				{ name: '获取练习统计', method: 'GET', url: '/app/practice/statistics', api: () => practiceApi.getStatistics() }
			]
		}
	},
	
	onLoad() {
		this.baseUrl = this.$baseUrl || 'http://localhost:8080/jeecg-boot'
		this.testConnection()
	},
	
	methods: {
		async testConnection() {
			this.testing = true
			try {
				const response = await uni.request({
					url: this.baseUrl + '/app/common/health',
					method: 'GET',
					timeout: 5000
				})
				
				this.isConnected = response.statusCode === 200
				this.lastTestTime = new Date().toLocaleString()
				
				if (this.isConnected) {
					uni.showToast({
						title: '连接成功',
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: '连接失败',
						icon: 'none'
					})
				}
			} catch (error) {
				this.isConnected = false
				this.lastTestTime = new Date().toLocaleString()
				console.error('连接测试失败:', error)
				
				uni.showToast({
					title: '连接失败',
					icon: 'none'
				})
			} finally {
				this.testing = false
			}
		},
		
		async testApi(apiConfig) {
			apiConfig.testing = true
			const startTime = Date.now()
			
			try {
				const result = await apiConfig.api()
				const endTime = Date.now()
				
				this.addTestResult({
					name: apiConfig.name,
					method: apiConfig.method,
					url: apiConfig.url,
					success: true,
					message: `请求成功 (${endTime - startTime}ms)`,
					data: result,
					time: new Date()
				})
				
				uni.showToast({
					title: '测试成功',
					icon: 'success'
				})
				
			} catch (error) {
				const endTime = Date.now()
				
				this.addTestResult({
					name: apiConfig.name,
					method: apiConfig.method,
					url: apiConfig.url,
					success: false,
					message: error.message || error.errMsg || '请求失败',
					data: error,
					time: new Date()
				})
				
				uni.showToast({
					title: '测试失败',
					icon: 'none'
				})
			} finally {
				apiConfig.testing = false
			}
		},
		
		addTestResult(result) {
			result.id = Date.now() + Math.random()
			this.testResults.unshift(result)
			
			// 限制结果数量
			if (this.testResults.length > 20) {
				this.testResults = this.testResults.slice(0, 20)
			}
		},
		
		clearResults() {
			this.testResults = []
			uni.showToast({
				title: '已清空结果',
				icon: 'success'
			})
		},
		
		formatTime(date) {
			if (!date) return ''
			const d = new Date(date)
			return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`
		}
	}
}
</script>

<style lang="scss" scoped>
.api-test-container {
	padding: 20px;
	background-color: #f8f9fa;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30px;
	
	.title {
		display: block;
		font-size: 24px;
		font-weight: bold;
		color: #333;
		margin-bottom: 8px;
	}
	
	.subtitle {
		display: block;
		font-size: 14px;
		color: #666;
	}
}

.section {
	background: white;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
		
		.section-title {
			font-size: 16px;
			font-weight: bold;
			color: #333;
		}
		
		.status-indicator {
			width: 12px;
			height: 12px;
			border-radius: 50%;
			background: #ccc;
			
			&.connected {
				background: #10b981;
			}
		}
		
		.clear-btn {
			padding: 4px 12px;
			font-size: 12px;
			border-radius: 6px;
			border: 1px solid #ddd;
			background: #f8f9fa;
			color: #666;
		}
	}
}

.connection-info {
	margin-bottom: 15px;
	
	.info-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 8px;
		
		.label {
			font-size: 14px;
			color: #666;
		}
		
		.value {
			font-size: 14px;
			color: #333;
			
			&.success {
				color: #10b981;
			}
			
			&.error {
				color: #ef4444;
			}
		}
	}
}

.test-btn {
	width: 100%;
	padding: 12px;
	background: #667eea;
	color: white;
	border-radius: 8px;
	border: none;
	font-size: 14px;
	
	&:disabled {
		background: #ccc;
	}
}

.api-group {
	margin-bottom: 20px;
	
	.group-title {
		display: block;
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 10px;
	}
}

.api-list {
	.api-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 12px;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		margin-bottom: 8px;
		
		.api-info {
			flex: 1;
			
			.api-name {
				display: block;
				font-size: 14px;
				font-weight: 500;
				color: #333;
				margin-bottom: 4px;
			}
			
			.api-url {
				display: block;
				font-size: 12px;
				color: #666;
				font-family: monospace;
			}
		}
		
		.api-test-btn {
			padding: 6px 12px;
			background: #667eea;
			color: white;
			border-radius: 6px;
			border: none;
			font-size: 12px;
			
			&:disabled {
				background: #ccc;
			}
		}
	}
}

.results-list {
	.result-item {
		border: 1px solid #e9ecef;
		border-radius: 8px;
		margin-bottom: 10px;
		overflow: hidden;
		
		.result-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px;
			background: #f8f9fa;
			
			.result-name {
				font-size: 14px;
				font-weight: 500;
				color: #333;
			}
			
			.result-status {
				font-size: 12px;
				padding: 2px 8px;
				border-radius: 4px;
				color: white;
				
				&.success {
					background: #10b981;
				}
				
				&.error {
					background: #ef4444;
				}
			}
			
			.result-time {
				font-size: 12px;
				color: #666;
			}
		}
		
		.result-details {
			padding: 12px;
			
			.result-url {
				display: block;
				font-size: 12px;
				color: #666;
				font-family: monospace;
				margin-bottom: 8px;
			}
			
			.result-message {
				display: block;
				font-size: 14px;
				color: #333;
				margin-bottom: 8px;
			}
			
			.result-data {
				.data-title {
					display: block;
					font-size: 12px;
					color: #666;
					margin-bottom: 4px;
				}
				
				.data-content {
					display: block;
					font-size: 11px;
					color: #333;
					background: #f8f9fa;
					padding: 8px;
					border-radius: 4px;
					font-family: monospace;
					white-space: pre-wrap;
					word-break: break-all;
				}
			}
		}
	}
	
	.empty-state {
		text-align: center;
		padding: 40px 20px;
		color: #666;
	}
}
</style>
