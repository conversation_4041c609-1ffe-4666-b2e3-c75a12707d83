<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>口语便利店 - 个人中心</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { 
            overflow: hidden; 
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar { display: none; }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .content {
            height: calc(100% - 44px - 83px);
            overflow-y: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .content::-webkit-scrollbar { display: none; }
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
            font-size: 12px;
        }
        .tab-item.active {
            color: #667eea;
        }
        .profile-header {
            padding: 30px 20px;
            text-align: center;
            color: white;
        }
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 16px;
            border: 4px solid white;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }
        .level-badge {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-top: 8px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin: 20px;
        }
        .stat-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        .menu-section {
            background: #f8fafc;
            border-radius: 24px 24px 0 0;
            margin-top: 20px;
            padding: 20px 0;
            min-height: 400px;
        }
        .menu-group {
            margin: 20px 16px;
        }
        .menu-title {
            font-size: 16px;
            font-weight: bold;
            color: #374151;
            margin-bottom: 12px;
            padding: 0 4px;
        }
        .menu-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            background: white;
            border-radius: 12px;
            margin-bottom: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            cursor: pointer;
            transition: all 0.2s;
        }
        .menu-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .menu-item-left {
            display: flex;
            align-items: center;
        }
        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }
        .menu-icon.blue { background: linear-gradient(135deg, #667eea, #764ba2); }
        .menu-icon.green { background: linear-gradient(135deg, #56ab2f, #a8e6cf); }
        .menu-icon.orange { background: linear-gradient(135deg, #ff9a9e, #fecfef); }
        .menu-icon.purple { background: linear-gradient(135deg, #a8edea, #fed6e3); }
        .menu-icon.red { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
        .menu-text {
            flex: 1;
        }
        .menu-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 2px;
        }
        .menu-desc {
            font-size: 12px;
            color: #9ca3af;
        }
        .vip-card {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border-radius: 16px;
            margin: 16px;
            padding: 20px;
            color: #333;
            position: relative;
            overflow: hidden;
        }
        .vip-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
        }
    </style>
</head>
<body>
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 个人信息头部 -->
                <div class="profile-header">
                    <div class="profile-avatar">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face" alt="用户头像" class="w-full h-full object-cover">
                    </div>
                    <h2 class="text-xl font-bold mb-2">小明</h2>
                    <p class="text-white/80 text-sm mb-2">学习日语 156 天</p>
                    <div class="level-badge">
                        <i class="fas fa-star mr-1"></i>
                        中级学员 Lv.3
                    </div>
                </div>

                <!-- 学习统计 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">学习天数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">28</div>
                        <div class="stat-label">连续打卡</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">85</div>
                        <div class="stat-label">平均分数</div>
                    </div>
                </div>

                <!-- VIP会员卡 -->
                <div class="vip-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-bold text-lg mb-1">
                                <i class="fas fa-crown mr-2"></i>
                                升级VIP会员
                            </h3>
                            <p class="text-sm opacity-80">解锁全部高级课程</p>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold">¥99/年</div>
                            <div class="text-xs opacity-70">限时优惠</div>
                        </div>
                    </div>
                </div>

                <!-- 菜单区域 -->
                <div class="menu-section">
                    <!-- 学习管理 -->
                    <div class="menu-group">
                        <div class="menu-title">学习管理</div>
                        
                        <div class="menu-item">
                            <div class="menu-item-left">
                                <div class="menu-icon blue">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="menu-text">
                                    <div class="menu-label">学习报告</div>
                                    <div class="menu-desc">查看详细学习数据</div>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="menu-item">
                            <div class="menu-item-left">
                                <div class="menu-icon green">
                                    <i class="fas fa-bookmark"></i>
                                </div>
                                <div class="menu-text">
                                    <div class="menu-label">我的收藏</div>
                                    <div class="menu-desc">收藏的课程和词汇</div>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="menu-item">
                            <div class="menu-item-left">
                                <div class="menu-icon orange">
                                    <i class="fas fa-download"></i>
                                </div>
                                <div class="menu-text">
                                    <div class="menu-label">离线下载</div>
                                    <div class="menu-desc">下载课程离线学习</div>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 设置选项 -->
                    <div class="menu-group">
                        <div class="menu-title">设置</div>
                        
                        <div class="menu-item">
                            <div class="menu-item-left">
                                <div class="menu-icon purple">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="menu-text">
                                    <div class="menu-label">消息通知</div>
                                    <div class="menu-desc">学习提醒设置</div>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="menu-item">
                            <div class="menu-item-left">
                                <div class="menu-icon blue">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="menu-text">
                                    <div class="menu-label">通用设置</div>
                                    <div class="menu-desc">语言、主题等设置</div>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="menu-item">
                            <div class="menu-item-left">
                                <div class="menu-icon green">
                                    <i class="fas fa-question-circle"></i>
                                </div>
                                <div class="menu-text">
                                    <div class="menu-label">帮助中心</div>
                                    <div class="menu-desc">常见问题与反馈</div>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>

                        <div class="menu-item">
                            <div class="menu-item-left">
                                <div class="menu-icon red">
                                    <i class="fas fa-sign-out-alt"></i>
                                </div>
                                <div class="menu-text">
                                    <div class="menu-label">退出登录</div>
                                    <div class="menu-desc">安全退出当前账号</div>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="tab-bar">
                <div class="tab-item">
                    <i class="fas fa-home text-xl mb-1"></i>
                    <span>首页</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-book text-xl mb-1"></i>
                    <span>课程</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-calendar-check text-xl mb-1"></i>
                    <span>打卡</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-trophy text-xl mb-1"></i>
                    <span>排行</span>
                </div>
                <div class="tab-item active">
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
