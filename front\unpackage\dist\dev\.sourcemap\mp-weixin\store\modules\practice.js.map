{"version": 3, "file": "practice.js", "sources": ["store/modules/practice.js"], "sourcesContent": ["const practice = {\n  namespaced: true,\n  \n  state: {\n    // 当前练习课程\n    currentPractice: null,\n    \n    // 练习历史记录\n    practiceHistory: [],\n    \n    // 当前练习进度\n    currentProgress: {\n      courseId: null,\n      currentSentence: 0,\n      totalSentences: 0,\n      completedSentences: []\n    },\n    \n    // AI评分结果\n    aiScoreResult: {\n      totalScore: 0,\n      pronunciationScore: 0,\n      fluencyScore: 0,\n      toneScore: 0,\n      feedback: ''\n    },\n    \n    // 录音状态\n    recordingState: {\n      isRecording: false,\n      hasRecorded: false,\n      audioPath: '',\n      duration: 0\n    },\n    \n    // 练习统计\n    practiceStats: {\n      totalPractices: 0,\n      averageScore: 0,\n      bestScore: 0,\n      totalTime: 0,\n      improvementRate: 0\n    }\n  },\n  \n  mutations: {\n    // 设置当前练习\n    SET_CURRENT_PRACTICE(state, practice) {\n      state.currentPractice = practice\n    },\n    \n    // 设置练习历史\n    SET_PRACTICE_HISTORY(state, history) {\n      state.practiceHistory = history\n    },\n    \n    // 添加练习记录\n    ADD_PRACTICE_RECORD(state, record) {\n      state.practiceHistory.unshift(record)\n    },\n    \n    // 设置练习进度\n    SET_PRACTICE_PROGRESS(state, progress) {\n      state.currentProgress = { ...state.currentProgress, ...progress }\n    },\n    \n    // 更新当前句子\n    UPDATE_CURRENT_SENTENCE(state, sentenceIndex) {\n      state.currentProgress.currentSentence = sentenceIndex\n    },\n    \n    // 标记句子完成\n    MARK_SENTENCE_COMPLETED(state, sentenceIndex) {\n      if (!state.currentProgress.completedSentences.includes(sentenceIndex)) {\n        state.currentProgress.completedSentences.push(sentenceIndex)\n      }\n    },\n    \n    // 设置AI评分结果\n    SET_AI_SCORE_RESULT(state, result) {\n      state.aiScoreResult = { ...state.aiScoreResult, ...result }\n    },\n    \n    // 设置录音状态\n    SET_RECORDING_STATE(state, recordingState) {\n      state.recordingState = { ...state.recordingState, ...recordingState }\n    },\n    \n    // 重置录音状态\n    RESET_RECORDING_STATE(state) {\n      state.recordingState = {\n        isRecording: false,\n        hasRecorded: false,\n        audioPath: '',\n        duration: 0\n      }\n    },\n    \n    // 设置练习统计\n    SET_PRACTICE_STATS(state, stats) {\n      state.practiceStats = { ...state.practiceStats, ...stats }\n    },\n    \n    // 重置练习进度\n    RESET_PRACTICE_PROGRESS(state) {\n      state.currentProgress = {\n        courseId: null,\n        currentSentence: 0,\n        totalSentences: 0,\n        completedSentences: []\n      }\n    }\n  },\n  \n  actions: {\n    // 开始练习\n    async startPractice({ commit }, { courseId, sentences }) {\n      try {\n        commit('SET_PRACTICE_PROGRESS', {\n          courseId,\n          currentSentence: 0,\n          totalSentences: sentences.length,\n          completedSentences: []\n        })\n        \n        commit('RESET_RECORDING_STATE')\n        \n        return { success: true }\n      } catch (error) {\n        console.error('开始练习失败:', error)\n        return { success: false, message: '开始练习失败' }\n      }\n    },\n    \n    // 提交录音进行AI评分\n    async submitRecording({ commit, rootState }, { courseId, sentenceText, audioFile }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/practice/submit',\n          method: 'POST',\n          data: {\n            courseId,\n            sentenceText,\n            audioFile\n          },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          const scoreResult = response.data.data\n          commit('SET_AI_SCORE_RESULT', scoreResult)\n          \n          // 添加练习记录\n          const practiceRecord = {\n            id: Date.now(),\n            courseId,\n            sentenceText,\n            ...scoreResult,\n            createdTime: new Date().toISOString()\n          }\n          commit('ADD_PRACTICE_RECORD', practiceRecord)\n          \n          return { success: true, data: scoreResult }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('提交录音失败:', error)\n        return { success: false, message: '评分失败，请重试' }\n      }\n    },\n    \n    // 下一句练习\n    nextSentence({ commit, state }) {\n      const nextIndex = state.currentProgress.currentSentence + 1\n      if (nextIndex < state.currentProgress.totalSentences) {\n        commit('UPDATE_CURRENT_SENTENCE', nextIndex)\n        commit('RESET_RECORDING_STATE')\n        return { hasNext: true, currentIndex: nextIndex }\n      } else {\n        return { hasNext: false, completed: true }\n      }\n    },\n    \n    // 完成当前句子\n    completeSentence({ commit, state }) {\n      const currentIndex = state.currentProgress.currentSentence\n      commit('MARK_SENTENCE_COMPLETED', currentIndex)\n    },\n    \n    // 加载练习历史\n    async loadPracticeHistory({ commit, rootState }, { courseId, page = 1, size = 20 }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/practice/history',\n          method: 'GET',\n          data: { courseId, page, size },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          commit('SET_PRACTICE_HISTORY', response.data.data.list)\n          return { success: true, data: response.data.data }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('加载练习历史失败:', error)\n        return { success: false, message: '加载失败，请重试' }\n      }\n    },\n    \n    // 加载练习统计\n    async loadPracticeStats({ commit, rootState }) {\n      try {\n        const response = await uni.request({\n          url: '/api/v1/practice/statistics',\n          method: 'GET',\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          commit('SET_PRACTICE_STATS', response.data.data)\n          return { success: true, data: response.data.data }\n        }\n      } catch (error) {\n        console.error('加载练习统计失败:', error)\n      }\n    },\n    \n    // 重新录音\n    retryRecording({ commit }) {\n      commit('RESET_RECORDING_STATE')\n      commit('SET_AI_SCORE_RESULT', {\n        totalScore: 0,\n        pronunciationScore: 0,\n        fluencyScore: 0,\n        toneScore: 0,\n        feedback: ''\n      })\n    },\n    \n    // 完成练习\n    async completePractice({ commit, state, rootState }) {\n      try {\n        const { courseId, completedSentences, totalSentences } = state.currentProgress\n        const completionRate = (completedSentences.length / totalSentences) * 100\n        \n        const response = await uni.request({\n          url: '/api/v1/practice/complete',\n          method: 'POST',\n          data: {\n            courseId,\n            completedSentences: completedSentences.length,\n            totalSentences,\n            completionRate\n          },\n          header: {\n            'Authorization': `Bearer ${rootState.user.token}`\n          }\n        })\n        \n        if (response.data.code === 200) {\n          commit('RESET_PRACTICE_PROGRESS')\n          return { success: true, data: response.data.data }\n        } else {\n          return { success: false, message: response.data.message }\n        }\n      } catch (error) {\n        console.error('完成练习失败:', error)\n        return { success: false, message: '完成练习失败' }\n      }\n    }\n  },\n  \n  getters: {\n    // 获取当前练习进度百分比\n    practiceProgressPercent: (state) => {\n      if (state.currentProgress.totalSentences === 0) return 0\n      return Math.round((state.currentProgress.completedSentences.length / state.currentProgress.totalSentences) * 100)\n    },\n    \n    // 检查当前句子是否已完成\n    isCurrentSentenceCompleted: (state) => {\n      return state.currentProgress.completedSentences.includes(state.currentProgress.currentSentence)\n    },\n    \n    // 获取练习完成状态\n    isPracticeCompleted: (state) => {\n      return state.currentProgress.completedSentences.length === state.currentProgress.totalSentences\n    },\n    \n    // 获取最近的练习记录\n    recentPracticeRecords: (state) => (limit = 10) => {\n      return state.practiceHistory.slice(0, limit)\n    },\n    \n    // 获取指定课程的练习记录\n    getPracticeRecordsByCourse: (state) => (courseId) => {\n      return state.practiceHistory.filter(record => record.courseId === courseId)\n    },\n    \n    // 计算平均分数\n    averageScore: (state) => {\n      if (state.practiceHistory.length === 0) return 0\n      const totalScore = state.practiceHistory.reduce((sum, record) => sum + record.totalScore, 0)\n      return Math.round(totalScore / state.practiceHistory.length)\n    },\n    \n    // 获取最高分数\n    bestScore: (state) => {\n      if (state.practiceHistory.length === 0) return 0\n      return Math.max(...state.practiceHistory.map(record => record.totalScore))\n    },\n    \n    // 获取进步率\n    improvementRate: (state) => {\n      if (state.practiceHistory.length < 2) return 0\n      \n      const recent = state.practiceHistory.slice(0, 5)\n      const older = state.practiceHistory.slice(5, 10)\n      \n      if (older.length === 0) return 0\n      \n      const recentAvg = recent.reduce((sum, r) => sum + r.totalScore, 0) / recent.length\n      const olderAvg = older.reduce((sum, r) => sum + r.totalScore, 0) / older.length\n      \n      return Math.round(((recentAvg - olderAvg) / olderAvg) * 100)\n    }\n  }\n}\n\nexport default practice\n"], "names": ["practice", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAK,MAAC,WAAW;AAAA,EACf,YAAY;AAAA,EAEZ,OAAO;AAAA;AAAA,IAEL,iBAAiB;AAAA;AAAA,IAGjB,iBAAiB,CAAE;AAAA;AAAA,IAGnB,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,oBAAoB,CAAE;AAAA,IACvB;AAAA;AAAA,IAGD,eAAe;AAAA,MACb,YAAY;AAAA,MACZ,oBAAoB;AAAA,MACpB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,UAAU;AAAA,IACX;AAAA;AAAA,IAGD,gBAAgB;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,WAAW;AAAA,MACX,UAAU;AAAA,IACX;AAAA;AAAA,IAGD,eAAe;AAAA,MACb,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,WAAW;AAAA,MACX,iBAAiB;AAAA,IAClB;AAAA,EACF;AAAA,EAED,WAAW;AAAA;AAAA,IAET,qBAAqB,OAAOA,WAAU;AACpC,YAAM,kBAAkBA;AAAA,IACzB;AAAA;AAAA,IAGD,qBAAqB,OAAO,SAAS;AACnC,YAAM,kBAAkB;AAAA,IACzB;AAAA;AAAA,IAGD,oBAAoB,OAAO,QAAQ;AACjC,YAAM,gBAAgB,QAAQ,MAAM;AAAA,IACrC;AAAA;AAAA,IAGD,sBAAsB,OAAO,UAAU;AACrC,YAAM,kBAAkB,kCAAK,MAAM,kBAAoB;AAAA,IACxD;AAAA;AAAA,IAGD,wBAAwB,OAAO,eAAe;AAC5C,YAAM,gBAAgB,kBAAkB;AAAA,IACzC;AAAA;AAAA,IAGD,wBAAwB,OAAO,eAAe;AAC5C,UAAI,CAAC,MAAM,gBAAgB,mBAAmB,SAAS,aAAa,GAAG;AACrE,cAAM,gBAAgB,mBAAmB,KAAK,aAAa;AAAA,MAC5D;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB,OAAO,QAAQ;AACjC,YAAM,gBAAgB,kCAAK,MAAM,gBAAkB;AAAA,IACpD;AAAA;AAAA,IAGD,oBAAoB,OAAO,gBAAgB;AACzC,YAAM,iBAAiB,kCAAK,MAAM,iBAAmB;AAAA,IACtD;AAAA;AAAA,IAGD,sBAAsB,OAAO;AAC3B,YAAM,iBAAiB;AAAA,QACrB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,MACX;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB,OAAO,OAAO;AAC/B,YAAM,gBAAgB,kCAAK,MAAM,gBAAkB;AAAA,IACpD;AAAA;AAAA,IAGD,wBAAwB,OAAO;AAC7B,YAAM,kBAAkB;AAAA,QACtB,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,oBAAoB,CAAE;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAED,cAAc,IAAY,IAAyB;AAAA,iDAArC,EAAE,OAAM,GAAI,EAAE,UAAU,UAAS,GAAI;AACvD,YAAI;AACF,iBAAO,yBAAyB;AAAA,YAC9B;AAAA,YACA,iBAAiB;AAAA,YACjB,gBAAgB,UAAU;AAAA,YAC1B,oBAAoB,CAAE;AAAA,UAChC,CAAS;AAED,iBAAO,uBAAuB;AAE9B,iBAAO,EAAE,SAAS,KAAM;AAAA,QACzB,SAAQ,OAAO;AACdC,wBAAAA,MAAA,MAAA,SAAA,oCAAc,WAAW,KAAK;AAC9B,iBAAO,EAAE,SAAS,OAAO,SAAS,SAAU;AAAA,QAC7C;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,gBAAgB,IAAuB,IAAuC;AAAA,iDAA9D,EAAE,QAAQ,UAAS,GAAI,EAAE,UAAU,cAAc,aAAa;AAClF,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,YACD,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,kBAAM,cAAc,SAAS,KAAK;AAClC,mBAAO,uBAAuB,WAAW;AAGzC,kBAAM,iBAAiB;AAAA,cACrB,IAAI,KAAK,IAAK;AAAA,cACd;AAAA,cACA;AAAA,eACG,cAJkB;AAAA,cAKrB,cAAa,oBAAI,KAAM,GAAC,YAAa;AAAA,YACtC;AACD,mBAAO,uBAAuB,cAAc;AAE5C,mBAAO,EAAE,SAAS,MAAM,MAAM,YAAa;AAAA,UACrD,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,oCAAc,WAAW,KAAK;AAC9B,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA;AAAA,IAGD,aAAa,EAAE,QAAQ,SAAS;AAC9B,YAAM,YAAY,MAAM,gBAAgB,kBAAkB;AAC1D,UAAI,YAAY,MAAM,gBAAgB,gBAAgB;AACpD,eAAO,2BAA2B,SAAS;AAC3C,eAAO,uBAAuB;AAC9B,eAAO,EAAE,SAAS,MAAM,cAAc,UAAW;AAAA,MACzD,OAAa;AACL,eAAO,EAAE,SAAS,OAAO,WAAW,KAAM;AAAA,MAC3C;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,EAAE,QAAQ,SAAS;AAClC,YAAM,eAAe,MAAM,gBAAgB;AAC3C,aAAO,2BAA2B,YAAY;AAAA,IAC/C;AAAA;AAAA,IAGK,oBAAoB,IAAuB,IAAmC;AAAA,iDAA1D,EAAE,QAAQ,aAAa,EAAE,UAAU,OAAO,GAAG,OAAO,MAAM;AAClF,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM,EAAE,UAAU,MAAM,KAAM;AAAA,YAC9B,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,mBAAO,wBAAwB,SAAS,KAAK,KAAK,IAAI;AACtD,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,oCAAA,aAAa,KAAK;AAChC,iBAAO,EAAE,SAAS,OAAO,SAAS,WAAY;AAAA,QAC/C;AAAA,MACF;AAAA;AAAA;AAAA,IAGK,kBAAkB,IAAuB;AAAA,iDAAvB,EAAE,QAAQ,aAAa;AAC7C,YAAI;AACF,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,mBAAO,sBAAsB,SAAS,KAAK,IAAI;AAC/C,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UACnD;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,oCAAA,aAAa,KAAK;AAAA,QACjC;AAAA,MACF;AAAA;AAAA;AAAA,IAGD,eAAe,EAAE,UAAU;AACzB,aAAO,uBAAuB;AAC9B,aAAO,uBAAuB;AAAA,QAC5B,YAAY;AAAA,QACZ,oBAAoB;AAAA,QACpB,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MAClB,CAAO;AAAA,IACF;AAAA;AAAA,IAGK,iBAAiB,IAA8B;AAAA,iDAA9B,EAAE,QAAQ,OAAO,UAAS,GAAI;AACnD,YAAI;AACF,gBAAM,EAAE,UAAU,oBAAoB,eAAgB,IAAG,MAAM;AAC/D,gBAAM,iBAAkB,mBAAmB,SAAS,iBAAkB;AAEtE,gBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,YACjC,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,MAAM;AAAA,cACJ;AAAA,cACA,oBAAoB,mBAAmB;AAAA,cACvC;AAAA,cACA;AAAA,YACD;AAAA,YACD,QAAQ;AAAA,cACN,iBAAiB,UAAU,UAAU,KAAK,KAAK;AAAA,YAChD;AAAA,UACX,CAAS;AAED,cAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,mBAAO,yBAAyB;AAChC,mBAAO,EAAE,SAAS,MAAM,MAAM,SAAS,KAAK,KAAM;AAAA,UAC5D,OAAe;AACL,mBAAO,EAAE,SAAS,OAAO,SAAS,SAAS,KAAK,QAAS;AAAA,UAC1D;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,oCAAc,WAAW,KAAK;AAC9B,iBAAO,EAAE,SAAS,OAAO,SAAS,SAAU;AAAA,QAC7C;AAAA,MACF;AAAA;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,yBAAyB,CAAC,UAAU;AAClC,UAAI,MAAM,gBAAgB,mBAAmB;AAAG,eAAO;AACvD,aAAO,KAAK,MAAO,MAAM,gBAAgB,mBAAmB,SAAS,MAAM,gBAAgB,iBAAkB,GAAG;AAAA,IACjH;AAAA;AAAA,IAGD,4BAA4B,CAAC,UAAU;AACrC,aAAO,MAAM,gBAAgB,mBAAmB,SAAS,MAAM,gBAAgB,eAAe;AAAA,IAC/F;AAAA;AAAA,IAGD,qBAAqB,CAAC,UAAU;AAC9B,aAAO,MAAM,gBAAgB,mBAAmB,WAAW,MAAM,gBAAgB;AAAA,IAClF;AAAA;AAAA,IAGD,uBAAuB,CAAC,UAAU,CAAC,QAAQ,OAAO;AAChD,aAAO,MAAM,gBAAgB,MAAM,GAAG,KAAK;AAAA,IAC5C;AAAA;AAAA,IAGD,4BAA4B,CAAC,UAAU,CAAC,aAAa;AACnD,aAAO,MAAM,gBAAgB,OAAO,YAAU,OAAO,aAAa,QAAQ;AAAA,IAC3E;AAAA;AAAA,IAGD,cAAc,CAAC,UAAU;AACvB,UAAI,MAAM,gBAAgB,WAAW;AAAG,eAAO;AAC/C,YAAM,aAAa,MAAM,gBAAgB,OAAO,CAAC,KAAK,WAAW,MAAM,OAAO,YAAY,CAAC;AAC3F,aAAO,KAAK,MAAM,aAAa,MAAM,gBAAgB,MAAM;AAAA,IAC5D;AAAA;AAAA,IAGD,WAAW,CAAC,UAAU;AACpB,UAAI,MAAM,gBAAgB,WAAW;AAAG,eAAO;AAC/C,aAAO,KAAK,IAAI,GAAG,MAAM,gBAAgB,IAAI,YAAU,OAAO,UAAU,CAAC;AAAA,IAC1E;AAAA;AAAA,IAGD,iBAAiB,CAAC,UAAU;AAC1B,UAAI,MAAM,gBAAgB,SAAS;AAAG,eAAO;AAE7C,YAAM,SAAS,MAAM,gBAAgB,MAAM,GAAG,CAAC;AAC/C,YAAM,QAAQ,MAAM,gBAAgB,MAAM,GAAG,EAAE;AAE/C,UAAI,MAAM,WAAW;AAAG,eAAO;AAE/B,YAAM,YAAY,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,YAAY,CAAC,IAAI,OAAO;AAC5E,YAAM,WAAW,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,YAAY,CAAC,IAAI,MAAM;AAEzE,aAAO,KAAK,OAAQ,YAAY,YAAY,WAAY,GAAG;AAAA,IAC5D;AAAA,EACF;AACH;;"}