import { createSSRApp } from 'vue'
import App from './App.vue'
import store from './store'
import { getApiBaseUrl } from '@/config/api.js'
export function createApp() {
  const app = createSSRApp(App)

  // 使用Vuex状态管理
  app.use(store)

  // 全局错误处理
  app.config.errorHandler = (error, instance, info) => {
    console.error('Vue全局错误:', error, info)
  }
  


  const baseUrl = getApiBaseUrl()
  const isDev = baseUrl.includes('localhost')

  app.config.globalProperties.$baseUrl = baseUrl
  app.config.globalProperties.$isDev = isDev
  
  // 全局请求方法
  app.config.globalProperties.$request = (options) => {
    return new Promise((resolve, reject) => {
      const token = uni.getStorageSync('token')

      uni.request({
        url: app.config.globalProperties.$baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.success !== false) {
              resolve(res.data)
            } else {
              uni.showToast({
                title: res.data.message || '请求失败',
                icon: 'none'
              })
              reject(res.data)
            }
          } else if (res.statusCode === 401) {
            // token过期，跳转登录
            uni.removeStorageSync('token')
            uni.removeStorageSync('userInfo')
            uni.showToast({
              title: '登录已过期',
              icon: 'none'
            })
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/login/login'
              })
            }, 1000)
            reject(res)
          } else {
            uni.showToast({
              title: '网络错误',
              icon: 'none'
            })
            reject(res)
          }
        },
        fail: (err) => {
          uni.showToast({
            title: '网络连接失败',
            icon: 'none'
          })
          reject(err)
        }
      })
    })
  }
  
  // 全局工具方法
  app.config.globalProperties.$utils = {
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hour = date.getHours().toString().padStart(2, '0')
      const minute = date.getMinutes().toString().padStart(2, '0')
      return `${year}-${month}-${day} ${hour}:${minute}`
    },
    
    // 格式化日期
    formatDate(timestamp) {
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    
    // 防抖函数
    debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },
    
    // 节流函数
    throttle(func, limit) {
      let inThrottle
      return function() {
        const args = arguments
        const context = this
        if (!inThrottle) {
          func.apply(context, args)
          inThrottle = true
          setTimeout(() => inThrottle = false, limit)
        }
      }
    },
    
    // 深拷贝
    deepClone(obj) {
      if (obj === null || typeof obj !== 'object') return obj
      if (obj instanceof Date) return new Date(obj.getTime())
      if (obj instanceof Array) return obj.map(item => this.deepClone(item))
      if (typeof obj === 'object') {
        const clonedObj = {}
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            clonedObj[key] = this.deepClone(obj[key])
          }
        }
        return clonedObj
      }
    },
    
    // 存储管理
    setStorage(key, value) {
      try {
        uni.setStorageSync(key, JSON.stringify(value))
      } catch (e) {
        console.error('存储失败:', e)
      }
    },
    
    getStorage(key) {
      try {
        const value = uni.getStorageSync(key)
        return value ? JSON.parse(value) : null
      } catch (e) {
        console.error('读取存储失败:', e)
        return null
      }
    },
    
    removeStorage(key) {
      try {
        uni.removeStorageSync(key)
      } catch (e) {
        console.error('删除存储失败:', e)
      }
    },
    
    // 音频播放管理
    createAudioContext() {
      return uni.createInnerAudioContext()
    },
    
    // 录音管理
    createRecorderManager() {
      return uni.getRecorderManager()
    },
    
    // 权限检查
    checkPermission(scope) {
      return new Promise((resolve, reject) => {
        uni.getSetting({
          success: (res) => {
            if (res.authSetting[scope]) {
              resolve(true)
            } else {
              uni.authorize({
                scope,
                success: () => resolve(true),
                fail: () => reject(false)
              })
            }
          },
          fail: () => reject(false)
        })
      })
    }
  }
  
  return {
    app
  }
}
