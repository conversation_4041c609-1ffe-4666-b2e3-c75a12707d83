const user = {
  namespaced: true,
  
  state: {
    // 用户信息
    userInfo: {
      id: null,
      username: '',
      nickname: '',
      avatar: '',
      phone: '',
      email: '',
      level: 'beginner',
      totalStudyDays: 0,
      continuousDays: 0,
      maxContinuousDays: 0,
      totalScore: 0,
      vipStatus: 0,
      vipExpireTime: null
    },
    
    // 登录状态
    isLogin: false,
    token: '',
    
    // 学习统计
    studyStats: {
      todayProgress: 0,
      completedCourses: 0,
      totalCourses: 0,
      averageScore: 0,
      weeklyProgress: []
    }
  },
  
  mutations: {
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = { ...state.userInfo, ...userInfo }
    },
    
    // 设置登录状态
    SET_LOGIN_STATUS(state, isLogin) {
      state.isLogin = isLogin
    },
    
    // 设置token
    SET_TOKEN(state, token) {
      state.token = token
      if (token) {
        uni.setStorageSync('token', token)
      } else {
        uni.removeStorageSync('token')
      }
    },
    
    // 设置学习统计
    SET_STUDY_STATS(state, stats) {
      state.studyStats = { ...state.studyStats, ...stats }
    },
    
    // 更新连续打卡天数
    UPDATE_CONTINUOUS_DAYS(state, days) {
      state.userInfo.continuousDays = days
      if (days > state.userInfo.maxContinuousDays) {
        state.userInfo.maxContinuousDays = days
      }
    },
    
    // 更新总学习天数
    UPDATE_TOTAL_STUDY_DAYS(state, days) {
      state.userInfo.totalStudyDays = days
    },
    
    // 更新总积分
    UPDATE_TOTAL_SCORE(state, score) {
      state.userInfo.totalScore = score
    },
    
    // 清除用户数据
    CLEAR_USER_DATA(state) {
      state.userInfo = {
        id: null,
        username: '',
        nickname: '',
        avatar: '',
        phone: '',
        email: '',
        level: 'beginner',
        totalStudyDays: 0,
        continuousDays: 0,
        maxContinuousDays: 0,
        totalScore: 0,
        vipStatus: 0,
        vipExpireTime: null
      }
      state.isLogin = false
      state.token = ''
      state.studyStats = {
        todayProgress: 0,
        completedCourses: 0,
        totalCourses: 0,
        averageScore: 0,
        weeklyProgress: []
      }
    }
  },
  
  actions: {
    // 登录
    async login({ commit }, { username, password }) {
      try {
        // 这里应该调用实际的登录API
        const response = await uni.request({
          url: '/api/v1/auth/login',
          method: 'POST',
          data: { username, password }
        })
        
        if (response.data.code === 200) {
          const { token, userInfo } = response.data.data
          
          commit('SET_TOKEN', token)
          commit('SET_USER_INFO', userInfo)
          commit('SET_LOGIN_STATUS', true)
          
          // 保存用户信息到本地
          uni.setStorageSync('userInfo', userInfo)
          
          return { success: true, data: userInfo }
        } else {
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, message: '登录失败，请重试' }
      }
    },
    
    // 退出登录
    async logout({ commit }) {
      try {
        // 调用退出登录API
        await uni.request({
          url: '/api/v1/auth/logout',
          method: 'POST'
        })
      } catch (error) {
        console.error('退出登录API调用失败:', error)
      } finally {
        // 清除本地数据
        commit('CLEAR_USER_DATA')
        commit('SET_TOKEN', '')
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('token')
      }
    },
    
    // 检查登录状态
    async checkLoginStatus({ commit, dispatch }) {
      const token = uni.getStorageSync('token')
      const userInfo = uni.getStorageSync('userInfo')
      
      if (token && userInfo) {
        try {
          // 验证token有效性
          const response = await uni.request({
            url: '/api/v1/user/profile',
            method: 'GET',
            header: {
              'Authorization': `Bearer ${token}`
            }
          })
          
          if (response.data.code === 200) {
            commit('SET_TOKEN', token)
            commit('SET_USER_INFO', response.data.data)
            commit('SET_LOGIN_STATUS', true)
            
            // 加载学习统计
            await dispatch('loadStudyStats')
          } else {
            // token无效，清除数据
            await dispatch('logout')
          }
        } catch (error) {
          console.error('验证登录状态失败:', error)
          await dispatch('logout')
        }
      }
    },
    
    // 更新用户信息
    async updateUserInfo({ commit, state }, userInfo) {
      try {
        const response = await uni.request({
          url: '/api/v1/user/profile',
          method: 'PUT',
          data: userInfo,
          header: {
            'Authorization': `Bearer ${state.token}`
          }
        })
        
        if (response.data.code === 200) {
          commit('SET_USER_INFO', response.data.data)
          uni.setStorageSync('userInfo', response.data.data)
          return { success: true }
        } else {
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        console.error('更新用户信息失败:', error)
        return { success: false, message: '更新失败，请重试' }
      }
    },
    
    // 加载学习统计
    async loadStudyStats({ commit, state }) {
      try {
        const response = await uni.request({
          url: '/api/v1/study/statistics',
          method: 'GET',
          header: {
            'Authorization': `Bearer ${state.token}`
          }
        })
        
        if (response.data.code === 200) {
          commit('SET_STUDY_STATS', response.data.data)
        }
      } catch (error) {
        console.error('加载学习统计失败:', error)
      }
    },
    
    // 上传头像
    async uploadAvatar({ commit, state }, filePath) {
      try {
        const response = await uni.uploadFile({
          url: '/api/v1/user/avatar',
          filePath: filePath,
          name: 'avatar',
          header: {
            'Authorization': `Bearer ${state.token}`
          }
        })
        
        const data = JSON.parse(response.data)
        if (data.code === 200) {
          commit('SET_USER_INFO', { avatar: data.data.avatar })
          return { success: true, avatar: data.data.avatar }
        } else {
          return { success: false, message: data.message }
        }
      } catch (error) {
        console.error('上传头像失败:', error)
        return { success: false, message: '上传失败，请重试' }
      }
    }
  },
  
  getters: {
    // 是否为VIP用户
    isVip: (state) => {
      if (state.userInfo.vipStatus === 0) return false
      if (!state.userInfo.vipExpireTime) return false
      return new Date(state.userInfo.vipExpireTime) > new Date()
    },
    
    // 用户级别文本
    levelText: (state) => {
      const levelMap = {
        'beginner': '初级学员',
        'intermediate': '中级学员',
        'advanced': '高级学员',
        'professional': '专业学员'
      }
      return levelMap[state.userInfo.level] || '初级学员'
    },
    
    // 学习进度百分比
    studyProgressPercent: (state) => {
      if (state.studyStats.totalCourses === 0) return 0
      return Math.round((state.studyStats.completedCourses / state.studyStats.totalCourses) * 100)
    }
  }
}

export default user
