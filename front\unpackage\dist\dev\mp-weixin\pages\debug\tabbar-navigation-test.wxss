/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.tabbar-test-container.data-v-8392dee8 {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
}
.test-header.data-v-8392dee8 {
  text-align: center;
  margin-bottom: 40rpx;
}
.test-header .test-title.data-v-8392dee8 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-8392dee8 {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.test-section .section-title.data-v-8392dee8 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 16rpx;
}
.tabbar-list .tabbar-item.data-v-8392dee8, .tabbar-list .normal-item.data-v-8392dee8, .normal-list .tabbar-item.data-v-8392dee8, .normal-list .normal-item.data-v-8392dee8 {
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
}
.tabbar-list .tabbar-item .page-name.data-v-8392dee8, .tabbar-list .normal-item .page-name.data-v-8392dee8, .normal-list .tabbar-item .page-name.data-v-8392dee8, .normal-list .normal-item .page-name.data-v-8392dee8 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.tabbar-list .tabbar-item .page-path.data-v-8392dee8, .tabbar-list .normal-item .page-path.data-v-8392dee8, .normal-list .tabbar-item .page-path.data-v-8392dee8, .normal-list .normal-item .page-path.data-v-8392dee8 {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-family: monospace;
}
.tabbar-list .tabbar-item .page-actions.data-v-8392dee8, .tabbar-list .normal-item .page-actions.data-v-8392dee8, .normal-list .tabbar-item .page-actions.data-v-8392dee8, .normal-list .normal-item .page-actions.data-v-8392dee8 {
  display: flex;
  gap: 12rpx;
}
.tabbar-list .tabbar-item .page-actions .action-btn.data-v-8392dee8, .tabbar-list .normal-item .page-actions .action-btn.data-v-8392dee8, .normal-list .tabbar-item .page-actions .action-btn.data-v-8392dee8, .normal-list .normal-item .page-actions .action-btn.data-v-8392dee8 {
  flex: 1;
  height: 60rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: white;
}
.tabbar-list .tabbar-item .page-actions .action-btn.primary.data-v-8392dee8, .tabbar-list .normal-item .page-actions .action-btn.primary.data-v-8392dee8, .normal-list .tabbar-item .page-actions .action-btn.primary.data-v-8392dee8, .normal-list .normal-item .page-actions .action-btn.primary.data-v-8392dee8 {
  background: #1890ff;
}
.tabbar-list .tabbar-item .page-actions .action-btn.success.data-v-8392dee8, .tabbar-list .normal-item .page-actions .action-btn.success.data-v-8392dee8, .normal-list .tabbar-item .page-actions .action-btn.success.data-v-8392dee8, .normal-list .normal-item .page-actions .action-btn.success.data-v-8392dee8 {
  background: #52c41a;
}
.tabbar-list .tabbar-item .page-actions .action-btn.error.data-v-8392dee8, .tabbar-list .normal-item .page-actions .action-btn.error.data-v-8392dee8, .normal-list .tabbar-item .page-actions .action-btn.error.data-v-8392dee8, .normal-list .normal-item .page-actions .action-btn.error.data-v-8392dee8 {
  background: #ff4d4f;
}
.tabbar-list .tabbar-item .page-actions .action-btn.data-v-8392dee8:active, .tabbar-list .normal-item .page-actions .action-btn.data-v-8392dee8:active, .normal-list .tabbar-item .page-actions .action-btn.data-v-8392dee8:active, .normal-list .normal-item .page-actions .action-btn.data-v-8392dee8:active {
  opacity: 0.8;
}
.log-container.data-v-8392dee8 {
  height: 300rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 16rpx;
}
.log-container .log-item.data-v-8392dee8 {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 24rpx;
}
.log-container .log-item .log-time.data-v-8392dee8 {
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
}
.log-container .log-item .log-message.data-v-8392dee8 {
  color: #333;
  word-break: break-all;
}
.log-container .log-item .log-message.success.data-v-8392dee8 {
  color: #52c41a;
}
.log-container .log-item .log-message.error.data-v-8392dee8 {
  color: #ff4d4f;
}
.log-container .log-item .log-message.warning.data-v-8392dee8 {
  color: #faad14;
}
.log-container .log-item .log-message.info.data-v-8392dee8 {
  color: #1890ff;
}
.usage-info .usage-title.data-v-8392dee8 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin: 16rpx 0 8rpx 0;
}
.usage-info .usage-title.error.data-v-8392dee8 {
  color: #ff4d4f;
}
.usage-info .usage-desc.data-v-8392dee8 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  padding-left: 16rpx;
}