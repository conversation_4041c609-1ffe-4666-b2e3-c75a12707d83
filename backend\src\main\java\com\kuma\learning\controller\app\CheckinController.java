package com.kuma.learning.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuma.learning.entity.KumaCheckinRecord;
import com.kuma.learning.entity.KumaUserProfile;
import com.kuma.learning.service.IKumaCheckinRecordService;
import com.kuma.learning.service.IKumaUserProfileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "APP-打卡管理")
@RestController
@RequestMapping("/app/checkin")
public class CheckinController {

    @Autowired
    private IKumaCheckinRecordService checkinRecordService;
    
    @Autowired
    private IKumaUserProfileService userProfileService;

    @AutoLog(value = "打卡-每日打卡")
    @ApiOperation(value = "打卡-每日打卡", notes = "打卡-每日打卡")
    @PostMapping("/daily")
    public Result<Map<String, Object>> dailyCheckin(@RequestBody Map<String, Object> params) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        
        // 检查今日是否已打卡
        LocalDate today = LocalDate.now();
        Date todayDate = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        LambdaQueryWrapper<KumaCheckinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCheckinRecord::getUserId, loginUser.getId())
                   .eq(KumaCheckinRecord::getCheckinDate, todayDate);
        
        KumaCheckinRecord existRecord = checkinRecordService.getOne(queryWrapper);
        if (existRecord != null) {
            return Result.error("今日已打卡");
        }
        
        // 获取用户信息
        LambdaQueryWrapper<KumaUserProfile> userQuery = new LambdaQueryWrapper<>();
        userQuery.eq(KumaUserProfile::getUserId, loginUser.getId());
        KumaUserProfile userProfile = userProfileService.getOne(userQuery);
        
        if (userProfile == null) {
            return Result.error("用户信息不存在");
        }
        
        // 计算连续打卡天数
        int continuousDays = calculateContinuousDays(loginUser.getId());
        
        // 创建打卡记录
        KumaCheckinRecord checkinRecord = new KumaCheckinRecord();
        checkinRecord.setUserId(loginUser.getId());
        checkinRecord.setCheckinDate(todayDate);
        checkinRecord.setCoursesCompleted((Integer) params.getOrDefault("coursesCompleted", 0));
        checkinRecord.setStudyDuration((Integer) params.getOrDefault("studyDuration", 0));
        checkinRecord.setIsMakeup(0);
        checkinRecord.setContinuousDays(continuousDays);
        checkinRecord.setSysSign("KUMA");
        
        checkinRecordService.save(checkinRecord);
        
        // 更新用户信息
        userProfile.setContinuousDays(continuousDays);
        userProfile.setTotalStudyDays(userProfile.getTotalStudyDays() + 1);
        if (continuousDays > userProfile.getMaxContinuousDays()) {
            userProfile.setMaxContinuousDays(continuousDays);
        }
        userProfile.setTotalScore(userProfile.getTotalScore() + 10); // 打卡奖励10积分
        userProfileService.updateById(userProfile);
        
        Map<String, Object> result = new HashMap<>();
        result.put("continuousDays", continuousDays);
        result.put("totalScore", userProfile.getTotalScore());
        result.put("checkinRecord", checkinRecord);
        
        return Result.OK(result);
    }

    @AutoLog(value = "打卡-获取打卡记录")
    @ApiOperation(value = "打卡-获取打卡记录", notes = "打卡-获取打卡记录")
    @GetMapping("/records")
    public Result<IPage<KumaCheckinRecord>> getCheckinRecords(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "year", required = false) Integer year,
            @RequestParam(name = "month", required = false) Integer month) {
        
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        
        Page<KumaCheckinRecord> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<KumaCheckinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCheckinRecord::getUserId, loginUser.getId());
        
        // 如果指定了年月，则按年月查询
        if (year != null && month != null) {
            LocalDate startDate = LocalDate.of(year, month, 1);
            LocalDate endDate = startDate.plusMonths(1).minusDays(1);
            
            Date startDateTime = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date endDateTime = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            
            queryWrapper.between(KumaCheckinRecord::getCheckinDate, startDateTime, endDateTime);
        }
        
        queryWrapper.orderByDesc(KumaCheckinRecord::getCheckinDate);
        
        IPage<KumaCheckinRecord> pageList = checkinRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog(value = "打卡-获取打卡统计")
    @ApiOperation(value = "打卡-获取打卡统计", notes = "打卡-获取打卡统计")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getCheckinStatistics() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            return Result.error("用户未登录");
        }
        
        // 获取用户信息
        LambdaQueryWrapper<KumaUserProfile> userQuery = new LambdaQueryWrapper<>();
        userQuery.eq(KumaUserProfile::getUserId, loginUser.getId());
        KumaUserProfile userProfile = userProfileService.getOne(userQuery);
        
        if (userProfile == null) {
            return Result.error("用户信息不存在");
        }
        
        // 获取本月打卡记录
        LocalDate now = LocalDate.now();
        LocalDate startOfMonth = now.withDayOfMonth(1);
        LocalDate endOfMonth = now.withDayOfMonth(now.lengthOfMonth());
        
        Date startDate = Date.from(startOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        LambdaQueryWrapper<KumaCheckinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCheckinRecord::getUserId, loginUser.getId())
                   .between(KumaCheckinRecord::getCheckinDate, startDate, endDate);
        
        List<KumaCheckinRecord> monthlyRecords = checkinRecordService.list(queryWrapper);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalStudyDays", userProfile.getTotalStudyDays());
        statistics.put("continuousDays", userProfile.getContinuousDays());
        statistics.put("maxContinuousDays", userProfile.getMaxContinuousDays());
        statistics.put("thisMonthDays", monthlyRecords.size());
        statistics.put("monthlyRecords", monthlyRecords);
        
        return Result.OK(statistics);
    }

    /**
     * 计算连续打卡天数
     */
    private int calculateContinuousDays(String userId) {
        LocalDate today = LocalDate.now();
        int continuousDays = 1;
        
        // 查询最近30天的打卡记录
        LocalDate startDate = today.minusDays(30);
        Date startDateTime = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDateTime = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        LambdaQueryWrapper<KumaCheckinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCheckinRecord::getUserId, userId)
                   .between(KumaCheckinRecord::getCheckinDate, startDateTime, endDateTime)
                   .orderByDesc(KumaCheckinRecord::getCheckinDate);
        
        List<KumaCheckinRecord> records = checkinRecordService.list(queryWrapper);
        
        // 如果没有记录，返回1（今天打卡）
        if (records.isEmpty()) {
            return 1;
        }
        
        // 检查是否连续
        LocalDate lastCheckinDate = records.get(0).getCheckinDate().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();
        
        if (lastCheckinDate.equals(today.minusDays(1))) {
            // 如果昨天打卡了，继续检查之前的记录
            for (int i = 1; i < records.size(); i++) {
                LocalDate currentDate = records.get(i).getCheckinDate().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate();
                
                if (currentDate.equals(lastCheckinDate.minusDays(1))) {
                    continuousDays++;
                    lastCheckinDate = currentDate;
                } else {
                    break;
                }
            }
        }
        
        return continuousDays;
    }
}
