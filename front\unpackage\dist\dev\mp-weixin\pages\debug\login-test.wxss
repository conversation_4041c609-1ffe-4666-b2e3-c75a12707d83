/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.login-test-container.data-v-6bd25c5c {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
}
.test-header.data-v-6bd25c5c {
  text-align: center;
  margin-bottom: 40rpx;
}
.test-header .test-title.data-v-6bd25c5c {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-6bd25c5c {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.test-section .section-title.data-v-6bd25c5c {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 16rpx;
}
.status-item.data-v-6bd25c5c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.status-item.data-v-6bd25c5c:last-child {
  border-bottom: none;
}
.status-item .status-label.data-v-6bd25c5c {
  font-size: 28rpx;
  color: #666;
}
.status-item .status-value.data-v-6bd25c5c {
  font-size: 28rpx;
  color: #333;
}
.status-item .status-value.success.data-v-6bd25c5c {
  color: #52c41a;
}
.status-item .status-value.error.data-v-6bd25c5c {
  color: #ff4d4f;
}
.status-item .status-value.token.data-v-6bd25c5c {
  font-family: monospace;
  font-size: 24rpx;
  max-width: 300rpx;
  overflow: hidden;
}
.headers-display .header-item.data-v-6bd25c5c {
  display: flex;
  margin-bottom: 16rpx;
}
.headers-display .header-item .header-key.data-v-6bd25c5c {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
  flex-shrink: 0;
}
.headers-display .header-item .header-value.data-v-6bd25c5c {
  font-size: 28rpx;
  color: #333;
  font-family: monospace;
  word-break: break-all;
}
.captcha-test .captcha-display.data-v-6bd25c5c {
  text-align: center;
  margin-bottom: 24rpx;
}
.captcha-test .captcha-display .test-captcha-img.data-v-6bd25c5c {
  width: 200rpx;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
}
.captcha-test .captcha-display .no-captcha.data-v-6bd25c5c {
  display: inline-block;
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #f5f5f5;
  color: #999;
  font-size: 24rpx;
  border-radius: 8rpx;
}
.captcha-test .captcha-actions.data-v-6bd25c5c {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  justify-content: center;
}
.test-buttons.data-v-6bd25c5c {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.test-buttons .test-btn.data-v-6bd25c5c {
  height: 80rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.test-buttons .test-btn.small.data-v-6bd25c5c {
  height: 60rpx;
  font-size: 24rpx;
}
.test-buttons .test-btn.danger.data-v-6bd25c5c {
  background: #ff4d4f;
}
.test-buttons .test-btn.data-v-6bd25c5c:active {
  opacity: 0.8;
}
.log-container.data-v-6bd25c5c {
  height: 400rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 16rpx;
}
.log-container .log-item.data-v-6bd25c5c {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 24rpx;
}
.log-container .log-item .log-time.data-v-6bd25c5c {
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
}
.log-container .log-item .log-message.data-v-6bd25c5c {
  color: #333;
}
.log-container .log-item .log-message.success.data-v-6bd25c5c {
  color: #52c41a;
}
.log-container .log-item .log-message.error.data-v-6bd25c5c {
  color: #ff4d4f;
}
.log-container .log-item .log-message.warning.data-v-6bd25c5c {
  color: #faad14;
}
.log-container .log-item .log-message.info.data-v-6bd25c5c {
  color: #1890ff;
}