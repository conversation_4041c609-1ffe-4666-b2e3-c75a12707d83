/**
 * 页面通用混入
 * 为小程序页面提供统一的基础功能
 */

export default {
  data() {
    return {
      // 页面状态
      loading: false,
      refreshing: false,
      
      // 系统信息
      statusBarHeight: 0,
      navBarHeight: 0,
      windowHeight: 0,
      windowWidth: 0,
      
      // 用户信息
      userInfo: null,
      isLoggedIn: false,
      
      // 网络状态
      isConnected: true,
      
      // 错误处理
      hasError: false,
      errorMessage: ''
    }
  },
  
  onLoad(options) {
    const route = this.getPageRoute()
    console.log('📄 页面加载:', route, options)

    // 初始化页面
    this.initPage(options)
  },

  onShow() {
    const route = this.getPageRoute()
    console.log('👁️ 页面显示:', route)

    // 检查登录状态
    this.checkLoginStatus()

    // 检查网络状态
    this.checkNetworkStatus()
  },

  onHide() {
    const route = this.getPageRoute()
    console.log('🙈 页面隐藏:', route)
  },

  onUnload() {
    const route = this.getPageRoute()
    console.log('🗑️ 页面卸载:', route)

    // 清理资源
    this.cleanup()
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    console.log('🔄 下拉刷新')
    this.handleRefresh()
  },
  
  // 上拉加载更多
  onReachBottom() {
    console.log('📄 上拉加载更多')
    this.handleLoadMore()
  },
  
  // 页面滚动
  onPageScroll(e) {
    // 可在子页面中重写
  },
  
  // 分享
  onShareAppMessage() {
    return {
      title: '口语便利店 - 日语学习打卡',
      path: '/pages/index/index',
      imageUrl: '/static/images/share-cover.jpg'
    }
  },
  
  // 分享到朋友圈（微信小程序）
  onShareTimeline() {
    return {
      title: '口语便利店 - 日语学习打卡',
      query: 'from=timeline',
      imageUrl: '/static/images/share-cover.jpg'
    }
  },
  
  methods: {
    /**
     * 安全获取页面路由
     */
    getPageRoute() {
      try {
        // 尝试从$mp.page获取
        if (this.$mp && this.$mp.page && this.$mp.page.route) {
          return this.$mp.page.route
        }

        // 尝试从getCurrentPages获取
        const pages = getCurrentPages()
        if (pages && pages.length > 0) {
          const currentPage = pages[pages.length - 1]
          return currentPage.route || 'unknown'
        }

        // 兜底返回
        return 'unknown'
      } catch (error) {
        console.warn('获取页面路由失败:', error)
        return 'unknown'
      }
    },

    /**
     * 初始化页面
     */
    initPage(options = {}) {
      try {
        // 获取系统信息
        this.getSystemInfo()

        // 获取用户信息
        this.getUserInfo()

        // 页面特定初始化（子页面可重写）
        if (this.pageInit && typeof this.pageInit === 'function') {
          this.pageInit(options)
        }

      } catch (error) {
        console.error('❌ 页面初始化失败:', error)
        this.handleError(error)
      }
    },
    
    /**
     * 获取系统信息
     */
    getSystemInfo() {
      try {
        const app = getApp()
        if (app && app.globalData) {
          this.statusBarHeight = app.globalData.statusBarHeight || 0
          this.navBarHeight = app.globalData.navBarHeight || 44
          this.windowHeight = app.globalData.windowHeight || 0
          this.windowWidth = app.globalData.windowWidth || 0
        } else {
          // 备用方案
          const systemInfo = uni.getSystemInfoSync()
          this.statusBarHeight = systemInfo.statusBarHeight || 0
          this.navBarHeight = (systemInfo.statusBarHeight || 0) + 44
          this.windowHeight = systemInfo.windowHeight || 0
          this.windowWidth = systemInfo.windowWidth || 0
        }
      } catch (error) {
        console.error('❌ 获取系统信息失败:', error)
      }
    },
    
    /**
     * 获取用户信息
     */
    getUserInfo() {
      try {
        const app = getApp()
        if (app && app.globalData) {
          this.userInfo = app.globalData.userInfo
          this.isLoggedIn = app.globalData.isLoggedIn || false
        } else {
          // 从本地存储获取
          this.userInfo = uni.getStorageSync('userInfo')
          this.isLoggedIn = !!uni.getStorageSync('token')
        }
      } catch (error) {
        console.error('❌ 获取用户信息失败:', error)
      }
    },
    
    /**
     * 检查登录状态
     */
    checkLoginStatus() {
      const token = uni.getStorageSync('token')
      const userInfo = uni.getStorageSync('userInfo')
      
      this.isLoggedIn = !!(token && userInfo)
      
      if (this.isLoggedIn) {
        this.userInfo = userInfo
      }
    },
    
    /**
     * 检查网络状态
     */
    checkNetworkStatus() {
      uni.getNetworkType({
        success: (res) => {
          this.isConnected = res.networkType !== 'none'
          
          if (!this.isConnected) {
            this.showToast('网络连接已断开', 'none')
          }
        },
        fail: () => {
          console.warn('⚠️ 获取网络状态失败')
        }
      })
    },
    
    /**
     * 处理刷新
     */
    async handleRefresh() {
      if (this.refreshing) return
      
      this.refreshing = true
      
      try {
        // 子页面可重写此方法
        if (this.onRefresh && typeof this.onRefresh === 'function') {
          await this.onRefresh()
        }
      } catch (error) {
        console.error('❌ 刷新失败:', error)
        this.handleError(error)
      } finally {
        this.refreshing = false
        uni.stopPullDownRefresh()
      }
    },
    
    /**
     * 处理加载更多
     */
    async handleLoadMore() {
      try {
        // 子页面可重写此方法
        if (this.onLoadMore && typeof this.onLoadMore === 'function') {
          await this.onLoadMore()
        }
      } catch (error) {
        console.error('❌ 加载更多失败:', error)
        this.handleError(error)
      }
    },
    
    /**
     * 显示加载状态
     */
    showLoading(title = '加载中...') {
      this.loading = true
      uni.showLoading({
        title,
        mask: true
      })
    },
    
    /**
     * 隐藏加载状态
     */
    hideLoading() {
      this.loading = false
      uni.hideLoading()
    },
    
    /**
     * 显示提示信息
     */
    showToast(title, icon = 'none', duration = 2000) {
      uni.showToast({
        title,
        icon,
        duration
      })
    },
    
    /**
     * 显示确认对话框
     */
    showConfirm(content, title = '提示') {
      return new Promise((resolve) => {
        uni.showModal({
          title,
          content,
          success: (res) => {
            resolve(res.confirm)
          },
          fail: () => {
            resolve(false)
          }
        })
      })
    },
    
    /**
     * 错误处理
     */
    handleError(error, showToast = true) {
      console.error('❌ 页面错误:', error)
      
      this.hasError = true
      this.errorMessage = error.message || '发生未知错误'
      
      // 记录错误到全局状态
      const app = getApp()
      if (app && app.addError) {
        app.addError({
          type: 'page_error',
          page: this.$mp.page.route,
          message: this.errorMessage,
          stack: error.stack,
          timestamp: Date.now()
        })
      }
      
      // 显示错误提示
      if (showToast) {
        this.showToast(this.errorMessage)
      }
    },
    
    /**
     * 清理资源
     */
    cleanup() {
      // 子页面可重写此方法进行资源清理
      if (this.pageCleanup && typeof this.pageCleanup === 'function') {
        this.pageCleanup()
      }
    },
    
    /**
     * 跳转到登录页
     */
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },
    
    /**
     * 检查登录状态，未登录则跳转
     */
    requireLogin() {
      if (!this.isLoggedIn) {
        this.showToast('请先登录')
        setTimeout(() => {
          this.goToLogin()
        }, 1500)
        return false
      }
      return true
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
      if (!timestamp) return ''
      
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      
      return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds)
    }
  }
}
