package com.learning.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import com.learning.entity.KumaCourseTagRelation;
import com.learning.service.IKumaCourseTagRelationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: kuma_course_tag_relation
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Api(tags="kuma_course_tag_relation")
@RestController
@RequestMapping("/learning/kumaCourseTagRelation")
@Slf4j
public class KumaCourseTagRelationController extends JeecgController<KumaCourseTagRelation, IKumaCourseTagRelationService> {
	@Autowired
	private IKumaCourseTagRelationService kumaCourseTagRelationService;
	
	/**
	 * 分页列表查询
	 *
	 * @param kumaCourseTagRelation
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "kuma_course_tag_relation-分页列表查询")
	@ApiOperation(value="kuma_course_tag_relation-分页列表查询", notes="kuma_course_tag_relation-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<KumaCourseTagRelation>> queryPageList(KumaCourseTagRelation kumaCourseTagRelation,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<KumaCourseTagRelation> queryWrapper = QueryGenerator.initQueryWrapper(kumaCourseTagRelation, req.getParameterMap());
		Page<KumaCourseTagRelation> page = new Page<KumaCourseTagRelation>(pageNo, pageSize);
		IPage<KumaCourseTagRelation> pageList = kumaCourseTagRelationService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param kumaCourseTagRelation
	 * @return
	 */
	@AutoLog(value = "kuma_course_tag_relation-添加")
	@ApiOperation(value="kuma_course_tag_relation-添加", notes="kuma_course_tag_relation-添加")
	@RequiresPermissions("learning:kuma_course_tag_relation:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody KumaCourseTagRelation kumaCourseTagRelation) {
		kumaCourseTagRelationService.save(kumaCourseTagRelation);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param kumaCourseTagRelation
	 * @return
	 */
	@AutoLog(value = "kuma_course_tag_relation-编辑")
	@ApiOperation(value="kuma_course_tag_relation-编辑", notes="kuma_course_tag_relation-编辑")
	@RequiresPermissions("learning:kuma_course_tag_relation:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody KumaCourseTagRelation kumaCourseTagRelation) {
		kumaCourseTagRelationService.updateById(kumaCourseTagRelation);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "kuma_course_tag_relation-通过id删除")
	@ApiOperation(value="kuma_course_tag_relation-通过id删除", notes="kuma_course_tag_relation-通过id删除")
	@RequiresPermissions("learning:kuma_course_tag_relation:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		kumaCourseTagRelationService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "kuma_course_tag_relation-批量删除")
	@ApiOperation(value="kuma_course_tag_relation-批量删除", notes="kuma_course_tag_relation-批量删除")
	@RequiresPermissions("learning:kuma_course_tag_relation:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.kumaCourseTagRelationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "kuma_course_tag_relation-通过id查询")
	@ApiOperation(value="kuma_course_tag_relation-通过id查询", notes="kuma_course_tag_relation-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<KumaCourseTagRelation> queryById(@RequestParam(name="id",required=true) String id) {
		KumaCourseTagRelation kumaCourseTagRelation = kumaCourseTagRelationService.getById(id);
		if(kumaCourseTagRelation==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(kumaCourseTagRelation);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param kumaCourseTagRelation
    */
    @RequiresPermissions("learning:kuma_course_tag_relation:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, KumaCourseTagRelation kumaCourseTagRelation) {
        return super.exportXls(request, kumaCourseTagRelation, KumaCourseTagRelation.class, "kuma_course_tag_relation");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("learning:kuma_course_tag_relation:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, KumaCourseTagRelation.class);
    }

}
