<template>
	<view class="page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 自定义导航栏 -->
		<view class="nav-bar">
			<view class="nav-content">
				<view class="nav-left" @click="goBack">
					<text class="iconfont icon-arrow-left"></text>
				</view>
				<view class="nav-title">{{ pageTitle }}</view>
				<view class="nav-right"></view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="content">
			<!-- 学习进度统计 -->
			<view class="progress-section">
				<view class="progress-card card">
					<view class="flex-between mb-3">
						<view>
							<text class="progress-title">学习进度</text>
							<text class="progress-desc">已完成 {{ completedCount }}/{{ totalCount }} 课程</text>
						</view>
						<view class="progress-percent">
							<text class="percent-text">{{ progressPercent }}%</text>
							<text class="percent-label">完成率</text>
						</view>
					</view>
					<view class="progress-bar">
						<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
					</view>
				</view>
			</view>

			<!-- 课程列表 -->
			<view class="course-list">
				<view 
					class="course-item" 
					v-for="course in courseList" 
					:key="course.id"
					@click="goToCourseDetail(course)"
				>
					<view class="course-card" :class="{ 'premium': !course.isFree, 'current': course.isCurrent }">
						<view class="flex-between mb-3">
							<view class="flex">
								<view class="course-icon" :style="{ backgroundColor: course.cover }">
									<text class="iconfont icon-play" :style="{ color: course.iconColor }"></text>
								</view>
								<view class="course-info">
									<text class="course-title">{{ course.title }}</text>
									<text class="course-desc">{{ course.description }}</text>
								</view>
							</view>
							<view class="course-status">
								<view v-if="course.isFree" class="free-badge">免费</view>
								<view v-else class="price-badge">¥{{ course.price }}</view>
								<text class="status-icon iconfont" :class="getStatusIcon(course)"></text>
							</view>
						</view>
						
						<view class="flex-between course-meta mb-2">
							<text class="meta-item">
								<text class="iconfont icon-clock"></text>
								{{ course.duration }}分钟
							</text>
							<text class="meta-item">
								<text class="iconfont icon-headphones"></text>
								{{ getStatusText(course) }}
							</text>
						</view>
						
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: course.progress + '%' }"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { courseApi } from '@/api/index.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			pageTitle: '',
			level: '',
			loading: true,
			completedCount: 0,
			totalCount: 0,
			progressPercent: 0,
			courseList: [],
			// 默认课程数据（加载失败时使用）
			defaultCourseList: [
				{
					id: 1,
					title: '第1课：自己紹介',
					description: '自我介绍基础对话',
					duration: 5,
					isFree: true,
					price: 0,
					progress: 100,
					status: 'completed',
					iconBg: '#dcfce7',
					iconColor: '#16a34a',
					isCurrent: false
				},
				{
					id: 2,
					title: '第2课：挨拶',
					description: '日常问候用语',
					duration: 4,
					isFree: true,
					price: 0,
					progress: 100,
					status: 'completed',
					iconBg: '#dbeafe',
					iconColor: '#2563eb',
					isCurrent: false
				},
				{
					id: 3,
					title: '第3课：買い物',
					description: '购物场景对话',
					duration: 6,
					isFree: false,
					price: 9.9,
					progress: 0,
					status: 'locked',
					iconBg: '#fed7aa',
					iconColor: '#ea580c',
					isCurrent: false
				},
				{
					id: 4,
					title: '第4课：レストラン',
					description: '餐厅点餐对话',
					duration: 7,
					isFree: true,
					price: 0,
					progress: 45,
					status: 'learning',
					iconBg: '#fce7f3',
					iconColor: '#ec4899',
					isCurrent: true
				},
				{
					id: 5,
					title: '第5课：交通',
					description: '交通出行用语',
					duration: 5,
					isFree: true,
					price: 0,
					progress: 0,
					status: 'locked',
					iconBg: '#f3f4f6',
					iconColor: '#6b7280',
					isCurrent: false
				}
			]
		}
	},
	
	onLoad(options) {
		// 优先从URL参数获取，然后从存储获取
		this.level = options.level || 'beginner'
		this.pageTitle = decodeURIComponent(options.title || '课程列表')

		// 检查是否有存储的级别信息（从首页跳转过来）
		this.checkStoredLevelInfo()

		this.initPage()
	},

	onShow() {
		// 每次显示时检查存储的级别信息
		this.checkStoredLevelInfo()
		this.loadCourseList()
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.loadCourseList().finally(() => {
			uni.stopPullDownRefresh()
		})
	},

	methods: {
		initPage() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight
		},

		// 检查存储的级别信息
		checkStoredLevelInfo() {
			try {
				const storedLevel = uni.getStorageSync('selectedCourseLevel')

				if (storedLevel && storedLevel.timestamp) {
					// 检查时间戳，避免使用过期数据（5分钟内有效）
					const now = Date.now()
					const timeDiff = now - storedLevel.timestamp

					if (timeDiff < 5 * 60 * 1000) { // 5分钟内有效
						this.level = storedLevel.level || this.level
						this.pageTitle = storedLevel.title || this.pageTitle

						console.log('✅ 使用存储的级别信息:', {
							level: this.level,
							title: this.pageTitle
						})

						// 使用后清除存储，避免重复使用
						uni.removeStorageSync('selectedCourseLevel')
					} else {
						console.log('⚠️ 存储的级别信息已过期，清除')
						uni.removeStorageSync('selectedCourseLevel')
					}
				}
			} catch (error) {
				console.warn('检查存储级别信息失败:', error)
			}
		},

		// 加载课程列表
		async loadCourseList() {
			this.loading = true

			try {
				console.log('加载课程列表:', this.level)

				const response = await courseApi.getCourseList({
					level: this.level,
					includeProgress: true
				})

				if (response.success && response.data.records) {
					this.courseList = this.formatCourseData(response.data.records)
					this.calculateProgress()
				} else {
					throw new Error(response.message || '获取课程列表失败')
				}

			} catch (error) {
				console.error('加载课程列表失败:', error)

				// // 使用默认数据
				// this.courseList = this.defaultCourseList
				// this.calculateProgress()

				// // 显示错误提示
				// uni.showToast({
				// 	title: '加载失败，显示离线数据',
				// 	icon: 'none'
				// })
			} finally {
				this.loading = false
			}
		},

		// 格式化课程数据
		formatCourseData(courseData) {
			return courseData.map(course => ({
				id: course.id,
				title: course.title,
				description: course.description || course.subtitle,
				duration: course.duration || 5,
				isFree: course.isFree !== false,
				price: course.price || 0,
				progress: course.progress || 0,
				status: this.getCourseStatus(course),
				iconBg: this.getIconBackground(course.level),
				iconColor: this.getIconColor(course.level),
				isCurrent: course.isCurrent || false,
				lessonCount: course.lessonCount || 0,
				difficulty: course.difficulty || 'beginner'
			}))
		},

		// 计算学习进度
		calculateProgress() {
			this.totalCount = this.courseList.length
			this.completedCount = this.courseList.filter(course => course.status === 'completed').length
			this.progressPercent = this.totalCount > 0 ? Math.round((this.completedCount / this.totalCount) * 100) : 0
		},

		// 获取课程状态
		getCourseStatus(course) {
			if (course.isCompleted) return 'completed'
			if (course.progress > 0) return 'learning'
			if (course.isLocked) return 'locked'
			return 'available'
		},

		// 获取图标背景色
		getIconBackground(level) {
			const colors = {
				beginner: '#dcfce7',
				intermediate: '#dbeafe',
				advanced: '#f3e8ff',
				professional: '#fed7aa'
			}
			return colors[level] || '#f3f4f6'
		},

		// 获取图标颜色
		getIconColor(level) {
			const colors = {
				beginner: '#16a34a',
				intermediate: '#2563eb',
				advanced: '#9333ea',
				professional: '#ea580c'
			}
			return colors[level] || '#6b7280'
		},
		
		goBack() {
			// 由于这是tabBar页面，返回按钮应该跳转到首页
			uni.switchTab({
				url: '/pages/index/index'
			})
		},
		
		goToCourseDetail(course) {
			if (course.status === 'locked' && !course.isFree) {
				this.showPurchaseDialog(course)
				return
			}
			
			if (course.status === 'locked') {
				uni.showToast({
					title: '请先完成前面的课程',
					icon: 'none'
				})
				return
			}
			
			uni.navigateTo({
				url: `/pages/course/detail?id=${course.id}&title=${course.title}`
			})
		},
		
		showPurchaseDialog(course) {
			uni.showModal({
				title: '购买课程',
				content: `该课程需要支付 ¥${course.price} 元，是否立即购买？`,
				confirmText: '立即购买',
				success: (res) => {
					if (res.confirm) {
						this.purchaseCourse(course)
					}
				}
			})
		},
		
		purchaseCourse(course) {
			// 调用支付接口
			console.log('购买课程:', course.id)
			uni.showToast({
				title: '支付功能开发中',
				icon: 'none'
			})
		},
		
		getStatusIcon(course) {
			switch (course.status) {
				case 'completed':
					return 'icon-check-circle'
				case 'learning':
					return 'icon-play-circle'
				case 'locked':
					return course.isFree ? 'icon-lock' : 'icon-lock'
				default:
					return 'icon-lock'
			}
		},
		
		getStatusText(course) {
			switch (course.status) {
				case 'completed':
					return '已完成'
				case 'learning':
					return '进行中'
				case 'locked':
					return course.isFree ? '完成前课解锁' : '高质量内容'
				default:
					return '未开始'
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.status-bar {
	background: transparent;
}

.nav-bar {
	background: transparent;
	
	.nav-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		padding: 0 32rpx;
		
		.nav-left, .nav-right {
			width: 88rpx;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.nav-left {
			.iconfont {
				color: white;
				font-size: 40rpx;
			}
		}
		
		.nav-title {
			flex: 1;
			text-align: center;
			color: white;
			font-size: 36rpx;
			font-weight: bold;
		}
	}
}

.content {
	background: #f8fafc;
	border-radius: 48rpx 48rpx 0 0;
	min-height: calc(100vh - var(--status-bar-height) - 88rpx);
	padding-bottom: 200rpx;
}

.progress-section {
	padding: 48rpx 32rpx;
	
	.progress-card {
		.progress-title {
			display: block;
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 8rpx;
		}
		
		.progress-desc {
			color: #666;
			font-size: 28rpx;
		}
		
		.progress-percent {
			text-align: right;
			
			.percent-text {
				display: block;
				font-size: 48rpx;
				font-weight: bold;
				color: #f5576c;
				line-height: 1;
			}
			
			.percent-label {
				color: #666;
				font-size: 24rpx;
			}
		}
		
		.progress-bar {
			width: 100%;
			height: 12rpx;
			background: #e5e7eb;
			border-radius: 6rpx;
			overflow: hidden;
			
			.progress-fill {
				height: 100%;
				background: linear-gradient(90deg, #10b981, #34d399);
				border-radius: 6rpx;
				transition: width 0.3s ease;
			}
		}
	}
}

.course-list {
	padding: 0 16rpx;
}

.course-item {
	margin-bottom: 24rpx;
	padding: 0 16rpx;
}

.course-card {
	background: white;
	border-radius: 32rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
	border: 2rpx solid transparent;
	
	&.premium {
		background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
		border-color: #f59e0b;
	}
	
	&.current {
		border-color: #f5576c;
		border-width: 4rpx;
	}
	
	.course-icon {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		
		.iconfont {
			font-size: 36rpx;
		}
	}
	
	.course-info {
		flex: 1;
		
		.course-title {
			display: block;
			font-weight: bold;
			color: #333;
			font-size: 32rpx;
			margin-bottom: 8rpx;
		}
		
		.course-desc {
			color: #666;
			font-size: 28rpx;
		}
	}
	
	.course-status {
		display: flex;
		align-items: center;
		
		.free-badge {
			background: #dcfce7;
			color: #16a34a;
			padding: 8rpx 16rpx;
			border-radius: 24rpx;
			font-size: 24rpx;
			font-weight: 500;
			margin-right: 16rpx;
		}
		
		.price-badge {
			background: linear-gradient(135deg, #f59e0b, #d97706);
			color: white;
			padding: 8rpx 16rpx;
			border-radius: 24rpx;
			font-size: 20rpx;
			font-weight: bold;
			margin-right: 16rpx;
		}
		
		.status-icon {
			font-size: 40rpx;
			
			&.icon-check-circle {
				color: #16a34a;
			}
			
			&.icon-play-circle {
				color: #f5576c;
			}
			
			&.icon-lock {
				color: #6b7280;
			}
		}
	}
	
	.course-meta {
		.meta-item {
			color: #666;
			font-size: 24rpx;
			
			.iconfont {
				margin-right: 8rpx;
			}
		}
	}
	
	.progress-bar {
		width: 100%;
		height: 12rpx;
		background: #e5e7eb;
		border-radius: 6rpx;
		overflow: hidden;
		
		.progress-fill {
			height: 100%;
			background: linear-gradient(90deg, #10b981, #34d399);
			border-radius: 6rpx;
			transition: width 0.3s ease;
		}
	}
}

/* 字体图标 */
.icon-arrow-left::before { content: '\e005'; }
.icon-play::before { content: '\e006'; }
.icon-clock::before { content: '\e007'; }
.icon-headphones::before { content: '\e008'; }
.icon-check-circle::before { content: '\e009'; }
.icon-play-circle::before { content: '\e010'; }
.icon-lock::before { content: '\e011'; }
</style>
