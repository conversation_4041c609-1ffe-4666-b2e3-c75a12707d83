<template>
	<view class="captcha-test-container">
		<view class="test-header">
			<text class="test-title">验证码结构测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">后台返回结构</text>
			<view class="structure-display">
				<text class="structure-title">实际返回格式:</text>
				<view class="structure-code">
					<text class="code-text">{{`{
  "success": true,
  "message": "",
  "code": 0,
  "result": "data:image/jpg;base64,/9j/4AAQ...",
  "timestamp": 1749478235718
}`}}</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">验证码显示测试</text>
			<view class="captcha-display">
				<view class="captcha-info">
					<text class="info-label">验证码状态:</text>
					<text class="info-value" :class="captchaLoaded ? 'success' : 'error'">
						{{ captchaLoaded ? '已加载' : '未加载' }}
					</text>
				</view>
				<view class="captcha-info">
					<text class="info-label">图片格式:</text>
					<text class="info-value">{{ imageFormat }}</text>
				</view>
				<view class="captcha-info">
					<text class="info-label">验证码Key:</text>
					<text class="info-value">{{ captchaKey }}</text>
				</view>
			</view>
			
			<view class="captcha-image-container">
				<image 
					v-if="captchaImage" 
					:src="captchaImage" 
					mode="aspectFit"
					class="captcha-image"
					@load="onImageLoad"
					@error="onImageError"
				></image>
				<text v-else class="no-image">暂无验证码图片</text>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试操作</text>
			<view class="test-buttons">
				<button class="test-btn" @tap="testGetCaptcha">获取验证码</button>
				<button class="test-btn" @tap="testRefreshCaptcha">刷新验证码</button>
				<button class="test-btn" @tap="testImageFormat">检查图片格式</button>
				<button class="test-btn" @tap="clearCaptcha">清除验证码</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试日志</text>
			<scroll-view class="log-container" scroll-y>
				<view class="log-item" v-for="(log, index) in testLogs" :key="index">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message" :class="log.type">{{ log.message }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { getCaptcha, refreshCaptcha, getCaptchaImageUrl, getCaptchaKey, clearCaptcha } from '@/utils/captcha.js'

export default {
	data() {
		return {
			captchaLoaded: false,
			captchaImage: '',
			captchaKey: '',
			imageFormat: '',
			testLogs: []
		}
	},
	
	onLoad() {
		this.addLog('页面加载完成', 'info')
		this.testGetCaptcha()
	},
	
	methods: {
		// 添加日志
		addLog(message, type = 'info') {
			const now = new Date()
			const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
			
			this.testLogs.unshift({
				time,
				message,
				type
			})
			
			if (this.testLogs.length > 30) {
				this.testLogs = this.testLogs.slice(0, 30)
			}
		},
		
		// 更新验证码信息
		updateCaptchaInfo() {
			this.captchaImage = getCaptchaImageUrl()
			this.captchaKey = getCaptchaKey()
			this.captchaLoaded = !!this.captchaImage
			
			if (this.captchaImage) {
				if (this.captchaImage.startsWith('data:image/jpg;base64,')) {
					this.imageFormat = 'JPG Base64'
				} else if (this.captchaImage.startsWith('data:image/png;base64,')) {
					this.imageFormat = 'PNG Base64'
				} else if (this.captchaImage.startsWith('data:image/')) {
					this.imageFormat = 'Base64 (其他格式)'
				} else {
					this.imageFormat = 'URL或其他格式'
				}
			} else {
				this.imageFormat = '无'
			}
		},
		
		// 测试获取验证码
		async testGetCaptcha() {
			this.addLog('开始获取验证码', 'info')
			
			try {
				const result = await getCaptcha(true)
				
				if (result.success) {
					this.updateCaptchaInfo()
					this.addLog('验证码获取成功', 'success')
					this.addLog(`返回结构: ${JSON.stringify(result.data)}`, 'info')
				} else {
					this.addLog(`验证码获取失败: ${result.message}`, 'error')
				}
			} catch (error) {
				this.addLog(`验证码获取异常: ${error.message}`, 'error')
			}
		},
		
		// 测试刷新验证码
		async testRefreshCaptcha() {
			this.addLog('开始刷新验证码', 'info')
			
			try {
				const result = await refreshCaptcha()
				
				if (result.success) {
					this.updateCaptchaInfo()
					this.addLog('验证码刷新成功', 'success')
				} else {
					this.addLog(`验证码刷新失败: ${result.message}`, 'error')
				}
			} catch (error) {
				this.addLog(`验证码刷新异常: ${error.message}`, 'error')
			}
		},
		
		// 测试图片格式
		testImageFormat() {
			this.addLog('检查图片格式', 'info')
			
			if (!this.captchaImage) {
				this.addLog('没有验证码图片', 'warning')
				return
			}
			
			this.addLog(`图片URL长度: ${this.captchaImage.length}`, 'info')
			this.addLog(`图片前缀: ${this.captchaImage.substring(0, 50)}...`, 'info')
			
			if (this.captchaImage.startsWith('data:image/')) {
				this.addLog('✅ 图片格式正确 - Base64格式', 'success')
			} else {
				this.addLog('❌ 图片格式异常', 'error')
			}
		},
		
		// 清除验证码
		testClearCaptcha() {
			this.addLog('清除验证码', 'info')
			clearCaptcha()
			this.updateCaptchaInfo()
			this.addLog('验证码已清除', 'success')
		},
		
		// 图片加载成功
		onImageLoad() {
			this.addLog('✅ 验证码图片加载成功', 'success')
		},
		
		// 图片加载失败
		onImageError() {
			this.addLog('❌ 验证码图片加载失败', 'error')
		}
	}
}
</script>

<style lang="scss" scoped>
.captcha-test-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.test-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 24rpx;
		border-bottom: 2rpx solid #eee;
		padding-bottom: 16rpx;
	}
}

.structure-display {
	.structure-title {
		display: block;
		font-size: 28rpx;
		color: #666;
		margin-bottom: 16rpx;
	}
	
	.structure-code {
		background: #f5f5f5;
		border-radius: 8rpx;
		padding: 16rpx;
		
		.code-text {
			font-family: monospace;
			font-size: 24rpx;
			color: #333;
			white-space: pre-wrap;
		}
	}
}

.captcha-display {
	margin-bottom: 24rpx;
	
	.captcha-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 12rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		
		&:last-child {
			border-bottom: none;
		}
		
		.info-label {
			font-size: 28rpx;
			color: #666;
		}
		
		.info-value {
			font-size: 28rpx;
			color: #333;
			word-break: break-all;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
		}
	}
}

.captcha-image-container {
	text-align: center;
	padding: 24rpx;
	border: 2rpx dashed #ddd;
	border-radius: 8rpx;
	
	.captcha-image {
		max-width: 100%;
		height: 120rpx;
		border-radius: 8rpx;
	}
	
	.no-image {
		font-size: 28rpx;
		color: #999;
	}
}

.test-buttons {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
	
	.test-btn {
		height: 80rpx;
		background: #1890ff;
		color: white;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		
		&:active {
			opacity: 0.8;
		}
	}
}

.log-container {
	height: 400rpx;
	
	.log-item {
		display: flex;
		margin-bottom: 12rpx;
		font-size: 24rpx;
		
		.log-time {
			color: #999;
			width: 120rpx;
			flex-shrink: 0;
		}
		
		.log-message {
			color: #333;
			word-break: break-all;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
			
			&.warning {
				color: #faad14;
			}
			
			&.info {
				color: #1890ff;
			}
		}
	}
}
</style>
