/**
 * API接口统一管理
 * 基于小程序优化的请求管理器
 * 集成jeecg-boot后台接口
 */
import { get, post, put, del, upload } from '@/utils/request.js'
import { API_ENDPOINTS, buildApiUrl } from '@/config/api.js'

// 统一API对象
const api = {
  get,
  post,
  put,
  delete: del,
  upload
}

// 用户相关接口 - jeecg-boot标准接口
export const userApi = {
  // 登录 - jeecg-boot标准登录
  login: (data) => api.post(API_ENDPOINTS.USER.LOGIN, data),

  // 退出登录 - jeecg-boot标准登出
  logout: () => api.post(API_ENDPOINTS.USER.LOGOUT),

  // 获取用户基础信息 - jeecg-boot标准接口
  getUserInfo: () => api.get(API_ENDPOINTS.USER.INFO),

  // 获取部门信息 - jeecg-boot标准接口
  getDepartInfo: () => api.get(API_ENDPOINTS.USER.DEPART_INFO),

  // 修改密码 - jeecg-boot标准接口
  updatePassword: (data) => api.put(API_ENDPOINTS.USER.UPDATE_PASSWORD, data),

  // 上传头像 - jeecg-boot标准文件上传
  uploadAvatar: (filePath) => api.upload(API_ENDPOINTS.USER.UPLOAD_AVATAR, filePath, 'file'),

  // 获取用户扩展信息 - 自定义接口
  getUserProfile: () => api.get(API_ENDPOINTS.USER.PROFILE),

  // 更新用户扩展信息 - 自定义接口
  updateUserProfile: (data) => api.put(API_ENDPOINTS.USER.UPDATE_PROFILE, data),

  // 用户签到 - 自定义接口
  checkin: () => api.post(API_ENDPOINTS.USER.CHECKIN),

  // 获取签到记录 - 自定义接口
  getCheckinHistory: (params) => api.get(API_ENDPOINTS.USER.CHECKIN_HISTORY, params),

  // 获取学习统计 - 自定义接口
  getStudyStats: () => api.get(API_ENDPOINTS.USER.STATISTICS),

  // 验证码相关接口
  getCaptcha: (key = '1629428467008') => {
    const url = buildApiUrl(API_ENDPOINTS.USER.CAPTCHA, { key })
    console.log('📤 请求验证码:', url)
    return api.get(url)
  },
  checkCaptcha: (data) => {
    console.log('📤 验证验证码:', data)
    return api.post(API_ENDPOINTS.USER.CHECK_CAPTCHA, data)
  }
}

// 课程相关接口
export const courseApi = {
  // 获取课程列表
  getCourseList: (params) => api.get('/app/course/list', params),

  // 获取课程详情
  getCourseDetail: (courseId) => api.get(`/app/course/${courseId}`),

  // 获取按级别分类的课程
  getCoursesByLevel: (level) => api.get(`/app/course/list`,{"level": level}),

  // 获取课时列表
  getLessonList: (courseId, params) => api.get(`/app/course/${courseId}/lessons`, params),

  // 获取课程的课时列表（别名方法，用于课时一览页面）
  getLessonsByCourseId: (courseId) => api.get(`/app/lesson/list`,{courseId: courseId}),

  // 获取课时详情
  getLessonDetail: (lessonId) => api.get(`/app/lesson/queryById?id=${lessonId}`),

  // 开始学习课时
  startLesson: (lessonId) => api.post(`/app/lesson/${lessonId}/start`),

  // 完成课时学习
  completeLesson: (lessonId, data) => api.post(`/app/lesson/${lessonId}/complete`, data),

  // 获取课时内容
  getLessonContent: (lessonId) => api.get(`/app/lesson/${lessonId}/content`),

  // 获取课时打卡记录
  getLessonCheckinRecords: (lessonId, params) => api.get(`/app/lesson/${lessonId}/checkin/list`, params),

  // 提交课时打卡
  submitLessonCheckin: (lessonId, data) => api.post(`/app/lesson/${lessonId}/checkin`, data),

  // 收藏/取消收藏课程
  toggleFavorite: (courseId, isFavorite) => {
    return isFavorite
      ? api.delete(`/app/course/${courseId}/favorite`)
      : api.post(`/app/course/${courseId}/favorite`)
  }
}

// 排行榜相关接口
export const leaderboardApi = {
  // 获取积分排行榜
  getPointsRanking: (params) => api.get('/app/leaderboard/points', params),

  // 获取学习时长排行榜
  getStudyTimeRanking: (params) => api.get('/app/leaderboard/studytime', params),

  // 获取连续签到排行榜
  getCheckinRanking: (params) => api.get('/app/leaderboard/checkin', params)
}

// 练习相关接口
export const practiceApi = {
  // 提交录音练习（文件上传）
  submitPractice: (data) => api.post('/app/practice/submit', data),

  // 提交录音练习（Base64）
  submitPracticeBase64: (data) => api.post('/app/practice/submitBase64', data),

  // 获取练习历史
  getHistory: (params) => api.get('/app/practice/history', params),

  // 获取练习统计
  getStatistics: () => api.get('/app/practice/statistics'),

  // 获取支持的语言类型
  getSupportedLanguages: () => api.get('/app/practice/supportedLanguages'),

  // 获取支持的题型
  getSupportedCategories: (language) => api.get('/app/practice/supportedCategories', { language }),

  // 获取评测配置
  getEvaluationConfig: () => api.get('/app/practice/evaluationConfig')
}

// 支付相关接口
export const paymentApi = {
  // 创建订单
  createOrder: (data) => api.post('/api/v1/payment/create', data),
  
  // 查询订单状态
  getOrderStatus: (orderId) => api.get(`/api/v1/payment/status/${orderId}`),
  
  // 获取支付方式
  getPaymentMethods: () => api.get('/api/v1/payment/methods')
}

// AI相关接口
export const aiApi = {
  // 语音评测
  evaluate: (data) => api.post('/api/v1/ai/evaluate', data),
  
  // 获取评测历史
  getEvaluationHistory: (params) => api.get('/api/v1/ai/history', params)
}

// 系统相关接口
export const systemApi = {
  // 获取系统配置
  getConfig: () => api.get('/api/v1/system/config'),

  // 获取版本信息
  getVersion: () => api.get('/api/v1/system/version'),

  // 意见反馈
  feedback: (data) => api.post('/api/v1/system/feedback', data),

  // 获取帮助文档
  getHelp: () => api.get('/api/v1/system/help')
}

// 签到相关接口（为了向后兼容，创建别名）
export const checkinApi = {
  // 每日签到
  checkin: () => userApi.checkin(),

  // 获取签到记录
  getHistory: (params) => userApi.getCheckinHistory(params),

  // 获取签到统计
  getStatistics: () => userApi.getStudyStats(),

  // 获取排行榜
  getRanking: (params) => leaderboardApi.getCheckinRanking(params)
}

// 导出所有API
export default {
  user: userApi,
  course: courseApi,
  checkin: checkinApi,
  leaderboard: leaderboardApi,
  practice: practiceApi,
  payment: paymentApi,
  ai: aiApi,
  system: systemApi
}
