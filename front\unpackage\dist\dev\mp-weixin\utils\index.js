"use strict";
const common_vendor = require("../common/vendor.js");
const navigate = {
  // tabBar页面列表
  tabBarPages: [
    "pages/index/index",
    "pages/course/list",
    "pages/checkin/checkin",
    "pages/leaderboard/leaderboard",
    "pages/profile/profile"
  ],
  // 智能跳转 - 自动识别tabBar页面
  to: (url, params = {}) => {
    const cleanUrl = url.startsWith("/") ? url.substring(1) : url;
    const isTabBarPage = navigate.tabBarPages.includes(cleanUrl);
    if (isTabBarPage) {
      if (Object.keys(params).length > 0) {
        common_vendor.index.__f__("warn", "at utils/index.js:355", "⚠️ tabBar页面不支持URL参数，参数将被忽略:", params);
        common_vendor.index.setStorageSync("pageParams", {
          url: cleanUrl,
          params,
          timestamp: Date.now()
        });
      }
      common_vendor.index.switchTab({
        url: `/${cleanUrl}`,
        success: () => {
          common_vendor.index.__f__("log", "at utils/index.js:367", "✅ 成功跳转到tabBar页面:", cleanUrl);
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at utils/index.js:370", "❌ 跳转tabBar页面失败:", error);
        }
      });
    } else {
      const query = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join("&");
      const fullUrl = query ? `/${cleanUrl}?${query}` : `/${cleanUrl}`;
      common_vendor.index.navigateTo({
        url: fullUrl,
        success: () => {
          common_vendor.index.__f__("log", "at utils/index.js:381", "✅ 成功跳转到普通页面:", fullUrl);
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at utils/index.js:384", "❌ 跳转普通页面失败:", error);
        }
      });
    }
  },
  back: (delta = 1) => {
    common_vendor.index.navigateBack({ delta });
  },
  redirect: (url, params = {}) => {
    const cleanUrl = url.startsWith("/") ? url.substring(1) : url;
    const query = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join("&");
    const fullUrl = query ? `/${cleanUrl}?${query}` : `/${cleanUrl}`;
    common_vendor.index.redirectTo({ url: fullUrl });
  },
  reLaunch: (url, params = {}) => {
    const cleanUrl = url.startsWith("/") ? url.substring(1) : url;
    const query = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join("&");
    const fullUrl = query ? `/${cleanUrl}?${query}` : `/${cleanUrl}`;
    common_vendor.index.reLaunch({ url: fullUrl });
  },
  // 专门的tabBar跳转方法
  switchTab: (url, params = {}) => {
    const cleanUrl = url.startsWith("/") ? url.substring(1) : url;
    if (Object.keys(params).length > 0) {
      common_vendor.index.setStorageSync("pageParams", {
        url: cleanUrl,
        params,
        timestamp: Date.now()
      });
    }
    common_vendor.index.switchTab({
      url: `/${cleanUrl}`,
      success: () => {
        common_vendor.index.__f__("log", "at utils/index.js:424", "✅ 成功跳转到tabBar页面:", cleanUrl);
      },
      fail: (error) => {
        common_vendor.index.__f__("error", "at utils/index.js:427", "❌ 跳转tabBar页面失败:", error);
      }
    });
  },
  switchTab: (url) => {
    common_vendor.index.switchTab({ url });
  }
};
exports.navigate = navigate;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/index.js.map
