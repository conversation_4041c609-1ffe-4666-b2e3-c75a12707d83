"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const utils_common = require("../../utils/common.js");
const _sfc_main = {
  data() {
    return {
      appInfo: {},
      globalData: {},
      loginStatus: {},
      errorStats: {
        total: 0,
        byLevel: {},
        byType: {},
        recent: []
      }
    };
  },
  computed: {
    globalDataStr() {
      return JSON.stringify(this.globalData, null, 2);
    }
  },
  onLoad() {
    this.loadDebugInfo();
  },
  onShow() {
    this.refreshErrorStats();
  },
  methods: {
    loadDebugInfo() {
      this.refreshAppInfo();
      this.refreshGlobalData();
      this.refreshLoginStatus();
      this.refreshErrorStats();
    },
    refreshAppInfo() {
      try {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        this.appInfo = {
          version: utils_common.getGlobalData("version") || "1.0.0",
          platform: systemInfo.platform || "unknown",
          system: `${systemInfo.system} ${systemInfo.version}`,
          screenSize: `${systemInfo.screenWidth}x${systemInfo.screenHeight}`
        };
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/debug/debug.vue:169", "获取应用信息失败:", error);
      }
    },
    refreshGlobalData() {
      try {
        this.globalData = utils_common.getGlobalData();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/debug/debug.vue:177", "获取全局数据失败:", error);
        this.globalData = { error: "获取失败" };
      }
    },
    refreshLoginStatus() {
      try {
        const token = utils_common.getToken();
        const userInfo = utils_common.getUserInfo();
        const isLogin = utils_common.isLoggedIn();
        this.loginStatus = {
          isLogin,
          token,
          tokenPreview: token ? `${token.substring(0, 10)}...` : null,
          userInfo
        };
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/debug/debug.vue:195", "获取登录状态失败:", error);
        this.loginStatus = { isLogin: false };
      }
    },
    refreshErrorStats() {
      this.errorStats = {
        total: 0,
        byLevel: {},
        byType: {},
        recent: []
      };
    },
    clearErrors() {
      common_vendor.index.showToast({
        title: "错误记录已清空",
        icon: "success"
      });
      this.refreshErrorStats();
    },
    exportDebugInfo() {
      try {
        const debugInfo = {
          timestamp: (/* @__PURE__ */ new Date()).toISOString(),
          appInfo: this.appInfo,
          globalData: this.globalData,
          loginStatus: this.loginStatus,
          errorStats: this.errorStats
        };
        const debugStr = JSON.stringify(debugInfo, null, 2);
        common_vendor.index.setClipboardData({
          data: debugStr,
          success: () => {
            common_vendor.index.showToast({
              title: "调试信息已复制到剪贴板",
              icon: "success"
            });
          }
        });
      } catch (error) {
        handleError(error, ErrorTypes.SYSTEM_ERROR, ErrorLevels.MEDIUM);
      }
    },
    testError() {
      common_vendor.index.__f__("error", "at pages/debug/debug.vue:247", "这是一个测试错误");
      common_vendor.index.showToast({
        title: "测试错误已触发",
        icon: "none"
      });
    },
    testRequest() {
      return __async(this, null, function* () {
        try {
          common_vendor.index.showLoading({ title: "测试中..." });
          yield this.$request({
            url: "/api/test/debug",
            method: "GET"
          });
        } catch (error) {
          common_vendor.index.__f__("log", "at pages/debug/debug.vue:265", "测试请求完成（预期会失败）");
        } finally {
          common_vendor.index.hideLoading();
          setTimeout(() => {
            this.refreshErrorStats();
          }, 100);
        }
      });
    },
    getLevelName(level) {
      const names = {
        LOW: "低",
        MEDIUM: "中",
        HIGH: "高",
        CRITICAL: "严重"
      };
      return names[level] || level;
    },
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    },
    goToApiTest() {
      common_vendor.index.navigateTo({
        url: "/pages/debug/api-test"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.refreshAppInfo && $options.refreshAppInfo(...args)),
    b: common_vendor.t($data.appInfo.version),
    c: common_vendor.t($data.appInfo.platform),
    d: common_vendor.t($data.appInfo.system),
    e: common_vendor.t($data.appInfo.screenSize),
    f: common_vendor.o((...args) => $options.refreshGlobalData && $options.refreshGlobalData(...args)),
    g: common_vendor.t($options.globalDataStr),
    h: $data.loginStatus.isLogin ? 1 : "",
    i: common_vendor.t($data.loginStatus.isLogin ? "已登录" : "未登录"),
    j: $data.loginStatus.isLogin ? 1 : "",
    k: !$data.loginStatus.isLogin ? 1 : "",
    l: $data.loginStatus.token
  }, $data.loginStatus.token ? {
    m: common_vendor.t($data.loginStatus.tokenPreview)
  } : {}, {
    n: $data.loginStatus.userInfo
  }, $data.loginStatus.userInfo ? {
    o: common_vendor.t($data.loginStatus.userInfo.nickname || "未设置")
  } : {}, {
    p: common_vendor.o((...args) => $options.clearErrors && $options.clearErrors(...args)),
    q: common_vendor.t($data.errorStats.total),
    r: common_vendor.f($data.errorStats.byLevel, (count, level, i0) => {
      return {
        a: common_vendor.t(count),
        b: common_vendor.n(level.toLowerCase()),
        c: common_vendor.t($options.getLevelName(level)),
        d: level
      };
    }),
    s: common_vendor.t($data.errorStats.recent.length),
    t: common_vendor.f($data.errorStats.recent, (error, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(error.type),
        b: common_vendor.t(error.level),
        c: common_vendor.n(error.level.toLowerCase()),
        d: common_vendor.t($options.formatTime(error.timestamp)),
        e: common_vendor.t(error.message),
        f: error.context.page
      }, error.context.page ? {
        g: common_vendor.t(error.context.page)
      } : {}, {
        h: error.id
      });
    }),
    v: $data.errorStats.recent.length === 0
  }, $data.errorStats.recent.length === 0 ? {} : {}, {
    w: common_vendor.o((...args) => $options.exportDebugInfo && $options.exportDebugInfo(...args)),
    x: common_vendor.o((...args) => $options.testError && $options.testError(...args)),
    y: common_vendor.o((...args) => $options.testRequest && $options.testRequest(...args)),
    z: common_vendor.o((...args) => $options.goToApiTest && $options.goToApiTest(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-12bbb3dc"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/debug.js.map
