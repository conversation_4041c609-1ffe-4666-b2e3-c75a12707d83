/**
 * 修复getEnvVar未定义错误
 * 清理编译缓存并重新构建
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 修复getEnvVar未定义错误...')

// 1. 清理编译缓存
const unpackagePath = path.join(process.cwd(), 'unpackage')
if (fs.existsSync(unpackagePath)) {
  console.log('🗑️ 清理编译缓存...')
  try {
    fs.rmSync(unpackagePath, { recursive: true, force: true })
    console.log('✅ 编译缓存清理完成')
  } catch (error) {
    console.log('⚠️ 编译缓存清理失败:', error.message)
  }
}

// 2. 清理node_modules/.cache
const cachePath = path.join(process.cwd(), 'node_modules', '.cache')
if (fs.existsSync(cachePath)) {
  console.log('🗑️ 清理node_modules缓存...')
  try {
    fs.rmSync(cachePath, { recursive: true, force: true })
    console.log('✅ node_modules缓存清理完成')
  } catch (error) {
    console.log('⚠️ node_modules缓存清理失败:', error.message)
  }
}

// 3. 检查config/api.js是否正确
const apiConfigPath = path.join(process.cwd(), 'config', 'api.js')
if (fs.existsSync(apiConfigPath)) {
  const content = fs.readFileSync(apiConfigPath, 'utf8')
  
  if (content.includes('getEnvVar')) {
    console.log('❌ config/api.js 仍然包含getEnvVar引用')
    console.log('请手动检查并移除getEnvVar相关代码')
  } else {
    console.log('✅ config/api.js 配置正确')
  }
} else {
  console.log('❌ config/api.js 文件不存在')
}

// 4. 检查其他可能的引用
const filesToCheck = [
  'utils/request.js',
  'main.js',
  'main-mp.js',
  'App.vue'
]

filesToCheck.forEach(file => {
  const filePath = path.join(process.cwd(), file)
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8')
    if (content.includes('getEnvVar')) {
      console.log(`❌ ${file} 包含getEnvVar引用`)
    } else {
      console.log(`✅ ${file} 配置正确`)
    }
  }
})

console.log('\n🎉 错误修复完成！')
console.log('请重新运行开发命令：')
console.log('npm run dev:mp-weixin')
console.log('或')
console.log('npm run dev:h5')
