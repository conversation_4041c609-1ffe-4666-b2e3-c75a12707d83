# 口语便利店小程序产品设计文档

## 产品概述
"口语便利店"是一款面向日语学习者的小程序，专注于提升用户的日语口语能力。通过每日打卡机制，用户可以进行日语对话和新闻的听力与跟读练习，获得AI评分反馈，并与其他学习者进行简单互动。

## 目标用户
已有一定日语基础，希望提升口语能力的学习者。

## 核心功能

### 1. 课程体系
- 课程分为初级、中级、高级和专业四个级别
- 每个级别包含免费和付费内容
- 课程内容为简短日语对话或新闻片段

### 2. 打卡系统
- 每日打卡机制
- 每月有5次补打卡机会
- 打卡内容包括听力和跟读录音练习

### 3. AI评分系统
- 对用户录音进行自动评分
- 评分维度：发音准确度、流利度、语调等
- 推荐方案：科大讯飞开放平台的语音评测API（性价比高且成熟）

### 4. 学习资料
- 音频材料
- 文字稿
- 中文翻译
- 重点词汇解释

### 5. 社交互动
- 用户打卡排名展示
- 打卡评论功能

### 6. 会员权益
- 区分免费课程和付费课程
- 付费课程可单独购买，无需订阅会员

## 技术架构
- 前端：UniApp（支持微信和钉钉小程序）
- 后端：Jeecg-boot
- 数据库：MySQL
- AI评分：科大讯飞开放平台API