{"version": 3, "file": "captcha.js", "sources": ["utils/captcha.js"], "sourcesContent": ["/**\n * 验证码管理器\n * 处理验证码获取、验证、刷新等功能\n */\n\nimport { userApi } from '@/api/index.js'\n\n/**\n * 验证码管理类\n */\nclass CaptchaManager {\n  constructor() {\n    this.captchaData = {\n      image: '',      // 验证码图片base64\n      key: '',        // 验证码key\n      timestamp: 0    // 获取时间戳\n    }\n\n    this.config = {\n      expireTime: 5 * 60 * 1000,  // 验证码过期时间（5分钟）\n      autoRefresh: true,           // 是否自动刷新过期验证码\n      retryCount: 3,              // 获取验证码重试次数\n      pathKey: '1629428467008'    // 固定的path参数key\n    }\n  }\n\n  /**\n   * 获取验证码\n   * @param {boolean} force 是否强制刷新\n   * @returns {Promise<Object>} 验证码数据\n   */\n  async getCaptcha(force = false) {\n    try {\n      // 检查是否需要刷新验证码\n      if (!force && this.isValid()) {\n        console.log('✅ 使用缓存的验证码')\n        return {\n          success: true,\n          data: this.captchaData\n        }\n      }\n\n      console.log('🔄 获取新的验证码')\n\n      const response = await userApi.getCaptcha(this.config.pathKey)\n\n      if (response.success) {\n        // 适配实际返回结构: {\"success\":true,\"message\":\"\",\"code\":0,\"result\":\"data:image/jpg;base64,...\",\"timestamp\":1749478235718}\n        const imageData = response.result || response.data\n        const timestamp = response.timestamp || Date.now()\n\n        // 更新验证码数据\n        this.captchaData = {\n          image: imageData,  // 直接使用result字段，已经是完整的base64格式\n          key: this.config.pathKey,  // 使用配置的pathKey作为验证码key\n          timestamp: timestamp\n        }\n\n        console.log('✅ 验证码获取成功:', {\n          hasImage: !!this.captchaData.image,\n          imageFormat: this.captchaData.image ? this.captchaData.image.substring(0, 30) + '...' : '',\n          key: this.captchaData.key,\n          timestamp: this.captchaData.timestamp\n        })\n\n        return {\n          success: true,\n          data: this.captchaData\n        }\n      } else {\n        console.error('❌ 验证码获取失败:', response.message)\n        return {\n          success: false,\n          message: response.message || '获取验证码失败'\n        }\n      }\n    } catch (error) {\n      console.error('❌ 验证码获取异常:', error)\n      return {\n        success: false,\n        message: error.message || '获取验证码异常'\n      }\n    }\n  }\n\n  /**\n   * 验证验证码\n   * @param {string} code 用户输入的验证码\n   * @returns {Promise<Object>} 验证结果\n   */\n  async verifyCaptcha(code) {\n    try {\n      if (!code || !code.trim()) {\n        return {\n          success: false,\n          message: '请输入验证码'\n        }\n      }\n\n      if (!this.captchaData.key) {\n        return {\n          success: false,\n          message: '验证码已过期，请刷新'\n        }\n      }\n\n      console.log('🔍 验证验证码:', {\n        code: code,\n        key: this.captchaData.key\n      })\n\n      // 验证码验证参数（根据jeecg-boot标准）\n      const response = await userApi.checkCaptcha({\n        captcha: code.trim(),\n        checkKey: this.captchaData.key\n      })\n\n      if (response.success) {\n        console.log('✅ 验证码验证成功')\n        return {\n          success: true,\n          message: '验证码正确'\n        }\n      } else {\n        console.error('❌ 验证码验证失败:', response.message)\n        \n        // 验证失败后自动刷新验证码\n        if (this.config.autoRefresh) {\n          setTimeout(() => {\n            this.getCaptcha(true)\n          }, 1000)\n        }\n\n        return {\n          success: false,\n          message: response.message || '验证码错误'\n        }\n      }\n    } catch (error) {\n      console.error('❌ 验证码验证异常:', error)\n      return {\n        success: false,\n        message: error.message || '验证码验证异常'\n      }\n    }\n  }\n\n  /**\n   * 刷新验证码\n   * @returns {Promise<Object>} 新的验证码数据\n   */\n  async refreshCaptcha() {\n    console.log('🔄 刷新验证码')\n    return await this.getCaptcha(true)\n  }\n\n  /**\n   * 检查验证码是否有效\n   * @returns {boolean} 是否有效\n   */\n  isValid() {\n    if (!this.captchaData.image || !this.captchaData.key) {\n      return false\n    }\n\n    const now = Date.now()\n    const elapsed = now - this.captchaData.timestamp\n\n    return elapsed < this.config.expireTime\n  }\n\n  /**\n   * 获取验证码图片URL\n   * @returns {string} 图片URL\n   */\n  getImageUrl() {\n    if (!this.captchaData.image) {\n      return ''\n    }\n\n    // 后台返回的是完整的base64格式: \"data:image/jpg;base64,/9j/4AAQ...\"\n    // 直接返回即可\n    if (this.captchaData.image.startsWith('data:image/')) {\n      return this.captchaData.image\n    }\n\n    // 兼容其他可能的格式\n    if (this.captchaData.image.match(/^[A-Za-z0-9+/=]+$/)) {\n      return `data:image/png;base64,${this.captchaData.image}`\n    }\n\n    // 如果是URL，直接返回\n    return this.captchaData.image\n  }\n\n  /**\n   * 获取验证码key\n   * @returns {string} 验证码key\n   */\n  getKey() {\n    return this.captchaData.key\n  }\n\n  /**\n   * 清除验证码数据\n   */\n  clear() {\n    console.log('🗑️ 清除验证码数据')\n    this.captchaData = {\n      image: '',\n      key: '',\n      timestamp: 0\n    }\n  }\n\n  /**\n   * 获取剩余有效时间（秒）\n   * @returns {number} 剩余秒数\n   */\n  getRemainingTime() {\n    if (!this.isValid()) {\n      return 0\n    }\n\n    const now = Date.now()\n    const elapsed = now - this.captchaData.timestamp\n    const remaining = this.config.expireTime - elapsed\n\n    return Math.max(0, Math.floor(remaining / 1000))\n  }\n\n  /**\n   * 生成随机path参数\n   * @returns {string} 随机数字符串\n   */\n  generatePathKey() {\n    // 可以使用时间戳或随机数\n    return Date.now().toString()\n  }\n\n  /**\n   * 设置path参数\n   * @param {string} key path参数，如果不提供则使用默认值\n   */\n  setPathKey(key) {\n    if (key) {\n      this.config.pathKey = key\n    } else {\n      this.config.pathKey = this.generatePathKey()\n    }\n    console.log('🔑 设置验证码path参数:', this.config.pathKey)\n  }\n\n  /**\n   * 获取当前path参数\n   * @returns {string} 当前path参数\n   */\n  getPathKey() {\n    return this.config.pathKey\n  }\n\n  /**\n   * 设置配置\n   * @param {Object} config 配置对象\n   */\n  setConfig(config) {\n    this.config = {\n      ...this.config,\n      ...config\n    }\n  }\n}\n\n// 创建全局验证码管理器实例\nconst captchaManager = new CaptchaManager()\n\n// 导出管理器实例和类\nexport default captchaManager\nexport { CaptchaManager }\n\n// 导出便捷方法\nexport const getCaptcha = (force = false) => captchaManager.getCaptcha(force)\nexport const verifyCaptcha = (code) => captchaManager.verifyCaptcha(code)\nexport const refreshCaptcha = () => captchaManager.refreshCaptcha()\nexport const getCaptchaImageUrl = () => captchaManager.getImageUrl()\nexport const getCaptchaKey = () => captchaManager.getKey()\nexport const isCaptchaValid = () => captchaManager.isValid()\nexport const clearCaptcha = () => captchaManager.clear()\nexport const getCaptchaRemainingTime = () => captchaManager.getRemainingTime()\nexport const setPathKey = (key) => captchaManager.setPathKey(key)\nexport const getPathKey = () => captchaManager.getPathKey()\nexport const generatePathKey = () => captchaManager.generatePathKey()\n"], "names": ["uni", "userApi"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,MAAM,eAAe;AAAA,EACnB,cAAc;AACZ,SAAK,cAAc;AAAA,MACjB,OAAO;AAAA;AAAA,MACP,KAAK;AAAA;AAAA,MACL,WAAW;AAAA;AAAA,IACZ;AAED,SAAK,SAAS;AAAA,MACZ,YAAY,IAAI,KAAK;AAAA;AAAA,MACrB,aAAa;AAAA;AAAA,MACb,YAAY;AAAA;AAAA,MACZ,SAAS;AAAA;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOK,WAAW,QAAQ,OAAO;AAAA;AAC9B,UAAI;AAEF,YAAI,CAAC,SAAS,KAAK,WAAW;AAC5BA,wBAAAA,6CAAY,YAAY;AACxB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,MAAM,KAAK;AAAA,UACZ;AAAA,QACF;AAEDA,sBAAAA,MAAA,MAAA,OAAA,0BAAY,YAAY;AAExB,cAAM,WAAW,MAAMC,UAAO,QAAC,WAAW,KAAK,OAAO,OAAO;AAE7D,YAAI,SAAS,SAAS;AAEpB,gBAAM,YAAY,SAAS,UAAU,SAAS;AAC9C,gBAAM,YAAY,SAAS,aAAa,KAAK,IAAK;AAGlD,eAAK,cAAc;AAAA,YACjB,OAAO;AAAA;AAAA,YACP,KAAK,KAAK,OAAO;AAAA;AAAA,YACjB;AAAA,UACD;AAEDD,wBAAAA,MAAY,MAAA,OAAA,0BAAA,cAAc;AAAA,YACxB,UAAU,CAAC,CAAC,KAAK,YAAY;AAAA,YAC7B,aAAa,KAAK,YAAY,QAAQ,KAAK,YAAY,MAAM,UAAU,GAAG,EAAE,IAAI,QAAQ;AAAA,YACxF,KAAK,KAAK,YAAY;AAAA,YACtB,WAAW,KAAK,YAAY;AAAA,UACtC,CAAS;AAED,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,MAAM,KAAK;AAAA,UACZ;AAAA,QACT,OAAa;AACLA,wBAAA,MAAA,MAAA,SAAA,0BAAc,cAAc,SAAS,OAAO;AAC5C,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,SAAS,WAAW;AAAA,UAC9B;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,+CAAc,cAAc,KAAK;AACjC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,MAAM,WAAW;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOK,cAAc,MAAM;AAAA;AACxB,UAAI;AACF,YAAI,CAAC,QAAQ,CAAC,KAAK,KAAI,GAAI;AACzB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS;AAAA,UACV;AAAA,QACF;AAED,YAAI,CAAC,KAAK,YAAY,KAAK;AACzB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS;AAAA,UACV;AAAA,QACF;AAEDA,sBAAAA,MAAA,MAAA,OAAA,2BAAY,aAAa;AAAA,UACvB;AAAA,UACA,KAAK,KAAK,YAAY;AAAA,QAC9B,CAAO;AAGD,cAAM,WAAW,MAAMC,UAAO,QAAC,aAAa;AAAA,UAC1C,SAAS,KAAK,KAAM;AAAA,UACpB,UAAU,KAAK,YAAY;AAAA,QACnC,CAAO;AAED,YAAI,SAAS,SAAS;AACpBD,wBAAAA,MAAY,MAAA,OAAA,2BAAA,WAAW;AACvB,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS;AAAA,UACV;AAAA,QACT,OAAa;AACLA,wBAAA,MAAA,MAAA,SAAA,2BAAc,cAAc,SAAS,OAAO;AAG5C,cAAI,KAAK,OAAO,aAAa;AAC3B,uBAAW,MAAM;AACf,mBAAK,WAAW,IAAI;AAAA,YACrB,GAAE,GAAI;AAAA,UACR;AAED,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,SAAS,WAAW;AAAA,UAC9B;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,gDAAc,cAAc,KAAK;AACjC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,MAAM,WAAW;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMK,iBAAiB;AAAA;AACrBA,oBAAAA,MAAA,MAAA,OAAA,2BAAY,UAAU;AACtB,aAAO,MAAM,KAAK,WAAW,IAAI;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU;AACR,QAAI,CAAC,KAAK,YAAY,SAAS,CAAC,KAAK,YAAY,KAAK;AACpD,aAAO;AAAA,IACR;AAED,UAAM,MAAM,KAAK,IAAK;AACtB,UAAM,UAAU,MAAM,KAAK,YAAY;AAEvC,WAAO,UAAU,KAAK,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,cAAc;AACZ,QAAI,CAAC,KAAK,YAAY,OAAO;AAC3B,aAAO;AAAA,IACR;AAID,QAAI,KAAK,YAAY,MAAM,WAAW,aAAa,GAAG;AACpD,aAAO,KAAK,YAAY;AAAA,IACzB;AAGD,QAAI,KAAK,YAAY,MAAM,MAAM,mBAAmB,GAAG;AACrD,aAAO,yBAAyB,KAAK,YAAY,KAAK;AAAA,IACvD;AAGD,WAAO,KAAK,YAAY;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,SAAS;AACP,WAAO,KAAK,YAAY;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAKD,QAAQ;AACNA,kBAAAA,MAAY,MAAA,OAAA,2BAAA,aAAa;AACzB,SAAK,cAAc;AAAA,MACjB,OAAO;AAAA,MACP,KAAK;AAAA,MACL,WAAW;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,mBAAmB;AACjB,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO;AAAA,IACR;AAED,UAAM,MAAM,KAAK,IAAK;AACtB,UAAM,UAAU,MAAM,KAAK,YAAY;AACvC,UAAM,YAAY,KAAK,OAAO,aAAa;AAE3C,WAAO,KAAK,IAAI,GAAG,KAAK,MAAM,YAAY,GAAI,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,kBAAkB;AAEhB,WAAO,KAAK,IAAK,EAAC,SAAU;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW,KAAK;AACd,QAAI,KAAK;AACP,WAAK,OAAO,UAAU;AAAA,IAC5B,OAAW;AACL,WAAK,OAAO,UAAU,KAAK,gBAAiB;AAAA,IAC7C;AACDA,gEAAY,mBAAmB,KAAK,OAAO,OAAO;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,aAAa;AACX,WAAO,KAAK,OAAO;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU,QAAQ;AAChB,SAAK,SAAS,kCACT,KAAK,SACL;AAAA,EAEN;AACH;AAGA,MAAM,iBAAiB,IAAI,eAAgB;AAO/B,MAAC,aAAa,CAAC,QAAQ,UAAU,eAAe,WAAW,KAAK;AAEhE,MAAC,iBAAiB,MAAM,eAAe,eAAgB;AACvD,MAAC,qBAAqB,MAAM,eAAe,YAAa;AACxD,MAAC,gBAAgB,MAAM,eAAe,OAAQ;AAE9C,MAAC,eAAe,MAAM,eAAe,MAAO;AAE5C,MAAC,aAAa,CAAC,QAAQ,eAAe,WAAW,GAAG;AACpD,MAAC,aAAa,MAAM,eAAe,WAAY;AAC/C,MAAC,kBAAkB,MAAM,eAAe,gBAAe;;;;;;;;;"}