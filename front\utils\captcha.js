/**
 * 验证码管理器
 * 处理验证码获取、验证、刷新等功能
 */

import { userApi } from '@/api/index.js'

/**
 * 验证码管理类
 */
class CaptchaManager {
  constructor() {
    this.captchaData = {
      image: '',      // 验证码图片base64
      key: '',        // 验证码key
      timestamp: 0    // 获取时间戳
    }

    this.config = {
      expireTime: 5 * 60 * 1000,  // 验证码过期时间（5分钟）
      autoRefresh: true,           // 是否自动刷新过期验证码
      retryCount: 3,              // 获取验证码重试次数
      pathKey: '1629428467008'    // 固定的path参数key
    }
  }

  /**
   * 获取验证码
   * @param {boolean} force 是否强制刷新
   * @returns {Promise<Object>} 验证码数据
   */
  async getCaptcha(force = false) {
    try {
      // 检查是否需要刷新验证码
      if (!force && this.isValid()) {
        console.log('✅ 使用缓存的验证码')
        return {
          success: true,
          data: this.captchaData
        }
      }

      console.log('🔄 获取新的验证码')

      const response = await userApi.getCaptcha(this.config.pathKey)

      if (response.success) {
        // 适配实际返回结构: {"success":true,"message":"","code":0,"result":"data:image/jpg;base64,...","timestamp":1749478235718}
        const imageData = response.result || response.data
        const timestamp = response.timestamp || Date.now()

        // 更新验证码数据
        this.captchaData = {
          image: imageData,  // 直接使用result字段，已经是完整的base64格式
          key: this.config.pathKey,  // 使用配置的pathKey作为验证码key
          timestamp: timestamp
        }

        console.log('✅ 验证码获取成功:', {
          hasImage: !!this.captchaData.image,
          imageFormat: this.captchaData.image ? this.captchaData.image.substring(0, 30) + '...' : '',
          key: this.captchaData.key,
          timestamp: this.captchaData.timestamp
        })

        return {
          success: true,
          data: this.captchaData
        }
      } else {
        console.error('❌ 验证码获取失败:', response.message)
        return {
          success: false,
          message: response.message || '获取验证码失败'
        }
      }
    } catch (error) {
      console.error('❌ 验证码获取异常:', error)
      return {
        success: false,
        message: error.message || '获取验证码异常'
      }
    }
  }

  /**
   * 验证验证码
   * @param {string} code 用户输入的验证码
   * @returns {Promise<Object>} 验证结果
   */
  async verifyCaptcha(code) {
    try {
      if (!code || !code.trim()) {
        return {
          success: false,
          message: '请输入验证码'
        }
      }

      if (!this.captchaData.key) {
        return {
          success: false,
          message: '验证码已过期，请刷新'
        }
      }

      console.log('🔍 验证验证码:', {
        code: code,
        key: this.captchaData.key
      })

      // 验证码验证参数（根据jeecg-boot标准）
      const response = await userApi.checkCaptcha({
        captcha: code.trim(),
        checkKey: this.captchaData.key
      })

      if (response.success) {
        console.log('✅ 验证码验证成功')
        return {
          success: true,
          message: '验证码正确'
        }
      } else {
        console.error('❌ 验证码验证失败:', response.message)
        
        // 验证失败后自动刷新验证码
        if (this.config.autoRefresh) {
          setTimeout(() => {
            this.getCaptcha(true)
          }, 1000)
        }

        return {
          success: false,
          message: response.message || '验证码错误'
        }
      }
    } catch (error) {
      console.error('❌ 验证码验证异常:', error)
      return {
        success: false,
        message: error.message || '验证码验证异常'
      }
    }
  }

  /**
   * 刷新验证码
   * @returns {Promise<Object>} 新的验证码数据
   */
  async refreshCaptcha() {
    console.log('🔄 刷新验证码')
    return await this.getCaptcha(true)
  }

  /**
   * 检查验证码是否有效
   * @returns {boolean} 是否有效
   */
  isValid() {
    if (!this.captchaData.image || !this.captchaData.key) {
      return false
    }

    const now = Date.now()
    const elapsed = now - this.captchaData.timestamp

    return elapsed < this.config.expireTime
  }

  /**
   * 获取验证码图片URL
   * @returns {string} 图片URL
   */
  getImageUrl() {
    if (!this.captchaData.image) {
      return ''
    }

    // 后台返回的是完整的base64格式: "data:image/jpg;base64,/9j/4AAQ..."
    // 直接返回即可
    if (this.captchaData.image.startsWith('data:image/')) {
      return this.captchaData.image
    }

    // 兼容其他可能的格式
    if (this.captchaData.image.match(/^[A-Za-z0-9+/=]+$/)) {
      return `data:image/png;base64,${this.captchaData.image}`
    }

    // 如果是URL，直接返回
    return this.captchaData.image
  }

  /**
   * 获取验证码key
   * @returns {string} 验证码key
   */
  getKey() {
    return this.captchaData.key
  }

  /**
   * 清除验证码数据
   */
  clear() {
    console.log('🗑️ 清除验证码数据')
    this.captchaData = {
      image: '',
      key: '',
      timestamp: 0
    }
  }

  /**
   * 获取剩余有效时间（秒）
   * @returns {number} 剩余秒数
   */
  getRemainingTime() {
    if (!this.isValid()) {
      return 0
    }

    const now = Date.now()
    const elapsed = now - this.captchaData.timestamp
    const remaining = this.config.expireTime - elapsed

    return Math.max(0, Math.floor(remaining / 1000))
  }

  /**
   * 生成随机path参数
   * @returns {string} 随机数字符串
   */
  generatePathKey() {
    // 可以使用时间戳或随机数
    return Date.now().toString()
  }

  /**
   * 设置path参数
   * @param {string} key path参数，如果不提供则使用默认值
   */
  setPathKey(key) {
    if (key) {
      this.config.pathKey = key
    } else {
      this.config.pathKey = this.generatePathKey()
    }
    console.log('🔑 设置验证码path参数:', this.config.pathKey)
  }

  /**
   * 获取当前path参数
   * @returns {string} 当前path参数
   */
  getPathKey() {
    return this.config.pathKey
  }

  /**
   * 设置配置
   * @param {Object} config 配置对象
   */
  setConfig(config) {
    this.config = {
      ...this.config,
      ...config
    }
  }
}

// 创建全局验证码管理器实例
const captchaManager = new CaptchaManager()

// 导出管理器实例和类
export default captchaManager
export { CaptchaManager }

// 导出便捷方法
export const getCaptcha = (force = false) => captchaManager.getCaptcha(force)
export const verifyCaptcha = (code) => captchaManager.verifyCaptcha(code)
export const refreshCaptcha = () => captchaManager.refreshCaptcha()
export const getCaptchaImageUrl = () => captchaManager.getImageUrl()
export const getCaptchaKey = () => captchaManager.getKey()
export const isCaptchaValid = () => captchaManager.isValid()
export const clearCaptcha = () => captchaManager.clear()
export const getCaptchaRemainingTime = () => captchaManager.getRemainingTime()
export const setPathKey = (key) => captchaManager.setPathKey(key)
export const getPathKey = () => captchaManager.getPathKey()
export const generatePathKey = () => captchaManager.generatePathKey()
