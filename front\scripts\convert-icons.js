/**
 * SVG转PNG图标转换脚本
 * 需要安装依赖：npm install sharp
 * 运行命令：node scripts/convert-icons.js
 */

const fs = require('fs');
const path = require('path');

// 模拟sharp库的功能（实际使用时需要安装sharp）
function convertSvgToPng(svgPath, pngPath, size = 48) {
  console.log(`转换 ${svgPath} -> ${pngPath} (${size}x${size})`);
  
  // 这里是模拟转换过程
  // 实际使用时需要安装sharp库：npm install sharp
  // const sharp = require('sharp');
  // return sharp(svgPath)
  //   .resize(size, size)
  //   .png()
  //   .toFile(pngPath);
  
  // 模拟创建PNG文件（实际开发中请使用真实的转换库）
  const svgContent = fs.readFileSync(svgPath, 'utf8');
  const mockPngComment = `<!-- 这是从 ${path.basename(svgPath)} 转换而来的PNG文件占位符 -->\n${svgContent}`;
  fs.writeFileSync(pngPath.replace('.png', '.svg.placeholder'), mockPngComment);
  
  return Promise.resolve();
}

async function convertIcons() {
  const iconsDir = path.join(__dirname, '../static/icons');
  const tabbarDir = path.join(__dirname, '../static/tabbar');
  
  // 创建tabbar目录
  if (!fs.existsSync(tabbarDir)) {
    fs.mkdirSync(tabbarDir, { recursive: true });
  }
  
  // 定义需要转换的图标
  const icons = [
    'home',
    'home-active',
    'course',
    'course-active',
    'checkin',
    'checkin-active',
    'leaderboard',
    'leaderboard-active',
    'profile',
    'profile-active'
  ];
  
  console.log('开始转换tabbar图标...');
  
  for (const icon of icons) {
    const svgPath = path.join(iconsDir, `${icon}.svg`);
    const pngPath = path.join(tabbarDir, `${icon}.png`);
    
    if (fs.existsSync(svgPath)) {
      try {
        await convertSvgToPng(svgPath, pngPath, 48);
        console.log(`✓ 转换成功: ${icon}.png`);
      } catch (error) {
        console.error(`✗ 转换失败: ${icon}.svg`, error.message);
      }
    } else {
      console.warn(`⚠ 文件不存在: ${svgPath}`);
    }
  }
  
  console.log('\n图标转换完成！');
  console.log('\n注意事项：');
  console.log('1. 当前为模拟转换，实际使用请安装 sharp 库：npm install sharp');
  console.log('2. 转换后的PNG文件将保存在 static/tabbar/ 目录下');
  console.log('3. 建议图标尺寸为 48x48px，适配不同设备的显示需求');
  console.log('4. 请确保图标颜色符合设计规范：未选中 #999999，选中 #667eea');
}

// 运行转换
convertIcons().catch(console.error);
