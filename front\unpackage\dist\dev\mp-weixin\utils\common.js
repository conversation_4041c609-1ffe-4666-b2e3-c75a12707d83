"use strict";
const common_vendor = require("../common/vendor.js");
function getAppInstance() {
  try {
    return getApp();
  } catch (error) {
    common_vendor.index.__f__("warn", "at utils/common.js:12", "获取App实例失败:", error);
    return null;
  }
}
function getGlobalData(key) {
  const app = getAppInstance();
  if (!app || !app.globalData) {
    return key ? void 0 : {};
  }
  return key ? app.globalData[key] : app.globalData;
}
function getUserInfo() {
  const globalUserInfo = getGlobalData("userInfo");
  if (globalUserInfo)
    return globalUserInfo;
  try {
    return common_vendor.index.getStorageSync("userInfo");
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/common.js:57", "获取用户信息失败:", error);
    return null;
  }
}
function getToken() {
  const globalToken = getGlobalData("token");
  if (globalToken)
    return globalToken;
  try {
    return common_vendor.index.getStorageSync("token");
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/common.js:72", "获取Token失败:", error);
    return null;
  }
}
function isLoggedIn() {
  const token = getToken();
  const userInfo = getUserInfo();
  return !!(token && userInfo);
}
exports.getGlobalData = getGlobalData;
exports.getToken = getToken;
exports.getUserInfo = getUserInfo;
exports.isLoggedIn = isLoggedIn;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/common.js.map
