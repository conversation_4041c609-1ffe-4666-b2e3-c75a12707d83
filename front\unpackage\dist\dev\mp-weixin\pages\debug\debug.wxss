/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.debug-container.data-v-12bbb3dc {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}
.header.data-v-12bbb3dc {
  text-align: center;
  margin-bottom: 30px;
}
.header .title.data-v-12bbb3dc {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}
.header .subtitle.data-v-12bbb3dc {
  display: block;
  font-size: 14px;
  color: #666;
}
.section.data-v-12bbb3dc {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.section .section-header.data-v-12bbb3dc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section .section-header .section-title.data-v-12bbb3dc {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.section .section-header .refresh-btn.data-v-12bbb3dc, .section .section-header .clear-btn.data-v-12bbb3dc {
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 6px;
  border: 1px solid #ddd;
  background: #f8f9fa;
  color: #666;
}
.section .section-header .count-badge.data-v-12bbb3dc {
  background: #667eea;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}
.section .section-header .status-indicator.data-v-12bbb3dc {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ccc;
}
.section .section-header .status-indicator.active.data-v-12bbb3dc {
  background: #10b981;
}
.info-grid.data-v-12bbb3dc {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}
.info-grid .info-item .label.data-v-12bbb3dc {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}
.info-grid .info-item .value.data-v-12bbb3dc {
  display: block;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}
.info-grid .info-item .value.success.data-v-12bbb3dc {
  color: #10b981;
}
.info-grid .info-item .value.error.data-v-12bbb3dc {
  color: #ef4444;
}
.info-grid .info-item .value.token.data-v-12bbb3dc {
  font-family: monospace;
  font-size: 12px;
}
.json-viewer.data-v-12bbb3dc {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}
.json-viewer .json-content.data-v-12bbb3dc {
  font-family: monospace;
  font-size: 12px;
  color: #333;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}
.error-stats.data-v-12bbb3dc {
  display: flex;
  gap: 20px;
}
.error-stats .stat-item.data-v-12bbb3dc {
  text-align: center;
}
.error-stats .stat-item .stat-number.data-v-12bbb3dc {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}
.error-stats .stat-item .stat-number.low.data-v-12bbb3dc {
  color: #10b981;
}
.error-stats .stat-item .stat-number.medium.data-v-12bbb3dc {
  color: #f59e0b;
}
.error-stats .stat-item .stat-number.high.data-v-12bbb3dc {
  color: #ef4444;
}
.error-stats .stat-item .stat-number.critical.data-v-12bbb3dc {
  color: #dc2626;
}
.error-stats .stat-item .stat-label.data-v-12bbb3dc {
  display: block;
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}
.error-list .error-item.data-v-12bbb3dc {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 10px;
}
.error-list .error-item .error-header.data-v-12bbb3dc {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 8px;
}
.error-list .error-item .error-header .error-type.data-v-12bbb3dc {
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
}
.error-list .error-item .error-header .error-level.data-v-12bbb3dc {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
}
.error-list .error-item .error-header .error-level.low.data-v-12bbb3dc {
  background: #10b981;
}
.error-list .error-item .error-header .error-level.medium.data-v-12bbb3dc {
  background: #f59e0b;
}
.error-list .error-item .error-header .error-level.high.data-v-12bbb3dc {
  background: #ef4444;
}
.error-list .error-item .error-header .error-level.critical.data-v-12bbb3dc {
  background: #dc2626;
}
.error-list .error-item .error-header .error-time.data-v-12bbb3dc {
  font-size: 10px;
  color: #999;
  margin-left: auto;
}
.error-list .error-item .error-message.data-v-12bbb3dc {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}
.error-list .error-item .error-page.data-v-12bbb3dc {
  display: block;
  font-size: 12px;
  color: #666;
}
.error-list .empty-state.data-v-12bbb3dc {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}
.actions.data-v-12bbb3dc {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}
.actions .action-btn.data-v-12bbb3dc {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  font-size: 14px;
}
.actions .action-btn.primary.data-v-12bbb3dc {
  background: #667eea;
  color: white;
  border-color: #667eea;
}