{"name": "japanese-learning-miniprogram", "version": "1.0.0", "description": "口语便利店 - 日语学习打卡小程序", "main": "main.js", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-dingtalk": "uni -p mp-dingtalk", "dev:quickapp-webview": "uni -p quickapp-webview", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-dingtalk": "uni build -p mp-dingtalk", "build:quickapp-webview": "uni build -p quickapp-webview", "icons:generate": "open scripts/generate-icons.html", "icons:convert": "node scripts/convert-icons.js", "check:vue3": "node scripts/check-vue3-support.js", "check:miniprogram": "node scripts/check-miniprogram.js", "check:api": "node scripts/check-api-definitions.js", "test:api-integration": "node scripts/test-api-integration.js", "postinstall": "node scripts/test-api-integration.js", "debug": "node scripts/start-debug.js", "test:api": "echo 'API测试页面: http://localhost:3000/#/pages/debug/api-test'", "mp:weixin": "node scripts/start-miniprogram.js weixin", "mp:dingtalk": "node scripts/start-miniprogram.js dingtalk", "start:weixin": "npm run mp:weixin", "start:dingtalk": "npm run mp:dingt<PERSON>"}, "keywords": ["uniapp", "miniprogram", "japanese", "learning", "education"], "author": "Japanese Learning Team", "license": "MIT", "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-app-plus": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-components": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-h5": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-3060920230817001", "vue": "^3.3.4", "vue-i18n": "^9.1.9"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-3060920230817001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-3060920230817001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-3060920230817001", "@vue/tsconfig": "^0.1.3", "sass": "^1.56.1", "typescript": "^4.9.4", "vite": "^4.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}