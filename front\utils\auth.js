/**
 * jeecg-boot认证管理器
 * 处理用户登录、token管理、权限验证等
 */

import { userApi } from '@/api/index.js'

// Token存储键名
const TOKEN_KEY = 'X-Access-Token'
const USER_INFO_KEY = 'userInfo'
const PERMISSIONS_KEY = 'permissions'
const ROLES_KEY = 'roles'

/**
 * 安全获取本地存储数据
 * @param {string} key 存储键名
 * @param {*} defaultValue 默认值
 * @param {string} expectedType 期望的数据类型 'object' | 'array' | 'string'
 */
function safeGetStorage(key, defaultValue = null, expectedType = 'object') {
  try {
    const data = uni.getStorageSync(key)

    if (!data) {
      return defaultValue
    }

    // 如果数据类型符合期望，直接返回
    if (expectedType === 'object' && typeof data === 'object' && !Array.isArray(data)) {
      return data
    }

    if (expectedType === 'array' && Array.isArray(data)) {
      return data
    }

    if (expectedType === 'string' && typeof data === 'string') {
      return data
    }

    // 如果是字符串，尝试解析JSON
    if (typeof data === 'string') {
      try {
        const parsed = JSON.parse(data)

        // 验证解析后的数据类型
        if (expectedType === 'object' && typeof parsed === 'object' && !Array.isArray(parsed)) {
          return parsed
        }

        if (expectedType === 'array' && Array.isArray(parsed)) {
          return parsed
        }

        if (expectedType === 'string') {
          return parsed
        }

        // 类型不匹配，返回默认值
        console.warn(`存储数据类型不匹配，期望: ${expectedType}, 实际: ${typeof parsed}`, key, parsed)
        return defaultValue

      } catch (parseError) {
        console.error(`JSON解析失败 [${key}]:`, parseError, '原始数据:', data)
        return defaultValue
      }
    }

    // 其他情况，返回默认值
    console.warn(`存储数据类型异常 [${key}]:`, typeof data, data)
    return defaultValue

  } catch (error) {
    console.error(`获取存储数据失败 [${key}]:`, error)
    return defaultValue
  }
}

/**
 * 安全设置本地存储数据
 * @param {string} key 存储键名
 * @param {*} data 要存储的数据
 */
function safeSetStorage(key, data) {
  try {
    if (data === null || data === undefined) {
      uni.removeStorageSync(key)
      return true
    }

    // 对象和数组转换为JSON字符串存储
    if (typeof data === 'object') {
      uni.setStorageSync(key, JSON.stringify(data))
    } else {
      uni.setStorageSync(key, data)
    }

    return true
  } catch (error) {
    console.error(`设置存储数据失败 [${key}]:`, error)
    return false
  }
}

/**
 * 获取Token
 */
export function getToken() {
  try {
    return uni.getStorageSync(TOKEN_KEY)
  } catch (error) {
    console.error('获取Token失败:', error)
    return null
  }
}

/**
 * 设置Token
 */
export function setToken(token) {
  try {
    uni.setStorageSync(TOKEN_KEY, token)
    
    // 同步到全局数据
    const app = getApp()
    if (app && app.globalData) {
      app.globalData.token = token
    }
    
    return true
  } catch (error) {
    console.error('设置Token失败:', error)
    return false
  }
}

/**
 * 移除Token
 */
export function removeToken() {
  try {
    uni.removeStorageSync(TOKEN_KEY)
    
    // 清除全局数据
    const app = getApp()
    if (app && app.globalData) {
      app.globalData.token = null
    }
    
    return true
  } catch (error) {
    console.error('移除Token失败:', error)
    return false
  }
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return safeGetStorage(USER_INFO_KEY, null, 'object')
}

/**
 * 设置用户信息
 */
export function setUserInfo(userInfo) {
  try {
    if (!userInfo || typeof userInfo !== 'object') {
      console.error('用户信息格式错误:', userInfo)
      return false
    }

    // 使用安全存储函数
    const success = safeSetStorage(USER_INFO_KEY, userInfo)

    if (success) {
      // 同步到全局数据
      const app = getApp()
      if (app && app.globalData) {
        app.globalData.userInfo = userInfo
        app.globalData.isLogin = true  // 使用App.vue中定义的字段名
        app.globalData.isLoggedIn = true  // 兼容性字段
      }

      console.log('✅ 用户信息保存成功:', {
        username: userInfo.username,
        realname: userInfo.realname,
        id: userInfo.id
      })
    }

    return success
  } catch (error) {
    console.error('设置用户信息失败:', error)
    return false
  }
}

/**
 * 移除用户信息
 */
export function removeUserInfo() {
  try {
    uni.removeStorageSync(USER_INFO_KEY)
    
    // 清除全局数据
    const app = getApp()
    if (app && app.globalData) {
      app.globalData.userInfo = null
      app.globalData.isLogin = false  // 使用App.vue中定义的字段名
      app.globalData.isLoggedIn = false  // 兼容性字段
    }
    
    return true
  } catch (error) {
    console.error('移除用户信息失败:', error)
    return false
  }
}

/**
 * 获取用户权限
 */
export function getPermissions() {
  return safeGetStorage(PERMISSIONS_KEY, [], 'array')
}

/**
 * 设置用户权限
 */
export function setPermissions(permissions) {
  if (!Array.isArray(permissions)) {
    console.error('权限信息必须是数组:', permissions)
    return false
  }
  return safeSetStorage(PERMISSIONS_KEY, permissions)
}

/**
 * 获取用户角色
 */
export function getRoles() {
  return safeGetStorage(ROLES_KEY, [], 'array')
}

/**
 * 设置用户角色
 */
export function setRoles(roles) {
  if (!Array.isArray(roles)) {
    console.error('角色信息必须是数组:', roles)
    return false
  }
  return safeSetStorage(ROLES_KEY, roles)
}

/**
 * 检查是否已登录
 */
export function isLoggedIn() {
  const token = getToken()
  const userInfo = getUserInfo()
  return !!(token && userInfo)
}

/**
 * 用户登录
 */
export async function login(loginData) {
  try {
    console.log('🔐 开始登录:', loginData)
    
    const response = await userApi.login(loginData)
    
    if (response.success) {
      const { token, userInfo } = response.result || response.data
      
      // 保存认证信息
      setToken(token)
      setUserInfo(userInfo)
      
      // 获取用户权限和角色（如果有）
      if (userInfo.permissions) {
        setPermissions(userInfo.permissions)
      }
      if (userInfo.roles) {
        setRoles(userInfo.roles)
      }
      
      console.log('✅ 登录成功:', userInfo)
      
      return {
        success: true,
        data: userInfo,
        token: token
      }
    } else {
      console.error('❌ 登录失败:', response.message)
      return {
        success: false,
        message: response.message || '登录失败'
      }
    }
  } catch (error) {
    console.error('❌ 登录异常:', error)
    return {
      success: false,
      message: error.message || '登录异常，请重试'
    }
  }
}

/**
 * 用户登出
 */
export async function logout() {
  try {
    console.log('🚪 开始登出')
    
    // 调用后端登出接口
    try {
      await userApi.logout()
    } catch (error) {
      console.warn('后端登出接口调用失败:', error)
    }
    
    // 清除本地存储
    removeToken()
    removeUserInfo()
    uni.removeStorageSync(PERMISSIONS_KEY)
    uni.removeStorageSync(ROLES_KEY)
    
    console.log('✅ 登出成功')
    
    return { success: true }
  } catch (error) {
    console.error('❌ 登出异常:', error)
    return {
      success: false,
      message: error.message || '登出异常'
    }
  }
}

/**
 * 刷新用户信息
 */
export async function refreshUserInfo() {
  try {
    console.log('🔄 刷新用户信息')
    
    const response = await userApi.getUserInfo()
    
    if (response.success) {
      const userInfo = response.result || response.data
      setUserInfo(userInfo)
      
      console.log('✅ 用户信息刷新成功')
      return { success: true, data: userInfo }
    } else {
      console.error('❌ 用户信息刷新失败:', response.message)
      return { success: false, message: response.message }
    }
  } catch (error) {
    console.error('❌ 用户信息刷新异常:', error)
    
    // 如果是401错误，清除登录状态
    if (error.statusCode === 401) {
      await logout()
      return { success: false, message: '登录已过期，请重新登录', needLogin: true }
    }
    
    return { success: false, message: error.message || '刷新失败' }
  }
}

/**
 * 检查权限
 */
export function hasPermission(permission) {
  const permissions = getPermissions()
  return permissions.includes(permission)
}

/**
 * 检查角色
 */
export function hasRole(role) {
  const roles = getRoles()
  return roles.includes(role)
}

/**
 * 检查多个权限（AND关系）
 */
export function hasAllPermissions(permissions) {
  const userPermissions = getPermissions()
  return permissions.every(permission => userPermissions.includes(permission))
}

/**
 * 检查多个权限（OR关系）
 */
export function hasAnyPermission(permissions) {
  const userPermissions = getPermissions()
  return permissions.some(permission => userPermissions.includes(permission))
}

/**
 * 获取用户显示名称
 */
export function getUserDisplayName() {
  const userInfo = getUserInfo()
  if (!userInfo) return '未登录'
  
  return userInfo.realname || userInfo.username || '用户'
}

/**
 * 获取用户头像URL
 */
export function getUserAvatar() {
  const userInfo = getUserInfo()
  if (!userInfo || !userInfo.avatar) {
    return '/static/images/default-avatar.png'
  }

  // 如果是相对路径，添加基础URL
  if (userInfo.avatar.startsWith('/')) {
    return `${getApiBaseUrl()}${userInfo.avatar}`
  }

  return userInfo.avatar
}

/**
 * 检查是否需要登录
 * 如果未登录，自动跳转到登录页面
 */
export function requireLogin(currentRoute = '') {
  if (!isLoggedIn()) {
    console.log('🔐 需要登录，跳转到登录页面')

    const redirectUrl = currentRoute && currentRoute !== 'pages/login/login'
      ? encodeURIComponent(`/${currentRoute}`)
      : ''

    uni.reLaunch({
      url: `/pages/login/login${redirectUrl ? `?redirect=${redirectUrl}` : ''}`
    })

    return false
  }

  return true
}

/**
 * 导出认证管理器
 */
export default {
  getToken,
  setToken,
  removeToken,
  getUserInfo,
  setUserInfo,
  removeUserInfo,
  getPermissions,
  setPermissions,
  getRoles,
  setRoles,
  isLoggedIn,
  login,
  logout,
  refreshUserInfo,
  hasPermission,
  hasRole,
  hasAllPermissions,
  hasAnyPermission,
  getUserDisplayName,
  getUserAvatar,
  requireLogin
}
