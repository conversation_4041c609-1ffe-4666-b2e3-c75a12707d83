<view class="login-success-test-container data-v-201a54f3"><view class="test-header data-v-201a54f3"><text class="test-title data-v-201a54f3">登录成功数据处理测试</text></view><view class="test-section data-v-201a54f3"><text class="section-title data-v-201a54f3">当前认证状态</text><view class="auth-status data-v-201a54f3"><view class="status-item data-v-201a54f3"><text class="status-label data-v-201a54f3">Token状态:</text><text class="{{['status-value', 'data-v-201a54f3', b]}}">{{a}}</text></view><view class="status-item data-v-201a54f3"><text class="status-label data-v-201a54f3">Token值:</text><text class="status-value token data-v-201a54f3">{{c}}</text></view><view class="status-item data-v-201a54f3"><text class="status-label data-v-201a54f3">用户信息:</text><text class="{{['status-value', 'data-v-201a54f3', e]}}">{{d}}</text></view><view class="status-item data-v-201a54f3"><text class="status-label data-v-201a54f3">用户名:</text><text class="status-value data-v-201a54f3">{{f}}</text></view><view class="status-item data-v-201a54f3"><text class="status-label data-v-201a54f3">真实姓名:</text><text class="status-value data-v-201a54f3">{{g}}</text></view><view class="status-item data-v-201a54f3"><text class="status-label data-v-201a54f3">全局登录状态:</text><text class="{{['status-value', 'data-v-201a54f3', i]}}">{{h}}</text></view></view></view><view class="test-section data-v-201a54f3"><text class="section-title data-v-201a54f3">模拟登录数据测试</text><view class="mock-login data-v-201a54f3"><view class="mock-data data-v-201a54f3"><text class="mock-title data-v-201a54f3">模拟登录响应数据:</text><view class="mock-code data-v-201a54f3"><text class="code-text data-v-201a54f3">{{j}}</text></view></view><view class="test-buttons data-v-201a54f3"><button class="test-btn primary data-v-201a54f3" bindtap="{{k}}">测试保存认证信息</button><button class="test-btn success data-v-201a54f3" bindtap="{{l}}">测试请求头</button><button class="test-btn warning data-v-201a54f3" bindtap="{{m}}">测试全局状态</button><button class="test-btn error data-v-201a54f3" bindtap="{{n}}">清除认证信息</button></view></view></view><view class="test-section data-v-201a54f3"><text class="section-title data-v-201a54f3">本地存储检查</text><view class="storage-check data-v-201a54f3"><view wx:for="{{o}}" wx:for-item="item" wx:key="c" class="storage-item data-v-201a54f3"><text class="storage-key data-v-201a54f3">{{item.a}}:</text><text class="storage-value data-v-201a54f3">{{item.b}}</text></view></view></view><view class="test-section data-v-201a54f3"><text class="section-title data-v-201a54f3">测试日志</text><scroll-view class="log-container data-v-201a54f3" scroll-y><view wx:for="{{p}}" wx:for-item="log" wx:key="d" class="log-item data-v-201a54f3"><text class="log-time data-v-201a54f3">{{log.a}}</text><text class="{{['log-message', 'data-v-201a54f3', log.c]}}">{{log.b}}</text></view></scroll-view></view></view>