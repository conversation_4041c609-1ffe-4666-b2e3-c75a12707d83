<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>口语便利店 - 首页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { 
            overflow: hidden; 
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar { display: none; }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .content {
            height: calc(100% - 44px - 83px);
            overflow-y: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .content::-webkit-scrollbar { display: none; }
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
            font-size: 12px;
        }
        .tab-item.active {
            color: #667eea;
        }
        .level-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .progress-ring {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(#667eea 0deg 216deg, #e5e7eb 216deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .progress-ring::before {
            content: '';
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }
        .progress-text {
            position: relative;
            z-index: 1;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 头部问候 -->
                <div class="px-6 py-6">
                    <h1 class="text-white text-2xl font-bold mb-2">こんにちは！</h1>
                    <p class="text-white/80 text-base">今天也要加油学习日语哦～</p>
                </div>

                <!-- 今日打卡状态 -->
                <div class="level-card">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-bold text-gray-800">今日学习</h2>
                        <div class="progress-ring">
                            <span class="progress-text">60%</span>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-600">
                        <span>已完成 3/5 课程</span>
                        <span class="text-green-500 font-medium">已打卡</span>
                    </div>
                </div>

                <!-- 课程级别选择 -->
                <div class="px-6">
                    <h2 class="text-white text-lg font-bold mb-4">选择学习级别</h2>
                    
                    <!-- 初级 -->
                    <div class="level-card mb-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-seedling text-green-500 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-800">初级 N5-N4</h3>
                                    <p class="text-sm text-gray-600">基础对话练习</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">12/20 课程</div>
                                <div class="w-16 h-2 bg-gray-200 rounded-full mt-1">
                                    <div class="w-3/5 h-full bg-green-400 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 中级 -->
                    <div class="level-card mb-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-mountain text-blue-500 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-800">中级 N3-N2</h3>
                                    <p class="text-sm text-gray-600">日常会话提升</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">5/25 课程</div>
                                <div class="w-16 h-2 bg-gray-200 rounded-full mt-1">
                                    <div class="w-1/5 h-full bg-blue-400 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 高级 -->
                    <div class="level-card mb-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-crown text-purple-500 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-800">高级 N1</h3>
                                    <p class="text-sm text-gray-600">商务日语精进</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">0/30 课程</div>
                                <div class="w-16 h-2 bg-gray-200 rounded-full mt-1">
                                    <div class="w-0 h-full bg-purple-400 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 专业级 -->
                    <div class="level-card mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-star text-orange-500 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-800">专业级</h3>
                                    <p class="text-sm text-gray-600">新闻听力训练</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-orange-500 font-medium">VIP</div>
                                <div class="text-xs text-gray-500">解锁更多</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="tab-bar">
                <div class="tab-item active">
                    <i class="fas fa-home text-xl mb-1"></i>
                    <span>首页</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-book text-xl mb-1"></i>
                    <span>课程</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-calendar-check text-xl mb-1"></i>
                    <span>打卡</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-trophy text-xl mb-1"></i>
                    <span>排行</span>
                </div>
                <div class="tab-item">
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
