"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const config_api = require("../../config/api.js");
const utils_request = require("../../utils/request.js");
const utils_captcha = require("../../utils/captcha.js");
const api_jeecgUser = require("../../api/jeecg-user.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      apiBaseUrl: "",
      currentPlatform: "",
      testHeaders: {},
      testResults: []
    };
  },
  onLoad() {
    this.initTestInfo();
  },
  methods: {
    // 初始化测试信息
    initTestInfo() {
      this.apiBaseUrl = config_api.getApiBaseUrl();
      this.testHeaders = config_api.getRequestHeaders();
      this.currentPlatform = "微信小程序";
      this.addResult("初始化", "配置信息加载完成", true, {
        baseUrl: this.apiBaseUrl,
        platform: this.currentPlatform,
        headers: this.testHeaders
      });
    },
    // 添加测试结果
    addResult(title, message, success, details = null) {
      const now = /* @__PURE__ */ new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;
      this.testResults.unshift({
        time,
        title,
        message,
        success,
        details
      });
      if (this.testResults.length > 20) {
        this.testResults = this.testResults.slice(0, 20);
      }
    },
    // 测试基础连接
    testBaseConnection() {
      return __async(this, null, function* () {
        this.addResult("基础连接测试", "开始测试...", true);
        try {
          const response = yield utils_request.request({
            url: "/sys/common/403",
            method: "GET",
            showLoading: false
          });
          this.addResult("基础连接测试", "连接成功", true, response);
        } catch (error) {
          if (error.message.includes("403") || error.message.includes("权限")) {
            this.addResult("基础连接测试", "连接成功（403权限错误是正常的）", true, {
              error: error.message
            });
          } else {
            this.addResult("基础连接测试", `连接失败: ${error.message}`, false, {
              error: error.message
            });
          }
        }
      });
    },
    // 测试验证码接口
    testCaptchaApi() {
      return __async(this, null, function* () {
        this.addResult("验证码接口测试", "开始测试...", true);
        try {
          const result = yield utils_captcha.getCaptcha(true);
          if (result.success) {
            this.addResult("验证码接口测试", "验证码获取成功", true, {
              hasImage: !!result.data.image,
              hasKey: !!result.data.key
            });
          } else {
            this.addResult("验证码接口测试", `验证码获取失败: ${result.message}`, false, result);
          }
        } catch (error) {
          this.addResult("验证码接口测试", `验证码接口异常: ${error.message}`, false, {
            error: error.message
          });
        }
      });
    },
    // 测试登录接口
    testLoginApi() {
      return __async(this, null, function* () {
        this.addResult("登录接口测试", "开始测试...", true);
        try {
          const captchaResult = yield utils_captcha.getCaptcha(true);
          if (!captchaResult.success) {
            this.addResult("登录接口测试", "获取验证码失败，无法测试登录", false);
            return;
          }
          const loginResult = yield api_jeecgUser.login({
            username: "test",
            password: "wrongpassword",
            captcha: "1234",
            checkKey: captchaResult.data.key
          });
          this.addResult("登录接口测试", "登录接口调用成功（预期登录失败）", true, {
            loginResult
          });
        } catch (error) {
          if (error.message.includes("用户名") || error.message.includes("密码") || error.message.includes("验证码")) {
            this.addResult("登录接口测试", "登录接口调用成功（预期登录失败）", true, {
              error: error.message
            });
          } else {
            this.addResult("登录接口测试", `登录接口异常: ${error.message}`, false, {
              error: error.message
            });
          }
        }
      });
    },
    // 测试用户信息接口
    testUserInfoApi() {
      return __async(this, null, function* () {
        this.addResult("用户信息接口测试", "开始测试...", true);
        try {
          const response = yield utils_request.request({
            url: "/sys/user/userInfo",
            method: "GET",
            showLoading: false
          });
          this.addResult("用户信息接口测试", "用户信息获取成功", true, response);
        } catch (error) {
          if (error.message.includes("401") || error.message.includes("登录")) {
            this.addResult("用户信息接口测试", "接口存在（需要登录）", true, {
              error: error.message
            });
          } else {
            this.addResult("用户信息接口测试", `接口异常: ${error.message}`, false, {
              error: error.message
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.apiBaseUrl),
    b: common_vendor.t($data.currentPlatform),
    c: common_vendor.f($data.testHeaders, (value, key, i0) => {
      return {
        a: common_vendor.t(key),
        b: common_vendor.t(value || "(空)"),
        c: key
      };
    }),
    d: common_vendor.o((...args) => $options.testBaseConnection && $options.testBaseConnection(...args)),
    e: common_vendor.o((...args) => $options.testCaptchaApi && $options.testCaptchaApi(...args)),
    f: common_vendor.o((...args) => $options.testLoginApi && $options.testLoginApi(...args)),
    g: common_vendor.o((...args) => $options.testUserInfoApi && $options.testUserInfoApi(...args)),
    h: common_vendor.f($data.testResults, (result, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(result.time),
        b: common_vendor.t(result.success ? "成功" : "失败"),
        c: common_vendor.n(result.success ? "success" : "error"),
        d: common_vendor.t(result.title),
        e: common_vendor.t(result.message),
        f: result.details
      }, result.details ? {
        g: common_vendor.t(JSON.stringify(result.details, null, 2))
      } : {}, {
        h: index
      });
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e7967972"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/api-config-test.js.map
