"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const mixins_pageMixin = require("../../mixins/page-mixin.js");
const mixins_authMixin = require("../../mixins/auth-mixin.js");
const MpPageWrapper = () => "../../components/mp-page-wrapper/mp-page-wrapper.js";
const _sfc_main = {
  mixins: [mixins_pageMixin.pageMixin, mixins_authMixin.authMixin],
  components: {
    MpPageWrapper
  },
  data() {
    return {
      statusBarHeight: 0,
      loading: true,
      // 用户学习数据
      todayProgress: 0,
      completedCourses: 0,
      totalCourses: 0,
      isCheckedIn: false,
      // 学习统计
      studyStats: null,
      // 课程级别数据
      levels: [
        {
          id: 1,
          name: "初级 N5-N4",
          description: "基础对话练习",
          icon: "icon-seedling",
          iconBg: "#dcfce7",
          iconColor: "#16a34a",
          completed: 0,
          total: 0,
          progress: 0,
          progressColor: "#16a34a",
          level: "beginner"
        },
        {
          id: 2,
          name: "中级 N3-N2",
          description: "日常会话提升",
          icon: "icon-mountain",
          iconBg: "#dbeafe",
          iconColor: "#2563eb",
          completed: 0,
          total: 0,
          progress: 0,
          progressColor: "#2563eb",
          level: "intermediate"
        },
        {
          id: 3,
          name: "高级 N1",
          description: "商务日语精进",
          icon: "icon-crown",
          iconBg: "#f3e8ff",
          iconColor: "#9333ea",
          completed: 0,
          total: 0,
          progress: 0,
          progressColor: "#9333ea",
          level: "advanced"
        },
        {
          id: 4,
          name: "专业级",
          description: "新闻听力训练",
          icon: "icon-star",
          iconBg: "#fed7aa",
          iconColor: "#ea580c",
          completed: 0,
          total: 0,
          progress: 0,
          progressColor: "#ea580c",
          level: "professional",
          isVip: true
        }
      ]
    };
  },
  // 页面生命周期
  onShow() {
    this.checkAuthStatus();
  },
  // 监听认证状态变化
  onLoad() {
    common_vendor.index.$on("authStateChanged", this.handleAuthStateChanged);
  },
  onUnload() {
    common_vendor.index.$off("authStateChanged", this.handleAuthStateChanged);
  },
  methods: {
    // 处理认证状态变化
    handleAuthStateChanged(data) {
      common_vendor.index.__f__("log", "at pages/index/index.vue:172", "🔄 首页收到认证状态变化事件:", data);
      if (data.isAuthenticated) {
        this.checkAuthStatus();
        this.loadUserData();
      } else {
        this.setDefaultStudyData();
      }
    },
    // 页面特定初始化
    pageInit() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:184", "🏠 首页初始化");
      this.loadUserData();
    },
    // 刷新数据
    onRefresh() {
      return __async(this, null, function* () {
        common_vendor.index.__f__("log", "at pages/index/index.vue:190", "🔄 首页刷新");
        yield this.loadUserData();
      });
    },
    loadUserData() {
      return __async(this, null, function* () {
        this.loading = true;
        try {
          this.checkAuthStatus();
          yield Promise.all([
            this.getUserStudyData(),
            this.getCourseProgress(),
            this.checkTodayCheckin()
          ]);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:208", "加载用户数据失败:", error);
          this.showErrorToast("数据加载失败，请下拉刷新重试");
        } finally {
          this.loading = false;
        }
      });
    },
    // 获取用户学习统计数据
    getUserStudyData() {
      return __async(this, null, function* () {
        try {
          const response = yield api_index.userApi.getStudyStats();
          if (response.success) {
            this.studyStats = response.data;
            const stats = response.data;
            this.todayProgress = stats.todayProgress || 0;
            this.completedCourses = stats.todayCompletedCourses || 0;
            this.totalCourses = stats.todayTotalCourses || 0;
            common_vendor.index.__f__("log", "at pages/index/index.vue:229", "用户学习统计:", stats);
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:233", "获取学习统计失败:", error);
          this.setDefaultStudyData();
        }
      });
    },
    // 获取各级别课程进度
    getCourseProgress() {
      return __async(this, null, function* () {
        try {
          const response = yield api_index.courseApi.getCourseList({
            includeProgress: true
          });
          common_vendor.index.__f__("log", "at pages/index/index.vue:245", "XXXXXXXXXXXXX:", response);
          if (response.success && response.data.records) {
            this.updateLevelsProgress(response.data.records);
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:250", "获取课程进度失败:", error);
        }
      });
    },
    // 检查今日签到状态
    checkTodayCheckin() {
      return __async(this, null, function* () {
        try {
          const response = yield api_index.userApi.getCheckinHistory({
            date: (/* @__PURE__ */ new Date()).toISOString().split("T")[0]
          });
          if (response.success) {
            this.isCheckedIn = response.data.isCheckedToday || false;
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:265", "检查签到状态失败:", error);
          this.isCheckedIn = false;
        }
      });
    },
    // 更新级别进度数据
    updateLevelsProgress(courseData) {
      const levelStats = {
        beginner: { completed: 0, total: 0 },
        intermediate: { completed: 0, total: 0 },
        advanced: { completed: 0, total: 0 },
        professional: { completed: 0, total: 0 }
      };
      courseData.forEach((course) => {
        const level = course.level || "beginner";
        if (levelStats[level]) {
          levelStats[level].total++;
          if (course.isCompleted) {
            levelStats[level].completed++;
          }
        }
      });
      this.levels.forEach((level) => {
        const stats = levelStats[level.level];
        if (stats) {
          level.completed = stats.completed;
          level.total = stats.total;
          level.progress = stats.total > 0 ? Math.round(stats.completed / stats.total * 100) : 0;
        }
      });
    },
    // 设置默认学习数据
    setDefaultStudyData() {
      this.todayProgress = 0;
      this.completedCourses = 0;
      this.totalCourses = 0;
    },
    // 显示错误提示
    showErrorToast(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 3e3
      });
    },
    // 跳转到课程列表
    goToCourseList(level) {
      if (level.isVip && !this.checkVipStatus()) {
        this.showVipDialog();
        return;
      }
      common_vendor.index.setStorageSync("selectedCourseLevel", {
        level: level.level,
        title: level.name,
        timestamp: Date.now()
      });
      common_vendor.index.switchTab({
        url: "/pages/course/list",
        success: () => {
          common_vendor.index.__f__("log", "at pages/index/index.vue:337", "✅ 成功跳转到课程列表页面");
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/index/index.vue:340", "❌ 跳转课程列表失败:", error);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "none"
          });
        }
      });
    },
    // 检查VIP状态
    checkVipStatus() {
      if (this.currentUser && this.currentUser.vipStatus) {
        return this.currentUser.vipStatus.isVip || false;
      }
      return false;
    },
    // 显示VIP对话框
    showVipDialog() {
      common_vendor.index.showModal({
        title: "提示",
        content: "该课程需要VIP会员才能学习，是否立即开通？",
        confirmText: "开通VIP",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            this.goToVipPage();
          }
        }
      });
    },
    // 跳转到VIP页面
    goToVipPage() {
      common_vendor.index.navigateTo({
        url: "/pages/vip/vip"
      });
    },
    // 手动签到
    handleCheckin() {
      return __async(this, null, function* () {
        if (this.isCheckedIn) {
          common_vendor.index.showToast({
            title: "今日已签到",
            icon: "none"
          });
          return;
        }
        try {
          common_vendor.index.showLoading({ title: "签到中..." });
          const response = yield api_index.userApi.checkin();
          if (response.success) {
            this.isCheckedIn = true;
            common_vendor.index.showToast({
              title: "签到成功！",
              icon: "success"
            });
            this.getUserStudyData();
          } else {
            common_vendor.index.showToast({
              title: response.message || "签到失败",
              icon: "none"
            });
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:410", "签到失败:", error);
          common_vendor.index.showToast({
            title: "签到失败，请重试",
            icon: "none"
          });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: _ctx.currentUser
  }, _ctx.currentUser ? {
    c: common_vendor.t(_ctx.getCurrentUserName())
  } : {}, {
    d: common_vendor.t($data.todayProgress),
    e: common_vendor.t($data.completedCourses),
    f: common_vendor.t($data.totalCourses),
    g: common_vendor.t($data.isCheckedIn ? "✓ 已打卡" : "📅 点击打卡"),
    h: $data.isCheckedIn ? 1 : "",
    i: common_vendor.o((...args) => $options.handleCheckin && $options.handleCheckin(...args)),
    j: $data.loading
  }, $data.loading ? {
    k: common_vendor.f(4, (i, k0, i0) => {
      return {
        a: i
      };
    })
  } : {
    l: common_vendor.f($data.levels, (level, k0, i0) => {
      return {
        a: common_vendor.n(level.icon),
        b: level.iconColor,
        c: level.iconBg,
        d: common_vendor.t(level.name),
        e: common_vendor.t(level.description),
        f: common_vendor.t(level.completed),
        g: common_vendor.t(level.total),
        h: level.progress + "%",
        i: level.progressColor,
        j: level.id,
        k: common_vendor.o(($event) => $options.goToCourseList(level), level.id)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
