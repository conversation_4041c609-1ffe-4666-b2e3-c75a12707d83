package com.kuma.learning.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.kuma.learning.entity.KumaLearningRecord;
import com.kuma.learning.service.IKumaLearningRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: kuma_learning_record
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Api(tags="kuma_learning_record")
@RestController
@RequestMapping("/learning/kumaLearningRecord")
@Slf4j
public class KumaLearningRecordController extends JeecgController<KumaLearningRecord, IKumaLearningRecordService> {
	@Autowired
	private IKumaLearningRecordService kumaLearningRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param kumaLearningRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "kuma_learning_record-分页列表查询")
	@ApiOperation(value="kuma_learning_record-分页列表查询", notes="kuma_learning_record-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<KumaLearningRecord>> queryPageList(KumaLearningRecord kumaLearningRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<KumaLearningRecord> queryWrapper = QueryGenerator.initQueryWrapper(kumaLearningRecord, req.getParameterMap());
		Page<KumaLearningRecord> page = new Page<KumaLearningRecord>(pageNo, pageSize);
		IPage<KumaLearningRecord> pageList = kumaLearningRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param kumaLearningRecord
	 * @return
	 */
	@AutoLog(value = "kuma_learning_record-添加")
	@ApiOperation(value="kuma_learning_record-添加", notes="kuma_learning_record-添加")
	@RequiresPermissions("learning:kuma_learning_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody KumaLearningRecord kumaLearningRecord) {
		kumaLearningRecordService.save(kumaLearningRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param kumaLearningRecord
	 * @return
	 */
	@AutoLog(value = "kuma_learning_record-编辑")
	@ApiOperation(value="kuma_learning_record-编辑", notes="kuma_learning_record-编辑")
	@RequiresPermissions("learning:kuma_learning_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody KumaLearningRecord kumaLearningRecord) {
		kumaLearningRecordService.updateById(kumaLearningRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "kuma_learning_record-通过id删除")
	@ApiOperation(value="kuma_learning_record-通过id删除", notes="kuma_learning_record-通过id删除")
	@RequiresPermissions("learning:kuma_learning_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		kumaLearningRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "kuma_learning_record-批量删除")
	@ApiOperation(value="kuma_learning_record-批量删除", notes="kuma_learning_record-批量删除")
	@RequiresPermissions("learning:kuma_learning_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.kumaLearningRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "kuma_learning_record-通过id查询")
	@ApiOperation(value="kuma_learning_record-通过id查询", notes="kuma_learning_record-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<KumaLearningRecord> queryById(@RequestParam(name="id",required=true) String id) {
		KumaLearningRecord kumaLearningRecord = kumaLearningRecordService.getById(id);
		if(kumaLearningRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(kumaLearningRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param kumaLearningRecord
    */
    @RequiresPermissions("learning:kuma_learning_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, KumaLearningRecord kumaLearningRecord) {
        return super.exportXls(request, kumaLearningRecord, KumaLearningRecord.class, "kuma_learning_record");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("learning:kuma_learning_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, KumaLearningRecord.class);
    }

}
