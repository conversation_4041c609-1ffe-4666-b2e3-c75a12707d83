"use strict";
const common_vendor = require("../../common/vendor.js");
const mixins_authMixin = require("../../mixins/auth-mixin.js");
const utils_auth = require("../../utils/auth.js");
const _sfc_main = {
  mixins: [mixins_authMixin.authMixin],
  data() {
    return {
      hasToken: false,
      hasStorageUserInfo: false,
      storageUserInfo: null,
      globalIsLogin: false,
      hasGlobalUserInfo: false,
      globalUserInfo: null,
      testLogs: []
    };
  },
  onLoad() {
    this.addLog("用户信息显示测试页面加载", "info");
    this.refreshAllInfo();
  },
  onShow() {
    this.refreshAllInfo();
  },
  methods: {
    // 添加日志
    addLog(message, type = "info") {
      const now = /* @__PURE__ */ new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;
      this.testLogs.unshift({
        time,
        message,
        type
      });
      if (this.testLogs.length > 20) {
        this.testLogs = this.testLogs.slice(0, 20);
      }
    },
    // 刷新所有信息
    refreshAllInfo() {
      this.checkTokenStatus();
      this.checkStorageUserInfo();
      this.checkGlobalState();
      this.addLog("所有信息刷新完成", "info");
    },
    // 检查Token状态
    checkTokenStatus() {
      const token = common_vendor.index.getStorageSync("X-Access-Token");
      this.hasToken = !!token;
    },
    // 检查本地存储用户信息
    checkStorageUserInfo() {
      try {
        const userInfo = utils_auth.getUserInfo();
        this.hasStorageUserInfo = !!userInfo;
        this.storageUserInfo = userInfo;
      } catch (error) {
        this.hasStorageUserInfo = false;
        this.storageUserInfo = null;
      }
    },
    // 检查全局状态
    checkGlobalState() {
      try {
        const app = getApp();
        if (app && app.globalData) {
          this.globalIsLogin = app.globalData.isLogin || false;
          this.globalUserInfo = app.globalData.userInfo;
          this.hasGlobalUserInfo = !!this.globalUserInfo;
        }
      } catch (error) {
        this.globalIsLogin = false;
        this.hasGlobalUserInfo = false;
        this.globalUserInfo = null;
      }
    },
    // 获取用户头像
    getCurrentUserAvatar() {
      if (this.currentUser && this.currentUser.avatar) {
        return this.currentUser.avatar;
      }
      return "/static/images/default-avatar.png";
    },
    // 刷新用户信息
    refreshUserInfo() {
      this.addLog("手动刷新用户信息", "info");
      this.checkAuthStatus();
      this.refreshAllInfo();
    },
    // 模拟登录
    simulateLogin() {
      this.addLog("模拟登录操作", "warning");
      const mockUserInfo = {
        id: "1001",
        username: "testuser",
        realname: "测试用户",
        avatar: "/static/images/default-avatar.png",
        phone: "13800138000",
        email: "<EMAIL>"
      };
      utils_auth.setUserInfo(mockUserInfo);
      this.addLog("模拟用户信息已保存", "success");
      this.refreshAllInfo();
    },
    // 清除用户信息
    clearUserInfo() {
      this.addLog("清除用户信息", "warning");
      utils_auth.removeUserInfo();
      this.refreshAllInfo();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t(_ctx.isAuthenticated ? "已登录" : "未登录"),
    b: common_vendor.n(_ctx.isAuthenticated ? "success" : "error"),
    c: common_vendor.t($data.hasToken ? "有效" : "无效"),
    d: common_vendor.n($data.hasToken ? "success" : "error"),
    e: common_vendor.t(_ctx.currentUser ? _ctx.currentUser.id : "无"),
    f: common_vendor.t(_ctx.currentUser ? _ctx.currentUser.username : "无"),
    g: common_vendor.t(_ctx.currentUser ? _ctx.currentUser.realname : "无"),
    h: common_vendor.t(_ctx.getCurrentUserName()),
    i: common_vendor.t(_ctx.currentUser ? _ctx.currentUser.avatar : "无"),
    j: common_vendor.t(_ctx.currentUser ? _ctx.currentUser.phone : "无"),
    k: common_vendor.t(_ctx.currentUser ? _ctx.currentUser.email : "无"),
    l: common_vendor.t($data.hasStorageUserInfo ? "已存储" : "未存储"),
    m: common_vendor.n($data.hasStorageUserInfo ? "success" : "error"),
    n: $data.storageUserInfo
  }, $data.storageUserInfo ? {
    o: common_vendor.t($data.storageUserInfo.username || "无")
  } : {}, {
    p: $data.storageUserInfo
  }, $data.storageUserInfo ? {
    q: common_vendor.t($data.storageUserInfo.realname || "无")
  } : {}, {
    r: common_vendor.t($data.globalIsLogin ? "true" : "false"),
    s: common_vendor.n($data.globalIsLogin ? "success" : "error"),
    t: common_vendor.t($data.hasGlobalUserInfo ? "已设置" : "未设置"),
    v: common_vendor.n($data.hasGlobalUserInfo ? "success" : "error"),
    w: $data.globalUserInfo
  }, $data.globalUserInfo ? {
    x: common_vendor.t($data.globalUserInfo.username || "无")
  } : {}, {
    y: common_vendor.t(_ctx.getCurrentUserName()),
    z: $options.getCurrentUserAvatar(),
    A: common_vendor.t(_ctx.getCurrentUserName()),
    B: common_vendor.o((...args) => $options.refreshUserInfo && $options.refreshUserInfo(...args)),
    C: common_vendor.o((...args) => _ctx.checkAuthStatus && _ctx.checkAuthStatus(...args)),
    D: common_vendor.o((...args) => $options.simulateLogin && $options.simulateLogin(...args)),
    E: common_vendor.o((...args) => $options.clearUserInfo && $options.clearUserInfo(...args)),
    F: common_vendor.f($data.testLogs, (log, index, i0) => {
      return {
        a: common_vendor.t(log.time),
        b: common_vendor.t(log.message),
        c: common_vendor.n(log.type),
        d: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a7929c82"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/user-info-display-test.js.map
