<template>
	<view class="page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 自定义导航栏 -->
		<view class="nav-bar">
			<view class="nav-content">
				<view class="nav-left" @click="goBack">
					<text class="iconfont icon-arrow-left"></text>
				</view>
				<view class="nav-title">
					<text class="title">{{ lessionTitle }}</text>
					<text class="subtitle">{{ courseSubtitle }}</text>
				</view>
				<view class="nav-right"></view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="content">
			<!-- 音频播放器 -->
			<view class="audio-player card">
				<view class="flex-between mb-4">
					<view>
						<text class="player-title">课程音频</text>
						<text class="player-duration">时长: {{ courseDuration }}分钟</text>
					</view>
					<view class="play-button" @click="togglePlay">
						<text class="iconfont" :class="isPlaying ? 'icon-pause' : 'icon-play'"></text>
					</view>
				</view>
				
				<view class="waveform" @click="seekAudio">
					<view class="waveform-progress" :style="{ width: audioProgress + '%' }"></view>
				</view>
				
				<view class="flex-between audio-controls">
					<text class="time-text">{{ formatTime(currentTime) }}</text>
					<view class="control-buttons">
						<text class="iconfont icon-backward" @click="seekBackward"></text>
						<text class="iconfont icon-forward" @click="seekForward"></text>
						<text class="iconfont icon-repeat" @click="toggleRepeat" :class="{ active: isRepeat }"></text>
					</view>
					<text class="time-text">{{ formatTime(totalTime) }}</text>
				</view>
			</view>

			<!-- 内容切换标签 -->
			<view class="content-tabs card">
				<view class="tab-buttons">
					<view 
						class="tab-button" 
						v-for="(tab, index) in tabs" 
						:key="index"
						:class="{ active: activeTab === index }"
						@click="switchTab(index)"
					>
						{{ tab.name }}
					</view>
				</view>

				<!-- 标签内容 -->
				<view class="tab-content">
					<!-- 日文原文 -->
					<view v-if="activeTab === 0" class="text-content">
						<view class="dialogue-item" v-for="(item, index) in dialogues" :key="index">
							<view class="speaker">{{ item.speaker }}:</view>
							<view class="text">{{ item.text }}</view>
						</view>
					</view>
					
					<!-- 中文翻译 -->
					<view v-if="activeTab === 1" class="text-content">
						<view class="dialogue-item" v-for="(item, index) in dialogues" :key="index">
							<view class="speaker">{{ item.speaker }}:</view>
							<view class="text">{{ item.translation }}</view>
						</view>
					</view>
					
					<!-- 重点词汇 -->
					<view v-if="activeTab === 2" class="vocabulary-content">
						<view class="vocabulary-item" v-for="(word, index) in vocabulary" :key="index">
							<view class="word-info">
								<text class="word">{{ word.word }}</text>
								<text class="pronunciation">{{ word.pronunciation }}</text>
							</view>
							<text class="meaning">{{ word.meaning }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 学习要点 -->
			<view class="study-tips card">
				<view class="tips-header">
					<text class="iconfont icon-lightbulb"></text>
					<text class="tips-title">学习要点</text>
				</view>
				<view class="tips-list">
					<view class="tip-item" v-for="(tip, index) in studyTips" :key="index">
						<view class="tip-number">{{ index + 1 }}</view>
						<view class="tip-content">
							<text class="tip-title">{{ tip.title }}</text>
							<text class="tip-desc">{{ tip.description }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-action">
			<button class="practice-btn" @click="startPractice">
				<text class="iconfont icon-microphone"></text>
				开始跟读练习
			</button>
		</view>
	</view>
</template>

<script>
import { courseApi } from '@/api/index.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			lessionId: '',
			loading: true,
			courseDetail: null,
			lessionTitle: '加载中...',
			courseSubtitle: '',
			courseDuration: 0,
			audioUrl: '',
			isPlaying: false,
			isRepeat: false,
			currentTime: 0,
			totalTime: 0,
			audioProgress: 0,
			activeTab: 0,
			tabs: [
				{ name: '日文原文' },
				{ name: '中文翻译' },
				{ name: '重点词汇' }
			],
			dialogues: [
				{
					speaker: 'A',
					text: 'いらっしゃいませ。何名様ですか？',
					translation: '欢迎光临。请问几位？'
				},
				{
					speaker: 'B',
					text: '二人です。',
					translation: '两个人。'
				},
				{
					speaker: 'A',
					text: 'こちらのお席へどうぞ。メニューです。',
					translation: '请到这边的座位。这是菜单。'
				},
				{
					speaker: 'B',
					text: 'ありがとうございます。',
					translation: '谢谢。'
				}
			],
			vocabulary: [
				{
					word: 'いらっしゃいませ',
					pronunciation: 'irasshaimase',
					meaning: '欢迎光临'
				},
				{
					word: '何名様',
					pronunciation: 'nanmeisama',
					meaning: '几位客人'
				},
				{
					word: 'メニュー',
					pronunciation: 'menyuu',
					meaning: '菜单'
				}
			],
			studyTips: [
				{
					title: '敬语的使用',
					description: '注意服务员使用的敬语表达'
				},
				{
					title: '点餐用语',
					description: '掌握常用的点餐表达方式'
				},
				{
					title: '支付方式',
					description: '了解不同支付方式的表达'
				}
			]
		}
	},
	
	onLoad(options) {
		this.lessionId = options.lessonId
		this.lessionTitle = decodeURIComponent(options.title || '加载中...')
		this.initPage()
	},

	methods: {
		initPage() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight
			this.loadCourseDetail()
		},

		// 加载课程详情
		async loadCourseDetail() {
			if (!this.lessionId) {
				uni.showToast({
					title: '课程ID不能为空',
					icon: 'none'
				})
				return
			}

			this.loading = true

			try {
				console.log('加载课程详情:', this.lessionId)

				const response = await courseApi.getLessonDetail(this.lessionId)

				if (response.success && response.data) {
					this.courseDetail = response.data
					this.updateCourseInfo(response.data)
				} else {
					throw new Error(response.message || '获取课程详情失败')
				}

			} catch (error) {
				console.error('加载课程详情失败:', error)

				uni.showToast({
					title: '课程详情加载失败，请重试',
					icon: 'none',
					duration: 2000
				})
			} finally {
				this.loading = false
			}
		},

		// 更新课程信息
		updateCourseInfo(courseData) {
			this.lessionTitle = courseData.title || this.lessionTitle
			this.courseSubtitle = courseData.subtitle || courseData.description || ''
			this.courseDuration = courseData.duration || 0
			this.audioUrl = courseData.audioUrl || ''
			this.totalTime = (courseData.duration || 0) * 60 // 转换为秒

			// 更新对话内容
			if (courseData.dialogues && courseData.dialogues.length > 0) {
				this.dialogues = courseData.dialogues
			}

			// 更新词汇表
			if (courseData.vocabulary && courseData.vocabulary.length > 0) {
				this.vocabulary = courseData.vocabulary
			}

			// 更新学习要点
			if (courseData.studyTips && courseData.studyTips.length > 0) {
				this.studyTips = courseData.studyTips
			}
		},


		
		goBack() {
			uni.navigateBack()
		},
		
		togglePlay() {
			this.isPlaying = !this.isPlaying
			if (this.isPlaying) {
				this.startAudioProgress()
			} else {
				this.pauseAudioProgress()
			}
		},
		
		startAudioProgress() {
			// 模拟音频播放进度
			this.audioTimer = setInterval(() => {
				if (this.currentTime < this.totalTime) {
					this.currentTime += 1
					this.audioProgress = (this.currentTime / this.totalTime) * 100
				} else {
					this.isPlaying = false
					clearInterval(this.audioTimer)
				}
			}, 1000)
		},
		
		pauseAudioProgress() {
			if (this.audioTimer) {
				clearInterval(this.audioTimer)
			}
		},
		
		seekAudio(e) {
			// 点击波形图跳转播放位置
			const rect = e.currentTarget.getBoundingClientRect()
			const clickX = e.detail.x - rect.left
			const progress = clickX / rect.width
			this.currentTime = Math.floor(this.totalTime * progress)
			this.audioProgress = progress * 100
		},
		
		seekBackward() {
			this.currentTime = Math.max(0, this.currentTime - 10)
			this.audioProgress = (this.currentTime / this.totalTime) * 100
		},
		
		seekForward() {
			this.currentTime = Math.min(this.totalTime, this.currentTime + 10)
			this.audioProgress = (this.currentTime / this.totalTime) * 100
		},
		
		toggleRepeat() {
			this.isRepeat = !this.isRepeat
		},
		
		switchTab(index) {
			this.activeTab = index
		},
		
		formatTime(seconds) {
			const mins = Math.floor(seconds / 60)
			const secs = seconds % 60
			return `${mins}:${secs.toString().padStart(2, '0')}`
		},
		
		startPractice() {
			uni.navigateTo({
				url: `/pages/practice/practice?lessionId=${this.lessionId}&title=${this.lessionTitle}`
			})
		}
	},
	
	onUnload() {
		if (this.audioTimer) {
			clearInterval(this.audioTimer)
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	padding-bottom: 160rpx;
}

.status-bar {
	background: transparent;
}

.nav-bar {
	background: transparent;
	
	.nav-content {
		height: 120rpx;
		display: flex;
		align-items: center;
		padding: 0 32rpx;
		
		.nav-left, .nav-right {
			width: 88rpx;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.nav-left {
			.iconfont {
				color: white;
				font-size: 40rpx;
			}
		}
		
		.nav-title {
			flex: 1;
			
			.title {
				display: block;
				color: white;
				font-size: 36rpx;
				font-weight: bold;
				line-height: 1.2;
			}
			
			.subtitle {
				display: block;
				color: rgba(255, 255, 255, 0.8);
				font-size: 28rpx;
			}
		}
	}
}

.content {
	background: #f8fafc;
	border-radius: 48rpx 48rpx 0 0;
	min-height: calc(100vh - var(--status-bar-height) - 120rpx);
	padding: 40rpx 0;
}

.audio-player {
	margin: 0 40rpx 32rpx;
	
	.player-title {
		display: block;
		font-weight: bold;
		color: #333;
		font-size: 32rpx;
		margin-bottom: 8rpx;
	}
	
	.player-duration {
		color: #666;
		font-size: 28rpx;
	}
	
	.play-button {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, #4facfe, #00f2fe);
		display: flex;
		align-items: center;
		justify-content: center;
		
		.iconfont {
			color: white;
			font-size: 48rpx;
		}
	}
	
	.waveform {
		height: 80rpx;
		background: linear-gradient(90deg, #4facfe 0%, #00f2fe 30%, #e5e7eb 30%, #e5e7eb 100%);
		border-radius: 40rpx;
		position: relative;
		overflow: hidden;
		margin: 32rpx 0;
		
		.waveform-progress {
			height: 100%;
			background: linear-gradient(90deg, #4facfe, #00f2fe);
			border-radius: 40rpx;
			transition: width 0.3s ease;
		}
	}
	
	.audio-controls {
		.time-text {
			color: #666;
			font-size: 28rpx;
		}
		
		.control-buttons {
			display: flex;
			align-items: center;
			gap: 32rpx;
			
			.iconfont {
				color: #666;
				font-size: 36rpx;
				
				&.active {
					color: #4facfe;
				}
			}
		}
	}
}

.content-tabs {
	margin: 0 40rpx 32rpx;
	
	.tab-buttons {
		display: flex;
		background: #f1f5f9;
		border-radius: 24rpx;
		padding: 8rpx;
		margin-bottom: 32rpx;
		
		.tab-button {
			flex: 1;
			padding: 16rpx 32rpx;
			text-align: center;
			border-radius: 16rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #64748b;
			transition: all 0.2s;
			
			&.active {
				background: white;
				color: #4facfe;
				box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
			}
		}
	}
	
	.text-content {
		.dialogue-item {
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f1f5f9;
			
			&:last-child {
				border-bottom: none;
			}
			
			.speaker {
				font-weight: bold;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.text {
				color: #333;
				line-height: 1.6;
				font-size: 32rpx;
			}
		}
	}
	
	.vocabulary-content {
		.vocabulary-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f1f5f9;
			
			&:last-child {
				border-bottom: none;
			}
			
			.word-info {
				.word {
					display: block;
					font-weight: bold;
					color: #333;
					font-size: 32rpx;
					margin-bottom: 4rpx;
				}
				
				.pronunciation {
					color: #666;
					font-size: 24rpx;
				}
			}
			
			.meaning {
				color: #333;
				font-size: 28rpx;
			}
		}
	}
}

.study-tips {
	margin: 0 40rpx;
	
	.tips-header {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
		
		.iconfont {
			color: #f59e0b;
			font-size: 40rpx;
			margin-right: 16rpx;
		}
		
		.tips-title {
			font-weight: bold;
			color: #333;
			font-size: 32rpx;
		}
	}
	
	.tips-list {
		.tip-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 24rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.tip-number {
				width: 48rpx;
				height: 48rpx;
				background: #dbeafe;
				color: #2563eb;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
				font-weight: bold;
				margin-right: 24rpx;
				margin-top: 8rpx;
			}
			
			.tip-content {
				flex: 1;
				
				.tip-title {
					display: block;
					font-weight: 500;
					color: #333;
					font-size: 28rpx;
					margin-bottom: 4rpx;
				}
				
				.tip-desc {
					color: #666;
					font-size: 24rpx;
					line-height: 1.5;
				}
			}
		}
	}
}

.bottom-action {
	position: fixed;
	bottom: 40rpx;
	left: 40rpx;
	right: 40rpx;
	
	.practice-btn {
		width: 100%;
		background: linear-gradient(135deg, #4facfe, #00f2fe);
		color: white;
		padding: 32rpx;
		border-radius: 32rpx;
		font-weight: bold;
		font-size: 32rpx;
		box-shadow: 0 16rpx 64rpx rgba(79, 172, 254, 0.3);
		border: none;
		
		.iconfont {
			margin-right: 16rpx;
			font-size: 36rpx;
		}
	}
}

/* 字体图标 */
.icon-pause::before { content: '\e012'; }
.icon-backward::before { content: '\e013'; }
.icon-forward::before { content: '\e014'; }
.icon-repeat::before { content: '\e015'; }
.icon-lightbulb::before { content: '\e016'; }
.icon-microphone::before { content: '\e017'; }
</style>
