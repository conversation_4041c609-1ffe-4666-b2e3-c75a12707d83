<template>
	<view class="lesson-detail-page">
		<!-- 自定义状态栏 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 自定义导航栏 -->
		<view class="nav-bar">
			<view class="nav-content">
				<view class="nav-left" @click="goBack">
					<text class="iconfont icon-arrow-left"></text>
				</view>
				<view class="nav-title">
					<text class="title">课时详情</text>
				</view>
				<view class="nav-right"></view>
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 1. 课时标题区域 -->
			<view class="lesson-header-card">
				<view class="lesson-title-section">
					<text class="lesson-title">{{ lessonInfo.title || lessionTitle }}</text>
					<text class="lesson-description">{{ lessonInfo.description || courseSubtitle }}</text>
				</view>
				<view class="lesson-meta">
					<view class="meta-item">
						<text class="iconfont icon-clock"></text>
						<text class="meta-text">{{ lessonInfo.duration || courseDuration }}分钟</text>
					</view>
					<view class="meta-item">
						<text class="iconfont icon-user"></text>
						<text class="meta-text">{{ checkinCount }}人已打卡</text>
					</view>
				</view>
			</view>

			<!-- 2. 音频播放卡片 -->
			<view class="audio-player-card">
				<view class="audio-header">
					<text class="audio-title">课程音频</text>
					<text class="audio-duration">{{ formatTime(totalTime) }}</text>
				</view>

				<view class="audio-controls">
					<view class="play-button" @click="togglePlay">
						<text class="iconfont" :class="isPlaying ? 'icon-pause' : 'icon-play'"></text>
					</view>

					<view class="progress-section">
						<view class="waveform" @click="seekAudio">
							<view class="waveform-progress" :style="{ width: audioProgress + '%' }"></view>
						</view>
						<view class="time-display">
							<text class="current-time">{{ formatTime(currentTime) }}</text>
							<text class="total-time">{{ formatTime(totalTime) }}</text>
						</view>
					</view>

					<view class="control-buttons">
						<text class="iconfont icon-backward" @click="seekBackward"></text>
						<text class="iconfont icon-forward" @click="seekForward"></text>
						<text class="iconfont icon-repeat" @click="toggleRepeat" :class="{ active: isRepeat }"></text>
					</view>
				</view>
			</view>

			<!-- 3. 课时详细内容区域 -->
			<view class="lesson-content-card">
				<view class="content-header">
					<text class="content-title">课时内容</text>
				</view>

				<!-- 加载状态 -->
				<view v-if="contentLoading" class="content-loading">
					<view class="loading-skeleton" v-for="i in 3" :key="i"></view>
				</view>

				<!-- 课时内容 -->
				<view v-else-if="lessonContent.length > 0" class="content-sections">
					<view
						class="content-section"
						v-for="(section, index) in lessonContent"
						:key="index"
					>
						<text class="section-title">{{ section.title }}</text>
						<text class="section-text">{{ section.content }}</text>

						<!-- 例句 -->
						<view v-if="section.examples && section.examples.length > 0" class="examples">
							<text class="examples-title">例句：</text>
							<view
								class="example-item"
								v-for="(example, idx) in section.examples"
								:key="idx"
							>
								<text class="example-japanese">{{ example.japanese }}</text>
								<text class="example-chinese">{{ example.chinese }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view v-else class="content-empty">
					<text class="iconfont icon-book empty-icon"></text>
					<text class="empty-text">暂无课时内容</text>
				</view>
			</view>

			<!-- 4. 打卡记录列表 -->
			<view class="checkin-records-card">
				<view class="records-header">
					<text class="records-title">打卡记录</text>
					<view class="my-checkin-btn" @click="showCheckinDialog">
						<text class="iconfont icon-edit"></text>
						<text class="btn-text">我要打卡</text>
					</view>
				</view>

				<!-- 加载状态 -->
				<view v-if="recordsLoading" class="records-loading">
					<view class="record-skeleton" v-for="i in 3" :key="i">
						<view class="avatar-skeleton"></view>
						<view class="content-skeleton">
							<view class="line-skeleton"></view>
							<view class="line-skeleton short"></view>
						</view>
					</view>
				</view>

				<!-- 打卡记录列表 -->
				<view v-else-if="checkinRecords.length > 0" class="records-list">
					<view
						class="record-item"
						v-for="record in checkinRecords"
						:key="record.id"
					>
						<view class="record-avatar">
							<image
								:src="record.userAvatar || '/static/images/default-avatar.png'"
								class="avatar-image"
								mode="aspectFill"
							/>
						</view>
						<view class="record-content">
							<view class="record-header">
								<text class="user-name">{{ record.userName }}</text>
								<text class="checkin-time">{{ formatCheckinTime(record.checkinTime) }}</text>
							</view>
							<text class="checkin-content">{{ record.content }}</text>
							<view v-if="record.tags && record.tags.length > 0" class="record-tags">
								<text
									class="tag"
									v-for="tag in record.tags"
									:key="tag"
								>{{ tag }}</text>
							</view>
						</view>
					</view>

					<!-- 加载更多 -->
					<view v-if="hasMoreRecords" class="load-more" @click="loadMoreRecords">
						<text class="load-more-text">加载更多</text>
					</view>
				</view>

				<!-- 空状态 -->
				<view v-else class="records-empty">
					<text class="iconfont icon-message empty-icon"></text>
					<text class="empty-text">暂无打卡记录</text>
					<text class="empty-desc">成为第一个打卡的人吧！</text>
				</view>
			</view>

			<!-- 底部操作按钮 -->
			<view class="bottom-actions">
				<view class="action-btn practice-btn" @click="startPractice">
					<text class="iconfont icon-microphone"></text>
					<text class="btn-text">跟读练习</text>
				</view>
			</view>
		</view>

		<!-- 打卡弹窗 -->
		<view v-if="showCheckin" class="checkin-modal" @click="hideCheckinDialog">
			<view class="checkin-content" @click.stop>
				<view class="checkin-header">
					<text class="checkin-title">课时打卡</text>
					<text class="iconfont icon-close" @click="hideCheckinDialog"></text>
				</view>

				<view class="checkin-form">
					<textarea
						class="checkin-textarea"
						v-model="checkinText"
						placeholder="分享你的学习心得和感悟..."
						maxlength="200"
					></textarea>
					<text class="char-count">{{ checkinText.length }}/200</text>
				</view>

				<view class="checkin-actions">
					<view class="cancel-btn" @click="hideCheckinDialog">取消</view>
					<view class="submit-btn" @click="submitCheckin" :class="{ disabled: !checkinText.trim() }">
						提交打卡
					</view>
				</view>
			</view>
		</view>

</template>

<script>
import { courseApi } from '@/api/index.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			lessionId: '',
			courseId: '',
			loading: true,

			// 课时信息
			lessonInfo: {},
			lessionTitle: '加载中...',
			courseSubtitle: '',
			courseDuration: 0,

			// 音频播放
			audioUrl: '',
			isPlaying: false,
			isRepeat: false,
			currentTime: 0,
			totalTime: 0,
			audioProgress: 0,

			// 课时内容
			contentLoading: false,
			lessonContent: [],

			// 打卡相关
			recordsLoading: false,
			checkinRecords: [],
			checkinCount: 0,
			hasMoreRecords: false,
			currentPage: 1,
			pageSize: 10,

			// 打卡弹窗
			showCheckin: false,
			checkinText: '',
			submittingCheckin: false
		}
	},
	
	onLoad(options) {
		console.log('课程详情页面加载:', options)

		this.lessionId = options.lessonId || options.id
		this.courseId = options.id || options.courseId
		this.lessionTitle = options.title ? decodeURIComponent(options.title) : '加载中...'

		this.initPage()
	},

	methods: {
		initPage() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight || 0

			// 设置导航栏标题
			uni.setNavigationBarTitle({
				title: '课时详情'
			})

			// 加载页面数据
			this.loadLessonDetail()
			this.loadLessonContent()
			this.loadCheckinRecords()
		},

		// 加载课时详情
		async loadLessonDetail() {
			if (!this.lessionId) {
				uni.showToast({
					title: '课时ID不能为空',
					icon: 'none'
				})
				return
			}

			this.loading = true

			try {
				console.log('加载课时详情:', this.lessionId)

				const response = await courseApi.getLessonDetail(this.lessionId)

				if (response.success && response.data) {
					this.lessonInfo = response.data
					this.updateLessonInfo(response.data)
				} else {
					console.log('⚠️ 暂无课时详情数据')
				}

			} catch (error) {
				console.error('加载课时详情失败:', error)

				uni.showToast({
					title: '课时详情加载失败，请重试',
					icon: 'none',
					duration: 2000
				})
			} finally {
				this.loading = false
			}
		},

		// 更新课时信息
		updateLessonInfo(lessonData) {
			this.lessionTitle = lessonData.title || this.lessionTitle
			this.courseSubtitle = lessonData.subtitle || lessonData.description || ''
			this.courseDuration = lessonData.duration || 0
			this.audioUrl = lessonData.audioUrl || ''
			this.totalTime = (lessonData.duration || 0) * 60 // 转换为秒

			console.log('课时信息更新完成')
		},

		// 加载课时内容
		async loadLessonContent() {
			if (!this.lessionId) return

			this.contentLoading = true

			try {
				console.log('加载课时内容:', this.lessionId)

				const response = await courseApi.getLessonContent(this.lessionId)

				if (response.success && response.data) {
					this.lessonContent = this.formatLessonContent(response.data)
					console.log('✅ 课时内容加载成功')
				} else {
					console.log('⚠️ 暂无课时内容')
					this.lessonContent = []
				}

			} catch (error) {
				console.error('❌ 加载课时内容失败:', error)
				this.lessonContent = []
			} finally {
				this.contentLoading = false
			}
		},

		// 格式化课时内容
		formatLessonContent(contentData) {
			if (Array.isArray(contentData)) {
				return contentData
			}

			// 如果是对象，转换为数组格式
			const sections = []

			if (contentData.introduction) {
				sections.push({
					title: '课程介绍',
					content: contentData.introduction
				})
			}

			if (contentData.vocabulary && contentData.vocabulary.length > 0) {
				sections.push({
					title: '重点词汇',
					content: '本课时的重点词汇',
					examples: contentData.vocabulary.map(item => ({
						japanese: item.word || item.japanese,
						chinese: item.meaning || item.chinese
					}))
				})
			}

			if (contentData.grammar && contentData.grammar.length > 0) {
				sections.push({
					title: '语法要点',
					content: '本课时的语法重点',
					examples: contentData.grammar.map(item => ({
						japanese: item.example || item.japanese,
						chinese: item.explanation || item.chinese
					}))
				})
			}

			return sections
		},


		
		// 加载打卡记录
		async loadCheckinRecords(loadMore = false) {
			if (!this.lessionId) return

			if (!loadMore) {
				this.recordsLoading = true
				this.currentPage = 1
			}

			try {
				console.log('加载打卡记录:', this.lessionId, '页码:', this.currentPage)

				const response = await courseApi.getLessonCheckinRecords(this.lessionId, {
					page: this.currentPage,
					pageSize: this.pageSize
				})

				if (response.success && response.data) {
					const records = this.formatCheckinRecords(response.data.records || [])

					if (loadMore) {
						this.checkinRecords = [...this.checkinRecords, ...records]
					} else {
						this.checkinRecords = records
					}

					this.checkinCount = response.data.total || 0
					this.hasMoreRecords = records.length === this.pageSize

					console.log('✅ 打卡记录加载成功:', records.length, '条')
				} else {
					console.log('⚠️ 暂无打卡记录')
					if (!loadMore) {
						this.checkinRecords = []
						this.checkinCount = 0
					}
				}

			} catch (error) {
				console.error('❌ 加载打卡记录失败:', error)
				if (!loadMore) {
					this.checkinRecords = []
					this.checkinCount = 0
				}
			} finally {
				if (!loadMore) {
					this.recordsLoading = false
				}
			}
		},

		// 格式化打卡记录
		formatCheckinRecords(records) {
			return records.map(record => ({
				id: record.id,
				userName: record.userName || record.user?.name || '匿名用户',
				userAvatar: record.userAvatar || record.user?.avatar || '',
				content: record.content || record.checkinContent || '',
				checkinTime: record.checkinTime || record.createTime,
				tags: record.tags || []
			}))
		},

		// 加载更多打卡记录
		loadMoreRecords() {
			if (!this.hasMoreRecords) return

			this.currentPage++
			this.loadCheckinRecords(true)
		},

		// 显示打卡弹窗
		showCheckinDialog() {
			this.showCheckin = true
			this.checkinText = ''
		},

		// 隐藏打卡弹窗
		hideCheckinDialog() {
			this.showCheckin = false
			this.checkinText = ''
		},

		// 提交打卡
		async submitCheckin() {
			if (!this.checkinText.trim()) {
				uni.showToast({
					title: '请输入打卡内容',
					icon: 'none'
				})
				return
			}

			if (this.submittingCheckin) return

			this.submittingCheckin = true

			try {
				console.log('提交打卡:', this.lessionId, this.checkinText)

				const response = await courseApi.submitLessonCheckin(this.lessionId, {
					content: this.checkinText.trim()
				})

				if (response.success) {
					uni.showToast({
						title: '打卡成功',
						icon: 'success'
					})

					this.hideCheckinDialog()
					// 重新加载打卡记录
					this.loadCheckinRecords()
				} else {
					throw new Error(response.message || '打卡失败')
				}

			} catch (error) {
				console.error('❌ 提交打卡失败:', error)
				uni.showToast({
					title: '打卡失败，请重试',
					icon: 'none'
				})
			} finally {
				this.submittingCheckin = false
			}
		},

		// 格式化打卡时间
		formatCheckinTime(time) {
			if (!time) return ''

			const date = new Date(time)
			const now = new Date()
			const diff = now - date

			// 小于1分钟
			if (diff < 60000) {
				return '刚刚'
			}

			// 小于1小时
			if (diff < 3600000) {
				return Math.floor(diff / 60000) + '分钟前'
			}

			// 小于1天
			if (diff < 86400000) {
				return Math.floor(diff / 3600000) + '小时前'
			}

			// 小于7天
			if (diff < 604800000) {
				return Math.floor(diff / 86400000) + '天前'
			}

			// 超过7天显示具体日期
			return date.toLocaleDateString()
		},

		goBack() {
			uni.navigateBack()
		},
		
		togglePlay() {
			this.isPlaying = !this.isPlaying
			if (this.isPlaying) {
				this.startAudioProgress()
			} else {
				this.pauseAudioProgress()
			}
		},
		
		startAudioProgress() {
			// 模拟音频播放进度
			this.audioTimer = setInterval(() => {
				if (this.currentTime < this.totalTime) {
					this.currentTime += 1
					this.audioProgress = (this.currentTime / this.totalTime) * 100
				} else {
					this.isPlaying = false
					clearInterval(this.audioTimer)
				}
			}, 1000)
		},
		
		pauseAudioProgress() {
			if (this.audioTimer) {
				clearInterval(this.audioTimer)
			}
		},
		
		seekAudio(e) {
			// 点击波形图跳转播放位置
			const rect = e.currentTarget.getBoundingClientRect()
			const clickX = e.detail.x - rect.left
			const progress = clickX / rect.width
			this.currentTime = Math.floor(this.totalTime * progress)
			this.audioProgress = progress * 100
		},
		
		seekBackward() {
			this.currentTime = Math.max(0, this.currentTime - 10)
			this.audioProgress = (this.currentTime / this.totalTime) * 100
		},
		
		seekForward() {
			this.currentTime = Math.min(this.totalTime, this.currentTime + 10)
			this.audioProgress = (this.currentTime / this.totalTime) * 100
		},
		
		toggleRepeat() {
			this.isRepeat = !this.isRepeat
		},
		
		switchTab(index) {
			this.activeTab = index
		},
		
		formatTime(seconds) {
			const mins = Math.floor(seconds / 60)
			const secs = seconds % 60
			return `${mins}:${secs.toString().padStart(2, '0')}`
		},
		
		startPractice() {
			uni.navigateTo({
				url: `/pages/practice/practice?lessionId=${this.lessionId}&title=${this.lessionTitle}`
			})
		}
	},
	
	onUnload() {
		if (this.audioTimer) {
			clearInterval(this.audioTimer)
		}
	}
}
</script>

<style lang="scss" scoped>
.lesson-detail-page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-bar {
	background: transparent;
}

.nav-bar {
	background: transparent;
	
	.nav-content {
		height: 120rpx;
		display: flex;
		align-items: center;
		padding: 0 32rpx;
		
		.nav-left, .nav-right {
			width: 88rpx;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.nav-left {
			.iconfont {
				color: white;
				font-size: 40rpx;
			}
		}
		
		.nav-title {
			flex: 1;
			
			.title {
				display: block;
				color: white;
				font-size: 36rpx;
				font-weight: bold;
				line-height: 1.2;
			}
			
			.subtitle {
				display: block;
				color: rgba(255, 255, 255, 0.8);
				font-size: 28rpx;
			}
		}
	}
}

.content {
	background: #f8fafc;
	border-radius: 48rpx 48rpx 0 0;
	min-height: calc(100vh - var(--status-bar-height) - 120rpx);
	padding: 40rpx 0;
}

.audio-player {
	margin: 0 40rpx 32rpx;
	
	.player-title {
		display: block;
		font-weight: bold;
		color: #333;
		font-size: 32rpx;
		margin-bottom: 8rpx;
	}
	
	.player-duration {
		color: #666;
		font-size: 28rpx;
	}
	
	.play-button {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, #4facfe, #00f2fe);
		display: flex;
		align-items: center;
		justify-content: center;
		
		.iconfont {
			color: white;
			font-size: 48rpx;
		}
	}
	
	.waveform {
		height: 80rpx;
		background: linear-gradient(90deg, #4facfe 0%, #00f2fe 30%, #e5e7eb 30%, #e5e7eb 100%);
		border-radius: 40rpx;
		position: relative;
		overflow: hidden;
		margin: 32rpx 0;
		
		.waveform-progress {
			height: 100%;
			background: linear-gradient(90deg, #4facfe, #00f2fe);
			border-radius: 40rpx;
			transition: width 0.3s ease;
		}
	}
	
	.audio-controls {
		.time-text {
			color: #666;
			font-size: 28rpx;
		}
		
		.control-buttons {
			display: flex;
			align-items: center;
			gap: 32rpx;
			
			.iconfont {
				color: #666;
				font-size: 36rpx;
				
				&.active {
					color: #4facfe;
				}
			}
		}
	}
}

.content-tabs {
	margin: 0 40rpx 32rpx;
	
	.tab-buttons {
		display: flex;
		background: #f1f5f9;
		border-radius: 24rpx;
		padding: 8rpx;
		margin-bottom: 32rpx;
		
		.tab-button {
			flex: 1;
			padding: 16rpx 32rpx;
			text-align: center;
			border-radius: 16rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #64748b;
			transition: all 0.2s;
			
			&.active {
				background: white;
				color: #4facfe;
				box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
			}
		}
	}
	
	.text-content {
		.dialogue-item {
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f1f5f9;
			
			&:last-child {
				border-bottom: none;
			}
			
			.speaker {
				font-weight: bold;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.text {
				color: #333;
				line-height: 1.6;
				font-size: 32rpx;
			}
		}
	}
	
	.vocabulary-content {
		.vocabulary-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f1f5f9;
			
			&:last-child {
				border-bottom: none;
			}
			
			.word-info {
				.word {
					display: block;
					font-weight: bold;
					color: #333;
					font-size: 32rpx;
					margin-bottom: 4rpx;
				}
				
				.pronunciation {
					color: #666;
					font-size: 24rpx;
				}
			}
			
			.meaning {
				color: #333;
				font-size: 28rpx;
			}
		}
	}
}

.study-tips {
	margin: 0 40rpx;
	
	.tips-header {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
		
		.iconfont {
			color: #f59e0b;
			font-size: 40rpx;
			margin-right: 16rpx;
		}
		
		.tips-title {
			font-weight: bold;
			color: #333;
			font-size: 32rpx;
		}
	}
	
	.tips-list {
		.tip-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 24rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.tip-number {
				width: 48rpx;
				height: 48rpx;
				background: #dbeafe;
				color: #2563eb;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
				font-weight: bold;
				margin-right: 24rpx;
				margin-top: 8rpx;
			}
			
			.tip-content {
				flex: 1;
				
				.tip-title {
					display: block;
					font-weight: 500;
					color: #333;
					font-size: 28rpx;
					margin-bottom: 4rpx;
				}
				
				.tip-desc {
					color: #666;
					font-size: 24rpx;
					line-height: 1.5;
				}
			}
		}
	}
}

.bottom-action {
	position: fixed;
	bottom: 40rpx;
	left: 40rpx;
	right: 40rpx;
	
	.practice-btn {
		width: 100%;
		background: linear-gradient(135deg, #4facfe, #00f2fe);
		color: white;
		padding: 32rpx;
		border-radius: 32rpx;
		font-weight: bold;
		font-size: 32rpx;
		box-shadow: 0 16rpx 64rpx rgba(79, 172, 254, 0.3);
		border: none;
		
		.iconfont {
			margin-right: 16rpx;
			font-size: 36rpx;
		}
	}
}

/* 字体图标 */
.icon-pause::before { content: '\e012'; }
.icon-backward::before { content: '\e013'; }
.icon-forward::before { content: '\e014'; }
.icon-repeat::before { content: '\e015'; }
.icon-lightbulb::before { content: '\e016'; }
.icon-microphone::before { content: '\e017'; }

// 3. 课时内容区域样式
.lesson-content-card {
	background: white;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

	.content-header {
		margin-bottom: 24rpx;

		.content-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.content-loading {
		.loading-skeleton {
			height: 80rpx;
			background: #f0f0f0;
			border-radius: 8rpx;
			margin-bottom: 16rpx;
			animation: skeleton-loading 1.5s ease-in-out infinite;
		}
	}

	.content-sections {
		.content-section {
			margin-bottom: 32rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.section-title {
				display: block;
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 16rpx;
			}

			.section-text {
				display: block;
				font-size: 26rpx;
				color: #666;
				line-height: 1.6;
				margin-bottom: 16rpx;
			}

			.examples {
				.examples-title {
					display: block;
					font-size: 24rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 12rpx;
				}

				.example-item {
					background: #f8f9fa;
					border-radius: 12rpx;
					padding: 16rpx;
					margin-bottom: 12rpx;

					.example-japanese {
						display: block;
						font-size: 26rpx;
						color: #333;
						margin-bottom: 8rpx;
						font-weight: 500;
					}

					.example-chinese {
						display: block;
						font-size: 24rpx;
						color: #666;
					}
				}
			}
		}
	}

	.content-empty {
		text-align: center;
		padding: 60rpx 20rpx;

		.empty-icon {
			font-size: 80rpx;
			color: #d1d5db;
			margin-bottom: 16rpx;
		}

		.empty-text {
			font-size: 28rpx;
			color: #666;
		}
	}
}

// 4. 打卡记录列表样式
.checkin-records-card {
	background: white;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

	.records-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;

		.records-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}

		.my-checkin-btn {
			display: flex;
			align-items: center;
			background: linear-gradient(135deg, #667eea, #764ba2);
			color: white;
			padding: 12rpx 20rpx;
			border-radius: 20rpx;
			font-size: 24rpx;

			.iconfont {
				font-size: 20rpx;
				margin-right: 8rpx;
			}
		}
	}

	.records-loading {
		.record-skeleton {
			display: flex;
			margin-bottom: 24rpx;

			.avatar-skeleton {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				background: #f0f0f0;
				margin-right: 16rpx;
				animation: skeleton-loading 1.5s ease-in-out infinite;
			}

			.content-skeleton {
				flex: 1;

				.line-skeleton {
					height: 32rpx;
					background: #f0f0f0;
					border-radius: 4rpx;
					margin-bottom: 12rpx;
					animation: skeleton-loading 1.5s ease-in-out infinite;

					&.short {
						width: 60%;
					}
				}
			}
		}
	}

	.records-list {
		.record-item {
			display: flex;
			margin-bottom: 24rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.record-avatar {
				margin-right: 16rpx;

				.avatar-image {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					background: #f0f0f0;
				}
			}

			.record-content {
				flex: 1;

				.record-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 8rpx;

					.user-name {
						font-size: 26rpx;
						font-weight: bold;
						color: #333;
					}

					.checkin-time {
						font-size: 22rpx;
						color: #999;
					}
				}

				.checkin-content {
					display: block;
					font-size: 26rpx;
					color: #666;
					line-height: 1.5;
					margin-bottom: 12rpx;
				}

				.record-tags {
					display: flex;
					flex-wrap: wrap;
					gap: 8rpx;

					.tag {
						background: #f0f0f0;
						color: #666;
						font-size: 20rpx;
						padding: 4rpx 8rpx;
						border-radius: 8rpx;
					}
				}
			}
		}

		.load-more {
			text-align: center;
			padding: 24rpx;

			.load-more-text {
				color: #667eea;
				font-size: 26rpx;
			}
		}
	}

	.records-empty {
		text-align: center;
		padding: 60rpx 20rpx;

		.empty-icon {
			font-size: 80rpx;
			color: #d1d5db;
			margin-bottom: 16rpx;
		}

		.empty-text {
			display: block;
			font-size: 28rpx;
			color: #666;
			margin-bottom: 8rpx;
		}

		.empty-desc {
			font-size: 24rpx;
			color: #999;
		}
	}
}

// 底部操作按钮
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 20rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);

	.action-btn {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 24rpx;
		border-radius: 16rpx;
		font-size: 28rpx;
		font-weight: bold;

		.iconfont {
			font-size: 32rpx;
			margin-right: 12rpx;
		}

		&.practice-btn {
			background: linear-gradient(135deg, #667eea, #764ba2);
			color: white;
		}
	}
}

// 打卡弹窗样式
.checkin-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;

	.checkin-content {
		background: white;
		border-radius: 20rpx;
		margin: 40rpx;
		max-width: 600rpx;
		width: 100%;

		.checkin-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 32rpx 32rpx 0;

			.checkin-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}

			.iconfont {
				font-size: 32rpx;
				color: #999;
				padding: 8rpx;
			}
		}

		.checkin-form {
			padding: 32rpx;

			.checkin-textarea {
				width: 100%;
				min-height: 200rpx;
				background: #f8f9fa;
				border-radius: 12rpx;
				padding: 20rpx;
				font-size: 26rpx;
				color: #333;
				border: none;
				resize: none;
			}

			.char-count {
				display: block;
				text-align: right;
				font-size: 22rpx;
				color: #999;
				margin-top: 12rpx;
			}
		}

		.checkin-actions {
			display: flex;
			padding: 0 32rpx 32rpx;
			gap: 16rpx;

			.cancel-btn, .submit-btn {
				flex: 1;
				text-align: center;
				padding: 24rpx;
				border-radius: 12rpx;
				font-size: 28rpx;
				font-weight: bold;
			}

			.cancel-btn {
				background: #f0f0f0;
				color: #666;
			}

			.submit-btn {
				background: linear-gradient(135deg, #667eea, #764ba2);
				color: white;

				&.disabled {
					opacity: 0.5;
				}
			}
		}
	}
}

@keyframes skeleton-loading {
	0%, 100% {
		opacity: 0.3;
	}
	50% {
		opacity: 0.7;
	}
}
</style>
