package com.kuma.learning.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuma.learning.entity.KumaCourseReview;
import com.kuma.learning.service.IKumaCourseReviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = "APP-课程评价管理")
@RestController
@RequestMapping("/app/course/review")
public class CourseReviewController {

    @Autowired
    private IKumaCourseReviewService reviewService;

    @AutoLog(value = "课程评价-分页列表查询")
    @ApiOperation(value = "课程评价-分页列表查询", notes = "课程评价-分页列表查询")
    @GetMapping("/list")
    public Result<IPage<KumaCourseReview>> queryPageList(
            @RequestParam(name = "courseId") String courseId,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        
        Page<KumaCourseReview> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<KumaCourseReview> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        queryWrapper.eq(KumaCourseReview::getCourseId, courseId)
                .eq(KumaCourseReview::getStatus, 1)
                .orderByDesc(KumaCourseReview::getCreateTime);
        
        IPage<KumaCourseReview> pageList = reviewService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog(value = "课程评价-添加")
    @ApiOperation(value = "课程评价-添加", notes = "课程评价-添加")
    @PostMapping("/add")
    public Result<String> add(@RequestBody KumaCourseReview review) {
        reviewService.save(review);
        return Result.OK("添加成功！");
    }
} 