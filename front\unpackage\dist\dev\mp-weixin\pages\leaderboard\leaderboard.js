"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 0,
      loading: true,
      activeTimeTab: 1,
      // 默认选中本月
      daysToNext: 0,
      currentUserRank: 0,
      timeTabs: [
        { name: "本周", desc: "本周", value: "week" },
        { name: "本月", desc: "本月", value: "month" },
        { name: "总榜", desc: "总", value: "all" }
      ],
      topUsers: [
        {
          id: 1,
          name: "田中君",
          avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face",
          days: 31
        },
        {
          id: 2,
          name: "小樱",
          avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face",
          days: 28
        },
        {
          id: 3,
          name: "美咲",
          avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face",
          days: 25
        }
      ],
      rankingList: [
        {
          id: 4,
          rank: 4,
          name: "佐藤",
          avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face",
          days: 22,
          isCurrentUser: false
        },
        {
          id: 5,
          rank: 5,
          name: "山田",
          avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=48&h=48&fit=crop&crop=face",
          days: 20,
          isCurrentUser: false
        },
        {
          id: 6,
          rank: 8,
          name: "我 (小明)",
          avatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=48&h=48&fit=crop&crop=face",
          days: 12,
          isCurrentUser: true
        },
        {
          id: 7,
          rank: 9,
          name: "花子",
          avatar: "https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=48&h=48&fit=crop&crop=face",
          days: 11,
          isCurrentUser: false
        },
        {
          id: 8,
          rank: 10,
          name: "太郎",
          avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=48&h=48&fit=crop&crop=face",
          days: 10,
          isCurrentUser: false
        }
      ]
    };
  },
  onLoad() {
    this.initPage();
  },
  onShow() {
    this.loadRankingData();
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.loadRankingData().finally(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  methods: {
    initPage() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight;
    },
    // 加载排行榜数据
    loadRankingData() {
      return __async(this, null, function* () {
        this.loading = true;
        try {
          const timeRange = this.timeTabs[this.activeTimeTab].value;
          common_vendor.index.__f__("log", "at pages/leaderboard/leaderboard.vue:212", "加载排行榜数据:", timeRange);
          let response;
          switch (timeRange) {
            case "week":
            case "month":
            case "all":
              response = yield api_index.leaderboardApi.getCheckinRanking({
                timeRange,
                limit: 50
              });
              break;
            default:
              response = yield api_index.leaderboardApi.getCheckinRanking({
                timeRange: "month",
                limit: 50
              });
          }
          if (response.success && response.data) {
            this.updateRankingData(response.data);
          } else {
            throw new Error(response.message || "获取排行榜数据失败");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/leaderboard/leaderboard.vue:239", "加载排行榜数据失败:", error);
        } finally {
          this.loading = false;
        }
      });
    },
    // 更新排行榜数据
    updateRankingData(data) {
      const { rankings, currentUser } = data;
      if (rankings && rankings.length > 0) {
        this.topUsers = rankings.slice(0, 3).map((user) => ({
          id: user.userId,
          name: user.nickname || user.username,
          avatar: user.avatar || "/static/images/default-avatar.png",
          days: user.continuousDays || user.totalDays || 0
        }));
        this.rankingList = rankings.slice(3).map((user) => ({
          id: user.userId,
          rank: user.rank,
          name: user.nickname || user.username,
          avatar: user.avatar || "/static/images/default-avatar.png",
          days: user.continuousDays || user.totalDays || 0,
          isCurrentUser: currentUser && user.userId === currentUser.userId
        }));
        if (currentUser && currentUser.rank <= 3) {
          this.currentUserRank = currentUser.rank;
          this.daysToNext = 0;
        } else if (currentUser) {
          this.currentUserRank = currentUser.rank;
          this.calculateDaysToNext(currentUser, rankings);
        }
      }
    },
    // 计算超越前一名需要的天数
    calculateDaysToNext(currentUser, rankings) {
      const currentRank = currentUser.rank;
      if (currentRank > 1) {
        const prevUser = rankings.find((user) => user.rank === currentRank - 1);
        if (prevUser) {
          const currentDays = currentUser.continuousDays || currentUser.totalDays || 0;
          const prevDays = prevUser.continuousDays || prevUser.totalDays || 0;
          this.daysToNext = Math.max(0, prevDays - currentDays + 1);
        }
      }
    },
    // 设置默认排行榜数据
    setDefaultRankingData() {
      common_vendor.index.__f__("log", "at pages/leaderboard/leaderboard.vue:303", "使用默认排行榜数据");
    },
    // 切换时间范围
    switchTimeTab(index) {
      if (this.activeTimeTab === index)
        return;
      this.activeTimeTab = index;
      this.loadRankingData();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.statusBarHeight + "px",
    b: common_vendor.f($data.timeTabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.activeTimeTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTimeTab(index), index)
      };
    }),
    c: $data.topUsers[1].avatar,
    d: common_vendor.t($data.topUsers[1].name),
    e: common_vendor.t($data.topUsers[1].days),
    f: $data.topUsers[0].avatar,
    g: common_vendor.t($data.topUsers[0].name),
    h: common_vendor.t($data.topUsers[0].days),
    i: $data.topUsers[2].avatar,
    j: common_vendor.t($data.topUsers[2].name),
    k: common_vendor.t($data.topUsers[2].days),
    l: common_vendor.t($data.timeTabs[$data.activeTimeTab].desc),
    m: common_vendor.f($data.rankingList, (user, index, i0) => {
      return {
        a: common_vendor.t(user.rank),
        b: user.avatar,
        c: common_vendor.t(user.name),
        d: common_vendor.t(user.days),
        e: common_vendor.t(user.days),
        f: user.id,
        g: user.isCurrentUser ? 1 : ""
      };
    }),
    n: common_vendor.t($data.daysToNext)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-049cb271"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/leaderboard/leaderboard.js.map
