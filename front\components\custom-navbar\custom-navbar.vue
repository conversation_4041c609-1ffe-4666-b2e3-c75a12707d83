<template>
	<view class="custom-navbar" :style="navbarStyle">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 导航栏内容 -->
		<view class="navbar-content" :style="{ height: navBarHeight + 'px' }">
			<!-- 左侧 -->
			<view class="navbar-left" @click="handleLeftClick">
				<slot name="left">
					<view v-if="showBack" class="back-button">
						<text class="iconfont icon-arrow-left" :style="{ color: textColor }"></text>
					</view>
				</slot>
			</view>
			
			<!-- 中间标题 -->
			<view class="navbar-center">
				<slot name="center">
					<text class="navbar-title" :style="{ color: textColor }">{{ title }}</text>
				</slot>
			</view>
			
			<!-- 右侧 -->
			<view class="navbar-right" @click="handleRightClick">
				<slot name="right"></slot>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomNavbar',
	props: {
		// 标题
		title: {
			type: String,
			default: ''
		},
		// 背景色
		backgroundColor: {
			type: String,
			default: 'transparent'
		},
		// 文字颜色
		textColor: {
			type: String,
			default: '#ffffff'
		},
		// 是否显示返回按钮
		showBack: {
			type: Boolean,
			default: true
		},
		// 是否固定定位
		fixed: {
			type: Boolean,
			default: false
		},
		// 层级
		zIndex: {
			type: Number,
			default: 999
		}
	},
	
	data() {
		return {
			statusBarHeight: 0,
			navBarHeight: 44
		}
	},
	
	computed: {
		navbarStyle() {
			return {
				background: this.backgroundColor,
				position: this.fixed ? 'fixed' : 'relative',
				top: this.fixed ? '0' : 'auto',
				left: this.fixed ? '0' : 'auto',
				right: this.fixed ? '0' : 'auto',
				zIndex: this.zIndex
			}
		}
	},
	
	mounted() {
		this.initNavbar()
	},
	
	methods: {
		initNavbar() {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight
			
			// 不同平台的导航栏高度
			// #ifdef MP-WEIXIN
			const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
			this.navBarHeight = menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2
			// #endif
			
			// #ifdef H5
			this.navBarHeight = 44
			// #endif
			
			// #ifdef APP-PLUS
			this.navBarHeight = 44
			// #endif
		},
		
		handleLeftClick() {
			this.$emit('leftClick')
			if (this.showBack) {
				uni.navigateBack()
			}
		},
		
		handleRightClick() {
			this.$emit('rightClick')
		}
	}
}
</script>

<style lang="scss" scoped>
.custom-navbar {
	width: 100%;
	background: transparent;
}

.status-bar {
	width: 100%;
	background: transparent;
}

.navbar-content {
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	position: relative;
}

.navbar-left,
.navbar-right {
	width: 88rpx;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navbar-center {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
}

.back-button {
	width: 64rpx;
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.1);
	
	.iconfont {
		font-size: 36rpx;
	}
}

.navbar-title {
	font-size: 36rpx;
	font-weight: bold;
	max-width: 400rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 字体图标 */
.icon-arrow-left::before {
	content: '\e005';
}
</style>
