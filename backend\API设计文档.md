# 口语便利店小程序 API 设计文档

## 基础信息
- 基础URL: `https://api.japanese-learning.com`
- API版本: v1
- 认证方式: JWT Token
- 数据格式: JSON

## 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200000
}
```

## 用户相关接口

### 1. 用户登录
- **接口**: `POST /api/v1/auth/login`
- **描述**: 用户登录获取token
- **请求参数**:
```json
{
  "username": "string",
  "password": "string"
}
```

### 2. 获取用户信息
- **接口**: `GET /api/v1/user/profile`
- **描述**: 获取当前用户详细信息
- **响应数据**:
```json
{
  "id": 1,
  "username": "user123",
  "nickname": "小明",
  "avatar": "https://example.com/avatar.jpg",
  "level": "beginner",
  "totalStudyDays": 156,
  "continuousDays": 12,
  "totalScore": 8500,
  "vipStatus": 0
}
```

## 课程相关接口

### 1. 获取课程列表
- **接口**: `GET /api/v1/courses`
- **描述**: 根据级别获取课程列表
- **请求参数**:
  - `level`: string (beginner/intermediate/advanced/professional)
  - `page`: int (页码，默认1)
  - `size`: int (每页数量，默认20)

### 2. 获取课程详情
- **接口**: `GET /api/v1/courses/{courseId}`
- **描述**: 获取指定课程的详细信息
- **响应数据**:
```json
{
  "id": 1,
  "title": "第1课：自己紹介",
  "description": "自我介绍基础对话",
  "level": "beginner",
  "duration": 300,
  "audioUrl": "https://example.com/audio.mp3",
  "transcript": "はじめまして...",
  "translation": "初次见面...",
  "vocabulary": [
    {
      "word": "はじめまして",
      "pronunciation": "hajimemashite",
      "meaning": "初次见面"
    }
  ],
  "isFree": true,
  "price": 0.00
}
```

## 学习记录接口

### 1. 更新学习进度
- **接口**: `POST /api/v1/study/progress`
- **描述**: 更新用户课程学习进度
- **请求参数**:
```json
{
  "courseId": 1,
  "progress": 80,
  "studyTime": 240
}
```

### 2. 获取学习统计
- **接口**: `GET /api/v1/study/statistics`
- **描述**: 获取用户学习统计数据
- **响应数据**:
```json
{
  "totalCourses": 50,
  "completedCourses": 30,
  "totalStudyTime": 18000,
  "averageScore": 85,
  "weeklyProgress": [
    {"date": "2024-03-18", "studyTime": 1800},
    {"date": "2024-03-19", "studyTime": 2400}
  ]
}
```

## 打卡相关接口

### 1. 每日打卡
- **接口**: `POST /api/v1/checkin`
- **描述**: 用户每日打卡
- **请求参数**:
```json
{
  "coursesCompleted": 3,
  "studyDuration": 1800
}
```

### 2. 获取打卡记录
- **接口**: `GET /api/v1/checkin/records`
- **描述**: 获取用户打卡记录
- **请求参数**:
  - `year`: int (年份)
  - `month`: int (月份)

### 3. 补打卡
- **接口**: `POST /api/v1/checkin/makeup`
- **描述**: 使用补打卡机会
- **请求参数**:
```json
{
  "date": "2024-03-15"
}
```

## 练习相关接口

### 1. 提交录音练习
- **接口**: `POST /api/v1/practice/submit`
- **描述**: 提交用户录音进行AI评分
- **请求参数**:
```json
{
  "courseId": 1,
  "sentenceText": "ラーメンとギョーザをお願いします",
  "audioFile": "base64_encoded_audio_data"
}
```

### 2. 获取练习历史
- **接口**: `GET /api/v1/practice/history`
- **描述**: 获取用户练习记录
- **请求参数**:
  - `courseId`: int (可选，指定课程)
  - `page`: int (页码)
  - `size`: int (每页数量)

## 排行榜接口

### 1. 获取排行榜
- **接口**: `GET /api/v1/leaderboard`
- **描述**: 获取排行榜数据
- **请求参数**:
  - `type`: string (weekly/monthly/total)
  - `limit`: int (返回数量，默认50)

## 支付相关接口

### 1. 创建订单
- **接口**: `POST /api/v1/payment/create`
- **描述**: 创建课程购买订单
- **请求参数**:
```json
{
  "courseId": 1,
  "paymentMethod": "wechat"
}
```

### 2. 查询订单状态
- **接口**: `GET /api/v1/payment/status/{orderId}`
- **描述**: 查询订单支付状态

## AI评分接口

### 1. 语音评测
- **接口**: `POST /api/v1/ai/evaluate`
- **描述**: 调用科大讯飞API进行语音评测
- **请求参数**:
```json
{
  "audioData": "base64_encoded_audio",
  "referenceText": "ラーメンとギョーザをお願いします",
  "language": "ja"
}
```

## 错误码说明
- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

## 技术栈
- 框架: Jeecg-boot (Spring Boot + MyBatis Plus)
- 数据库: MySQL 8.0
- 缓存: Redis
- 文件存储: 阿里云OSS
- AI服务: 科大讯飞开放平台
