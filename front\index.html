<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>口语便利店 - 日语学习打卡小程序</title>
    <meta name="description" content="专业的日语学习打卡小程序，提供AI语音评测、课程学习、打卡排行等功能">
    <meta name="keywords" content="日语学习,语音评测,打卡,小程序,AI">
    <link rel="icon" href="/static/logo.png">
    <script>
        var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
        document.write('<meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0' + (coverSupport ? ',viewport-fit=cover' : '') + '" />')
    </script>
</head>
<body>
    <div id="app">
        <!-- uni-app -->
    </div>
    <script type="module" src="/main.js"></script>
</body>
</html>
