"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
const config_api = require("../config/api.js");
const utils_auth = require("./auth.js");
function getGlobalApp() {
  try {
    return getApp();
  } catch (e) {
    common_vendor.index.__f__("warn", "at utils/request.js:14", "获取全局应用实例失败:", e);
    return null;
  }
}
function getBaseUrl() {
  return config_api.getApiBaseUrl();
}
function getHeaders(customHeaders = {}) {
  return config_api.getRequestHeaders(customHeaders);
}
function handleResponse(res, resolve, reject) {
  var _a;
  common_vendor.index.__f__("log", "at utils/request.js:31", "📥 响应数据:", res);
  if (res.statusCode === 200) {
    if (res.data) {
      if (res.data.success === true || res.data.code === 200) {
        resolve({
          success: true,
          data: res.data.result || res.data.data || res.data,
          message: res.data.message || "success",
          code: res.data.code || 200
        });
      } else {
        const errorMsg = res.data.message || "请求失败";
        common_vendor.index.__f__("error", "at utils/request.js:47", "❌ 业务错误:", errorMsg);
        common_vendor.index.showToast({
          title: errorMsg,
          icon: "none",
          duration: 2e3
        });
        reject(new Error(errorMsg));
      }
    } else {
      resolve({
        success: true,
        data: null,
        message: "success",
        code: 200
      });
    }
  } else if (res.statusCode === 401) {
    common_vendor.index.__f__("error", "at utils/request.js:68", "🔐 认证失败，清除登录状态");
    utils_auth.removeToken();
    utils_auth.removeUserInfo();
    const app = getGlobalApp();
    if (app && app.clearLoginInfo) {
      app.clearLoginInfo();
    }
    common_vendor.index.showToast({
      title: "登录已过期，请重新登录",
      icon: "none",
      duration: 2e3
    });
    let currentRoute = "";
    try {
      const pages = getCurrentPages();
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        currentRoute = currentPage && currentPage.route ? currentPage.route : "";
      }
    } catch (error) {
      common_vendor.index.__f__("warn", "at utils/request.js:95", "获取当前页面路径失败:", error);
    }
    setTimeout(() => {
      const redirectUrl = currentRoute && currentRoute !== "pages/login/login" ? encodeURIComponent(`/${currentRoute}`) : "";
      common_vendor.index.reLaunch({
        url: `/pages/login/login${redirectUrl ? `?redirect=${redirectUrl}` : ""}`
      });
    }, 2e3);
    reject(new Error("登录已过期"));
  } else if (res.statusCode === 403) {
    common_vendor.index.showToast({
      title: "权限不足",
      icon: "none"
    });
    reject(new Error("权限不足"));
  } else if (res.statusCode >= 500) {
    common_vendor.index.showToast({
      title: "服务器错误，请稍后重试",
      icon: "none"
    });
    reject(new Error(`服务器错误: ${res.statusCode}`));
  } else {
    const errorMsg = ((_a = res.data) == null ? void 0 : _a.message) || `请求失败: ${res.statusCode}`;
    common_vendor.index.showToast({
      title: errorMsg,
      icon: "none"
    });
    reject(new Error(errorMsg));
  }
}
function handleRequestFail(err, reject) {
  common_vendor.index.__f__("error", "at utils/request.js:137", "📡 请求失败:", err);
  let errorMessage = "网络请求失败";
  if (err.errMsg) {
    if (err.errMsg.includes("timeout")) {
      errorMessage = "请求超时，请检查网络连接";
    } else if (err.errMsg.includes("fail")) {
      errorMessage = "网络连接失败，请检查网络设置";
    } else if (err.errMsg.includes("abort")) {
      errorMessage = "请求被取消";
    }
  }
  const app = getGlobalApp();
  if (app && app.addError) {
    app.addError({
      type: "network_error",
      message: errorMessage,
      details: err,
      timestamp: Date.now()
    });
  }
  common_vendor.index.showToast({
    title: errorMessage,
    icon: "none",
    duration: 2e3
  });
  reject(new Error(errorMessage));
}
function request(options = {}) {
  return new Promise((resolve, reject) => {
    const baseUrl = getBaseUrl();
    const url = options.url.startsWith("http") ? options.url : baseUrl + options.url;
    const headers = getHeaders(options.header);
    const requestConfig = {
      url,
      method: options.method || "GET",
      data: options.data || {},
      header: headers,
      timeout: options.timeout || 1e4
    };
    common_vendor.index.__f__("log", "at utils/request.js:200", "📤 发起请求:", {
      url: requestConfig.url,
      method: requestConfig.method,
      data: requestConfig.data,
      header: requestConfig.header
    });
    if (options.showLoading !== false) {
      common_vendor.index.showLoading({
        title: options.loadingText || "加载中...",
        mask: true
      });
    }
    common_vendor.index.request(__spreadProps(__spreadValues({}, requestConfig), {
      success: (res) => {
        if (options.showLoading !== false) {
          common_vendor.index.hideLoading();
        }
        handleResponse(res, resolve, reject);
      },
      fail: (err) => {
        if (options.showLoading !== false) {
          common_vendor.index.hideLoading();
        }
        handleRequestFail(err, reject);
      }
    }));
  });
}
function get(url, params = {}, options = {}) {
  return request(__spreadValues({
    url,
    method: "GET",
    data: params
  }, options));
}
function post(url, data = {}, options = {}) {
  return request(__spreadValues({
    url,
    method: "POST",
    data
  }, options));
}
function put(url, data = {}, options = {}) {
  return request(__spreadValues({
    url,
    method: "PUT",
    data
  }, options));
}
function del(url, data = {}, options = {}) {
  return request(__spreadValues({
    url,
    method: "DELETE",
    data
  }, options));
}
function upload(url, filePath, name = "file", formData = {}) {
  return new Promise((resolve, reject) => {
    const baseUrl = getBaseUrl();
    const uploadUrl = url.startsWith("http") ? url : baseUrl + url;
    const headers = getHeaders();
    common_vendor.index.__f__("log", "at utils/request.js:291", "📤 上传文件:", {
      url: uploadUrl,
      filePath,
      name,
      formData
    });
    common_vendor.index.showLoading({
      title: "上传中...",
      mask: true
    });
    common_vendor.index.uploadFile({
      url: uploadUrl,
      filePath,
      name,
      formData,
      header: headers,
      success: (res) => {
        common_vendor.index.hideLoading();
        try {
          const data = JSON.parse(res.data);
          if (data.success !== false) {
            resolve(data);
          } else {
            reject(new Error(data.message || "上传失败"));
          }
        } catch (e) {
          reject(new Error("响应数据格式错误"));
        }
      },
      fail: (err) => {
        common_vendor.index.hideLoading();
        handleRequestFail(err, reject);
      }
    });
  });
}
exports.del = del;
exports.get = get;
exports.post = post;
exports.put = put;
exports.request = request;
exports.upload = upload;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
