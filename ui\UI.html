<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>口语便利店小程序 - UI设计展示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f6f6f6;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow-x: auto;
            overflow-y: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar {
            display: none;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }
        
        .header h1 {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #667eea;
        }
        
        .header p {
            font-size: 16px;
            color: #666;
            margin: 0;
        }
        
        .ui-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            justify-items: center;
        }
        
        .ui-item {
            text-align: center;
        }
        
        .ui-item h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .ui-frame {
            width: 400px;
            height: 850px;
            border: none;
            border-radius: 20px;
            background-color: #f6f6f6;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .ui-frame:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
        }
        
        .page-description {
            margin-top: 15px;
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            max-width: 350px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .footer {
            text-align: center;
            margin-top: 60px;
            padding: 30px;
            color: #999;
            border-top: 1px solid #e0e0e0;
        }
        
        .footer p {
            margin: 5px 0;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .ui-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .ui-frame {
                width: 350px;
                height: 750px;
            }
            
            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>口语便利店小程序</h1>
            <p>UI设计展示 - 日语学习打卡小程序</p>
        </div>
        
        <div class="ui-grid">
            <!-- 首页 -->
            <div class="ui-item">
                <h3>首页</h3>
                <iframe src="首页.html" class="ui-frame"></iframe>
                <div class="page-description">
                    展示课程分类和用户学习进度，包含今日打卡状态、学习统计和课程级别选择功能
                </div>
            </div>
            
            <!-- 课程列表页 -->
            <div class="ui-item">
                <h3>课程列表页</h3>
                <iframe src="课程列表页.html" class="ui-frame"></iframe>
                <div class="page-description">
                    展示选定级别的所有课程，包含课程浏览、免费/付费标识、课程进度显示
                </div>
            </div>
            
            <!-- 课程详情页 -->
            <div class="ui-item">
                <h3>课程详情页</h3>
                <iframe src="课程详情页.html" class="ui-frame"></iframe>
                <div class="page-description">
                    展示具体课程内容和学习材料，包含音频播放、文字稿显示、中文翻译、词汇解释
                </div>
            </div>
            
            <!-- 练习页面 -->
            <div class="ui-item">
                <h3>练习页面</h3>
                <iframe src="练习页面.html" class="ui-frame"></iframe>
                <div class="page-description">
                    进行听力和跟读练习，包含音频播放、录音功能、AI评分显示
                </div>
            </div>
            
            <!-- 打卡页面 -->
            <div class="ui-item">
                <h3>打卡页面</h3>
                <iframe src="打卡页面.html" class="ui-frame"></iframe>
                <div class="page-description">
                    完成每日打卡任务，包含打卡记录、补打卡、打卡日历功能
                </div>
            </div>
            
            <!-- 排行榜页面 -->
            <div class="ui-item">
                <h3>排行榜页面</h3>
                <iframe src="排行榜页面.html" class="ui-frame"></iframe>
                <div class="page-description">
                    展示用户打卡排名和社交互动，包含排行榜显示、评论功能、用户互动
                </div>
            </div>
            
            <!-- 个人中心页面 -->
            <div class="ui-item">
                <h3>个人中心页面</h3>
                <iframe src="个人中心页面.html" class="ui-frame"></iframe>
                <div class="page-description">
                    用户信息管理和学习数据查看，包含个人信息、学习统计、会员状态、设置
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>口语便利店小程序</strong></p>
            <p>专注于日语口语能力提升的学习平台</p>
            <p>设计规格：375x812px | 清新主义美学设计风格</p>
        </div>
    </div>
</body>
</html>
