package com.kuma.learning.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: kuma_course
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Data
@TableName("kuma_course")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="kuma_course对象", description="kuma_course")
public class KumaCourse implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**系统标识*/
	@Excel(name = "系统标识", width = 15)
    @ApiModelProperty(value = "系统标识")
    private java.lang.String sysSign;
	/**课程标题*/
	@Excel(name = "课程标题", width = 15)
    @ApiModelProperty(value = "课程标题")
    private java.lang.String title;
	/**课程描述*/
	@Excel(name = "课程描述", width = 15)
    @ApiModelProperty(value = "课程描述")
    private java.lang.String description;
	/**课程封面图片URL*/
	@Excel(name = "课程封面图片URL", width = 15)
    @ApiModelProperty(value = "课程封面图片URL")
    private java.lang.String cover;
	/**课程分类ID*/
	@Excel(name = "课程分类ID", width = 15)
    @ApiModelProperty(value = "课程分类ID")
    private java.lang.String categoryId;
	/**课程难度级别：beginner/intermediate/advanced*/
	@Excel(name = "课程难度级别：beginner/intermediate/advanced", width = 15)
    @ApiModelProperty(value = "课程难度级别：beginner/intermediate/advanced")
    private java.lang.String level;
	/**课程价格*/
	@Excel(name = "课程价格", width = 15)
    @ApiModelProperty(value = "课程价格")
    private java.math.BigDecimal price;
	/**原价*/
	@Excel(name = "原价", width = 15)
    @ApiModelProperty(value = "原价")
    private java.math.BigDecimal originalPrice;
	/**课程总时长(分钟)*/
	@Excel(name = "课程总时长(分钟)", width = 15)
    @ApiModelProperty(value = "课程总时长(分钟)")
    private java.lang.Integer duration;
	/**学习人数*/
	@Excel(name = "学习人数", width = 15)
    @ApiModelProperty(value = "学习人数")
    private java.lang.Integer studentCount;
	/**课时数量*/
	@Excel(name = "课时数量", width = 15)
    @ApiModelProperty(value = "课时数量")
    private java.lang.Integer lessonCount;
	/**状态：0-下架 1-上架*/
	@Excel(name = "状态：0-下架 1-上架", width = 15)
    @ApiModelProperty(value = "状态：0-下架 1-上架")
    private java.lang.Integer status;
	/**是否推荐：0-否 1-是*/
	@Excel(name = "是否推荐：0-否 1-是", width = 15)
    @ApiModelProperty(value = "是否推荐：0-否 1-是")
    private java.lang.Integer isRecommend;
	/**是否热门：0-否 1-是*/
	@Excel(name = "是否热门：0-否 1-是", width = 15)
    @ApiModelProperty(value = "是否热门：0-否 1-是")
    private java.lang.Integer isHot;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**删除标志（0-未删除，1-已删除）*/
	@Excel(name = "删除标志（0-未删除，1-已删除）", width = 15)
    @ApiModelProperty(value = "删除标志（0-未删除，1-已删除）")
    @TableLogic
    private java.lang.Integer delFlag;
}
