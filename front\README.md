# 口语便利店小程序前端

基于 uni-app 开发的日语学习打卡小程序前端项目。

## 项目结构

```
front/
├── pages/                 # 页面文件
│   ├── index/             # 首页
│   ├── course/            # 课程相关页面
│   │   ├── list.vue      # 课程列表
│   │   └── detail.vue    # 课程详情
│   ├── practice/          # 练习页面
│   ├── checkin/           # 打卡页面
│   ├── leaderboard/       # 排行榜页面
│   └── profile/           # 个人中心页面
├── static/                # 静态资源
├── components/            # 组件
├── utils/                 # 工具函数
├── store/                 # 状态管理
├── App.vue               # 应用入口
├── main.js               # 主入口文件
├── manifest.json         # 应用配置
├── pages.json            # 页面配置
├── uni.scss              # 全局样式
└── package.json          # 依赖配置
```

## 技术栈

- **框架**: uni-app (Vue 3)
- **样式**: SCSS + 原生CSS
- **状态管理**: Vuex (可选)
- **构建工具**: Vite
- **开发语言**: JavaScript/TypeScript

## 功能特性

### 🏠 首页
- 学习进度展示
- 课程级别选择
- 今日打卡状态
- 清新渐变背景设计

### 📚 课程模块
- **课程列表**: 分级别展示课程，支持免费/付费标识
- **课程详情**: 音频播放、文字稿、翻译、词汇解释
- **跟读练习**: 录音功能、AI评分、实时反馈

### 📅 打卡系统
- 每日打卡功能
- 打卡日历展示
- 补打卡机制
- 连续打卡统计

### 🏆 排行榜
- 多时间维度排行
- 前三名领奖台展示
- 个人排名高亮
- 激励机制

### 👤 个人中心
- 用户信息管理
- 学习数据统计
- VIP会员功能
- 设置选项

## 设计特色

### 🎨 视觉设计
- **清新主义美学**: 柔和渐变配色
- **统一设计语言**: 一致的圆角、阴影、间距
- **响应式布局**: 适配不同屏幕尺寸
- **无障碍设计**: 隐藏滚动条，流畅交互

### 📱 交互体验
- **自定义导航栏**: 沉浸式体验
- **流畅动画**: 页面切换和状态变化
- **即时反馈**: 操作响应和状态提示
- **手势支持**: 滑动、长按等交互

## 开发指南

### 环境要求
- Node.js >= 14.0.0
- HBuilderX 或 VS Code
- 微信开发者工具 (开发微信小程序)

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 开发运行
```bash
# 微信小程序
npm run dev:mp-weixin

# H5
npm run dev:h5

# App
npm run dev:app
```

### 构建发布
```bash
# 微信小程序
npm run build:mp-weixin

# H5
npm run build:h5

# App
npm run build:app
```

## 配置说明

### 页面配置 (pages.json)
- 页面路由配置
- 导航栏样式
- 底部tabBar配置
- 页面权限设置

### 应用配置 (manifest.json)
- 应用基本信息
- 平台特定配置
- 权限申请
- 插件配置

### 样式配置 (uni.scss)
- 全局样式变量
- 主题色彩定义
- 通用样式类
- 响应式断点

## API 集成

### 请求封装
```javascript
// 使用全局请求方法
this.$request({
  url: '/api/v1/courses',
  method: 'GET',
  data: { level: 'beginner' }
}).then(res => {
  console.log(res.data)
})
```

### 状态管理
```javascript
// 用户状态
const userStore = {
  state: {
    userInfo: {},
    isLogin: false
  },
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
    }
  }
}
```

## 部署说明

### 微信小程序
1. 在微信公众平台注册小程序
2. 配置服务器域名
3. 上传代码包
4. 提交审核

### H5部署
1. 构建生产版本
2. 上传到服务器
3. 配置域名和HTTPS
4. 设置缓存策略

## 开发规范

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循 Vue 官方风格指南
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 性能优化

### 包体积优化
- 按需引入组件和API
- 图片压缩和懒加载
- 代码分割和异步加载
- 移除未使用的代码

### 运行时优化
- 虚拟列表处理长列表
- 防抖节流处理频繁操作
- 缓存策略优化
- 预加载关键资源

## 常见问题

### Q: 如何调试小程序？
A: 使用微信开发者工具的调试功能，查看控制台和网络请求。

### Q: 如何处理跨平台兼容性？
A: 使用 uni-app 的条件编译，针对不同平台编写特定代码。

### Q: 如何优化首屏加载速度？
A: 减少首屏资源大小，使用骨架屏，优化关键渲染路径。

## 更新日志

### v1.0.0 (2024-03-22)
- ✨ 完成所有核心页面开发
- 🎨 实现清新主义UI设计
- 📱 支持微信小程序平台
- 🔧 完善开发工具链配置

## 联系方式

如有问题或建议，请联系开发团队。

---

**项目状态**: 开发中 🚧
**Vue版本**: 3.3.4 ✅
**构建工具**: Vite 4.4.9 ✅
**最后更新**: 2024年3月

## ✅ Vue3 支持确认

本项目已完整支持Vue3，所有必要文件已创建：

### 核心配置文件
- ✅ `index.html` - H5入口文件（已创建）
- ✅ `vite.config.js` - Vite构建配置（已创建）
- ✅ `tsconfig.json` - TypeScript配置（已创建）
- ✅ `manifest.json` - Vue版本设置为3
- ✅ `.env` / `.env.production` - 环境变量配置

### Vue3特性支持
- ✅ Composition API
- ✅ `<script setup>` 语法
- ✅ TypeScript 支持
- ✅ Vite 热重载
- ✅ 现代化构建流程

### 问题解决状态
- ✅ "根目录缺少 index.html" - 已解决
- ✅ Vue3模板配置 - 已完成
- ✅ 路径别名配置 - 已修复
- ✅ 环境变量管理 - 已配置

**确认**: 项目完全支持Vue3，可正常开发和构建 🎉
