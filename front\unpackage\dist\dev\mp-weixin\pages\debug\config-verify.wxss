/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.config-verify-container.data-v-10eedd14 {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
}
.verify-header.data-v-10eedd14 {
  text-align: center;
  margin-bottom: 40rpx;
}
.verify-header .verify-title.data-v-10eedd14 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.verify-section.data-v-10eedd14 {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.verify-section .section-title.data-v-10eedd14 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 16rpx;
}
.status-item.data-v-10eedd14 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.status-item.data-v-10eedd14:last-child {
  border-bottom: none;
}
.status-item .status-label.data-v-10eedd14 {
  font-size: 28rpx;
  color: #666;
}
.status-item .status-value.data-v-10eedd14 {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}
.status-item .status-value.success.data-v-10eedd14 {
  color: #52c41a;
}
.status-item .status-value.error.data-v-10eedd14 {
  color: #ff4d4f;
}
.test-buttons.data-v-10eedd14 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.test-buttons .test-btn.data-v-10eedd14 {
  height: 80rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.test-buttons .test-btn.data-v-10eedd14:active {
  opacity: 0.8;
}
.solution-item.data-v-10eedd14 {
  margin-bottom: 24rpx;
}
.solution-item .solution-title.data-v-10eedd14 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.solution-item .solution-desc.data-v-10eedd14 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}