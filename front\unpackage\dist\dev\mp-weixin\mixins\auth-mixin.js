"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const utils_auth = require("../utils/auth.js");
const authMixin = {
  data() {
    return {
      // 认证状态
      isAuthenticated: false,
      currentUser: null,
      authToken: null,
      // 登录检查状态
      authChecking: true,
      authRequired: true
      // 页面是否需要登录，子页面可重写
    };
  },
  onLoad(options) {
    this.checkAuthStatus();
  },
  onShow() {
    this.checkAuthStatus();
  },
  methods: {
    /**
     * 检查认证状态
     */
    checkAuthStatus() {
      common_vendor.index.__f__("log", "at mixins/auth-mixin.js:37", "🔐 检查认证状态");
      try {
        this.authChecking = true;
        this.isAuthenticated = utils_auth.isLoggedIn();
        this.currentUser = utils_auth.getUserInfo();
        this.authToken = utils_auth.getToken();
        common_vendor.index.__f__("log", "at mixins/auth-mixin.js:47", "认证状态:", {
          isAuthenticated: this.isAuthenticated,
          hasUser: !!this.currentUser,
          hasToken: !!this.authToken
        });
        if (this.authRequired && !this.isAuthenticated) {
          this.handleAuthRequired();
          return false;
        }
        this.onAuthCheckComplete();
        return true;
      } catch (error) {
        common_vendor.index.__f__("error", "at mixins/auth-mixin.js:64", "❌ 认证状态检查失败:", error);
        this.handleAuthError(error);
        return false;
      } finally {
        this.authChecking = false;
      }
    },
    /**
     * 处理需要登录的情况
     */
    handleAuthRequired() {
      common_vendor.index.__f__("log", "at mixins/auth-mixin.js:77", "🔐 页面需要登录，跳转到登录页面");
      const currentRoute = this.getCurrentPageRoute();
      utils_auth.requireLogin(currentRoute);
    },
    /**
     * 处理认证错误
     */
    handleAuthError(error) {
      common_vendor.index.__f__("error", "at mixins/auth-mixin.js:90", "❌ 认证错误:", error);
      common_vendor.index.showToast({
        title: "认证失败，请重新登录",
        icon: "none",
        duration: 2e3
      });
      if (this.authRequired) {
        setTimeout(() => {
          this.handleAuthRequired();
        }, 2e3);
      }
    },
    /**
     * 认证检查完成回调
     * 子页面可重写此方法
     */
    onAuthCheckComplete() {
      common_vendor.index.__f__("log", "at mixins/auth-mixin.js:111", "✅ 认证检查完成");
      if (this.onAuthReady && typeof this.onAuthReady === "function") {
        this.onAuthReady();
      }
    },
    /**
     * 强制要求登录
     */
    forceLogin() {
      const currentRoute = this.getCurrentPageRoute();
      utils_auth.requireLogin(currentRoute);
    },
    /**
     * 检查是否有权限执行某个操作
     */
    checkPermission(permission) {
      if (!this.isAuthenticated) {
        this.showLoginRequired();
        return false;
      }
      return true;
    },
    /**
     * 显示需要登录的提示
     */
    showLoginRequired() {
      common_vendor.index.showModal({
        title: "需要登录",
        content: "此功能需要登录后才能使用，是否立即登录？",
        confirmText: "立即登录",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            this.forceLogin();
          }
        }
      });
    },
    /**
     * 安全获取当前页面路由
     */
    getCurrentPageRoute() {
      try {
        if (this.$mp && this.$mp.page && this.$mp.page.route) {
          return this.$mp.page.route;
        }
        const pages = getCurrentPages();
        if (pages && pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          return currentPage.route || "";
        }
        return "";
      } catch (error) {
        common_vendor.index.__f__("warn", "at mixins/auth-mixin.js:176", "获取页面路由失败:", error);
        return "";
      }
    },
    /**
     * 获取当前用户显示名称
     */
    getCurrentUserName() {
      if (!this.currentUser)
        return "未登录";
      return this.currentUser.realname || this.currentUser.username || "用户";
    },
    /**
     * 获取当前用户头像
     */
    getCurrentUserAvatar() {
      if (!this.currentUser || !this.currentUser.avatar) {
        return "/static/images/default-avatar.png";
      }
      return this.currentUser.avatar;
    },
    /**
     * 刷新认证状态
     */
    refreshAuthStatus() {
      common_vendor.index.__f__("log", "at mixins/auth-mixin.js:203", "🔄 刷新认证状态");
      this.checkAuthStatus();
    },
    /**
     * 登出处理
     */
    handleLogout() {
      return __async(this, null, function* () {
        try {
          this.isAuthenticated = false;
          this.currentUser = null;
          this.authToken = null;
          common_vendor.index.showToast({
            title: "已退出登录",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.reLaunch({
              url: "/pages/index/index"
            });
          }, 1500);
        } catch (error) {
          common_vendor.index.__f__("error", "at mixins/auth-mixin.js:233", "❌ 登出失败:", error);
          common_vendor.index.showToast({
            title: "登出失败",
            icon: "none"
          });
        }
      });
    }
  }
};
exports.authMixin = authMixin;
//# sourceMappingURL=../../.sourcemap/mp-weixin/mixins/auth-mixin.js.map
