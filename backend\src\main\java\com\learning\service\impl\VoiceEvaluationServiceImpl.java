package com.learning.service.impl;

import com.learning.dto.IseRequest;
import com.learning.dto.IseResult;
import com.learning.service.IVoiceEvaluationService;
import com.learning.util.XunfeiIseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 语音评测服务实现类
 * <AUTHOR>
 * @since 2024-03-22
 */
@Slf4j
@Service
public class VoiceEvaluationServiceImpl implements IVoiceEvaluationService {
    
    @Autowired
    private XunfeiIseUtil xunfeiIseUtil;
    
    // 支持的语言类型
    private static final String[] SUPPORTED_LANGUAGES = {"cn", "en"};
    
    // 支持的题型
    private static final Map<String, String[]> SUPPORTED_CATEGORIES = new HashMap<>();
    
    static {
        SUPPORTED_CATEGORIES.put("cn", new String[]{
            "read_syllable", "read_word", "read_sentence", "read_chapter"
        });
        SUPPORTED_CATEGORIES.put("en", new String[]{
            "read_word", "read_sentence", "read_chapter"
        });
    }
    
    @Override
    public IseResult evaluate(MultipartFile audioFile, String text, String language, String category) {
        try {
            // 验证参数
            validateParameters(audioFile, text, language, category);
            
            // 转换音频文件为Base64
            byte[] audioBytes = audioFile.getBytes();
            String audioBase64 = Base64.getEncoder().encodeToString(audioBytes);
            
            // 构建评测请求
            IseRequest request = new IseRequest();
            request.setAudioData(audioBase64);
            request.setText(text);
            request.setLanguage(language);
            request.setCategory(category);
            
            // 根据文件类型设置音频格式
            String contentType = audioFile.getContentType();
            if (contentType != null) {
                if (contentType.contains("wav")) {
                    request.setAudioFormat("wav");
                } else if (contentType.contains("mp3")) {
                    request.setAudioFormat("mp3");
                } else {
                    request.setAudioFormat("raw");
                }
            }
            
            // 执行评测
            return xunfeiIseUtil.evaluate(request);
            
        } catch (IOException e) {
            log.error("读取音频文件失败", e);
            throw new RuntimeException("读取音频文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("语音评测失败", e);
            throw new RuntimeException("语音评测失败: " + e.getMessage());
        }
    }
    
    @Override
    public IseResult evaluate(IseRequest request) {
        try {
            // 验证请求参数
            validateRequest(request);
            
            // 执行评测
            return xunfeiIseUtil.evaluate(request);
            
        } catch (Exception e) {
            log.error("语音评测失败", e);
            throw new RuntimeException("语音评测失败: " + e.getMessage());
        }
    }
    
    @Override
    public String[] getSupportedLanguages() {
        return SUPPORTED_LANGUAGES.clone();
    }
    
    @Override
    public String[] getSupportedCategories(String language) {
        return SUPPORTED_CATEGORIES.getOrDefault(language, new String[0]);
    }
    
    /**
     * 验证参数
     */
    private void validateParameters(MultipartFile audioFile, String text, String language, String category) {
        if (audioFile == null || audioFile.isEmpty()) {
            throw new IllegalArgumentException("音频文件不能为空");
        }
        
        if (text == null || text.trim().isEmpty()) {
            throw new IllegalArgumentException("待评测文本不能为空");
        }
        
        if (language == null || language.trim().isEmpty()) {
            throw new IllegalArgumentException("语言类型不能为空");
        }
        
        if (category == null || category.trim().isEmpty()) {
            throw new IllegalArgumentException("题型不能为空");
        }
        
        // 验证语言类型
        boolean languageSupported = false;
        for (String supportedLang : SUPPORTED_LANGUAGES) {
            if (supportedLang.equals(language)) {
                languageSupported = true;
                break;
            }
        }
        if (!languageSupported) {
            throw new IllegalArgumentException("不支持的语言类型: " + language);
        }
        
        // 验证题型
        String[] categories = SUPPORTED_CATEGORIES.get(language);
        if (categories != null) {
            boolean categorySupported = false;
            for (String supportedCategory : categories) {
                if (supportedCategory.equals(category)) {
                    categorySupported = true;
                    break;
                }
            }
            if (!categorySupported) {
                throw new IllegalArgumentException("不支持的题型: " + category);
            }
        }
        
        // 验证文件大小(限制10MB)
        if (audioFile.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("音频文件大小不能超过10MB");
        }
        
        // 验证文件类型
        String contentType = audioFile.getContentType();
        if (contentType != null && !isValidAudioType(contentType)) {
            throw new IllegalArgumentException("不支持的音频格式: " + contentType);
        }
    }
    
    /**
     * 验证请求参数
     */
    private void validateRequest(IseRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("评测请求不能为空");
        }
        
        if (request.getAudioData() == null || request.getAudioData().trim().isEmpty()) {
            throw new IllegalArgumentException("音频数据不能为空");
        }
        
        if (request.getText() == null || request.getText().trim().isEmpty()) {
            throw new IllegalArgumentException("待评测文本不能为空");
        }
        
        // 验证Base64格式
        try {
            Base64.getDecoder().decode(request.getAudioData());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("音频数据格式错误，必须是Base64编码");
        }
    }
    
    /**
     * 验证音频文件类型
     */
    private boolean isValidAudioType(String contentType) {
        return contentType.contains("audio") || 
               contentType.contains("wav") || 
               contentType.contains("mp3") || 
               contentType.contains("m4a") ||
               contentType.contains("aac") ||
               contentType.contains("pcm");
    }
}
