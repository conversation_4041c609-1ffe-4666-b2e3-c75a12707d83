package com.learning.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.learning.entity.KumaCourse;
import com.learning.service.IKumaCourseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = "APP-课程管理")
@RestController
@RequestMapping("/app/course")
public class CourseController {

    @Autowired
    private IKumaCourseService courseService;

    @AutoLog(value = "课程-分页列表查询")
    @ApiOperation(value = "课程-分页列表查询", notes = "课程-分页列表查询")
    @GetMapping("/list")
    public Result<IPage<KumaCourse>> queryPageList(
            KumaCourse course,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "categoryId", required = false) String categoryId,
            @RequestParam(name = "keyword", required = false) String keyword) {
        
        Page<KumaCourse> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<KumaCourse> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        queryWrapper.eq(KumaCourse::getStatus, 1); // 只查询上架的课程
        if (categoryId != null) {
            queryWrapper.eq(KumaCourse::getCategoryId, categoryId);
        }
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.like(KumaCourse::getTitle, keyword)
                    .or()
                    .like(KumaCourse::getDescription, keyword);
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc(KumaCourse::getCreateTime);
        
        IPage<KumaCourse> pageList = courseService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog(value = "课程-通过id查询")
    @ApiOperation(value = "课程-通过id查询", notes = "课程-通过id查询")
    @GetMapping("/queryById")
    public Result<KumaCourse> queryById(@RequestParam(name = "id") String id) {
        KumaCourse course = courseService.getById(id);
        if (course == null) {
            return Result.error("未找到对应课程");
        }
        return Result.OK(course);
    }

    @AutoLog(value = "课程-获取推荐课程")
    @ApiOperation(value = "课程-获取推荐课程", notes = "课程-获取推荐课程")
    @GetMapping("/recommend")
    public Result<List<KumaCourse>> getRecommendCourses() {
        LambdaQueryWrapper<KumaCourse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCourse::getStatus, 1)
                .eq(KumaCourse::getIsRecommend, 1)
                .orderByDesc(KumaCourse::getCreateTime)
                .last("limit 10");
        
        List<KumaCourse> list = courseService.list(queryWrapper);
        return Result.OK(list);
    }

    @AutoLog(value = "课程-获取热门课程")
    @ApiOperation(value = "课程-获取热门课程", notes = "课程-获取热门课程")
    @GetMapping("/hot")
    public Result<List<KumaCourse>> getHotCourses() {
        LambdaQueryWrapper<KumaCourse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCourse::getStatus, 1)
                .eq(KumaCourse::getIsHot, 1)
                .orderByDesc(KumaCourse::getStudentCount)
                .last("limit 10");
        
        List<KumaCourse> list = courseService.list(queryWrapper);
        return Result.OK(list);
    }
} 