{"version": 3, "file": "auth-mixin.js", "sources": ["mixins/auth-mixin.js"], "sourcesContent": ["/**\n * 认证状态混入\n * 为页面提供统一的登录状态检查和处理\n */\n\nimport { isLoggedIn, requireLogin, getUserInfo, getToken } from '@/utils/auth.js'\n\nexport default {\n  data() {\n    return {\n      // 认证状态\n      isAuthenticated: false,\n      currentUser: null,\n      authToken: null,\n      \n      // 登录检查状态\n      authChecking: true,\n      authRequired: true  // 页面是否需要登录，子页面可重写\n    }\n  },\n  \n  onLoad(options) {\n    // 检查认证状态\n    this.checkAuthStatus()\n  },\n  \n  onShow() {\n    // 页面显示时重新检查认证状态\n    this.checkAuthStatus()\n  },\n  \n  methods: {\n    /**\n     * 检查认证状态\n     */\n    checkAuthStatus() {\n      console.log('🔐 检查认证状态')\n      \n      try {\n        this.authChecking = true\n        \n        // 检查登录状态\n        this.isAuthenticated = isLoggedIn()\n        this.currentUser = getUserInfo()\n        this.authToken = getToken()\n        \n        console.log('认证状态:', {\n          isAuthenticated: this.isAuthenticated,\n          hasUser: !!this.currentUser,\n          hasToken: !!this.authToken\n        })\n        \n        // 如果页面需要登录但用户未登录\n        if (this.authRequired && !this.isAuthenticated) {\n          this.handleAuthRequired()\n          return false\n        }\n        \n        // 认证检查完成\n        this.onAuthCheckComplete()\n        return true\n        \n      } catch (error) {\n        console.error('❌ 认证状态检查失败:', error)\n        this.handleAuthError(error)\n        return false\n        \n      } finally {\n        this.authChecking = false\n      }\n    },\n    \n    /**\n     * 处理需要登录的情况\n     */\n    handleAuthRequired() {\n      console.log('🔐 页面需要登录，跳转到登录页面')\n\n      // 安全获取当前页面路径\n      const currentRoute = this.getCurrentPageRoute()\n\n      // 跳转到登录页面\n      requireLogin(currentRoute)\n    },\n    \n    /**\n     * 处理认证错误\n     */\n    handleAuthError(error) {\n      console.error('❌ 认证错误:', error)\n      \n      uni.showToast({\n        title: '认证失败，请重新登录',\n        icon: 'none',\n        duration: 2000\n      })\n      \n      // 如果页面需要登录，跳转到登录页面\n      if (this.authRequired) {\n        setTimeout(() => {\n          this.handleAuthRequired()\n        }, 2000)\n      }\n    },\n    \n    /**\n     * 认证检查完成回调\n     * 子页面可重写此方法\n     */\n    onAuthCheckComplete() {\n      console.log('✅ 认证检查完成')\n      \n      // 如果子页面定义了认证完成回调，调用它\n      if (this.onAuthReady && typeof this.onAuthReady === 'function') {\n        this.onAuthReady()\n      }\n    },\n    \n    /**\n     * 强制要求登录\n     */\n    forceLogin() {\n      const currentRoute = this.getCurrentPageRoute()\n      requireLogin(currentRoute)\n    },\n    \n    /**\n     * 检查是否有权限执行某个操作\n     */\n    checkPermission(permission) {\n      if (!this.isAuthenticated) {\n        this.showLoginRequired()\n        return false\n      }\n      \n      // 这里可以添加具体的权限检查逻辑\n      return true\n    },\n    \n    /**\n     * 显示需要登录的提示\n     */\n    showLoginRequired() {\n      uni.showModal({\n        title: '需要登录',\n        content: '此功能需要登录后才能使用，是否立即登录？',\n        confirmText: '立即登录',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            this.forceLogin()\n          }\n        }\n      })\n    },\n    \n    /**\n     * 安全获取当前页面路由\n     */\n    getCurrentPageRoute() {\n      try {\n        // 尝试从$mp.page获取\n        if (this.$mp && this.$mp.page && this.$mp.page.route) {\n          return this.$mp.page.route\n        }\n\n        // 尝试从getCurrentPages获取\n        const pages = getCurrentPages()\n        if (pages && pages.length > 0) {\n          const currentPage = pages[pages.length - 1]\n          return currentPage.route || ''\n        }\n\n        return ''\n      } catch (error) {\n        console.warn('获取页面路由失败:', error)\n        return ''\n      }\n    },\n\n    /**\n     * 获取当前用户显示名称\n     */\n    getCurrentUserName() {\n      if (!this.currentUser) return '未登录'\n      return this.currentUser.realname || this.currentUser.username || '用户'\n    },\n    \n    /**\n     * 获取当前用户头像\n     */\n    getCurrentUserAvatar() {\n      if (!this.currentUser || !this.currentUser.avatar) {\n        return '/static/images/default-avatar.png'\n      }\n      return this.currentUser.avatar\n    },\n    \n    /**\n     * 刷新认证状态\n     */\n    refreshAuthStatus() {\n      console.log('🔄 刷新认证状态')\n      this.checkAuthStatus()\n    },\n    \n    /**\n     * 登出处理\n     */\n    async handleLogout() {\n      try {\n        // 这里可以调用登出接口\n        // await logout()\n        \n        // 清除认证状态\n        this.isAuthenticated = false\n        this.currentUser = null\n        this.authToken = null\n        \n        uni.showToast({\n          title: '已退出登录',\n          icon: 'success'\n        })\n        \n        // 跳转到首页或登录页\n        setTimeout(() => {\n          uni.reLaunch({\n            url: '/pages/index/index'\n          })\n        }, 1500)\n        \n      } catch (error) {\n        console.error('❌ 登出失败:', error)\n        uni.showToast({\n          title: '登出失败',\n          icon: 'none'\n        })\n      }\n    }\n  }\n}\n"], "names": ["uni", "isLoggedIn", "getUserInfo", "getToken", "requireLogin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAOA,MAAe,YAAA;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,WAAW;AAAA;AAAA,MAGX,cAAc;AAAA,MACd,cAAc;AAAA;AAAA,IACf;AAAA,EACF;AAAA,EAED,OAAO,SAAS;AAEd,SAAK,gBAAiB;AAAA,EACvB;AAAA,EAED,SAAS;AAEP,SAAK,gBAAiB;AAAA,EACvB;AAAA,EAED,SAAS;AAAA;AAAA;AAAA;AAAA,IAIP,kBAAkB;AAChBA,oBAAAA,iDAAY,WAAW;AAEvB,UAAI;AACF,aAAK,eAAe;AAGpB,aAAK,kBAAkBC,sBAAY;AACnC,aAAK,cAAcC,uBAAa;AAChC,aAAK,YAAYC,oBAAU;AAE3BH,sBAAAA,MAAA,MAAA,OAAA,8BAAY,SAAS;AAAA,UACnB,iBAAiB,KAAK;AAAA,UACtB,SAAS,CAAC,CAAC,KAAK;AAAA,UAChB,UAAU,CAAC,CAAC,KAAK;AAAA,QAC3B,CAAS;AAGD,YAAI,KAAK,gBAAgB,CAAC,KAAK,iBAAiB;AAC9C,eAAK,mBAAoB;AACzB,iBAAO;AAAA,QACR;AAGD,aAAK,oBAAqB;AAC1B,eAAO;AAAA,MAER,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,8BAAc,eAAe,KAAK;AAClC,aAAK,gBAAgB,KAAK;AAC1B,eAAO;AAAA,MAEf,UAAgB;AACR,aAAK,eAAe;AAAA,MACrB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,qBAAqB;AACnBA,oBAAAA,iDAAY,mBAAmB;AAG/B,YAAM,eAAe,KAAK,oBAAqB;AAG/CI,iBAAAA,aAAa,YAAY;AAAA,IAC1B;AAAA;AAAA;AAAA;AAAA,IAKD,gBAAgB,OAAO;AACrBJ,oBAAAA,MAAc,MAAA,SAAA,8BAAA,WAAW,KAAK;AAE9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MAClB,CAAO;AAGD,UAAI,KAAK,cAAc;AACrB,mBAAW,MAAM;AACf,eAAK,mBAAoB;AAAA,QAC1B,GAAE,GAAI;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,sBAAsB;AACpBA,oBAAAA,kDAAY,UAAU;AAGtB,UAAI,KAAK,eAAe,OAAO,KAAK,gBAAgB,YAAY;AAC9D,aAAK,YAAa;AAAA,MACnB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa;AACX,YAAM,eAAe,KAAK,oBAAqB;AAC/CI,iBAAAA,aAAa,YAAY;AAAA,IAC1B;AAAA;AAAA;AAAA;AAAA,IAKD,gBAAgB,YAAY;AAC1B,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,kBAAmB;AACxB,eAAO;AAAA,MACR;AAGD,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA,IAKD,oBAAoB;AAClBJ,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,WAAY;AAAA,UAClB;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,sBAAsB;AACpB,UAAI;AAEF,YAAI,KAAK,OAAO,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,OAAO;AACpD,iBAAO,KAAK,IAAI,KAAK;AAAA,QACtB;AAGD,cAAM,QAAQ,gBAAiB;AAC/B,YAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,gBAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,iBAAO,YAAY,SAAS;AAAA,QAC7B;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAa,MAAA,QAAA,+BAAA,aAAa,KAAK;AAC/B,eAAO;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,qBAAqB;AACnB,UAAI,CAAC,KAAK;AAAa,eAAO;AAC9B,aAAO,KAAK,YAAY,YAAY,KAAK,YAAY,YAAY;AAAA,IAClE;AAAA;AAAA;AAAA;AAAA,IAKD,uBAAuB;AACrB,UAAI,CAAC,KAAK,eAAe,CAAC,KAAK,YAAY,QAAQ;AACjD,eAAO;AAAA,MACR;AACD,aAAO,KAAK,YAAY;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA,IAKD,oBAAoB;AAClBA,oBAAAA,kDAAY,WAAW;AACvB,WAAK,gBAAiB;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA,IAKK,eAAe;AAAA;AACnB,YAAI;AAKF,eAAK,kBAAkB;AACvB,eAAK,cAAc;AACnB,eAAK,YAAY;AAEjBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAChB,CAAS;AAGD,qBAAW,MAAM;AACfA,0BAAAA,MAAI,SAAS;AAAA,cACX,KAAK;AAAA,YACjB,CAAW;AAAA,UACF,GAAE,IAAI;AAAA,QAER,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,+BAAc,WAAW,KAAK;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAChB,CAAS;AAAA,QACF;AAAA,MACF;AAAA;AAAA,EACF;AACH;;"}