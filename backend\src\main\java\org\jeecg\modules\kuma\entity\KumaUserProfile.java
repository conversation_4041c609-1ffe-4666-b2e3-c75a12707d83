package org.jeecg.modules.kuma.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户扩展信息表
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@TableName("kuma_user_profile")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "kuma_user_profile对象", description = "用户扩展信息表")
public class KumaUserProfile implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**关联sys_user表ID*/
    @Excel(name = "关联用户ID", width = 15)
    @ApiModelProperty(value = "关联sys_user表ID")
    private String userId;

    /**昵称*/
    @Excel(name = "昵称", width = 15)
    @ApiModelProperty(value = "昵称")
    private String nickname;

    /**头像URL*/
    @Excel(name = "头像URL", width = 15)
    @ApiModelProperty(value = "头像URL")
    private String avatar;

    /**学习级别*/
    @Excel(name = "学习级别", width = 15, dicCode = "kuma_user_level")
    @Dict(dicCode = "kuma_user_level")
    @ApiModelProperty(value = "学习级别：beginner,intermediate,advanced,professional")
    private String level;

    /**总学习天数*/
    @Excel(name = "总学习天数", width = 15)
    @ApiModelProperty(value = "总学习天数")
    private Integer totalStudyDays;

    /**连续打卡天数*/
    @Excel(name = "连续打卡天数", width = 15)
    @ApiModelProperty(value = "连续打卡天数")
    private Integer continuousDays;

    /**最长连续打卡天数*/
    @Excel(name = "最长连续打卡天数", width = 15)
    @ApiModelProperty(value = "最长连续打卡天数")
    private Integer maxContinuousDays;

    /**总积分*/
    @Excel(name = "总积分", width = 15)
    @ApiModelProperty(value = "总积分")
    private Integer totalScore;

    /**VIP状态*/
    @Excel(name = "VIP状态", width = 15, dicCode = "kuma_vip_status")
    @Dict(dicCode = "kuma_vip_status")
    @ApiModelProperty(value = "VIP状态 0-普通用户 1-VIP用户")
    private Integer vipStatus;

    /**VIP过期时间*/
    @Excel(name = "VIP过期时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "VIP过期时间")
    private Date vipExpireTime;

    /**系统标识*/
    @Excel(name = "系统标识", width = 15)
    @ApiModelProperty(value = "系统标识")
    private String sysSign;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**删除标志*/
    @ApiModelProperty(value = "删除标志 0-正常 1-删除")
    private Integer delFlag;
}
