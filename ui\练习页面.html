<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>口语便利店 - 跟读练习</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { 
            overflow: hidden; 
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar { display: none; }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .header {
            height: 60px;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
        }
        .content {
            height: calc(100% - 44px - 60px);
            overflow-y: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
            padding: 20px;
        }
        .content::-webkit-scrollbar { display: none; }
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#10b981 0deg 108deg, rgba(255,255,255,0.3) 108deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 0 auto 30px;
        }
        .progress-circle::before {
            content: '';
            width: 90px;
            height: 90px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            position: absolute;
        }
        .progress-text {
            position: relative;
            z-index: 1;
            text-align: center;
        }
        .sentence-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }
        .record-button {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            margin: 0 auto;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
        }
        .record-button.recording {
            background: linear-gradient(135deg, #ff4757, #c44569);
            animation: pulse 1.5s infinite;
        }
        .record-button.completed {
            background: linear-gradient(135deg, #2ed573, #1e90ff);
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .score-display {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            border-radius: 16px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .score-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
        }
        .score-bar {
            width: 100px;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
        }
        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #00b894, #00cec9);
            border-radius: 4px;
        }
        .control-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .control-btn:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 头部 -->
            <div class="header">
                <i class="fas fa-arrow-left text-xl"></i>
                <div class="text-center">
                    <h1 class="text-lg font-bold">跟读练习</h1>
                    <p class="text-sm opacity-80">第4课 - 句子 3/8</p>
                </div>
                <i class="fas fa-times text-xl"></i>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 进度圆环 -->
                <div class="progress-circle">
                    <div class="progress-text">
                        <div class="text-2xl font-bold text-green-600">3</div>
                        <div class="text-sm text-gray-600">/ 8</div>
                    </div>
                </div>

                <!-- 当前句子 -->
                <div class="sentence-card">
                    <div class="mb-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-2">请跟读以下句子</h3>
                        <p class="text-2xl text-gray-800 leading-relaxed mb-3">
                            ラーメンとギョーザをお願いします
                        </p>
                        <p class="text-base text-gray-600">
                            请给我拉面和饺子
                        </p>
                    </div>
                    
                    <!-- 原音播放控制 -->
                    <div class="control-buttons">
                        <div class="control-btn">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="control-btn">
                            <i class="fas fa-redo"></i>
                        </div>
                        <div class="control-btn">
                            <i class="fas fa-volume-up"></i>
                        </div>
                    </div>
                </div>

                <!-- 录音按钮 -->
                <div class="sentence-card">
                    <p class="text-gray-600 mb-4">点击按钮开始录音</p>
                    <div class="record-button">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <p class="text-sm text-gray-500 mt-4">长按录音，松开结束</p>
                </div>

                <!-- AI评分结果 -->
                <div class="score-display">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-robot mr-2"></i>
                        AI评分结果
                    </h3>
                    
                    <div class="text-3xl font-bold text-orange-600 mb-4">85分</div>
                    
                    <div class="space-y-3">
                        <div class="score-item">
                            <span class="text-gray-700">发音准确度</span>
                            <div class="flex items-center">
                                <div class="score-bar mr-2">
                                    <div class="score-fill" style="width: 90%"></div>
                                </div>
                                <span class="text-sm font-medium">90%</span>
                            </div>
                        </div>
                        
                        <div class="score-item">
                            <span class="text-gray-700">流利度</span>
                            <div class="flex items-center">
                                <div class="score-bar mr-2">
                                    <div class="score-fill" style="width: 80%"></div>
                                </div>
                                <span class="text-sm font-medium">80%</span>
                            </div>
                        </div>
                        
                        <div class="score-item">
                            <span class="text-gray-700">语调</span>
                            <div class="flex items-center">
                                <div class="score-bar mr-2">
                                    <div class="score-fill" style="width: 85%"></div>
                                </div>
                                <span class="text-sm font-medium">85%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-3 bg-white/50 rounded-lg">
                        <p class="text-sm text-gray-700">
                            <i class="fas fa-lightbulb text-yellow-500 mr-1"></i>
                            建议：「ギョーザ」的发音可以更清晰一些
                        </p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex gap-3 mt-6">
                    <button class="flex-1 bg-white/90 text-gray-700 py-3 px-6 rounded-full font-medium">
                        <i class="fas fa-redo mr-2"></i>
                        重新录音
                    </button>
                    <button class="flex-1 bg-gradient-to-r from-green-400 to-blue-500 text-white py-3 px-6 rounded-full font-medium">
                        <i class="fas fa-arrow-right mr-2"></i>
                        下一句
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
