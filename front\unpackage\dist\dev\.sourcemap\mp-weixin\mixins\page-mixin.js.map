{"version": 3, "file": "page-mixin.js", "sources": ["mixins/page-mixin.js"], "sourcesContent": ["/**\n * 页面通用混入\n * 为小程序页面提供统一的基础功能\n */\n\nexport default {\n  data() {\n    return {\n      // 页面状态\n      loading: false,\n      refreshing: false,\n      \n      // 系统信息\n      statusBarHeight: 0,\n      navBarHeight: 0,\n      windowHeight: 0,\n      windowWidth: 0,\n      \n      // 用户信息\n      userInfo: null,\n      isLoggedIn: false,\n      \n      // 网络状态\n      isConnected: true,\n      \n      // 错误处理\n      hasError: false,\n      errorMessage: ''\n    }\n  },\n  \n  onLoad(options) {\n    const route = this.getPageRoute()\n    console.log('📄 页面加载:', route, options)\n\n    // 初始化页面\n    this.initPage(options)\n  },\n\n  onShow() {\n    const route = this.getPageRoute()\n    console.log('👁️ 页面显示:', route)\n\n    // 检查登录状态\n    this.checkLoginStatus()\n\n    // 检查网络状态\n    this.checkNetworkStatus()\n  },\n\n  onHide() {\n    const route = this.getPageRoute()\n    console.log('🙈 页面隐藏:', route)\n  },\n\n  onUnload() {\n    const route = this.getPageRoute()\n    console.log('🗑️ 页面卸载:', route)\n\n    // 清理资源\n    this.cleanup()\n  },\n  \n  // 下拉刷新\n  onPullDownRefresh() {\n    console.log('🔄 下拉刷新')\n    this.handleRefresh()\n  },\n  \n  // 上拉加载更多\n  onReachBottom() {\n    console.log('📄 上拉加载更多')\n    this.handleLoadMore()\n  },\n  \n  // 页面滚动\n  onPageScroll(e) {\n    // 可在子页面中重写\n  },\n  \n  // 分享\n  onShareAppMessage() {\n    return {\n      title: '口语便利店 - 日语学习打卡',\n      path: '/pages/index/index',\n      imageUrl: '/static/images/share-cover.jpg'\n    }\n  },\n  \n  // 分享到朋友圈（微信小程序）\n  onShareTimeline() {\n    return {\n      title: '口语便利店 - 日语学习打卡',\n      query: 'from=timeline',\n      imageUrl: '/static/images/share-cover.jpg'\n    }\n  },\n  \n  methods: {\n    /**\n     * 安全获取页面路由\n     */\n    getPageRoute() {\n      try {\n        // 尝试从$mp.page获取\n        if (this.$mp && this.$mp.page && this.$mp.page.route) {\n          return this.$mp.page.route\n        }\n\n        // 尝试从getCurrentPages获取\n        const pages = getCurrentPages()\n        if (pages && pages.length > 0) {\n          const currentPage = pages[pages.length - 1]\n          return currentPage.route || 'unknown'\n        }\n\n        // 兜底返回\n        return 'unknown'\n      } catch (error) {\n        console.warn('获取页面路由失败:', error)\n        return 'unknown'\n      }\n    },\n\n    /**\n     * 初始化页面\n     */\n    initPage(options = {}) {\n      try {\n        // 获取系统信息\n        this.getSystemInfo()\n\n        // 获取用户信息\n        this.getUserInfo()\n\n        // 页面特定初始化（子页面可重写）\n        if (this.pageInit && typeof this.pageInit === 'function') {\n          this.pageInit(options)\n        }\n\n      } catch (error) {\n        console.error('❌ 页面初始化失败:', error)\n        this.handleError(error)\n      }\n    },\n    \n    /**\n     * 获取系统信息\n     */\n    getSystemInfo() {\n      try {\n        const app = getApp()\n        if (app && app.globalData) {\n          this.statusBarHeight = app.globalData.statusBarHeight || 0\n          this.navBarHeight = app.globalData.navBarHeight || 44\n          this.windowHeight = app.globalData.windowHeight || 0\n          this.windowWidth = app.globalData.windowWidth || 0\n        } else {\n          // 备用方案\n          const systemInfo = uni.getSystemInfoSync()\n          this.statusBarHeight = systemInfo.statusBarHeight || 0\n          this.navBarHeight = (systemInfo.statusBarHeight || 0) + 44\n          this.windowHeight = systemInfo.windowHeight || 0\n          this.windowWidth = systemInfo.windowWidth || 0\n        }\n      } catch (error) {\n        console.error('❌ 获取系统信息失败:', error)\n      }\n    },\n    \n    /**\n     * 获取用户信息\n     */\n    getUserInfo() {\n      try {\n        const app = getApp()\n        if (app && app.globalData) {\n          this.userInfo = app.globalData.userInfo\n          this.isLoggedIn = app.globalData.isLoggedIn || false\n        } else {\n          // 从本地存储获取\n          this.userInfo = uni.getStorageSync('userInfo')\n          this.isLoggedIn = !!uni.getStorageSync('token')\n        }\n      } catch (error) {\n        console.error('❌ 获取用户信息失败:', error)\n      }\n    },\n    \n    /**\n     * 检查登录状态\n     */\n    checkLoginStatus() {\n      const token = uni.getStorageSync('token')\n      const userInfo = uni.getStorageSync('userInfo')\n      \n      this.isLoggedIn = !!(token && userInfo)\n      \n      if (this.isLoggedIn) {\n        this.userInfo = userInfo\n      }\n    },\n    \n    /**\n     * 检查网络状态\n     */\n    checkNetworkStatus() {\n      uni.getNetworkType({\n        success: (res) => {\n          this.isConnected = res.networkType !== 'none'\n          \n          if (!this.isConnected) {\n            this.showToast('网络连接已断开', 'none')\n          }\n        },\n        fail: () => {\n          console.warn('⚠️ 获取网络状态失败')\n        }\n      })\n    },\n    \n    /**\n     * 处理刷新\n     */\n    async handleRefresh() {\n      if (this.refreshing) return\n      \n      this.refreshing = true\n      \n      try {\n        // 子页面可重写此方法\n        if (this.onRefresh && typeof this.onRefresh === 'function') {\n          await this.onRefresh()\n        }\n      } catch (error) {\n        console.error('❌ 刷新失败:', error)\n        this.handleError(error)\n      } finally {\n        this.refreshing = false\n        uni.stopPullDownRefresh()\n      }\n    },\n    \n    /**\n     * 处理加载更多\n     */\n    async handleLoadMore() {\n      try {\n        // 子页面可重写此方法\n        if (this.onLoadMore && typeof this.onLoadMore === 'function') {\n          await this.onLoadMore()\n        }\n      } catch (error) {\n        console.error('❌ 加载更多失败:', error)\n        this.handleError(error)\n      }\n    },\n    \n    /**\n     * 显示加载状态\n     */\n    showLoading(title = '加载中...') {\n      this.loading = true\n      uni.showLoading({\n        title,\n        mask: true\n      })\n    },\n    \n    /**\n     * 隐藏加载状态\n     */\n    hideLoading() {\n      this.loading = false\n      uni.hideLoading()\n    },\n    \n    /**\n     * 显示提示信息\n     */\n    showToast(title, icon = 'none', duration = 2000) {\n      uni.showToast({\n        title,\n        icon,\n        duration\n      })\n    },\n    \n    /**\n     * 显示确认对话框\n     */\n    showConfirm(content, title = '提示') {\n      return new Promise((resolve) => {\n        uni.showModal({\n          title,\n          content,\n          success: (res) => {\n            resolve(res.confirm)\n          },\n          fail: () => {\n            resolve(false)\n          }\n        })\n      })\n    },\n    \n    /**\n     * 错误处理\n     */\n    handleError(error, showToast = true) {\n      console.error('❌ 页面错误:', error)\n      \n      this.hasError = true\n      this.errorMessage = error.message || '发生未知错误'\n      \n      // 记录错误到全局状态\n      const app = getApp()\n      if (app && app.addError) {\n        app.addError({\n          type: 'page_error',\n          page: this.$mp.page.route,\n          message: this.errorMessage,\n          stack: error.stack,\n          timestamp: Date.now()\n        })\n      }\n      \n      // 显示错误提示\n      if (showToast) {\n        this.showToast(this.errorMessage)\n      }\n    },\n    \n    /**\n     * 清理资源\n     */\n    cleanup() {\n      // 子页面可重写此方法进行资源清理\n      if (this.pageCleanup && typeof this.pageCleanup === 'function') {\n        this.pageCleanup()\n      }\n    },\n    \n    /**\n     * 跳转到登录页\n     */\n    goToLogin() {\n      uni.navigateTo({\n        url: '/pages/login/login'\n      })\n    },\n    \n    /**\n     * 检查登录状态，未登录则跳转\n     */\n    requireLogin() {\n      if (!this.isLoggedIn) {\n        this.showToast('请先登录')\n        setTimeout(() => {\n          this.goToLogin()\n        }, 1500)\n        return false\n      }\n      return true\n    },\n    \n    /**\n     * 格式化时间\n     */\n    formatTime(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {\n      if (!timestamp) return ''\n      \n      const date = new Date(timestamp)\n      const year = date.getFullYear()\n      const month = String(date.getMonth() + 1).padStart(2, '0')\n      const day = String(date.getDate()).padStart(2, '0')\n      const hours = String(date.getHours()).padStart(2, '0')\n      const minutes = String(date.getMinutes()).padStart(2, '0')\n      const seconds = String(date.getSeconds()).padStart(2, '0')\n      \n      return format\n        .replace('YYYY', year)\n        .replace('MM', month)\n        .replace('DD', day)\n        .replace('HH', hours)\n        .replace('mm', minutes)\n        .replace('ss', seconds)\n    }\n  }\n}\n"], "names": ["uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAKA,MAAe,YAAA;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,SAAS;AAAA,MACT,YAAY;AAAA;AAAA,MAGZ,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA;AAAA,MAGb,UAAU;AAAA,MACV,YAAY;AAAA;AAAA,MAGZ,aAAa;AAAA;AAAA,MAGb,UAAU;AAAA,MACV,cAAc;AAAA,IACf;AAAA,EACF;AAAA,EAED,OAAO,SAAS;AACd,UAAM,QAAQ,KAAK,aAAc;AACjCA,kBAAA,MAAA,MAAA,OAAA,8BAAY,YAAY,OAAO,OAAO;AAGtC,SAAK,SAAS,OAAO;AAAA,EACtB;AAAA,EAED,SAAS;AACP,UAAM,QAAQ,KAAK,aAAc;AACjCA,kBAAAA,MAAA,MAAA,OAAA,8BAAY,aAAa,KAAK;AAG9B,SAAK,iBAAkB;AAGvB,SAAK,mBAAoB;AAAA,EAC1B;AAAA,EAED,SAAS;AACP,UAAM,QAAQ,KAAK,aAAc;AACjCA,kBAAAA,MAAA,MAAA,OAAA,8BAAY,YAAY,KAAK;AAAA,EAC9B;AAAA,EAED,WAAW;AACT,UAAM,QAAQ,KAAK,aAAc;AACjCA,kBAAAA,MAAA,MAAA,OAAA,8BAAY,aAAa,KAAK;AAG9B,SAAK,QAAS;AAAA,EACf;AAAA;AAAA,EAGD,oBAAoB;AAClBA,kBAAAA,MAAA,MAAA,OAAA,8BAAY,SAAS;AACrB,SAAK,cAAe;AAAA,EACrB;AAAA;AAAA,EAGD,gBAAgB;AACdA,kBAAAA,MAAA,MAAA,OAAA,8BAAY,WAAW;AACvB,SAAK,eAAgB;AAAA,EACtB;AAAA;AAAA,EAGD,aAAa,GAAG;AAAA,EAEf;AAAA;AAAA,EAGD,oBAAoB;AAClB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACF;AAAA;AAAA,EAGD,kBAAkB;AAChB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,IACX;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA;AAAA;AAAA,IAIP,eAAe;AACb,UAAI;AAEF,YAAI,KAAK,OAAO,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,OAAO;AACpD,iBAAO,KAAK,IAAI,KAAK;AAAA,QACtB;AAGD,cAAM,QAAQ,gBAAiB;AAC/B,YAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,gBAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,iBAAO,YAAY,SAAS;AAAA,QAC7B;AAGD,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAa,MAAA,QAAA,+BAAA,aAAa,KAAK;AAC/B,eAAO;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,SAAS,UAAU,IAAI;AACrB,UAAI;AAEF,aAAK,cAAe;AAGpB,aAAK,YAAa;AAGlB,YAAI,KAAK,YAAY,OAAO,KAAK,aAAa,YAAY;AACxD,eAAK,SAAS,OAAO;AAAA,QACtB;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,+BAAA,cAAc,KAAK;AACjC,aAAK,YAAY,KAAK;AAAA,MACvB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,gBAAgB;AACd,UAAI;AACF,cAAM,MAAM,OAAQ;AACpB,YAAI,OAAO,IAAI,YAAY;AACzB,eAAK,kBAAkB,IAAI,WAAW,mBAAmB;AACzD,eAAK,eAAe,IAAI,WAAW,gBAAgB;AACnD,eAAK,eAAe,IAAI,WAAW,gBAAgB;AACnD,eAAK,cAAc,IAAI,WAAW,eAAe;AAAA,QAC3D,OAAe;AAEL,gBAAM,aAAaA,cAAG,MAAC,kBAAmB;AAC1C,eAAK,kBAAkB,WAAW,mBAAmB;AACrD,eAAK,gBAAgB,WAAW,mBAAmB,KAAK;AACxD,eAAK,eAAe,WAAW,gBAAgB;AAC/C,eAAK,cAAc,WAAW,eAAe;AAAA,QAC9C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,+BAAc,eAAe,KAAK;AAAA,MACnC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,cAAc;AACZ,UAAI;AACF,cAAM,MAAM,OAAQ;AACpB,YAAI,OAAO,IAAI,YAAY;AACzB,eAAK,WAAW,IAAI,WAAW;AAC/B,eAAK,aAAa,IAAI,WAAW,cAAc;AAAA,QACzD,OAAe;AAEL,eAAK,WAAWA,oBAAI,eAAe,UAAU;AAC7C,eAAK,aAAa,CAAC,CAACA,cAAAA,MAAI,eAAe,OAAO;AAAA,QAC/C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,+BAAc,eAAe,KAAK;AAAA,MACnC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,mBAAmB;AACjB,YAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9C,WAAK,aAAa,CAAC,EAAE,SAAS;AAE9B,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW;AAAA,MACjB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,qBAAqB;AACnBA,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,CAAC,QAAQ;AAChB,eAAK,cAAc,IAAI,gBAAgB;AAEvC,cAAI,CAAC,KAAK,aAAa;AACrB,iBAAK,UAAU,WAAW,MAAM;AAAA,UACjC;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAa,MAAA,QAAA,+BAAA,aAAa;AAAA,QAC3B;AAAA,MACT,CAAO;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKK,gBAAgB;AAAA;AACpB,YAAI,KAAK;AAAY;AAErB,aAAK,aAAa;AAElB,YAAI;AAEF,cAAI,KAAK,aAAa,OAAO,KAAK,cAAc,YAAY;AAC1D,kBAAM,KAAK,UAAW;AAAA,UACvB;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,+BAAc,WAAW,KAAK;AAC9B,eAAK,YAAY,KAAK;AAAA,QAC9B,UAAgB;AACR,eAAK,aAAa;AAClBA,wBAAAA,MAAI,oBAAqB;AAAA,QAC1B;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAKK,iBAAiB;AAAA;AACrB,YAAI;AAEF,cAAI,KAAK,cAAc,OAAO,KAAK,eAAe,YAAY;AAC5D,kBAAM,KAAK,WAAY;AAAA,UACxB;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,+BAAA,aAAa,KAAK;AAChC,eAAK,YAAY,KAAK;AAAA,QACvB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,QAAQ,UAAU;AAC5B,WAAK,UAAU;AACfA,oBAAAA,MAAI,YAAY;AAAA,QACd;AAAA,QACA,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,cAAc;AACZ,WAAK,UAAU;AACfA,oBAAAA,MAAI,YAAa;AAAA,IAClB;AAAA;AAAA;AAAA;AAAA,IAKD,UAAU,OAAO,OAAO,QAAQ,WAAW,KAAM;AAC/CA,oBAAAA,MAAI,UAAU;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACR,CAAO;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,SAAS,QAAQ,MAAM;AACjC,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,UACA,SAAS,CAAC,QAAQ;AAChB,oBAAQ,IAAI,OAAO;AAAA,UACpB;AAAA,UACD,MAAM,MAAM;AACV,oBAAQ,KAAK;AAAA,UACd;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,OAAO,YAAY,MAAM;AACnCA,oBAAAA,MAAc,MAAA,SAAA,+BAAA,WAAW,KAAK;AAE9B,WAAK,WAAW;AAChB,WAAK,eAAe,MAAM,WAAW;AAGrC,YAAM,MAAM,OAAQ;AACpB,UAAI,OAAO,IAAI,UAAU;AACvB,YAAI,SAAS;AAAA,UACX,MAAM;AAAA,UACN,MAAM,KAAK,IAAI,KAAK;AAAA,UACpB,SAAS,KAAK;AAAA,UACd,OAAO,MAAM;AAAA,UACb,WAAW,KAAK,IAAK;AAAA,QAC/B,CAAS;AAAA,MACF;AAGD,UAAI,WAAW;AACb,aAAK,UAAU,KAAK,YAAY;AAAA,MACjC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,UAAU;AAER,UAAI,KAAK,eAAe,OAAO,KAAK,gBAAgB,YAAY;AAC9D,aAAK,YAAa;AAAA,MACnB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACb,CAAO;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe;AACb,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,UAAU,MAAM;AACrB,mBAAW,MAAM;AACf,eAAK,UAAW;AAAA,QACjB,GAAE,IAAI;AACP,eAAO;AAAA,MACR;AACD,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW,WAAW,SAAS,uBAAuB;AACpD,UAAI,CAAC;AAAW,eAAO;AAEvB,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,YAAM,OAAO,KAAK,YAAa;AAC/B,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,YAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,YAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAEzD,aAAO,OACJ,QAAQ,QAAQ,IAAI,EACpB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,GAAG,EACjB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,OAAO;AAAA,IACzB;AAAA,EACF;AACH;;"}