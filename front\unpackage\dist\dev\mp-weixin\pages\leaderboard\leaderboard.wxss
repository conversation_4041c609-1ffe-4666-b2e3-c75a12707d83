/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.page.data-v-049cb271 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.status-bar.data-v-049cb271 {
  background: transparent;
}
.content.data-v-049cb271 {
  padding-bottom: 200rpx;
}
.header.data-v-049cb271 {
  text-align: center;
  padding: 48rpx 40rpx;
}
.header .title.data-v-049cb271 {
  display: block;
  color: white;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.header .subtitle.data-v-049cb271 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 32rpx;
}
.tab-buttons.data-v-049cb271 {
  display: flex;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 8rpx;
  margin: 0 40rpx 40rpx;
}
.tab-buttons .tab-button.data-v-049cb271 {
  flex: 1;
  padding: 16rpx 32rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.2s;
}
.tab-buttons .tab-button.active.data-v-049cb271 {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}
.podium.data-v-049cb271 {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 40rpx;
  gap: 20rpx;
}
.podium .podium-item.data-v-049cb271 {
  text-align: center;
  color: white;
}
.podium .podium-item .podium-avatar.data-v-049cb271 {
  border-radius: 50%;
  margin: 0 auto 16rpx;
  border: 6rpx solid white;
  overflow: hidden;
}
.podium .podium-item .podium-avatar image.data-v-049cb271 {
  width: 100%;
  height: 100%;
}
.podium .podium-item .podium-base.data-v-049cb271 {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx 16rpx 0 0;
  padding: 32rpx 24rpx 16rpx;
  position: relative;
}
.podium .podium-item .podium-name.data-v-049cb271 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.podium .podium-item .podium-days.data-v-049cb271 {
  font-size: 24rpx;
  opacity: 0.8;
}
.podium .podium-item.first .podium-avatar.data-v-049cb271 {
  width: 160rpx;
  height: 160rpx;
  border-color: #ffd700;
}
.podium .podium-item.first .podium-base.data-v-049cb271 {
  height: 200rpx;
  background: rgba(255, 215, 0, 0.3);
}
.podium .podium-item.first .crown.data-v-049cb271 {
  position: absolute;
  top: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  color: #ffd700;
  font-size: 48rpx;
}
.podium .podium-item.second .podium-avatar.data-v-049cb271 {
  width: 120rpx;
  height: 120rpx;
  border-color: #c0c0c0;
}
.podium .podium-item.second .podium-base.data-v-049cb271 {
  height: 160rpx;
  background: rgba(192, 192, 192, 0.3);
}
.podium .podium-item.third .podium-avatar.data-v-049cb271 {
  width: 120rpx;
  height: 120rpx;
  border-color: #cd7f32;
}
.podium .podium-item.third .podium-base.data-v-049cb271 {
  height: 120rpx;
  background: rgba(205, 127, 50, 0.3);
}
.ranking-list.data-v-049cb271 {
  background: #f8fafc;
  border-radius: 48rpx 48rpx 0 0;
  margin-top: 40rpx;
  padding: 40rpx 0;
  min-height: 800rpx;
}
.ranking-list .ranking-header.data-v-049cb271 {
  padding: 0 48rpx 32rpx;
}
.ranking-list .ranking-header .ranking-title.data-v-049cb271 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.ranking-list .ranking-header .ranking-desc.data-v-049cb271 {
  color: #666;
  font-size: 28rpx;
}
.ranking-item.data-v-049cb271 {
  display: flex;
  align-items: center;
  padding: 32rpx 48rpx;
  background: white;
  margin: 16rpx 32rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.ranking-item.current-user.data-v-049cb271 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}
.ranking-item.current-user .rank-number.data-v-049cb271 {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}
.ranking-item.current-user .user-name.data-v-049cb271, .ranking-item.current-user .user-streak.data-v-049cb271, .ranking-item.current-user .score-number.data-v-049cb271, .ranking-item.current-user .score-unit.data-v-049cb271 {
  color: white;
}
.ranking-item .rank-number.data-v-049cb271 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #64748b;
  margin-right: 24rpx;
}
.ranking-item .user-avatar.data-v-049cb271 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  overflow: hidden;
}
.ranking-item .user-avatar image.data-v-049cb271 {
  width: 100%;
  height: 100%;
}
.ranking-item .user-info.data-v-049cb271 {
  flex: 1;
}
.ranking-item .user-info .user-name.data-v-049cb271 {
  display: block;
  font-weight: bold;
  color: #333;
  font-size: 32rpx;
  margin-bottom: 4rpx;
}
.ranking-item .user-info .user-streak.data-v-049cb271 {
  color: #666;
  font-size: 24rpx;
}
.ranking-item .user-score.data-v-049cb271 {
  text-align: right;
}
.ranking-item .user-score .score-number.data-v-049cb271 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}
.ranking-item .user-score .score-unit.data-v-049cb271 {
  color: #666;
  font-size: 24rpx;
}
.motivation-card.data-v-049cb271 {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #dbeafe, #e0e7ff);
  border-radius: 32rpx;
  padding: 32rpx;
  margin: 48rpx 32rpx 0;
}
.motivation-card .iconfont.data-v-049cb271 {
  color: #f59e0b;
  font-size: 48rpx;
  margin-right: 24rpx;
}
.motivation-card .motivation-content.data-v-049cb271 {
  flex: 1;
}
.motivation-card .motivation-content .motivation-title.data-v-049cb271 {
  display: block;
  font-weight: bold;
  color: #333;
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.motivation-card .motivation-content .motivation-desc.data-v-049cb271 {
  color: #666;
  font-size: 28rpx;
}

/* 字体图标 */
.icon-crown.data-v-049cb271::before {
  content: "\e027";
}
.icon-trophy.data-v-049cb271::before {
  content: "\e028";
}