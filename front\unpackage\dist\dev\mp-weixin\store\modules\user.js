"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const user = {
  namespaced: true,
  state: {
    // 用户信息
    userInfo: {
      id: null,
      username: "",
      nickname: "",
      avatar: "",
      phone: "",
      email: "",
      level: "beginner",
      totalStudyDays: 0,
      continuousDays: 0,
      maxContinuousDays: 0,
      totalScore: 0,
      vipStatus: 0,
      vipExpireTime: null
    },
    // 登录状态
    isLogin: false,
    token: "",
    // 学习统计
    studyStats: {
      todayProgress: 0,
      completedCourses: 0,
      totalCourses: 0,
      averageScore: 0,
      weeklyProgress: []
    }
  },
  mutations: {
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = __spreadValues(__spreadValues({}, state.userInfo), userInfo);
    },
    // 设置登录状态
    SET_LOGIN_STATUS(state, isLogin) {
      state.isLogin = isLogin;
    },
    // 设置token
    SET_TOKEN(state, token) {
      state.token = token;
      if (token) {
        common_vendor.index.setStorageSync("token", token);
      } else {
        common_vendor.index.removeStorageSync("token");
      }
    },
    // 设置学习统计
    SET_STUDY_STATS(state, stats) {
      state.studyStats = __spreadValues(__spreadValues({}, state.studyStats), stats);
    },
    // 更新连续打卡天数
    UPDATE_CONTINUOUS_DAYS(state, days) {
      state.userInfo.continuousDays = days;
      if (days > state.userInfo.maxContinuousDays) {
        state.userInfo.maxContinuousDays = days;
      }
    },
    // 更新总学习天数
    UPDATE_TOTAL_STUDY_DAYS(state, days) {
      state.userInfo.totalStudyDays = days;
    },
    // 更新总积分
    UPDATE_TOTAL_SCORE(state, score) {
      state.userInfo.totalScore = score;
    },
    // 清除用户数据
    CLEAR_USER_DATA(state) {
      state.userInfo = {
        id: null,
        username: "",
        nickname: "",
        avatar: "",
        phone: "",
        email: "",
        level: "beginner",
        totalStudyDays: 0,
        continuousDays: 0,
        maxContinuousDays: 0,
        totalScore: 0,
        vipStatus: 0,
        vipExpireTime: null
      };
      state.isLogin = false;
      state.token = "";
      state.studyStats = {
        todayProgress: 0,
        completedCourses: 0,
        totalCourses: 0,
        averageScore: 0,
        weeklyProgress: []
      };
    }
  },
  actions: {
    // 登录
    login(_0, _1) {
      return __async(this, arguments, function* ({ commit }, { username, password }) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/auth/login",
            method: "POST",
            data: { username, password }
          });
          if (response.data.code === 200) {
            const { token, userInfo } = response.data.data;
            commit("SET_TOKEN", token);
            commit("SET_USER_INFO", userInfo);
            commit("SET_LOGIN_STATUS", true);
            common_vendor.index.setStorageSync("userInfo", userInfo);
            return { success: true, data: userInfo };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/user.js:135", "登录失败:", error);
          return { success: false, message: "登录失败，请重试" };
        }
      });
    },
    // 退出登录
    logout(_0) {
      return __async(this, arguments, function* ({ commit }) {
        try {
          yield common_vendor.index.request({
            url: "/api/v1/auth/logout",
            method: "POST"
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/user.js:149", "退出登录API调用失败:", error);
        } finally {
          commit("CLEAR_USER_DATA");
          commit("SET_TOKEN", "");
          common_vendor.index.removeStorageSync("userInfo");
          common_vendor.index.removeStorageSync("token");
        }
      });
    },
    // 检查登录状态
    checkLoginStatus(_0) {
      return __async(this, arguments, function* ({ commit, dispatch }) {
        const token = common_vendor.index.getStorageSync("token");
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (token && userInfo) {
          try {
            const response = yield common_vendor.index.request({
              url: "/api/v1/user/profile",
              method: "GET",
              header: {
                "Authorization": `Bearer ${token}`
              }
            });
            if (response.data.code === 200) {
              commit("SET_TOKEN", token);
              commit("SET_USER_INFO", response.data.data);
              commit("SET_LOGIN_STATUS", true);
              yield dispatch("loadStudyStats");
            } else {
              yield dispatch("logout");
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at store/modules/user.js:187", "验证登录状态失败:", error);
            yield dispatch("logout");
          }
        }
      });
    },
    // 更新用户信息
    updateUserInfo(_0, _1) {
      return __async(this, arguments, function* ({ commit, state }, userInfo) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/user/profile",
            method: "PUT",
            data: userInfo,
            header: {
              "Authorization": `Bearer ${state.token}`
            }
          });
          if (response.data.code === 200) {
            commit("SET_USER_INFO", response.data.data);
            common_vendor.index.setStorageSync("userInfo", response.data.data);
            return { success: true };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/user.js:213", "更新用户信息失败:", error);
          return { success: false, message: "更新失败，请重试" };
        }
      });
    },
    // 加载学习统计
    loadStudyStats(_0) {
      return __async(this, arguments, function* ({ commit, state }) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/study/statistics",
            method: "GET",
            header: {
              "Authorization": `Bearer ${state.token}`
            }
          });
          if (response.data.code === 200) {
            commit("SET_STUDY_STATS", response.data.data);
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/user.js:233", "加载学习统计失败:", error);
        }
      });
    },
    // 上传头像
    uploadAvatar(_0, _1) {
      return __async(this, arguments, function* ({ commit, state }, filePath) {
        try {
          const response = yield common_vendor.index.uploadFile({
            url: "/api/v1/user/avatar",
            filePath,
            name: "avatar",
            header: {
              "Authorization": `Bearer ${state.token}`
            }
          });
          const data = JSON.parse(response.data);
          if (data.code === 200) {
            commit("SET_USER_INFO", { avatar: data.data.avatar });
            return { success: true, avatar: data.data.avatar };
          } else {
            return { success: false, message: data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/user.js:257", "上传头像失败:", error);
          return { success: false, message: "上传失败，请重试" };
        }
      });
    }
  },
  getters: {
    // 是否为VIP用户
    isVip: (state) => {
      if (state.userInfo.vipStatus === 0)
        return false;
      if (!state.userInfo.vipExpireTime)
        return false;
      return new Date(state.userInfo.vipExpireTime) > /* @__PURE__ */ new Date();
    },
    // 用户级别文本
    levelText: (state) => {
      const levelMap = {
        "beginner": "初级学员",
        "intermediate": "中级学员",
        "advanced": "高级学员",
        "professional": "专业学员"
      };
      return levelMap[state.userInfo.level] || "初级学员";
    },
    // 学习进度百分比
    studyProgressPercent: (state) => {
      if (state.studyStats.totalCourses === 0)
        return 0;
      return Math.round(state.studyStats.completedCourses / state.studyStats.totalCourses * 100);
    }
  }
};
exports.user = user;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/user.js.map
