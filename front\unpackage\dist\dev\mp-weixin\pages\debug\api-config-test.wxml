<view class="api-test-container data-v-e7967972"><view class="test-header data-v-e7967972"><text class="test-title data-v-e7967972">API配置测试</text></view><view class="test-section data-v-e7967972"><text class="section-title data-v-e7967972">配置信息</text><view class="config-item data-v-e7967972"><text class="config-label data-v-e7967972">API基础URL:</text><text class="config-value data-v-e7967972">{{a}}</text></view><view class="config-item data-v-e7967972"><text class="config-label data-v-e7967972">当前平台:</text><text class="config-value data-v-e7967972">{{b}}</text></view><view class="config-item data-v-e7967972"><text class="config-label data-v-e7967972">请求头配置:</text><view class="headers-display data-v-e7967972"><view wx:for="{{c}}" wx:for-item="value" wx:key="c" class="header-item data-v-e7967972"><text class="header-key data-v-e7967972">{{value.a}}:</text><text class="header-value data-v-e7967972">{{value.b}}</text></view></view></view></view><view class="test-section data-v-e7967972"><text class="section-title data-v-e7967972">接口测试</text><view class="test-buttons data-v-e7967972"><button class="test-btn data-v-e7967972" bindtap="{{d}}">测试基础连接</button><button class="test-btn data-v-e7967972" bindtap="{{e}}">测试验证码接口</button><button class="test-btn data-v-e7967972" bindtap="{{f}}">测试登录接口</button><button class="test-btn data-v-e7967972" bindtap="{{g}}">测试用户信息</button></view></view><view class="test-section data-v-e7967972"><text class="section-title data-v-e7967972">测试结果</text><scroll-view class="result-container data-v-e7967972" scroll-y><view wx:for="{{h}}" wx:for-item="result" wx:key="h" class="result-item data-v-e7967972"><view class="result-header data-v-e7967972"><text class="result-time data-v-e7967972">{{result.a}}</text><text class="{{['result-status', 'data-v-e7967972', result.c]}}">{{result.b}}</text></view><text class="result-title data-v-e7967972">{{result.d}}</text><text class="result-message data-v-e7967972">{{result.e}}</text><view wx:if="{{result.f}}" class="result-details data-v-e7967972"><text class="details-title data-v-e7967972">详细信息:</text><text class="details-content data-v-e7967972">{{result.g}}</text></view></view></scroll-view></view></view>