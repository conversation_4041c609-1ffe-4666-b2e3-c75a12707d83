package com.kuma.learning.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.kuma.learning.entity.KumaLesson;
import com.kuma.learning.service.IKumaLessonService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: kuma_lesson
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Api(tags="kuma_lesson")
@RestController
@RequestMapping("/learning/kumaLesson")
@Slf4j
public class KumaLessonController extends JeecgController<KumaLesson, IKumaLessonService> {
	@Autowired
	private IKumaLessonService kumaLessonService;
	
	/**
	 * 分页列表查询
	 *
	 * @param kumaLesson
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "kuma_lesson-分页列表查询")
	@ApiOperation(value="kuma_lesson-分页列表查询", notes="kuma_lesson-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<KumaLesson>> queryPageList(KumaLesson kumaLesson,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<KumaLesson> queryWrapper = QueryGenerator.initQueryWrapper(kumaLesson, req.getParameterMap());
		Page<KumaLesson> page = new Page<KumaLesson>(pageNo, pageSize);
		IPage<KumaLesson> pageList = kumaLessonService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param kumaLesson
	 * @return
	 */
	@AutoLog(value = "kuma_lesson-添加")
	@ApiOperation(value="kuma_lesson-添加", notes="kuma_lesson-添加")
	@RequiresPermissions("learning:kuma_lesson:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody KumaLesson kumaLesson) {
		kumaLessonService.save(kumaLesson);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param kumaLesson
	 * @return
	 */
	@AutoLog(value = "kuma_lesson-编辑")
	@ApiOperation(value="kuma_lesson-编辑", notes="kuma_lesson-编辑")
	@RequiresPermissions("learning:kuma_lesson:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody KumaLesson kumaLesson) {
		kumaLessonService.updateById(kumaLesson);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "kuma_lesson-通过id删除")
	@ApiOperation(value="kuma_lesson-通过id删除", notes="kuma_lesson-通过id删除")
	@RequiresPermissions("learning:kuma_lesson:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		kumaLessonService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "kuma_lesson-批量删除")
	@ApiOperation(value="kuma_lesson-批量删除", notes="kuma_lesson-批量删除")
	@RequiresPermissions("learning:kuma_lesson:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.kumaLessonService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "kuma_lesson-通过id查询")
	@ApiOperation(value="kuma_lesson-通过id查询", notes="kuma_lesson-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<KumaLesson> queryById(@RequestParam(name="id",required=true) String id) {
		KumaLesson kumaLesson = kumaLessonService.getById(id);
		if(kumaLesson==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(kumaLesson);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param kumaLesson
    */
    @RequiresPermissions("learning:kuma_lesson:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, KumaLesson kumaLesson) {
        return super.exportXls(request, kumaLesson, KumaLesson.class, "kuma_lesson");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("learning:kuma_lesson:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, KumaLesson.class);
    }

}
