<view class="json-parse-test-container data-v-f200c6d7"><view class="test-header data-v-f200c6d7"><text class="test-title data-v-f200c6d7">JSON解析错误修复测试</text></view><view class="test-section data-v-f200c6d7"><text class="section-title data-v-f200c6d7">当前存储状态</text><view class="storage-status data-v-f200c6d7"><view class="status-item data-v-f200c6d7"><text class="status-label data-v-f200c6d7">用户信息类型:</text><text class="status-value data-v-f200c6d7">{{a}}</text></view><view class="status-item data-v-f200c6d7"><text class="status-label data-v-f200c6d7">权限信息类型:</text><text class="status-value data-v-f200c6d7">{{b}}</text></view><view class="status-item data-v-f200c6d7"><text class="status-label data-v-f200c6d7">角色信息类型:</text><text class="status-value data-v-f200c6d7">{{c}}</text></view></view></view><view class="test-section data-v-f200c6d7"><text class="section-title data-v-f200c6d7">安全获取测试</text><view class="safe-get-test data-v-f200c6d7"><view class="test-item data-v-f200c6d7"><text class="test-label data-v-f200c6d7">getUserInfo():</text><text class="{{['test-result', 'data-v-f200c6d7', e]}}">{{d}}</text></view><view class="test-item data-v-f200c6d7"><text class="test-label data-v-f200c6d7">getPermissions():</text><text class="{{['test-result', 'data-v-f200c6d7', g]}}">{{f}}</text></view><view class="test-item data-v-f200c6d7"><text class="test-label data-v-f200c6d7">getRoles():</text><text class="{{['test-result', 'data-v-f200c6d7', i]}}">{{h}}</text></view></view></view><view class="test-section data-v-f200c6d7"><text class="section-title data-v-f200c6d7">异常数据模拟测试</text><view class="mock-test data-v-f200c6d7"><button class="test-btn warning data-v-f200c6d7" bindtap="{{j}}">模拟无效用户信息</button><button class="test-btn warning data-v-f200c6d7" bindtap="{{k}}">模拟无效权限信息</button><button class="test-btn warning data-v-f200c6d7" bindtap="{{l}}">模拟无效角色信息</button><button class="test-btn error data-v-f200c6d7" bindtap="{{m}}">模拟损坏数据</button></view></view><view class="test-section data-v-f200c6d7"><text class="section-title data-v-f200c6d7">正常数据恢复测试</text><view class="recovery-test data-v-f200c6d7"><button class="test-btn success data-v-f200c6d7" bindtap="{{n}}">恢复正常用户信息</button><button class="test-btn success data-v-f200c6d7" bindtap="{{o}}">恢复正常权限信息</button><button class="test-btn success data-v-f200c6d7" bindtap="{{p}}">恢复正常角色信息</button><button class="test-btn primary data-v-f200c6d7" bindtap="{{q}}">清除所有数据</button></view></view><view class="test-section data-v-f200c6d7"><text class="section-title data-v-f200c6d7">原始存储数据检查</text><scroll-view class="raw-data-container data-v-f200c6d7" scroll-y><view class="raw-data-item data-v-f200c6d7"><text class="data-label data-v-f200c6d7">userInfo原始数据:</text><text class="data-value data-v-f200c6d7">{{r}}</text></view><view class="raw-data-item data-v-f200c6d7"><text class="data-label data-v-f200c6d7">permissions原始数据:</text><text class="data-value data-v-f200c6d7">{{s}}</text></view><view class="raw-data-item data-v-f200c6d7"><text class="data-label data-v-f200c6d7">roles原始数据:</text><text class="data-value data-v-f200c6d7">{{t}}</text></view></scroll-view></view><view class="test-section data-v-f200c6d7"><text class="section-title data-v-f200c6d7">测试日志</text><scroll-view class="log-container data-v-f200c6d7" scroll-y><view wx:for="{{v}}" wx:for-item="log" wx:key="d" class="log-item data-v-f200c6d7"><text class="log-time data-v-f200c6d7">{{log.a}}</text><text class="{{['log-message', 'data-v-f200c6d7', log.c]}}">{{log.b}}</text></view></scroll-view></view></view>