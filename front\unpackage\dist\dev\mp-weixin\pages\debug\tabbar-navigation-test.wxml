<view class="tabbar-test-container data-v-8392dee8"><view class="test-header data-v-8392dee8"><text class="test-title data-v-8392dee8">TabBar跳转测试</text></view><view class="test-section data-v-8392dee8"><text class="section-title data-v-8392dee8">TabBar页面列表</text><view class="tabbar-list data-v-8392dee8"><view wx:for="{{a}}" wx:for-item="page" wx:key="f" class="tabbar-item data-v-8392dee8"><text class="page-name data-v-8392dee8">{{page.a}}</text><text class="page-path data-v-8392dee8">{{page.b}}</text><view class="page-actions data-v-8392dee8"><button class="action-btn success data-v-8392dee8" bindtap="{{page.c}}">switchTab</button><button class="action-btn error data-v-8392dee8" bindtap="{{page.d}}">navigateTo</button><button class="action-btn primary data-v-8392dee8" bindtap="{{page.e}}">智能跳转</button></view></view></view></view><view class="test-section data-v-8392dee8"><text class="section-title data-v-8392dee8">普通页面测试</text><view class="normal-list data-v-8392dee8"><view wx:for="{{b}}" wx:for-item="page" wx:key="e" class="normal-item data-v-8392dee8"><text class="page-name data-v-8392dee8">{{page.a}}</text><text class="page-path data-v-8392dee8">{{page.b}}</text><view class="page-actions data-v-8392dee8"><button class="action-btn primary data-v-8392dee8" bindtap="{{page.c}}">navigateTo</button><button class="action-btn primary data-v-8392dee8" bindtap="{{page.d}}">智能跳转</button></view></view></view></view><view class="test-section data-v-8392dee8"><text class="section-title data-v-8392dee8">测试日志</text><scroll-view class="log-container data-v-8392dee8" scroll-y><view wx:for="{{c}}" wx:for-item="log" wx:key="d" class="log-item data-v-8392dee8"><text class="log-time data-v-8392dee8">{{log.a}}</text><text class="{{['log-message', 'data-v-8392dee8', log.c]}}">{{log.b}}</text></view></scroll-view></view><view class="test-section data-v-8392dee8"><text class="section-title data-v-8392dee8">使用说明</text><view class="usage-info data-v-8392dee8"><text class="usage-title data-v-8392dee8">✅ 正确做法:</text><text class="usage-desc data-v-8392dee8">• TabBar页面使用 switchTab 跳转</text><text class="usage-desc data-v-8392dee8">• 普通页面使用 navigateTo 跳转</text><text class="usage-desc data-v-8392dee8">• 使用智能跳转工具自动识别</text><text class="usage-title error data-v-8392dee8">❌ 错误做法:</text><text class="usage-desc data-v-8392dee8">• TabBar页面使用 navigateTo 跳转（会报错）</text><text class="usage-desc data-v-8392dee8">• switchTab 跳转普通页面（不推荐）</text></view></view></view>