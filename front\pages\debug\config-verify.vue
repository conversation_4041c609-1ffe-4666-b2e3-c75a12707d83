<template>
	<view class="config-verify-container">
		<view class="verify-header">
			<text class="verify-title">配置验证</text>
		</view>
		
		<view class="verify-section">
			<text class="section-title">API配置状态</text>
			<view class="status-item">
				<text class="status-label">配置加载:</text>
				<text class="status-value" :class="configLoaded ? 'success' : 'error'">
					{{ configLoaded ? '成功' : '失败' }}
				</text>
			</view>
			<view class="status-item">
				<text class="status-label">基础URL:</text>
				<text class="status-value">{{ baseUrl || '未获取' }}</text>
			</view>
			<view class="status-item">
				<text class="status-label">错误信息:</text>
				<text class="status-value error">{{ errorMessage || '无' }}</text>
			</view>
		</view>
		
		<view class="verify-section">
			<text class="section-title">测试操作</text>
			<view class="test-buttons">
				<button class="test-btn" @tap="testConfig">重新测试配置</button>
				<button class="test-btn" @tap="testRequest">测试请求</button>
			</view>
		</view>
		
		<view class="verify-section">
			<text class="section-title">解决方案</text>
			<view class="solution-item">
				<text class="solution-title">1. 清理缓存</text>
				<text class="solution-desc">删除unpackage目录，重新编译</text>
			</view>
			<view class="solution-item">
				<text class="solution-title">2. 重启开发服务器</text>
				<text class="solution-desc">停止开发服务器，重新运行npm run dev:mp-weixin</text>
			</view>
			<view class="solution-item">
				<text class="solution-title">3. 检查导入</text>
				<text class="solution-desc">确保config/api.js正确导出所有函数</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			configLoaded: false,
			baseUrl: '',
			errorMessage: ''
		}
	},
	
	onLoad() {
		this.testConfig()
	},
	
	methods: {
		// 测试配置
		testConfig() {
			try {
				// 尝试导入API配置
				import('@/config/api.js').then(apiConfig => {
					console.log('✅ API配置导入成功:', apiConfig)
					
					if (apiConfig.getApiBaseUrl) {
						this.baseUrl = apiConfig.getApiBaseUrl()
						this.configLoaded = true
						this.errorMessage = ''
						
						console.log('✅ 基础URL获取成功:', this.baseUrl)
					} else {
						this.errorMessage = 'getApiBaseUrl函数不存在'
						console.error('❌ getApiBaseUrl函数不存在')
					}
				}).catch(error => {
					this.errorMessage = `API配置导入失败: ${error.message}`
					console.error('❌ API配置导入失败:', error)
				})
			} catch (error) {
				this.errorMessage = `配置测试异常: ${error.message}`
				console.error('❌ 配置测试异常:', error)
			}
		},
		
		// 测试请求
		async testRequest() {
			try {
				// 简单的网络请求测试
				uni.request({
					url: this.baseUrl + '/sys/common/403',
					method: 'GET',
					success: (res) => {
						console.log('✅ 请求测试成功:', res)
						uni.showToast({
							title: '请求测试成功',
							icon: 'success'
						})
					},
					fail: (err) => {
						console.log('⚠️ 请求测试失败（可能是正常的）:', err)
						uni.showToast({
							title: '请求已发送',
							icon: 'none'
						})
					}
				})
			} catch (error) {
				console.error('❌ 请求测试异常:', error)
				uni.showToast({
					title: '请求测试异常',
					icon: 'none'
				})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.config-verify-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

.verify-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.verify-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.verify-section {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 24rpx;
		border-bottom: 2rpx solid #eee;
		padding-bottom: 16rpx;
	}
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
	
	.status-label {
		font-size: 28rpx;
		color: #666;
	}
	
	.status-value {
		font-size: 28rpx;
		color: #333;
		word-break: break-all;
		
		&.success {
			color: #52c41a;
		}
		
		&.error {
			color: #ff4d4f;
		}
	}
}

.test-buttons {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
	
	.test-btn {
		height: 80rpx;
		background: #1890ff;
		color: white;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		
		&:active {
			opacity: 0.8;
		}
	}
}

.solution-item {
	margin-bottom: 24rpx;
	
	.solution-title {
		display: block;
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}
	
	.solution-desc {
		display: block;
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
	}
}
</style>
