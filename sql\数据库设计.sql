-- 口语便利店小程序数据库设计 (基于jeecg-boot框架)
-- 数据库名称: japanese_learning
-- 基于现有数据库表结构更新

-- 课程分类表
CREATE TABLE kuma_course_category (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    icon VARCHAR(255) COMMENT '分类图标URL',
    sort INT DEFAULT 0 COMMENT '排序',
    status INT DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_status (status),
    INDEX idx_sort (sort)
);

-- 课程表
CREATE TABLE kuma_course (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    title VARCHAR(100) NOT NULL COMMENT '课程标题',
    description TEXT COMMENT '课程描述',
    cover VARCHAR(255) COMMENT '课程封面图片URL',
    category_id VARCHAR(32) COMMENT '课程分类ID',
    level VARCHAR(20) NOT NULL COMMENT '课程难度级别：beginner/intermediate/advanced',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '课程价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    duration INT COMMENT '课程总时长(分钟)',
    student_count INT DEFAULT 0 COMMENT '学习人数',
    lesson_count INT DEFAULT 0 COMMENT '课时数量',
    status INT DEFAULT 1 COMMENT '状态：0-下架 1-上架',
    is_recommend INT DEFAULT 0 COMMENT '是否推荐：0-否 1-是',
    is_hot INT DEFAULT 0 COMMENT '是否热门：0-否 1-是',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_category_id (category_id),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_is_recommend (is_recommend),
    INDEX idx_is_hot (is_hot)
);

-- 课时表
CREATE TABLE kuma_lesson (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    course_id VARCHAR(32) NOT NULL COMMENT '所属课程ID',
    title VARCHAR(100) NOT NULL COMMENT '课时标题',
    description TEXT COMMENT '课时描述',
    video_url VARCHAR(500) COMMENT '视频URL',
    duration INT COMMENT '课时时长(分钟)',
    sort INT DEFAULT 0 COMMENT '排序',
    status INT DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_course_id (course_id),
    INDEX idx_status (status),
    INDEX idx_sort (sort)
);

-- 学习记录表
CREATE TABLE kuma_learning_record (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    course_id VARCHAR(32) NOT NULL COMMENT '课程ID',
    lesson_id VARCHAR(32) COMMENT '课时ID',
    progress INT DEFAULT 0 COMMENT '学习进度(0-100)',
    duration INT DEFAULT 0 COMMENT '学习时长(秒)',
    status INT DEFAULT 0 COMMENT '状态：0-学习中 1-已完成',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_lesson_id (lesson_id),
    INDEX idx_status (status)
);

-- 课程收藏表
CREATE TABLE kuma_course_favorite (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    course_id VARCHAR(32) NOT NULL COMMENT '课程ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    UNIQUE KEY uk_user_course (user_id, course_id)
);

-- 课程评价表
CREATE TABLE kuma_course_review (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    course_id VARCHAR(32) NOT NULL COMMENT '课程ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    rating INT NOT NULL COMMENT '评分(1-5)',
    content TEXT COMMENT '评价内容',
    status INT DEFAULT 1 COMMENT '状态：0-隐藏 1-显示',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_course_id (course_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating),
    INDEX idx_status (status)
);

-- 课程标签表
CREATE TABLE kuma_course_tag (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    name VARCHAR(50) NOT NULL COMMENT '标签名称',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    UNIQUE KEY uk_name (name)
);

-- 课程标签关联表
CREATE TABLE kuma_course_tag_relation (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    course_id VARCHAR(32) NOT NULL COMMENT '课程ID',
    tag_id VARCHAR(32) NOT NULL COMMENT '标签ID',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_course_id (course_id),
    INDEX idx_tag_id (tag_id),
    UNIQUE KEY uk_course_tag (course_id, tag_id)
);

-- 用户扩展信息表
CREATE TABLE kuma_user_profile (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    user_id VARCHAR(32) NOT NULL COMMENT '关联sys_user表ID',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    level VARCHAR(20) DEFAULT 'beginner' COMMENT '学习级别：beginner/intermediate/advanced',
    total_study_days INT DEFAULT 0 COMMENT '总学习天数',
    continuous_days INT DEFAULT 0 COMMENT '连续打卡天数',
    max_continuous_days INT DEFAULT 0 COMMENT '最长连续打卡天数',
    total_score INT DEFAULT 0 COMMENT '总积分',
    vip_status INT DEFAULT 0 COMMENT 'VIP状态：0-普通用户 1-VIP用户',
    vip_expire_time DATETIME COMMENT 'VIP过期时间',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_user_id (user_id),
    INDEX idx_level (level),
    INDEX idx_vip_status (vip_status)
);

-- 打卡记录表
CREATE TABLE kuma_checkin_record (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    checkin_date DATE NOT NULL COMMENT '打卡日期',
    courses_completed INT DEFAULT 0 COMMENT '完成课程数',
    study_duration INT DEFAULT 0 COMMENT '学习时长(秒)',
    is_makeup INT DEFAULT 0 COMMENT '是否补打卡：0-否 1-是',
    makeup_time DATETIME COMMENT '补打卡时间',
    continuous_days INT DEFAULT 0 COMMENT '当时连续天数',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_user_id (user_id),
    INDEX idx_checkin_date (checkin_date),
    INDEX idx_is_makeup (is_makeup),
    UNIQUE KEY uk_user_date (user_id, checkin_date)
);

-- 练习记录表
CREATE TABLE kuma_practice_record (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    course_id VARCHAR(32) NOT NULL COMMENT '课程ID',
    lesson_id VARCHAR(32) COMMENT '课时ID',
    sentence_text TEXT NOT NULL COMMENT '练习句子',
    audio_url VARCHAR(500) COMMENT '录音文件URL',
    audio_duration INT COMMENT '录音时长(秒)',
    total_score INT COMMENT 'AI评分总分',
    pronunciation_score INT COMMENT '发音得分',
    fluency_score INT COMMENT '流利度得分',
    tone_score INT COMMENT '语调得分',
    ai_feedback TEXT COMMENT 'AI反馈',
    evaluation_data JSON COMMENT 'AI评测详细数据',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag INT DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_lesson_id (lesson_id),
    INDEX idx_total_score (total_score),
    INDEX idx_create_time (create_time)
);

-- 打卡记录表
CREATE TABLE kuma_checkin_record (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    checkin_date DATE NOT NULL COMMENT '打卡日期',
    courses_completed INT DEFAULT 0 COMMENT '完成课程数',
    study_duration INT DEFAULT 0 COMMENT '学习时长(秒)',
    is_makeup TINYINT DEFAULT 0 COMMENT '是否补打卡',
    makeup_time DATETIME COMMENT '补打卡时间',
    continuous_days INT DEFAULT 0 COMMENT '当时连续天数',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag TINYINT DEFAULT 0 COMMENT '删除标志 0-正常 1-删除',
    INDEX idx_user_id (user_id),
    INDEX idx_checkin_date (checkin_date),
    INDEX idx_is_makeup (is_makeup),
    UNIQUE KEY uk_user_date (user_id, checkin_date)
);

-- 练习记录表
CREATE TABLE kuma_practice_record (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    course_id VARCHAR(32) NOT NULL COMMENT '课程ID',
    sentence_text TEXT NOT NULL COMMENT '练习句子',
    audio_url VARCHAR(500) COMMENT '录音文件URL',
    audio_duration INT COMMENT '录音时长(秒)',
    pronunciation_score INT COMMENT '发音分数',
    fluency_score INT COMMENT '流利度分数',
    tone_score INT COMMENT '语调分数',
    total_score INT COMMENT '总分',
    ai_feedback TEXT COMMENT 'AI反馈建议',
    evaluation_data JSON COMMENT 'AI评测详细数据',
    sys_sign VARCHAR(4) COMMENT '系统标识',
    create_by VARCHAR(32) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(32) COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag TINYINT DEFAULT 0 COMMENT '删除标志 0-正常 1-删除',
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_total_score (total_score),
    INDEX idx_create_time (create_time)
);

-- ========================================
-- 数据库表说明
-- ========================================

/*
基于现有数据库表结构的设计说明：

核心业务表：
1. kuma_course_category - 课程分类表
2. kuma_course - 课程信息表
3. kuma_lesson - 课时表
4. kuma_learning_record - 学习记录表
5. kuma_course_favorite - 课程收藏表
6. kuma_course_review - 课程评价表
7. kuma_course_tag - 课程标签表
8. kuma_course_tag_relation - 课程标签关联表
9. kuma_user_profile - 用户扩展信息表
10. kuma_checkin_record - 打卡记录表
11. kuma_practice_record - 练习记录表

表结构规范：
- 表名前缀: kuma_
- 主键类型: VARCHAR(32)
- 通用字段: sys_sign VARCHAR(4)
- 标准字段: create_by, create_time, update_by, update_time, del_flag
- 软删除: 使用del_flag字段，0-未删除，1-已删除
- 索引策略: 主要查询字段建立索引

业务特点：
- 课程体系: 分类 -> 课程 -> 课时的三级结构
- 学习跟踪: 详细的学习记录和进度管理
- 用户互动: 收藏、评价、标签等功能
- 打卡系统: 支持每日打卡和补打卡
- 练习系统: AI语音评测和练习记录
- 用户成长: 积分、等级、VIP等用户体系
*/

-- ========================================
-- 初始化数据
-- ========================================

-- 插入系统配置数据
INSERT INTO kuma_system_config (id, config_key, config_value, config_type, description, is_system, sys_sign, create_by, create_time) VALUES
('1', 'makeup_chances_per_month', '5', 'number', '每月补打卡次数', 1, 'KUMA', 'system', NOW()),
('2', 'vip_price_yearly', '99.00', 'number', 'VIP年费价格', 1, 'KUMA', 'system', NOW()),
('3', 'ai_api_provider', 'xunfei', 'string', 'AI语音评测服务商', 1, 'KUMA', 'system', NOW()),
('4', 'ai_api_appid', '', 'string', 'AI接口APPID', 1, 'KUMA', 'system', NOW()),
('5', 'ai_api_secret', '', 'string', 'AI接口密钥', 1, 'KUMA', 'system', NOW()),
('6', 'wechat_pay_mchid', '', 'string', '微信支付商户号', 1, 'KUMA', 'system', NOW()),
('7', 'wechat_pay_key', '', 'string', '微信支付密钥', 1, 'KUMA', 'system', NOW()),
('8', 'course_unlock_rule', 'sequential', 'string', '课程解锁规则：sequential(顺序),free(自由)', 1, 'KUMA', 'system', NOW()),
('9', 'checkin_reward_points', '10', 'number', '每日打卡奖励积分', 1, 'KUMA', 'system', NOW()),
('10', 'practice_reward_points', '5', 'number', '练习完成奖励积分', 1, 'KUMA', 'system', NOW());

-- 插入示例课程数据
INSERT INTO kuma_course (id, title, description, level, lesson_number, duration, transcript, translation, vocabulary, study_tips, is_free, price, sort_order, status, sys_sign, create_by, create_time) VALUES
('course_001', '第1课：自己紹介', '学习基本的自我介绍表达', 'beginner', 1, 300,
'はじめまして。私は田中です。よろしくお願いします。',
'初次见面。我是田中。请多关照。',
'[{"word":"はじめまして","pronunciation":"hajimemashite","meaning":"初次见面"},{"word":"私","pronunciation":"watashi","meaning":"我"},{"word":"よろしく","pronunciation":"yoroshiku","meaning":"请多关照"}]',
'[{"title":"敬语的使用","description":"注意使用适当的敬语表达"},{"title":"发音要点","description":"注意长音和促音的发音"}]',
1, 0.00, 1, 1, 'KUMA', 'system', NOW()),

('course_002', '第2课：挨拶', '学习日常问候用语', 'beginner', 2, 240,
'おはようございます。今日はいい天気ですね。',
'早上好。今天天气真好呢。',
'[{"word":"おはよう","pronunciation":"ohayou","meaning":"早上好"},{"word":"今日","pronunciation":"kyou","meaning":"今天"},{"word":"天気","pronunciation":"tenki","meaning":"天气"}]',
'[{"title":"时间表达","description":"学习不同时间的问候语"},{"title":"天气话题","description":"掌握天气相关的表达"}]',
1, 0.00, 2, 1, 'KUMA', 'system', NOW()),

('course_003', '第3课：買い物', '学习购物场景对话', 'beginner', 3, 360,
'すみません。これはいくらですか。',
'不好意思。这个多少钱？',
'[{"word":"すみません","pronunciation":"sumimasen","meaning":"不好意思"},{"word":"これ","pronunciation":"kore","meaning":"这个"},{"word":"いくら","pronunciation":"ikura","meaning":"多少钱"}]',
'[{"title":"购物用语","description":"掌握基本的购物表达"},{"title":"数字表达","description":"学习价格和数量的说法"}]',
0, 9.90, 3, 1, 'KUMA', 'system', NOW());

-- ========================================
-- 数据库表说明
-- ========================================

/*
表结构说明：
1. 所有表都使用VARCHAR(32)作为主键ID，符合jeecg-boot规范
2. 统一添加了jeecg-boot标准字段：
   - create_by: 创建人
   - create_time: 创建时间
   - update_by: 更新人
   - update_time: 更新时间
   - del_flag: 删除标志
   - sys_sign: 系统标识(4位)
3. 所有表名都使用kuma_前缀
4. 外键关系通过应用层维护，不使用数据库外键约束
5. 枚举类型改为VARCHAR类型，便于扩展

核心业务表：
- kuma_user_profile: 用户扩展信息
- kuma_course: 课程信息
- kuma_user_study_record: 学习记录
- kuma_checkin_record: 打卡记录
- kuma_practice_record: 练习记录
- kuma_order: 订单信息
- kuma_purchase_record: 购买记录
- kuma_leaderboard: 排行榜
- kuma_user_favorite: 用户收藏
- kuma_system_config: 系统配置
- kuma_feedback: 意见反馈

索引策略：
- 主要查询字段都建立了索引
- 联合查询建立了联合索引
- 唯一性约束通过唯一索引实现
*/
