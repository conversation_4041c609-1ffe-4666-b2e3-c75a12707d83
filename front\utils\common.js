/**
 * 通用工具函数
 */

/**
 * 安全获取App实例
 */
export function getAppInstance() {
  try {
    return getApp()
  } catch (error) {
    console.warn('获取App实例失败:', error)
    return null
  }
}

/**
 * 获取全局数据
 */
export function getGlobalData(key) {
  const app = getAppInstance()
  if (!app || !app.globalData) {
    return key ? undefined : {}
  }
  return key ? app.globalData[key] : app.globalData
}

/**
 * 设置全局数据
 */
export function setGlobalData(key, value) {
  const app = getAppInstance()
  if (!app) return false
  
  if (!app.globalData) {
    app.globalData = {}
  }
  
  if (typeof key === 'object') {
    Object.assign(app.globalData, key)
  } else {
    app.globalData[key] = value
  }
  return true
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  const globalUserInfo = getGlobalData('userInfo')
  if (globalUserInfo) return globalUserInfo
  
  try {
    return uni.getStorageSync('userInfo')
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

/**
 * 获取Token
 */
export function getToken() {
  const globalToken = getGlobalData('token')
  if (globalToken) return globalToken
  
  try {
    return uni.getStorageSync('token')
  } catch (error) {
    console.error('获取Token失败:', error)
    return null
  }
}

/**
 * 检查是否已登录
 */
export function isLoggedIn() {
  const token = getToken()
  const userInfo = getUserInfo()
  return !!(token && userInfo)
}

/**
 * 格式化时间
 */
export function formatTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  const year = d.getFullYear()
  const month = (d.getMonth() + 1).toString().padStart(2, '0')
  const day = d.getDate().toString().padStart(2, '0')
  const hour = d.getHours().toString().padStart(2, '0')
  const minute = d.getMinutes().toString().padStart(2, '0')
  const second = d.getSeconds().toString().padStart(2, '0')
  
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 防抖函数
 */
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 验证手机号
 */
export function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 */
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 获取URL参数
 */
export function getUrlParams(url) {
  const params = {}
  const urlObj = new URL(url)
  for (const [key, value] of urlObj.searchParams) {
    params[key] = value
  }
  return params
}

/**
 * 显示加载提示
 */
export function showLoading(title = '加载中...') {
  uni.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
export function hideLoading() {
  uni.hideLoading()
}

/**
 * 显示成功提示
 */
export function showSuccess(title, duration = 2000) {
  uni.showToast({
    title,
    icon: 'success',
    duration
  })
}

/**
 * 显示错误提示
 */
export function showError(title, duration = 2000) {
  uni.showToast({
    title,
    icon: 'none',
    duration
  })
}

/**
 * 显示确认对话框
 */
export function showConfirm(content, title = '提示') {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}
