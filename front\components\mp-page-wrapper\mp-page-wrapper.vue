<template>
	<view class="mp-page-wrapper">
		<!-- 自定义导航栏 -->
		<view v-if="showNavBar" class="mp-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="mp-navbar-content">
				<view class="mp-navbar-left" @tap="handleBack" v-if="showBack">
					<text class="mp-navbar-back">‹</text>
				</view>
				<view class="mp-navbar-title">{{ title }}</view>
				<view class="mp-navbar-right">
					<slot name="navbar-right"></slot>
				</view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="mp-page-content" :style="{ paddingTop: showNavBar ? navBarHeight + 'px' : '0' }">
			<!-- 加载状态 -->
			<view v-if="loading" class="mp-loading">
				<view class="mp-loading-spinner"></view>
				<text class="mp-loading-text">{{ loadingText }}</text>
			</view>
			
			<!-- 错误状态 -->
			<view v-else-if="hasError" class="mp-error">
				<view class="mp-error-icon">⚠️</view>
				<text class="mp-error-text">{{ errorMessage }}</text>
				<button class="mp-error-retry" @tap="handleRetry">重试</button>
			</view>
			
			<!-- 正常内容 -->
			<view v-else class="mp-content">
				<slot></slot>
			</view>
		</view>
		
		<!-- 底部安全区域 -->
		<view class="mp-safe-area-bottom"></view>
	</view>
</template>

<script>
export default {
	name: 'MpPageWrapper',
	props: {
		// 页面标题
		title: {
			type: String,
			default: '口语便利店'
		},
		// 是否显示导航栏
		showNavBar: {
			type: Boolean,
			default: true
		},
		// 是否显示返回按钮
		showBack: {
			type: Boolean,
			default: true
		},
		// 加载状态
		loading: {
			type: Boolean,
			default: false
		},
		// 加载文字
		loadingText: {
			type: String,
			default: '加载中...'
		},
		// 错误状态
		hasError: {
			type: Boolean,
			default: false
		},
		// 错误信息
		errorMessage: {
			type: String,
			default: '加载失败'
		}
	},
	
	data() {
		return {
			statusBarHeight: 0,
			navBarHeight: 0
		}
	},
	
	mounted() {
		this.initSystemInfo()
	},
	
	methods: {
		// 初始化系统信息
		initSystemInfo() {
			try {
				const app = getApp()
				if (app && app.globalData) {
					this.statusBarHeight = app.globalData.statusBarHeight || 0
					this.navBarHeight = app.globalData.navBarHeight || 44
				} else {
					const systemInfo = uni.getSystemInfoSync()
					this.statusBarHeight = systemInfo.statusBarHeight || 0
					this.navBarHeight = (systemInfo.statusBarHeight || 0) + 44
				}
			} catch (error) {
				console.error('获取系统信息失败:', error)
				this.statusBarHeight = 0
				this.navBarHeight = 44
			}
		},
		
		// 处理返回
		handleBack() {
			try {
				const pages = getCurrentPages()
				if (pages && pages.length > 1) {
					uni.navigateBack()
				} else {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}
			} catch (error) {
				console.warn('处理返回失败:', error)
				// 兜底处理
				uni.switchTab({
					url: '/pages/index/index'
				})
			}
		},
		
		// 处理重试
		handleRetry() {
			this.$emit('retry')
		}
	}
}
</script>

<style lang="scss" scoped>
.mp-page-wrapper {
	min-height: 100vh;
	background-color: #f8f9fa;
}

.mp-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	
	.mp-navbar-content {
		height: 44px;
		display: flex;
		align-items: center;
		padding: 0 16px;
		position: relative;
	}
	
	.mp-navbar-left {
		position: absolute;
		left: 16px;
		top: 50%;
		transform: translateY(-50%);
		
		.mp-navbar-back {
			font-size: 24px;
			color: white;
			font-weight: bold;
		}
	}
	
	.mp-navbar-title {
		flex: 1;
		text-align: center;
		font-size: 18px;
		font-weight: 600;
		color: white;
	}
	
	.mp-navbar-right {
		position: absolute;
		right: 16px;
		top: 50%;
		transform: translateY(-50%);
	}
}

.mp-page-content {
	min-height: 100vh;
	position: relative;
}

.mp-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80px 20px;
	
	.mp-loading-spinner {
		width: 40px;
		height: 40px;
		border: 3px solid #f3f3f3;
		border-top: 3px solid #667eea;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 16px;
	}
	
	.mp-loading-text {
		color: #666;
		font-size: 14px;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.mp-error {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80px 20px;
	
	.mp-error-icon {
		font-size: 48px;
		margin-bottom: 16px;
	}
	
	.mp-error-text {
		color: #666;
		font-size: 16px;
		margin-bottom: 24px;
		text-align: center;
	}
	
	.mp-error-retry {
		background: #667eea;
		color: white;
		border: none;
		border-radius: 20px;
		padding: 8px 24px;
		font-size: 14px;
	}
}

.mp-content {
	min-height: calc(100vh - 44px);
}

.mp-safe-area-bottom {
	height: env(safe-area-inset-bottom);
	background-color: #f8f9fa;
}

/* 小程序特定样式 */
/* #ifdef MP-WEIXIN || MP-DINGTALK */
.mp-page-wrapper {
	/* 小程序环境下的特殊处理 */
	overflow-x: hidden;
}

.mp-navbar {
	/* 小程序导航栏优化 */
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}
/* #endif */

/* H5特定样式 */
/* #ifdef H5 */
.mp-page-wrapper {
	/* H5环境下的特殊处理 */
	max-width: 414px;
	margin: 0 auto;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}
/* #endif */
</style>
