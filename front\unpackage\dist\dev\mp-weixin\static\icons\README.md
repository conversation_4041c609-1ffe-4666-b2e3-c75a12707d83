# 图标资源说明

本目录包含小程序使用的所有图标资源文件。

## 📁 目录结构

```
static/
├── icons/          # SVG源文件
│   ├── home.svg
│   ├── home-active.svg
│   ├── course.svg
│   ├── course-active.svg
│   ├── checkin.svg
│   ├── checkin-active.svg
│   ├── leaderboard.svg
│   ├── leaderboard-active.svg
│   ├── profile.svg
│   └── profile-active.svg
└── tabbar/         # PNG图标文件（用于tabbar）
    ├── home.png
    ├── home-active.png
    ├── course.png
    ├── course-active.png
    ├── checkin.png
    ├── checkin-active.png
    ├── leaderboard.png
    ├── leaderboard-active.png
    ├── profile.png
    └── profile-active.png
```

## 🎨 设计规范

### 颜色规范
- **未选中状态**: `#999999` (浅灰色)
- **选中状态**: `#667eea` (主题蓝色)
- **背景**: 透明

### 尺寸规范
- **SVG**: 24x24 viewBox
- **PNG**: 48x48px (推荐)
- **描边**: 2px 粗细
- **圆角**: 适当的圆角处理

### 设计风格
- 线性图标风格
- 简洁明了
- 统一的视觉语言
- 良好的可识别性

## 🔧 图标生成

### 方法一：使用在线生成器
1. 在浏览器中打开 `scripts/generate-icons.html`
2. 点击对应的下载按钮
3. 将下载的PNG文件放置到 `static/tabbar/` 目录

### 方法二：使用转换脚本
```bash
# 安装依赖
npm install sharp

# 运行转换脚本
npm run icons:convert
```

### 方法三：手动转换
使用在线工具或设计软件将SVG转换为PNG：
- [CloudConvert](https://cloudconvert.com/svg-to-png)
- Adobe Illustrator
- Sketch
- Figma

## 📱 使用方法

### 在pages.json中配置tabbar
```json
{
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#667eea",
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/tabbar/home.png",
        "selectedIconPath": "static/tabbar/home-active.png",
        "text": "首页"
      }
    ]
  }
}
```

### 在页面中使用图标
```vue
<template>
  <!-- 使用image组件 -->
  <image src="/static/icons/home.svg" class="icon" />
  
  <!-- 使用uni-icons组件 -->
  <uni-icons type="home" size="24" color="#667eea" />
</template>

<style>
.icon {
  width: 24px;
  height: 24px;
}
</style>
```

## 🎯 图标说明

### 首页 (Home)
- **图标**: 房屋
- **含义**: 主页、起始点、归属感
- **使用场景**: 底部导航首页tab

### 课程 (Course)
- **图标**: 书本
- **含义**: 学习内容、教材、知识
- **使用场景**: 底部导航课程tab、课程列表

### 打卡 (Checkin)
- **图标**: 日历+打勾
- **含义**: 每日任务、坚持学习、成就感
- **使用场景**: 底部导航打卡tab、打卡按钮

### 排行榜 (Leaderboard)
- **图标**: 柱状图+皇冠
- **含义**: 竞争、排名、激励
- **使用场景**: 底部导航排行tab、成绩展示

### 个人中心 (Profile)
- **图标**: 用户头像
- **含义**: 个人信息、设置、账户
- **使用场景**: 底部导航个人tab、用户相关页面

## 🔄 更新维护

### 修改图标
1. 编辑对应的SVG文件
2. 重新生成PNG文件
3. 测试在不同设备上的显示效果
4. 更新版本记录

### 添加新图标
1. 按照设计规范创建SVG文件
2. 生成对应的PNG文件
3. 更新文档说明
4. 在代码中引用新图标

### 版本记录
- v1.0.0 (2024-03-22): 初始版本，包含5个tabbar图标
- 设计师: Kuma Team
- 技术实现: uni-app + SVG + Canvas

## ⚠️ 注意事项

1. **文件命名**: 使用小写字母和连字符，保持一致性
2. **文件大小**: PNG文件尽量控制在5KB以内
3. **兼容性**: 确保在不同平台（微信小程序、H5、APP）正常显示
4. **版权**: 确保图标设计符合版权要求
5. **可访问性**: 考虑视觉障碍用户的使用需求

## 🛠️ 开发工具推荐

### 设计工具
- **Figma**: 在线协作设计
- **Sketch**: Mac平台设计工具
- **Adobe Illustrator**: 专业矢量图设计

### 转换工具
- **Sharp**: Node.js图像处理库
- **ImageMagick**: 命令行图像处理
- **SVGO**: SVG优化工具

### 在线工具
- **IcoMoon**: 图标字体生成
- **Feather Icons**: 开源图标库
- **Heroicons**: 现代图标库
