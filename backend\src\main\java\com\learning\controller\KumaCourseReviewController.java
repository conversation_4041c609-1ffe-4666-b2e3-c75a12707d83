package com.learning.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import com.learning.entity.KumaCourseReview;
import com.learning.service.IKumaCourseReviewService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: kuma_course_review
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Api(tags="kuma_course_review")
@RestController
@RequestMapping("/learning/kumaCourseReview")
@Slf4j
public class KumaCourseReviewController extends JeecgController<KumaCourseReview, IKumaCourseReviewService> {
	@Autowired
	private IKumaCourseReviewService kumaCourseReviewService;
	
	/**
	 * 分页列表查询
	 *
	 * @param kumaCourseReview
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "kuma_course_review-分页列表查询")
	@ApiOperation(value="kuma_course_review-分页列表查询", notes="kuma_course_review-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<KumaCourseReview>> queryPageList(KumaCourseReview kumaCourseReview,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<KumaCourseReview> queryWrapper = QueryGenerator.initQueryWrapper(kumaCourseReview, req.getParameterMap());
		Page<KumaCourseReview> page = new Page<KumaCourseReview>(pageNo, pageSize);
		IPage<KumaCourseReview> pageList = kumaCourseReviewService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param kumaCourseReview
	 * @return
	 */
	@AutoLog(value = "kuma_course_review-添加")
	@ApiOperation(value="kuma_course_review-添加", notes="kuma_course_review-添加")
	@RequiresPermissions("learning:kuma_course_review:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody KumaCourseReview kumaCourseReview) {
		kumaCourseReviewService.save(kumaCourseReview);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param kumaCourseReview
	 * @return
	 */
	@AutoLog(value = "kuma_course_review-编辑")
	@ApiOperation(value="kuma_course_review-编辑", notes="kuma_course_review-编辑")
	@RequiresPermissions("learning:kuma_course_review:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody KumaCourseReview kumaCourseReview) {
		kumaCourseReviewService.updateById(kumaCourseReview);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "kuma_course_review-通过id删除")
	@ApiOperation(value="kuma_course_review-通过id删除", notes="kuma_course_review-通过id删除")
	@RequiresPermissions("learning:kuma_course_review:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		kumaCourseReviewService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "kuma_course_review-批量删除")
	@ApiOperation(value="kuma_course_review-批量删除", notes="kuma_course_review-批量删除")
	@RequiresPermissions("learning:kuma_course_review:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.kumaCourseReviewService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "kuma_course_review-通过id查询")
	@ApiOperation(value="kuma_course_review-通过id查询", notes="kuma_course_review-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<KumaCourseReview> queryById(@RequestParam(name="id",required=true) String id) {
		KumaCourseReview kumaCourseReview = kumaCourseReviewService.getById(id);
		if(kumaCourseReview==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(kumaCourseReview);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param kumaCourseReview
    */
    @RequiresPermissions("learning:kuma_course_review:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, KumaCourseReview kumaCourseReview) {
        return super.exportXls(request, kumaCourseReview, KumaCourseReview.class, "kuma_course_review");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("learning:kuma_course_review:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, KumaCourseReview.class);
    }

}
