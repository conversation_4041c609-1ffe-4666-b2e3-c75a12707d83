<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>口语便利店 - 课程详情</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { 
            overflow: hidden; 
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar { display: none; }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .header {
            height: 60px;
            background: transparent;
            display: flex;
            align-items: center;
            padding: 0 20px;
            color: white;
        }
        .content {
            height: calc(100% - 44px - 60px);
            overflow-y: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
            background: #f8fafc;
            border-radius: 24px 24px 0 0;
        }
        .content::-webkit-scrollbar { display: none; }
        .audio-player {
            background: white;
            border-radius: 20px;
            margin: 20px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .play-button {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .play-button:hover {
            transform: scale(1.05);
        }
        .waveform {
            height: 40px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 30%, #e5e7eb 30%, #e5e7eb 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }
        .content-section {
            background: white;
            margin: 16px 20px;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .tab-buttons {
            display: flex;
            background: #f1f5f9;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 16px;
        }
        .tab-button {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s;
        }
        .tab-button.active {
            background: white;
            color: #4facfe;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .vocabulary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        .vocabulary-item:last-child {
            border-bottom: none;
        }
        .start-practice-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            padding: 16px;
            border-radius: 16px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 8px 32px rgba(79, 172, 254, 0.3);
        }
    </style>
</head>
<body>
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 头部 -->
            <div class="header">
                <i class="fas fa-arrow-left text-xl mr-4"></i>
                <div>
                    <h1 class="text-xl font-bold">第4课：レストラン</h1>
                    <p class="text-sm opacity-80">餐厅点餐对话</p>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 音频播放器 -->
                <div class="audio-player">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h3 class="font-bold text-gray-800 mb-1">课程音频</h3>
                            <p class="text-sm text-gray-600">时长: 7分钟</p>
                        </div>
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    
                    <div class="waveform mb-4"></div>
                    
                    <div class="flex justify-between text-sm text-gray-600">
                        <span>2:15</span>
                        <div class="flex items-center space-x-4">
                            <i class="fas fa-backward"></i>
                            <i class="fas fa-forward"></i>
                            <i class="fas fa-redo text-blue-500"></i>
                        </div>
                        <span>7:00</span>
                    </div>
                </div>

                <!-- 内容切换 -->
                <div class="content-section">
                    <div class="tab-buttons">
                        <div class="tab-button active">日文原文</div>
                        <div class="tab-button">中文翻译</div>
                        <div class="tab-button">重点词汇</div>
                    </div>

                    <!-- 日文原文内容 -->
                    <div class="text-content">
                        <div class="space-y-4">
                            <div class="p-4 bg-blue-50 rounded-lg">
                                <p class="text-gray-800 leading-relaxed">
                                    <span class="font-medium">A:</span> いらっしゃいませ。何名様ですか？<br>
                                    <span class="font-medium">B:</span> 二人です。<br>
                                    <span class="font-medium">A:</span> こちらのお席へどうぞ。メニューです。<br>
                                    <span class="font-medium">B:</span> ありがとうございます。
                                </p>
                            </div>
                            
                            <div class="p-4 bg-green-50 rounded-lg">
                                <p class="text-gray-800 leading-relaxed">
                                    <span class="font-medium">A:</span> ご注文はお決まりですか？<br>
                                    <span class="font-medium">B:</span> はい、ラーメンとギョーザをお願いします。<br>
                                    <span class="font-medium">A:</span> かしこまりました。お飲み物はいかがですか？<br>
                                    <span class="font-medium">B:</span> ビールを一つお願いします。
                                </p>
                            </div>
                            
                            <div class="p-4 bg-yellow-50 rounded-lg">
                                <p class="text-gray-800 leading-relaxed">
                                    <span class="font-medium">A:</span> お会計をお願いします。<br>
                                    <span class="font-medium">B:</span> 全部で1500円になります。<br>
                                    <span class="font-medium">A:</span> カードで払えますか？<br>
                                    <span class="font-medium">B:</span> はい、大丈夫です。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 学习提示 -->
                <div class="content-section">
                    <h3 class="font-bold text-gray-800 mb-3">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                        学习要点
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-xs font-bold text-blue-600">1</span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800">敬语的使用</p>
                                <p class="text-sm text-gray-600">注意服务员使用的敬语表达</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-xs font-bold text-green-600">2</span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800">点餐用语</p>
                                <p class="text-sm text-gray-600">掌握常用的点餐表达方式</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-xs font-bold text-purple-600">3</span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800">支付方式</p>
                                <p class="text-sm text-gray-600">了解不同支付方式的表达</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部留白，避免被按钮遮挡 -->
                <div class="h-20"></div>
            </div>

            <!-- 开始练习按钮 -->
            <div class="start-practice-btn">
                <i class="fas fa-microphone mr-2"></i>
                开始跟读练习
            </div>
        </div>
    </div>
</body>
</html>
