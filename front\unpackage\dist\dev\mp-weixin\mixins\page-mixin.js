"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const pageMixin = {
  data() {
    return {
      // 页面状态
      loading: false,
      refreshing: false,
      // 系统信息
      statusBarHeight: 0,
      navBarHeight: 0,
      windowHeight: 0,
      windowWidth: 0,
      // 用户信息
      userInfo: null,
      isLoggedIn: false,
      // 网络状态
      isConnected: true,
      // 错误处理
      hasError: false,
      errorMessage: ""
    };
  },
  onLoad(options) {
    const route = this.getPageRoute();
    common_vendor.index.__f__("log", "at mixins/page-mixin.js:34", "📄 页面加载:", route, options);
    this.initPage(options);
  },
  onShow() {
    const route = this.getPageRoute();
    common_vendor.index.__f__("log", "at mixins/page-mixin.js:42", "👁️ 页面显示:", route);
    this.checkLoginStatus();
    this.checkNetworkStatus();
  },
  onHide() {
    const route = this.getPageRoute();
    common_vendor.index.__f__("log", "at mixins/page-mixin.js:53", "🙈 页面隐藏:", route);
  },
  onUnload() {
    const route = this.getPageRoute();
    common_vendor.index.__f__("log", "at mixins/page-mixin.js:58", "🗑️ 页面卸载:", route);
    this.cleanup();
  },
  // 下拉刷新
  onPullDownRefresh() {
    common_vendor.index.__f__("log", "at mixins/page-mixin.js:66", "🔄 下拉刷新");
    this.handleRefresh();
  },
  // 上拉加载更多
  onReachBottom() {
    common_vendor.index.__f__("log", "at mixins/page-mixin.js:72", "📄 上拉加载更多");
    this.handleLoadMore();
  },
  // 页面滚动
  onPageScroll(e) {
  },
  // 分享
  onShareAppMessage() {
    return {
      title: "口语便利店 - 日语学习打卡",
      path: "/pages/index/index",
      imageUrl: "/static/images/share-cover.jpg"
    };
  },
  // 分享到朋友圈（微信小程序）
  onShareTimeline() {
    return {
      title: "口语便利店 - 日语学习打卡",
      query: "from=timeline",
      imageUrl: "/static/images/share-cover.jpg"
    };
  },
  methods: {
    /**
     * 安全获取页面路由
     */
    getPageRoute() {
      try {
        if (this.$mp && this.$mp.page && this.$mp.page.route) {
          return this.$mp.page.route;
        }
        const pages = getCurrentPages();
        if (pages && pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          return currentPage.route || "unknown";
        }
        return "unknown";
      } catch (error) {
        common_vendor.index.__f__("warn", "at mixins/page-mixin.js:120", "获取页面路由失败:", error);
        return "unknown";
      }
    },
    /**
     * 初始化页面
     */
    initPage(options = {}) {
      try {
        this.getSystemInfo();
        this.getUserInfo();
        if (this.pageInit && typeof this.pageInit === "function") {
          this.pageInit(options);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at mixins/page-mixin.js:142", "❌ 页面初始化失败:", error);
        this.handleError(error);
      }
    },
    /**
     * 获取系统信息
     */
    getSystemInfo() {
      try {
        const app = getApp();
        if (app && app.globalData) {
          this.statusBarHeight = app.globalData.statusBarHeight || 0;
          this.navBarHeight = app.globalData.navBarHeight || 44;
          this.windowHeight = app.globalData.windowHeight || 0;
          this.windowWidth = app.globalData.windowWidth || 0;
        } else {
          const systemInfo = common_vendor.index.getSystemInfoSync();
          this.statusBarHeight = systemInfo.statusBarHeight || 0;
          this.navBarHeight = (systemInfo.statusBarHeight || 0) + 44;
          this.windowHeight = systemInfo.windowHeight || 0;
          this.windowWidth = systemInfo.windowWidth || 0;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at mixins/page-mixin.js:167", "❌ 获取系统信息失败:", error);
      }
    },
    /**
     * 获取用户信息
     */
    getUserInfo() {
      try {
        const app = getApp();
        if (app && app.globalData) {
          this.userInfo = app.globalData.userInfo;
          this.isLoggedIn = app.globalData.isLoggedIn || false;
        } else {
          this.userInfo = common_vendor.index.getStorageSync("userInfo");
          this.isLoggedIn = !!common_vendor.index.getStorageSync("token");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at mixins/page-mixin.js:186", "❌ 获取用户信息失败:", error);
      }
    },
    /**
     * 检查登录状态
     */
    checkLoginStatus() {
      const token = common_vendor.index.getStorageSync("token");
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      this.isLoggedIn = !!(token && userInfo);
      if (this.isLoggedIn) {
        this.userInfo = userInfo;
      }
    },
    /**
     * 检查网络状态
     */
    checkNetworkStatus() {
      common_vendor.index.getNetworkType({
        success: (res) => {
          this.isConnected = res.networkType !== "none";
          if (!this.isConnected) {
            this.showToast("网络连接已断开", "none");
          }
        },
        fail: () => {
          common_vendor.index.__f__("warn", "at mixins/page-mixin.js:217", "⚠️ 获取网络状态失败");
        }
      });
    },
    /**
     * 处理刷新
     */
    handleRefresh() {
      return __async(this, null, function* () {
        if (this.refreshing)
          return;
        this.refreshing = true;
        try {
          if (this.onRefresh && typeof this.onRefresh === "function") {
            yield this.onRefresh();
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at mixins/page-mixin.js:236", "❌ 刷新失败:", error);
          this.handleError(error);
        } finally {
          this.refreshing = false;
          common_vendor.index.stopPullDownRefresh();
        }
      });
    },
    /**
     * 处理加载更多
     */
    handleLoadMore() {
      return __async(this, null, function* () {
        try {
          if (this.onLoadMore && typeof this.onLoadMore === "function") {
            yield this.onLoadMore();
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at mixins/page-mixin.js:254", "❌ 加载更多失败:", error);
          this.handleError(error);
        }
      });
    },
    /**
     * 显示加载状态
     */
    showLoading(title = "加载中...") {
      this.loading = true;
      common_vendor.index.showLoading({
        title,
        mask: true
      });
    },
    /**
     * 隐藏加载状态
     */
    hideLoading() {
      this.loading = false;
      common_vendor.index.hideLoading();
    },
    /**
     * 显示提示信息
     */
    showToast(title, icon = "none", duration = 2e3) {
      common_vendor.index.showToast({
        title,
        icon,
        duration
      });
    },
    /**
     * 显示确认对话框
     */
    showConfirm(content, title = "提示") {
      return new Promise((resolve) => {
        common_vendor.index.showModal({
          title,
          content,
          success: (res) => {
            resolve(res.confirm);
          },
          fail: () => {
            resolve(false);
          }
        });
      });
    },
    /**
     * 错误处理
     */
    handleError(error, showToast = true) {
      common_vendor.index.__f__("error", "at mixins/page-mixin.js:311", "❌ 页面错误:", error);
      this.hasError = true;
      this.errorMessage = error.message || "发生未知错误";
      const app = getApp();
      if (app && app.addError) {
        app.addError({
          type: "page_error",
          page: this.$mp.page.route,
          message: this.errorMessage,
          stack: error.stack,
          timestamp: Date.now()
        });
      }
      if (showToast) {
        this.showToast(this.errorMessage);
      }
    },
    /**
     * 清理资源
     */
    cleanup() {
      if (this.pageCleanup && typeof this.pageCleanup === "function") {
        this.pageCleanup();
      }
    },
    /**
     * 跳转到登录页
     */
    goToLogin() {
      common_vendor.index.navigateTo({
        url: "/pages/login/login"
      });
    },
    /**
     * 检查登录状态，未登录则跳转
     */
    requireLogin() {
      if (!this.isLoggedIn) {
        this.showToast("请先登录");
        setTimeout(() => {
          this.goToLogin();
        }, 1500);
        return false;
      }
      return true;
    },
    /**
     * 格式化时间
     */
    formatTime(timestamp, format = "YYYY-MM-DD HH:mm:ss") {
      if (!timestamp)
        return "";
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return format.replace("YYYY", year).replace("MM", month).replace("DD", day).replace("HH", hours).replace("mm", minutes).replace("ss", seconds);
    }
  }
};
exports.pageMixin = pageMixin;
//# sourceMappingURL=../../.sourcemap/mp-weixin/mixins/page-mixin.js.map
