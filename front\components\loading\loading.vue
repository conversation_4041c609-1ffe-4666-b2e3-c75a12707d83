<template>
	<view v-if="visible" class="loading-overlay" :style="overlayStyle" @click="handleOverlayClick">
		<view class="loading-content" :style="contentStyle">
			<!-- 加载动画 -->
			<view class="loading-spinner" :class="spinnerType">
				<view v-if="spinnerType === 'dots'" class="dots-spinner">
					<view class="dot" v-for="i in 3" :key="i"></view>
				</view>
				<view v-else-if="spinnerType === 'circle'" class="circle-spinner"></view>
				<view v-else-if="spinnerType === 'wave'" class="wave-spinner">
					<view class="wave-bar" v-for="i in 5" :key="i"></view>
				</view>
			</view>
			
			<!-- 加载文字 -->
			<text v-if="text" class="loading-text">{{ text }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'Loading',
	props: {
		// 是否显示
		visible: {
			type: Boolean,
			default: false
		},
		// 加载文字
		text: {
			type: String,
			default: '加载中...'
		},
		// 动画类型
		spinnerType: {
			type: String,
			default: 'dots', // dots, circle, wave
			validator: (value) => ['dots', 'circle', 'wave'].includes(value)
		},
		// 背景色
		backgroundColor: {
			type: String,
			default: 'rgba(0, 0, 0, 0.5)'
		},
		// 内容背景色
		contentBackgroundColor: {
			type: String,
			default: 'rgba(255, 255, 255, 0.95)'
		},
		// 是否可以点击遮罩关闭
		maskClosable: {
			type: Boolean,
			default: false
		},
		// 层级
		zIndex: {
			type: Number,
			default: 9999
		}
	},
	
	computed: {
		overlayStyle() {
			return {
				backgroundColor: this.backgroundColor,
				zIndex: this.zIndex
			}
		},
		
		contentStyle() {
			return {
				backgroundColor: this.contentBackgroundColor
			}
		}
	},
	
	methods: {
		handleOverlayClick() {
			if (this.maskClosable) {
				this.$emit('close')
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.5);
}

.loading-content {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-radius: 32rpx;
	padding: 80rpx 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 240rpx;
	box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
	margin-bottom: 32rpx;
}

/* 点点动画 */
.dots-spinner {
	display: flex;
	gap: 8rpx;
	
	.dot {
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		background: #667eea;
		animation: dots-bounce 1.4s infinite ease-in-out both;
		
		&:nth-child(1) { animation-delay: -0.32s; }
		&:nth-child(2) { animation-delay: -0.16s; }
		&:nth-child(3) { animation-delay: 0s; }
	}
}

@keyframes dots-bounce {
	0%, 80%, 100% {
		transform: scale(0);
		opacity: 0.5;
	}
	40% {
		transform: scale(1);
		opacity: 1;
	}
}

/* 圆圈动画 */
.circle-spinner {
	width: 64rpx;
	height: 64rpx;
	border: 6rpx solid #f3f4f6;
	border-top: 6rpx solid #667eea;
	border-radius: 50%;
	animation: circle-spin 1s linear infinite;
}

@keyframes circle-spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 波浪动画 */
.wave-spinner {
	display: flex;
	gap: 4rpx;
	align-items: center;
	height: 64rpx;
	
	.wave-bar {
		width: 8rpx;
		height: 32rpx;
		background: #667eea;
		border-radius: 4rpx;
		animation: wave-scale 1.2s infinite ease-in-out;
		
		&:nth-child(1) { animation-delay: -1.1s; }
		&:nth-child(2) { animation-delay: -1.0s; }
		&:nth-child(3) { animation-delay: -0.9s; }
		&:nth-child(4) { animation-delay: -0.8s; }
		&:nth-child(5) { animation-delay: -0.7s; }
	}
}

@keyframes wave-scale {
	0%, 40%, 100% {
		transform: scaleY(0.4);
		opacity: 0.5;
	}
	20% {
		transform: scaleY(1);
		opacity: 1;
	}
}

.loading-text {
	color: #666;
	font-size: 28rpx;
	text-align: center;
	line-height: 1.5;
}
</style>
