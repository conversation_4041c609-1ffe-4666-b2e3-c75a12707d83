<template>
	<view class="json-parse-test-container">
		<view class="test-header">
			<text class="test-title">JSON解析错误修复测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">当前存储状态</text>
			<view class="storage-status">
				<view class="status-item">
					<text class="status-label">用户信息类型:</text>
					<text class="status-value">{{ userInfoType }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">权限信息类型:</text>
					<text class="status-value">{{ permissionsType }}</text>
				</view>
				<view class="status-item">
					<text class="status-label">角色信息类型:</text>
					<text class="status-value">{{ rolesType }}</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">安全获取测试</text>
			<view class="safe-get-test">
				<view class="test-item">
					<text class="test-label">getUserInfo():</text>
					<text class="test-result" :class="userInfoResult.success ? 'success' : 'error'">
						{{ userInfoResult.message }}
					</text>
				</view>
				<view class="test-item">
					<text class="test-label">getPermissions():</text>
					<text class="test-result" :class="permissionsResult.success ? 'success' : 'error'">
						{{ permissionsResult.message }}
					</text>
				</view>
				<view class="test-item">
					<text class="test-label">getRoles():</text>
					<text class="test-result" :class="rolesResult.success ? 'success' : 'error'">
						{{ rolesResult.message }}
					</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">异常数据模拟测试</text>
			<view class="mock-test">
				<button class="test-btn warning" @tap="testInvalidUserInfo">模拟无效用户信息</button>
				<button class="test-btn warning" @tap="testInvalidPermissions">模拟无效权限信息</button>
				<button class="test-btn warning" @tap="testInvalidRoles">模拟无效角色信息</button>
				<button class="test-btn error" @tap="testCorruptedData">模拟损坏数据</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">正常数据恢复测试</text>
			<view class="recovery-test">
				<button class="test-btn success" @tap="testValidUserInfo">恢复正常用户信息</button>
				<button class="test-btn success" @tap="testValidPermissions">恢复正常权限信息</button>
				<button class="test-btn success" @tap="testValidRoles">恢复正常角色信息</button>
				<button class="test-btn primary" @tap="clearAllData">清除所有数据</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">原始存储数据检查</text>
			<scroll-view class="raw-data-container" scroll-y>
				<view class="raw-data-item">
					<text class="data-label">userInfo原始数据:</text>
					<text class="data-value">{{ rawUserInfo }}</text>
				</view>
				<view class="raw-data-item">
					<text class="data-label">permissions原始数据:</text>
					<text class="data-value">{{ rawPermissions }}</text>
				</view>
				<view class="raw-data-item">
					<text class="data-label">roles原始数据:</text>
					<text class="data-value">{{ rawRoles }}</text>
				</view>
			</scroll-view>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试日志</text>
			<scroll-view class="log-container" scroll-y>
				<view class="log-item" v-for="(log, index) in testLogs" :key="index">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message" :class="log.type">{{ log.message }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { getUserInfo, setUserInfo, getPermissions, setPermissions, getRoles, setRoles, removeUserInfo } from '@/utils/auth.js'

export default {
	data() {
		return {
			userInfoType: '',
			permissionsType: '',
			rolesType: '',
			userInfoResult: { success: false, message: '' },
			permissionsResult: { success: false, message: '' },
			rolesResult: { success: false, message: '' },
			rawUserInfo: '',
			rawPermissions: '',
			rawRoles: '',
			testLogs: []
		}
	},
	
	onLoad() {
		this.addLog('JSON解析错误修复测试页面加载', 'info')
		this.checkCurrentState()
	},
	
	onShow() {
		this.checkCurrentState()
	},
	
	methods: {
		// 添加日志
		addLog(message, type = 'info') {
			const now = new Date()
			const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
			
			this.testLogs.unshift({
				time,
				message,
				type
			})
			
			if (this.testLogs.length > 20) {
				this.testLogs = this.testLogs.slice(0, 20)
			}
		},
		
		// 检查当前状态
		checkCurrentState() {
			this.checkStorageTypes()
			this.checkRawData()
			this.testSafeGetters()
		},
		
		// 检查存储数据类型
		checkStorageTypes() {
			try {
				const userInfo = uni.getStorageSync('userInfo')
				const permissions = uni.getStorageSync('permissions')
				const roles = uni.getStorageSync('roles')
				
				this.userInfoType = userInfo ? typeof userInfo : 'null'
				this.permissionsType = permissions ? typeof permissions : 'null'
				this.rolesType = roles ? typeof roles : 'null'
				
			} catch (error) {
				this.addLog(`检查存储类型失败: ${error.message}`, 'error')
			}
		},
		
		// 检查原始数据
		checkRawData() {
			try {
				this.rawUserInfo = JSON.stringify(uni.getStorageSync('userInfo')) || 'null'
				this.rawPermissions = JSON.stringify(uni.getStorageSync('permissions')) || 'null'
				this.rawRoles = JSON.stringify(uni.getStorageSync('roles')) || 'null'
			} catch (error) {
				this.addLog(`检查原始数据失败: ${error.message}`, 'error')
			}
		},
		
		// 测试安全获取函数
		testSafeGetters() {
			// 测试getUserInfo
			try {
				const userInfo = getUserInfo()
				this.userInfoResult = {
					success: true,
					message: userInfo ? `成功获取用户信息: ${userInfo.username || 'unknown'}` : '用户信息为空'
				}
			} catch (error) {
				this.userInfoResult = {
					success: false,
					message: `获取失败: ${error.message}`
				}
			}
			
			// 测试getPermissions
			try {
				const permissions = getPermissions()
				this.permissionsResult = {
					success: true,
					message: `成功获取权限: ${permissions.length} 项`
				}
			} catch (error) {
				this.permissionsResult = {
					success: false,
					message: `获取失败: ${error.message}`
				}
			}
			
			// 测试getRoles
			try {
				const roles = getRoles()
				this.rolesResult = {
					success: true,
					message: `成功获取角色: ${roles.length} 项`
				}
			} catch (error) {
				this.rolesResult = {
					success: false,
					message: `获取失败: ${error.message}`
				}
			}
		},
		
		// 模拟无效用户信息
		testInvalidUserInfo() {
			this.addLog('模拟无效用户信息数据', 'warning')
			
			// 直接存储对象（可能导致问题）
			uni.setStorageSync('userInfo', { username: 'test', realname: '测试用户' })
			
			this.checkCurrentState()
		},
		
		// 模拟无效权限信息
		testInvalidPermissions() {
			this.addLog('模拟无效权限信息数据', 'warning')
			
			// 存储非数组数据
			uni.setStorageSync('permissions', 'invalid_permissions_data')
			
			this.checkCurrentState()
		},
		
		// 模拟无效角色信息
		testInvalidRoles() {
			this.addLog('模拟无效角色信息数据', 'warning')
			
			// 存储非数组数据
			uni.setStorageSync('roles', { admin: true })
			
			this.checkCurrentState()
		},
		
		// 模拟损坏数据
		testCorruptedData() {
			this.addLog('模拟损坏的JSON数据', 'warning')
			
			// 存储无效JSON字符串
			uni.setStorageSync('userInfo', '{"username":"test","invalid":}')
			uni.setStorageSync('permissions', '[invalid,json]')
			uni.setStorageSync('roles', '{broken:json}')
			
			this.checkCurrentState()
		},
		
		// 恢复正常用户信息
		testValidUserInfo() {
			this.addLog('恢复正常用户信息', 'success')
			
			const validUserInfo = {
				id: '1001',
				username: 'testuser',
				realname: '测试用户',
				avatar: '/static/images/default-avatar.png',
				phone: '13800138000',
				email: '<EMAIL>'
			}
			
			setUserInfo(validUserInfo)
			this.checkCurrentState()
		},
		
		// 恢复正常权限信息
		testValidPermissions() {
			this.addLog('恢复正常权限信息', 'success')
			
			const validPermissions = ['user:read', 'user:write', 'course:read']
			
			setPermissions(validPermissions)
			this.checkCurrentState()
		},
		
		// 恢复正常角色信息
		testValidRoles() {
			this.addLog('恢复正常角色信息', 'success')
			
			const validRoles = ['user', 'student']
			
			setRoles(validRoles)
			this.checkCurrentState()
		},
		
		// 清除所有数据
		clearAllData() {
			this.addLog('清除所有测试数据', 'info')
			
			removeUserInfo()
			uni.removeStorageSync('permissions')
			uni.removeStorageSync('roles')
			
			this.checkCurrentState()
		}
	}
}
</script>

<style lang="scss" scoped>
.json-parse-test-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.test-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 24rpx;
		border-bottom: 2rpx solid #eee;
		padding-bottom: 16rpx;
	}
}

.storage-status, .safe-get-test {
	.status-item, .test-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		
		&:last-child {
			border-bottom: none;
		}
		
		.status-label, .test-label {
			font-size: 28rpx;
			color: #666;
			width: 200rpx;
			flex-shrink: 0;
		}
		
		.status-value, .test-result {
			font-size: 28rpx;
			color: #333;
			word-break: break-all;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
		}
	}
}

.mock-test, .recovery-test {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
	
	.test-btn {
		height: 80rpx;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: white;
		
		&.primary {
			background: #1890ff;
		}
		
		&.success {
			background: #52c41a;
		}
		
		&.warning {
			background: #faad14;
		}
		
		&.error {
			background: #ff4d4f;
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}

.raw-data-container {
	height: 200rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	padding: 16rpx;
	
	.raw-data-item {
		margin-bottom: 16rpx;
		
		.data-label {
			display: block;
			font-size: 24rpx;
			color: #666;
			margin-bottom: 8rpx;
		}
		
		.data-value {
			font-size: 22rpx;
			color: #333;
			font-family: monospace;
			word-break: break-all;
			background: #f5f5f5;
			padding: 8rpx;
			border-radius: 4rpx;
		}
	}
}

.log-container {
	height: 300rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	padding: 16rpx;
	
	.log-item {
		display: flex;
		margin-bottom: 12rpx;
		font-size: 24rpx;
		
		.log-time {
			color: #999;
			width: 120rpx;
			flex-shrink: 0;
		}
		
		.log-message {
			color: #333;
			word-break: break-all;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
			
			&.warning {
				color: #faad14;
			}
			
			&.info {
				color: #1890ff;
			}
		}
	}
}
</style>
