<template>
	<view class="login-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 登录头部 -->
		<view class="login-header">
			<view class="logo-section">
				<image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
				<text class="app-name">口语便利店</text>
				<text class="app-desc">日语学习打卡小程序</text>
			</view>
		</view>
		
		<!-- 登录表单 -->
		<view class="login-form">
			<view class="form-item">
				<view class="input-wrapper">
					<text class="iconfont icon-user input-icon"></text>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入用户名"
						v-model="loginForm.username"
						:disabled="loading"
					/>
				</view>
			</view>
			
			<view class="form-item">
				<view class="input-wrapper">
					<text class="iconfont icon-lock input-icon"></text>
					<input 
						class="form-input" 
						:type="showPassword ? 'text' : 'password'" 
						placeholder="请输入密码"
						v-model="loginForm.password"
						:disabled="loading"
					/>
					<text 
						class="iconfont input-icon toggle-password"
						:class="showPassword ? 'icon-eye' : 'icon-eye-close'"
						@tap="togglePassword"
					></text>
				</view>
			</view>
			
			<!-- 验证码（如果需要） -->
			<view class="form-item" v-if="needCaptcha">
				<view class="captcha-wrapper">
					<view class="input-wrapper captcha-input">
						<text class="iconfont icon-shield input-icon"></text>
						<input
							class="form-input"
							type="text"
							placeholder="请输入验证码"
							v-model="loginForm.captcha"
							:disabled="loading"
						/>
					</view>
					<view class="captcha-image" @tap="handleRefreshCaptcha">
						<view v-if="captchaLoading" class="captcha-loading">
							<text class="iconfont icon-loading"></text>
						</view>
						<image
							v-else-if="captchaImage"
							:src="captchaImage"
							mode="aspectFit"
							class="captcha-img"
						></image>
						<text v-else class="captcha-placeholder">点击获取验证码</text>
					</view>
				</view>
			</view>
			
			<!-- 登录按钮 -->
			<button 
				class="login-btn"
				:class="{ 'loading': loading }"
				:disabled="!canLogin || loading"
				@tap="handleLogin"
			>
				<text v-if="loading" class="iconfont icon-loading"></text>
				{{ loading ? '登录中...' : '登录' }}
			</button>
			
			<!-- 其他操作 -->
			<view class="login-actions">
				<text class="action-link" @tap="goToRegister">注册账号</text>
				<text class="action-link" @tap="goToForgotPassword">忘记密码</text>
			</view>
		</view>
		
		<!-- 第三方登录 -->
		<view class="third-party-login">
			<view class="divider">
				<text class="divider-text">其他登录方式</text>
			</view>
			
			<view class="third-party-buttons">
				<!-- 微信登录 -->
				<!-- #ifdef MP-WEIXIN -->
				<button class="third-party-btn wechat" @tap="handleWechatLogin">
					<text class="iconfont icon-wechat"></text>
					<text class="btn-text">微信登录</text>
				</button>
				<!-- #endif -->
				
				<!-- 钉钉登录 -->
				<!-- #ifdef MP-DINGTALK -->
				<button class="third-party-btn dingtalk" @tap="handleDingtalkLogin">
					<text class="iconfont icon-dingtalk"></text>
					<text class="btn-text">钉钉登录</text>
				</button>
				<!-- #endif -->
			</view>
		</view>
	</view>
</template>

<script>
import * as jeecgUser from '@/api/jeecg-user.js'
import { getCaptcha, getCaptchaImageUrl, getCaptchaKey } from '@/utils/captcha.js'
import { setToken, setUserInfo, refreshUserInfo } from '@/utils/auth.js'
import { navigate } from '@/utils/index.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			loading: false,
			showPassword: false,
			needCaptcha: true,  // 默认需要验证码
			captchaImage: '',
			captchaLoading: false,

			// 登录表单
			loginForm: {
				username: '',
				password: '',
				captcha: '',
				checkKey: ''
			},

			// 来源页面（用于登录成功后跳转）
			redirectUrl: ''
		}
	},
	
	computed: {
		canLogin() {
			const { username, password, captcha } = this.loginForm
			const basicValid = username.trim() && password.trim()
			
			if (this.needCaptcha) {
				return basicValid && captcha.trim()
			}
			
			return basicValid
		}
	},
	
	onLoad(options) {
		console.log('🔐 登录页面加载:', options)
		
		// 获取系统信息
		this.getSystemInfo()
		
		// 获取重定向URL
		if (options.redirect) {
			this.redirectUrl = decodeURIComponent(options.redirect)
		}

		// 初始化验证码
		this.initCaptcha()
	},
	
	methods: {
		// 获取系统信息
		getSystemInfo() {
			try {
				const systemInfo = uni.getSystemInfoSync()
				this.statusBarHeight = systemInfo.statusBarHeight || 0
			} catch (error) {
				console.error('获取系统信息失败:', error)
			}
		},
		
		// 初始化验证码
		async initCaptcha() {
			if (!this.needCaptcha) return

			console.log('🔄 初始化验证码')
			await this.loadCaptcha()
		},

		// 加载验证码
		async loadCaptcha() {
			if (!this.needCaptcha) return

			this.captchaLoading = true

			try {
				const result = await getCaptcha(true)

				if (result.success) {
					this.captchaImage = getCaptchaImageUrl()
					this.loginForm.checkKey = getCaptchaKey()

					console.log('✅ 验证码加载成功:', {
						hasImage: !!this.captchaImage,
						checkKey: this.loginForm.checkKey,
						imageFormat: this.captchaImage ? this.captchaImage.substring(0, 30) + '...' : ''
					})
				} else {
					console.error('❌ 验证码加载失败:', result.message)
					uni.showToast({
						title: result.message || '验证码加载失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('❌ 验证码加载异常:', error)
				uni.showToast({
					title: '验证码加载失败',
					icon: 'none'
				})
			} finally {
				this.captchaLoading = false
			}
		},

		// 刷新验证码
		async handleRefreshCaptcha() {
			if (!this.needCaptcha || this.captchaLoading) return

			console.log('🔄 刷新验证码')

			// 清空验证码输入
			this.loginForm.captcha = ''

			// 重新加载验证码
			await this.loadCaptcha()
		},
		
		// 切换密码显示
		togglePassword() {
			this.showPassword = !this.showPassword
		},
		
		// 处理登录
		async handleLogin() {
			if (!this.canLogin || this.loading) return
			
			this.loading = true
			
			try {
				console.log('🔐 开始登录:', this.loginForm.username)
				
				// 构建登录参数
				const loginData = {
					username: this.loginForm.username.trim(),
					password: this.loginForm.password.trim()
				}
				
				// 如果需要验证码，添加验证码参数
				if (this.needCaptcha) {
					loginData.captcha = this.loginForm.captcha.trim()
					loginData.checkKey = this.loginForm.checkKey
				}
				
				// 调用登录接口
				const result = await jeecgUser.login(loginData)
				
				if (result.success) {
					console.log('✅ 登录成功:', result.data)

					// 保存认证信息
					await this.saveAuthInfo(result.data)

					uni.showToast({
						title: '登录成功',
						icon: 'success',
						duration: 1500
					})

					// 延迟跳转，让用户看到成功提示
					setTimeout(() => {
						this.handleLoginSuccess()
					}, 1500)

				} else {
					console.error('❌ 登录失败:', result.message)
					
					uni.showToast({
						title: result.message || '登录失败',
						icon: 'none',
						duration: 2000
					})
					
					// 如果是验证码错误，刷新验证码
					if (result.message && (result.message.includes('验证码') || result.message.includes('captcha'))) {
						await this.handleRefreshCaptcha()
					}
				}
				
			} catch (error) {
				console.error('❌ 登录异常:', error)
				
				uni.showToast({
					title: error.message || '登录失败，请重试',
					icon: 'none',
					duration: 2000
				})
				
			} finally {
				this.loading = false
			}
		},
		
		// 保存认证信息
		async saveAuthInfo(loginResData) {
			try {
				console.log('💾 保存认证信息:', loginResData)

				// 1. 保存Token（使用auth工具函数）
				const token = loginResData.token 
				if (token) {
					setToken(token)
					console.log('✅ Token保存成功:', token.substring(0, 20) + '...')
				} else {
					console.warn('⚠️ 登录响应中未找到token')
				}

				// 2. 保存用户信息（使用auth工具函数）
				// const userInfo = {
				// 	id: loginData.userInfo.id,
				// 	username: loginData.username,
				// 	realname: loginData.realname ,
				// 	avatar: loginData.userInfo.avatar ,
				// 	phone: loginData.userInfo.phone,
				// 	email: loginData.userInfo.email,
				// 	orgCode: loginData.userInfo.orgCode
				// 	// orgCodeTxt: loginData.userInfo.orgCodeTxt,
				// 	// departs: loginData.userInfo.departs || [],
				// 	// roles: loginData.userInfo.roles || [],
				// 	// permissions: loginData.userInfo.permissions || []
				// }

				setUserInfo(loginResData.userInfo)
				console.log('✅ 用户信息保存成功:', loginResData.userInfo)

				// 3. 更新全局状态
				this.updateGlobalAuthState(token, loginResData.userInfo)

				// // 4. 触发认证状态刷新
				// await this.refreshAuthState()

			} catch (error) {
				console.error('❌ 保存认证信息失败:', error)
				uni.showToast({
					title: '保存用户信息失败',
					icon: 'none'
				})
				throw error
			}
		},

		// 更新全局认证状态
		updateGlobalAuthState(token, userInfo) {
			try {
				// 更新App.vue中的globalData
				const app = getApp()
				if (app && app.globalData) {
					app.globalData.isLogin = true
					app.globalData.token = token
					app.globalData.userInfo = userInfo

					console.log('✅ 全局状态更新成功')
				}

				// // 更新当前页面的认证状态（如果使用了auth-mixin）
				// if (this.updateAuthStatus && typeof this.updateAuthStatus === 'function') {
				// 	this.updateAuthStatus()
				// }

			} catch (error) {
				console.error('❌ 更新全局状态失败:', error)
			}
		},

		// 刷新认证状态
		async refreshAuthState() {
			try {
				// 如果有refreshUserInfo方法，调用它
				if (typeof refreshUserInfo === 'function') {
					await refreshUserInfo()
					console.log('✅ 认证状态刷新成功')
				}

				// 触发全局认证状态更新事件
				uni.$emit('authStateChanged', {
					isAuthenticated: true,
					timestamp: Date.now()
				})

			} catch (error) {
				console.warn('⚠️ 刷新认证状态失败:', error)
			}
		},

		// 处理登录成功
		handleLoginSuccess() {
			try {
				console.log('🎉 处理登录成功跳转')

				// 清除验证码
				this.loginForm.captcha = ''
				this.loginForm.checkKey = ''

				// 智能跳转处理
				if (this.redirectUrl) {
					console.log('📍 跳转到重定向页面:', this.redirectUrl)

					// 检查重定向URL是否是tabBar页面
					const tabBarPages = [
						'pages/index/index',
						'pages/course/list',
						'pages/checkin/checkin',
						'pages/leaderboard/leaderboard',
						'pages/profile/profile'
					]

					const cleanUrl = this.redirectUrl.replace(/^\//, '').split('?')[0]
					const isTabBarPage = tabBarPages.includes(cleanUrl)

					if (isTabBarPage) {
						// tabBar页面使用switchTab
						uni.switchTab({
							url: `/${cleanUrl}`,
							success: () => {
								console.log('✅ 成功跳转到tabBar页面:', cleanUrl)
							},
							fail: (error) => {
								console.error('❌ 跳转tabBar页面失败:', error)
								// 兜底跳转到首页
								uni.switchTab({ url: '/pages/index/index' })
							}
						})
					} else {
						// 普通页面使用redirectTo
						uni.redirectTo({
							url: this.redirectUrl,
							success: () => {
								console.log('✅ 成功跳转到重定向页面:', this.redirectUrl)
							},
							fail: (error) => {
								console.error('❌ 跳转重定向页面失败:', error)
								// 兜底跳转到首页
								uni.switchTab({ url: '/pages/index/index' })
							}
						})
					}
				} else {
					// 默认跳转到首页
					console.log('📍 跳转到首页')
					uni.switchTab({
						url: '/pages/index/index',
						success: () => {
							console.log('✅ 成功跳转到首页')
						},
						fail: (error) => {
							console.error('❌ 跳转首页失败:', error)
						}
					})
				}

			} catch (error) {
				console.error('❌ 处理登录成功跳转失败:', error)
				// 兜底跳转
				uni.switchTab({ url: '/pages/index/index' })
			}
		},
		
		// 微信登录
		async handleWechatLogin() {
			// #ifdef MP-WEIXIN
			try {
				console.log('🔐 微信登录')
				
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				})
				
				// TODO: 实现微信登录逻辑
				
			} catch (error) {
				console.error('微信登录失败:', error)
			}
			// #endif
		},
		
		// 钉钉登录
		async handleDingtalkLogin() {
			// #ifdef MP-DINGTALK
			try {
				console.log('🔐 钉钉登录')
				
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				})
				
				// TODO: 实现钉钉登录逻辑
				
			} catch (error) {
				console.error('钉钉登录失败:', error)
			}
			// #endif
		},
		
		// 跳转到注册页面
		goToRegister() {
			uni.navigateTo({
				url: '/pages/register/register'
			})
		},
		
		// 跳转到忘记密码页面
		goToForgotPassword() {
			uni.navigateTo({
				url: '/pages/forgot-password/forgot-password'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	flex-direction: column;
}

.status-bar {
	background: transparent;
}

.login-header {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx 40rpx;
	
	.logo-section {
		text-align: center;
		color: white;
		
		.logo {
			width: 160rpx;
			height: 160rpx;
			margin-bottom: 32rpx;
		}
		
		.app-name {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			margin-bottom: 16rpx;
		}
		
		.app-desc {
			display: block;
			font-size: 28rpx;
			opacity: 0.8;
		}
	}
}

.login-form {
	background: white;
	border-radius: 48rpx 48rpx 0 0;
	padding: 80rpx 40rpx 40rpx;
	
	.form-item {
		margin-bottom: 32rpx;
		
		.input-wrapper {
			position: relative;
			display: flex;
			align-items: center;
			background: #f8f9fa;
			border-radius: 24rpx;
			padding: 0 24rpx;
			height: 96rpx;
			
			.input-icon {
				color: #999;
				font-size: 32rpx;
				margin-right: 16rpx;
			}
			
			.form-input {
				flex: 1;
				font-size: 32rpx;
				color: #333;
				
				&::placeholder {
					color: #999;
				}
			}
			
			.toggle-password {
				margin-left: 16rpx;
				margin-right: 0;
				cursor: pointer;
			}
		}
		
		.captcha-wrapper {
			display: flex;
			gap: 16rpx;
			
			.captcha-input {
				flex: 1;
			}
			
			.captcha-image {
				width: 200rpx;
				height: 96rpx;
				background: #f8f9fa;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;

				&:active {
					opacity: 0.8;
				}

				.captcha-loading {
					display: flex;
					align-items: center;
					justify-content: center;

					.icon-loading {
						font-size: 32rpx;
						color: #667eea;
						animation: rotate 1s linear infinite;
					}
				}

				.captcha-img {
					width: 100%;
					height: 100%;
					border-radius: 24rpx;
				}

				.captcha-placeholder {
					font-size: 24rpx;
					color: #999;
					text-align: center;
					padding: 0 8rpx;
				}
			}
		}
	}
	
	.login-btn {
		width: 100%;
		height: 96rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		color: white;
		border: none;
		border-radius: 24rpx;
		font-size: 32rpx;
		font-weight: bold;
		margin: 40rpx 0;
		display: flex;
		align-items: center;
		justify-content: center;
		
		&.loading {
			opacity: 0.7;
		}
		
		&:disabled {
			opacity: 0.5;
		}
		
		.icon-loading {
			margin-right: 16rpx;
			animation: rotate 1s linear infinite;
		}
	}
	
	.login-actions {
		display: flex;
		justify-content: space-between;
		
		.action-link {
			color: #667eea;
			font-size: 28rpx;
		}
	}
}

.third-party-login {
	padding: 40rpx;
	
	.divider {
		text-align: center;
		margin-bottom: 40rpx;
		
		.divider-text {
			color: white;
			font-size: 28rpx;
			opacity: 0.8;
		}
	}
	
	.third-party-buttons {
		display: flex;
		gap: 24rpx;
		
		.third-party-btn {
			flex: 1;
			height: 80rpx;
			border-radius: 20rpx;
			border: none;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			
			&.wechat {
				background: #07c160;
				color: white;
			}
			
			&.dingtalk {
				background: #0089ff;
				color: white;
			}
			
			.iconfont {
				margin-right: 12rpx;
				font-size: 32rpx;
			}
		}
	}
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

/* 字体图标 */
.icon-user::before { content: '\e001'; }
.icon-lock::before { content: '\e002'; }
.icon-eye::before { content: '\e003'; }
.icon-eye-close::before { content: '\e004'; }
.icon-shield::before { content: '\e005'; }
.icon-loading::before { content: '\e006'; }
.icon-wechat::before { content: '\e007'; }
.icon-dingtalk::before { content: '\e008'; }
</style>
