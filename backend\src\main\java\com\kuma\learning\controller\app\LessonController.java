package com.kuma.learning.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kuma.learning.entity.KumaLesson;
import com.kuma.learning.service.IKumaLessonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = "APP-课时管理")
@RestController
@RequestMapping("/app/lesson")
public class LessonController {

    @Autowired
    private IKumaLessonService lessonService;

    @AutoLog(value = "课时-获取课程课时列表")
    @ApiOperation(value = "课时-获取课程课时列表", notes = "课时-获取课程课时列表")
    @GetMapping("/list")
    public Result<List<KumaLesson>> getCourseLessons(@RequestParam(name = "courseId") String courseId) {
        LambdaQueryWrapper<KumaLesson> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaLesson::getCourseId, courseId)
                .eq(KumaLesson::getStatus, 1)
                .orderByAsc(KumaLesson::getSort);
        
        List<KumaLesson> list = lessonService.list(queryWrapper);
        return Result.OK(list);
    }

    @AutoLog(value = "课时-通过id查询")
    @ApiOperation(value = "课时-通过id查询", notes = "课时-通过id查询")
    @GetMapping("/queryById")
    public Result<KumaLesson> queryById(@RequestParam(name = "id") String id) {
        KumaLesson lesson = lessonService.getById(id);
        if (lesson == null) {
            return Result.error("未找到对应课时");
        }
        return Result.OK(lesson);
    }
} 