#!/usr/bin/env node

/**
 * 小程序启动脚本
 * 自动检查配置并启动对应的小程序开发环境
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// 获取命令行参数
const platform = process.argv[2] || 'weixin'

console.log('🚀 小程序启动脚本')
console.log('=' * 50)

/**
 * 检查配置文件
 */
function checkConfig() {
  console.log('🔍 检查配置文件...')
  
  const configFiles = [
    'manifest.json',
    'pages.json',
    'main.js',
    'App.vue'
  ]
  
  let allExists = true
  
  configFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file)
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file} - 存在`)
    } else {
      console.log(`❌ ${file} - 缺失`)
      allExists = false
    }
  })
  
  return allExists
}

/**
 * 检查Vue3配置
 */
function checkVue3Config() {
  console.log('🔍 检查Vue3配置...')
  
  try {
    const manifestPath = path.join(process.cwd(), 'manifest.json')
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    
    if (manifest.vueVersion === '3') {
      console.log('✅ Vue版本: 3')
      return true
    } else {
      console.log(`❌ Vue版本: ${manifest.vueVersion}，应该为3`)
      return false
    }
  } catch (error) {
    console.log('❌ manifest.json读取失败')
    return false
  }
}

/**
 * 检查小程序平台配置
 */
function checkPlatformConfig(platform) {
  console.log(`🔍 检查${platform}小程序配置...`)
  
  try {
    const manifestPath = path.join(process.cwd(), 'manifest.json')
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    
    const platformKey = platform === 'weixin' ? 'mp-weixin' : 'mp-dingtalk'
    
    if (manifest[platformKey]) {
      console.log(`✅ ${platform}小程序配置存在`)
      
      if (manifest[platformKey].usingComponents) {
        console.log(`✅ ${platform}小程序组件支持已启用`)
      } else {
        console.log(`⚠️  ${platform}小程序组件支持未启用`)
      }
      
      return true
    } else {
      console.log(`❌ ${platform}小程序配置缺失`)
      return false
    }
  } catch (error) {
    console.log('❌ 平台配置检查失败')
    return false
  }
}

/**
 * 检查依赖
 */
function checkDependencies() {
  console.log('🔍 检查依赖...')
  
  try {
    const packagePath = path.join(process.cwd(), 'package.json')
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies }
    
    const requiredDeps = [
      '@dcloudio/uni-app',
      '@dcloudio/vite-plugin-uni',
      'vue'
    ]
    
    let allExists = true
    
    requiredDeps.forEach(dep => {
      if (deps[dep]) {
        console.log(`✅ ${dep}`)
      } else {
        console.log(`❌ ${dep} - 缺失`)
        allExists = false
      }
    })
    
    return allExists
  } catch (error) {
    console.log('❌ 依赖检查失败')
    return false
  }
}

/**
 * 启动开发服务器
 */
function startDev(platform) {
  const platformMap = {
    'weixin': 'mp-weixin',
    'dingtalk': 'mp-dingtalk'
  }
  
  const targetPlatform = platformMap[platform] || 'mp-weixin'
  const command = `dev:${targetPlatform}`
  
  console.log(`🚀 启动${platform}小程序开发服务器...`)
  console.log(`执行命令: npm run ${command}`)
  
  const child = spawn('npm', ['run', command], {
    stdio: 'inherit',
    shell: true
  })
  
  child.on('error', (err) => {
    console.error('❌ 启动失败:', err.message)
    process.exit(1)
  })
  
  child.on('close', (code) => {
    console.log(`开发服务器退出，代码: ${code}`)
    process.exit(code)
  })
  
  // 处理进程退出
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭开发服务器...')
    child.kill('SIGINT')
  })
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 正在关闭开发服务器...')
    child.kill('SIGTERM')
  })
}

/**
 * 显示开发信息
 */
function showDevInfo(platform) {
  console.log('\n📋 开发信息:')
  console.log(`   平台: ${platform}小程序`)
  console.log(`   框架: uni-app + Vue3`)
  console.log(`   构建工具: Vite`)
  
  console.log('\n🛠️  开发工具:')
  if (platform === 'weixin') {
    console.log('   - 微信开发者工具')
    console.log('   - 导入项目目录: dist/dev/mp-weixin')
  } else if (platform === 'dingtalk') {
    console.log('   - 钉钉小程序开发者工具')
    console.log('   - 导入项目目录: dist/dev/mp-dingtalk')
  }
  
  console.log('\n🔧 调试工具:')
  console.log('   - 调试面板: pages/debug/debug')
  console.log('   - API测试: pages/debug/api-test')
  
  console.log('\n📖 文档:')
  console.log('   - 架构指南: docs/小程序架构重构指南.md')
  console.log('   - API文档: docs/API集成完成总结.md')
  
  console.log('\n' + '=' * 50)
}

/**
 * 主函数
 */
async function main() {
  try {
    // 验证平台参数
    if (!['weixin', 'dingtalk'].includes(platform)) {
      console.error('❌ 不支持的平台:', platform)
      console.log('支持的平台: weixin, dingtalk')
      console.log('使用方法: node scripts/start-miniprogram.js [weixin|dingtalk]')
      process.exit(1)
    }
    
    console.log(`🎯 目标平台: ${platform}小程序\n`)
    
    // 检查配置
    const configOk = checkConfig()
    const vue3Ok = checkVue3Config()
    const platformOk = checkPlatformConfig(platform)
    const depsOk = checkDependencies()
    
    if (!configOk || !vue3Ok || !platformOk || !depsOk) {
      console.log('\n❌ 配置检查失败，请修复上述问题后重试')
      console.log('💡 提示: 运行 npm run check:miniprogram 获取详细检查信息')
      process.exit(1)
    }
    
    console.log('\n✅ 所有配置检查通过！')
    
    // 显示开发信息
    showDevInfo(platform)
    
    // 启动开发服务器
    startDev(platform)
    
  } catch (error) {
    console.error('❌ 启动失败:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main()
