# 小程序架构重构指南

## 🎯 重构目标

基于uni-app框架，重新设计前端架构，主要面向微信小程序和钉钉小程序，确保Vue3正确加载和后台API正常调用。

## 🔧 架构优化

### 1. Vue3配置优化

#### manifest.json配置
```json
{
  "vueVersion": "3",
  "locale": "zh-Hans",
  "fallbackLocale": "zh-Hans",
  "mp-weixin": {
    "usingComponents": true,
    "setting": {
      "urlCheck": false,
      "es6": true,
      "enhance": true,
      "postcss": true,
      "minified": true
    }
  },
  "mp-dingtalk": {
    "usingComponents": true,
    "setting": {
      "urlCheck": false,
      "es6": true,
      "enhance": true,
      "postcss": true,
      "minified": true
    },
    "component2": true
  }
}
```

#### main.js优化
- ✅ 使用`createSSRApp`创建Vue3应用
- ✅ 针对小程序环境优化全局配置
- ✅ 统一的请求方法和错误处理
- ✅ 环境变量自动识别

### 2. 请求管理器重构

#### 新的请求架构 (`utils/request.js`)
```javascript
// 小程序专用请求管理器
import { get, post, put, del, upload } from '@/utils/request.js'

// 特性：
- 🔐 自动Token管理
- 🌐 网络状态检测
- 📱 小程序平台识别
- ⚠️ 统一错误处理
- 🔄 自动重试机制
```

#### API接口重构 (`api/index.js`)
```javascript
// 使用新的请求管理器
import { get, post, put, del, upload } from '@/utils/request.js'

const api = { get, post, put, delete: del, upload }

// 所有API接口统一使用新的请求方法
export const userApi = {
  login: (data) => api.post('/app/user/login', data),
  getUserInfo: () => api.get('/app/user/info')
}
```

### 3. 页面基类混入

#### 页面混入 (`mixins/page-mixin.js`)
提供统一的页面基础功能：

```javascript
// 功能特性：
- 📱 系统信息自动获取
- 👤 用户状态管理
- 🌐 网络状态监控
- 🔄 下拉刷新支持
- ⚠️ 统一错误处理
- 🎯 生命周期优化
```

#### 使用方式
```javascript
import pageMixin from '@/mixins/page-mixin.js'

export default {
  mixins: [pageMixin],
  
  methods: {
    // 页面特定初始化
    pageInit() {
      console.log('页面初始化')
    },
    
    // 刷新数据
    async onRefresh() {
      await this.loadData()
    }
  }
}
```

### 4. 全局应用管理

#### App.vue优化
```javascript
// 新增功能：
- 🚀 启动场景记录
- 🔄 小程序更新检查
- 🌐 网络状态监听
- ⚠️ 全局错误收集
- 📊 用户行为统计
```

## 📱 小程序特性支持

### 1. 微信小程序
```bash
# 开发
npm run dev:mp-weixin

# 构建
npm run build:mp-weixin
```

**特性支持**：
- ✅ 分享功能
- ✅ 分享到朋友圈
- ✅ 小程序更新管理
- ✅ 场景值识别
- ✅ 微信登录集成

### 2. 钉钉小程序
```bash
# 开发
npm run dev:mp-dingtalk

# 构建
npm run build:mp-dingtalk
```

**特性支持**：
- ✅ 钉钉登录集成
- ✅ 企业应用支持
- ✅ 组织架构集成
- ✅ 钉钉分享功能

## 🔗 API调用优化

### 1. 环境配置
```javascript
// 自动环境识别
const getBaseUrl = () => {
  // #ifdef MP-WEIXIN
  return 'https://api.example.com/kuma'  // 微信小程序
  // #endif
  
  // #ifdef MP-DINGTALK
  return 'https://api.example.com/kuma'  // 钉钉小程序
  // #endif
  
  // #ifdef H5
  return process.env.NODE_ENV === 'development' 
    ? 'http://localhost:8080/kuma' 
    : 'https://api.example.com/kuma'
  // #endif
}
```

### 2. 请求头优化
```javascript
// 自动添加平台标识
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${token}`,
  'Client-Type': 'wechat-miniprogram' // 或 'dingtalk-miniprogram'
}
```

### 3. 错误处理
```javascript
// 统一错误处理策略
- 401: 自动清除登录状态，跳转登录
- 403: 权限不足提示
- 500: 服务器错误提示
- 网络错误: 自动重试机制
```

## 🛠️ 开发工具

### 1. 调试工具
- **API测试页面**: `/pages/debug/api-test`
- **调试面板**: `/pages/debug/debug`
- **错误日志**: 全局错误收集和展示

### 2. 开发脚本
```bash
# 检查Vue3配置
npm run check:vue3

# 启动联调模式
npm run debug

# API接口测试
npm run test:api
```

## 📋 迁移检查清单

### 1. 配置文件
- [x] manifest.json - Vue3配置
- [x] package.json - 钉钉小程序脚本
- [x] vite.config.js - 构建配置
- [x] pages.json - 页面路由

### 2. 核心文件
- [x] main.js - Vue3应用创建
- [x] App.vue - 全局应用管理
- [x] utils/request.js - 请求管理器
- [x] api/index.js - API接口重构

### 3. 页面优化
- [x] mixins/page-mixin.js - 页面基类
- [x] pages/index/index.vue - 首页示例
- [ ] 其他页面逐步迁移

### 4. 功能测试
- [ ] Vue3组件正常渲染
- [ ] API请求正常响应
- [ ] 小程序特性正常工作
- [ ] 错误处理机制有效

## 🚀 启动步骤

### 1. 安装依赖
```bash
cd front
npm install
```

### 2. 检查配置
```bash
npm run check:vue3
```

### 3. 启动开发
```bash
# 微信小程序
npm run dev:mp-weixin

# 钉钉小程序
npm run dev:mp-dingtalk

# H5调试
npm run dev:h5
```

### 4. 测试功能
- 打开小程序开发者工具
- 导入项目目录
- 测试页面加载和API调用

## 🔍 问题排查

### 1. Vue3加载问题
**检查项**：
- manifest.json中`vueVersion: "3"`
- main.js使用`createSSRApp`
- 组件语法使用Vue3规范

### 2. API调用问题
**检查项**：
- 网络请求域名配置
- 请求头格式正确
- 错误处理逻辑完整

### 3. 小程序特性问题
**检查项**：
- 平台条件编译正确
- 小程序API使用规范
- 权限配置完整

## 📞 技术支持

**重构状态**: ✅ 架构优化完成  
**Vue3支持**: ✅ 完全兼容  
**小程序支持**: ✅ 微信+钉钉  
**API集成**: ✅ 统一管理  

现在可以开始小程序开发和测试了！🎉

---

**更新时间**: 2024-03-22  
**版本**: v2.0.0  
**架构**: uni-app + Vue3 + 小程序优化
