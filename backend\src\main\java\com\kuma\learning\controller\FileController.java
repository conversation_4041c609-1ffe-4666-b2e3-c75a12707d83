package com.kuma.learning.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件访问控制器
 * <AUTHOR>
 * @since 2024-03-22
 */
@Slf4j
@RestController
@RequestMapping("/files")
public class FileController {
    
    @Value("${kuma.file.upload-path:/data/uploads/kuma/}")
    private String uploadPath;
    
    /**
     * 访问上传的文件
     * @param filePath 文件路径
     * @param request HTTP请求
     * @return 文件资源
     */
    @GetMapping("/**")
    public ResponseEntity<Resource> getFile(HttpServletRequest request) {
        try {
            // 获取文件路径
            String requestPath = request.getRequestURI();
            String filePath = requestPath.substring("/files/".length());
            
            // 构建完整文件路径
            Path fullPath = Paths.get(uploadPath, filePath);
            File file = fullPath.toFile();
            
            // 检查文件是否存在
            if (!file.exists() || !file.isFile()) {
                log.warn("文件不存在: {}", fullPath);
                return ResponseEntity.notFound().build();
            }
            
            // 检查文件是否在允许的目录内（安全检查）
            if (!isFileInAllowedDirectory(fullPath)) {
                log.warn("文件访问被拒绝: {}", fullPath);
                return ResponseEntity.badRequest().build();
            }
            
            // 创建文件资源
            Resource resource = new FileSystemResource(file);
            
            // 确定文件类型
            String contentType = determineContentType(file);
            
            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(contentType));
            headers.setContentLength(file.length());
            
            // 对于音频文件，添加支持范围请求的头
            if (contentType.startsWith("audio/")) {
                headers.add("Accept-Ranges", "bytes");
            }
            
            log.info("文件访问成功: {}", fullPath);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("文件访问失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 检查文件是否在允许的目录内
     */
    private boolean isFileInAllowedDirectory(Path filePath) {
        try {
            Path uploadDir = Paths.get(uploadPath).toRealPath();
            Path realFilePath = filePath.toRealPath();
            return realFilePath.startsWith(uploadDir);
        } catch (Exception e) {
            log.error("检查文件路径失败", e);
            return false;
        }
    }
    
    /**
     * 确定文件的Content-Type
     */
    private String determineContentType(File file) {
        try {
            String contentType = Files.probeContentType(file.toPath());
            if (contentType != null) {
                return contentType;
            }
        } catch (Exception e) {
            log.warn("无法确定文件类型: {}", file.getName(), e);
        }
        
        // 根据文件扩展名确定类型
        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".wav")) {
            return "audio/wav";
        } else if (fileName.endsWith(".mp3")) {
            return "audio/mpeg";
        } else if (fileName.endsWith(".m4a")) {
            return "audio/mp4";
        } else if (fileName.endsWith(".aac")) {
            return "audio/aac";
        } else if (fileName.endsWith(".pcm")) {
            return "audio/pcm";
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".png")) {
            return "image/png";
        } else if (fileName.endsWith(".gif")) {
            return "image/gif";
        } else {
            return "application/octet-stream";
        }
    }
}
