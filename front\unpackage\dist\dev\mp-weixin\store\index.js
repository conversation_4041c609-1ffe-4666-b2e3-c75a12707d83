"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const store_modules_user = require("./modules/user.js");
const store_modules_course = require("./modules/course.js");
const store_modules_checkin = require("./modules/checkin.js");
const store_modules_practice = require("./modules/practice.js");
const store = common_vendor.createStore({
  modules: {
    user: store_modules_user.user,
    course: store_modules_course.course,
    checkin: store_modules_checkin.checkin,
    practice: store_modules_practice.practice
  },
  state: {
    // 全局状态
    systemInfo: {},
    networkType: "unknown",
    isOnline: true
  },
  mutations: {
    SET_SYSTEM_INFO(state, systemInfo) {
      state.systemInfo = systemInfo;
    },
    SET_NETWORK_TYPE(state, networkType) {
      state.networkType = networkType;
    },
    SET_ONLINE_STATUS(state, isOnline) {
      state.isOnline = isOnline;
    }
  },
  actions: {
    // 初始化应用
    initApp(_0) {
      return __async(this, arguments, function* ({ commit, dispatch }) {
        try {
          const systemInfo = common_vendor.index.getSystemInfoSync();
          commit("SET_SYSTEM_INFO", systemInfo);
          common_vendor.index.onNetworkStatusChange((res) => {
            commit("SET_NETWORK_TYPE", res.networkType);
            commit("SET_ONLINE_STATUS", res.isConnected);
          });
          yield dispatch("user/checkLoginStatus");
          common_vendor.index.__f__("log", "at store/index.js:53", "应用初始化完成");
        } catch (error) {
          common_vendor.index.__f__("error", "at store/index.js:55", "应用初始化失败:", error);
        }
      });
    }
  },
  getters: {
    // 是否为小程序环境
    isMiniProgram: (state) => {
      return state.systemInfo.uniPlatform && state.systemInfo.uniPlatform.startsWith("mp-");
    },
    // 是否为App环境
    isApp: (state) => {
      return state.systemInfo.uniPlatform === "app";
    },
    // 是否为H5环境
    isH5: (state) => {
      return state.systemInfo.uniPlatform === "h5";
    },
    // 状态栏高度
    statusBarHeight: (state) => {
      return state.systemInfo.statusBarHeight || 0;
    },
    // 导航栏高度
    navBarHeight: (state, getters) => {
      if (getters.isMiniProgram) {
        return 44;
      }
      return 44;
    }
  }
});
exports.store = store;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/index.js.map
