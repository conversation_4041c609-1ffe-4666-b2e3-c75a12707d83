server:
  port: 8080
  servlet:
    context-path: /kuma
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: kuma-japanese-learning
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *************************************************************************************************************************
      username: root
      password: password
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin123
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    jedis:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true

  # Jackson配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: org.jeecg.modules.kuma.entity
  global-config:
    db-config:
      id-type: ASSIGN_ID
      table-underline: true
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志配置
logging:
  level:
    org.jeecg.modules.kuma: DEBUG
    org.springframework.web: INFO
    org.mybatis: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/kuma-backend.log
    max-size: 100MB
    max-history: 30

# Swagger配置
swagger:
  enabled: true
  title: 口语便利店小程序API
  description: 日语学习打卡小程序后端接口文档
  version: 1.0.0
  base-package: org.jeecg.modules.kuma.controller
  contact:
    name: Kuma Team
    email: <EMAIL>

# 业务配置
kuma:
  # 系统标识
  sys-sign: KUMA
  
  # 文件上传配置
  file:
    upload-path: /data/uploads/kuma/
    domain: https://api.example.com
    allowed-types: jpg,jpeg,png,gif,mp3,wav,m4a
    max-size: 10485760  # 10MB
  
  # AI语音评测配置
  ai:
    provider: xunfei
    appid: ${AI_APPID:your_appid}
    secret: ${AI_SECRET:your_secret}
    url: https://ise-api.xfyun.cn/v2/open-ise
    timeout: 30000

# 科大讯飞语音评测配置
xunfei:
  ise:
    app-id: ${XUNFEI_APP_ID:your_app_id}
    api-key: ${XUNFEI_API_KEY:your_api_key}
    api-secret: ${XUNFEI_API_SECRET:your_api_secret}
    host-url: wss://ise-api.xfyun.cn/v2/open-ise
    connect-timeout: 30000
    read-timeout: 30000
  
  # 微信支付配置
  payment:
    wechat:
      app-id: ${WECHAT_APPID:your_appid}
      mch-id: ${WECHAT_MCHID:your_mchid}
      api-key: ${WECHAT_API_KEY:your_api_key}
      cert-path: ${WECHAT_CERT_PATH:classpath:cert/apiclient_cert.p12}
      notify-url: ${WECHAT_NOTIFY_URL:https://api.example.com/kuma/payment/notify}
  
  # 阿里云OSS配置
  oss:
    enabled: false
    endpoint: ${OSS_ENDPOINT:oss-cn-hangzhou.aliyuncs.com}
    access-key-id: ${OSS_ACCESS_KEY_ID:your_access_key}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET:your_secret}
    bucket-name: ${OSS_BUCKET_NAME:kuma-files}
    domain: ${OSS_DOMAIN:https://kuma-files.oss-cn-hangzhou.aliyuncs.com}
  
  # 业务规则配置
  business:
    # 每月补打卡次数
    makeup-chances-per-month: 5
    # 打卡奖励积分
    checkin-reward-points: 10
    # 练习奖励积分
    practice-reward-points: 5
    # 课程解锁规则 sequential-顺序解锁 free-自由选择
    course-unlock-rule: sequential
    # VIP年费价格
    vip-price-yearly: 99.00
    # 连续打卡里程碑
    checkin-milestones: 7,15,30,50,100,200,365

# 环境配置
---
spring:
  profiles: dev
  datasource:
    druid:
      url: *****************************************************************************************************************************
  redis:
    host: localhost
    port: 6379

logging:
  level:
    root: INFO
    org.jeecg.modules.kuma: DEBUG

---
spring:
  profiles: test
  datasource:
    druid:
      url: ****************************************************************************************************************************
  redis:
    host: test-redis
    port: 6379

---
spring:
  profiles: prod
  datasource:
    druid:
      url: ***********************************************************************************************************************
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}

logging:
  level:
    root: WARN
    org.jeecg.modules.kuma: INFO
  file:
    name: /var/log/kuma/kuma-backend.log
