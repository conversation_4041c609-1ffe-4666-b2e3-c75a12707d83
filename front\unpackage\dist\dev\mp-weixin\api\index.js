"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
const config_api = require("../config/api.js");
const api = {
  get: utils_request.get,
  post: utils_request.post,
  put: utils_request.put,
  delete: utils_request.del,
  upload: utils_request.upload
};
const userApi = {
  // 登录 - jeecg-boot标准登录
  login: (data) => api.post(config_api.API_ENDPOINTS.USER.LOGIN, data),
  // 退出登录 - jeecg-boot标准登出
  logout: () => api.post(config_api.API_ENDPOINTS.USER.LOGOUT),
  // 获取用户基础信息 - jeecg-boot标准接口
  getUserInfo: () => api.get(config_api.API_ENDPOINTS.USER.INFO),
  // 获取部门信息 - jeecg-boot标准接口
  getDepartInfo: () => api.get(config_api.API_ENDPOINTS.USER.DEPART_INFO),
  // 修改密码 - jeecg-boot标准接口
  updatePassword: (data) => api.put(config_api.API_ENDPOINTS.USER.UPDATE_PASSWORD, data),
  // 上传头像 - jeecg-boot标准文件上传
  uploadAvatar: (filePath) => api.upload(config_api.API_ENDPOINTS.USER.UPLOAD_AVATAR, filePath, "file"),
  // 获取用户扩展信息 - 自定义接口
  getUserProfile: () => api.get(config_api.API_ENDPOINTS.USER.PROFILE),
  // 更新用户扩展信息 - 自定义接口
  updateUserProfile: (data) => api.put(config_api.API_ENDPOINTS.USER.UPDATE_PROFILE, data),
  // 用户签到 - 自定义接口
  checkin: () => api.post(config_api.API_ENDPOINTS.USER.CHECKIN),
  // 获取签到记录 - 自定义接口
  getCheckinHistory: (params) => api.get(config_api.API_ENDPOINTS.USER.CHECKIN_HISTORY, params),
  // 获取学习统计 - 自定义接口
  getStudyStats: () => api.get(config_api.API_ENDPOINTS.USER.STATISTICS),
  // 验证码相关接口
  getCaptcha: (key = "1629428467008") => {
    const url = config_api.buildApiUrl(config_api.API_ENDPOINTS.USER.CAPTCHA, { key });
    common_vendor.index.__f__("log", "at api/index.js:56", "📤 请求验证码:", url);
    return api.get(url);
  },
  checkCaptcha: (data) => {
    common_vendor.index.__f__("log", "at api/index.js:60", "📤 验证验证码:", data);
    return api.post(config_api.API_ENDPOINTS.USER.CHECK_CAPTCHA, data);
  }
};
const courseApi = {
  // 获取课程列表
  getCourseList: (params) => api.get("/app/course/list", params),
  // 获取课程详情
  getCourseDetail: (courseId) => api.get(`/app/course/${courseId}`),
  // 获取课时列表
  getLessonList: (courseId, params) => api.get(`/app/course/${courseId}/lessons`, params),
  // 获取课时详情
  getLessonDetail: (lessonId) => api.get(`/app/lesson/${lessonId}`),
  // 开始学习课时
  startLesson: (lessonId) => api.post(`/app/lesson/${lessonId}/start`),
  // 完成课时学习
  completeLesson: (lessonId, data) => api.post(`/app/lesson/${lessonId}/complete`, data),
  // 收藏/取消收藏课程
  toggleFavorite: (courseId, isFavorite) => {
    return isFavorite ? api.delete(`/app/course/${courseId}/favorite`) : api.post(`/app/course/${courseId}/favorite`);
  }
};
const leaderboardApi = {
  // 获取积分排行榜
  getPointsRanking: (params) => api.get("/app/leaderboard/points", params),
  // 获取学习时长排行榜
  getStudyTimeRanking: (params) => api.get("/app/leaderboard/studytime", params),
  // 获取连续签到排行榜
  getCheckinRanking: (params) => api.get("/app/leaderboard/checkin", params)
};
const practiceApi = {
  // 提交录音练习（文件上传）
  submitPractice: (data) => api.post("/app/practice/submit", data),
  // 提交录音练习（Base64）
  submitPracticeBase64: (data) => api.post("/app/practice/submitBase64", data),
  // 获取练习历史
  getHistory: (params) => api.get("/app/practice/history", params),
  // 获取练习统计
  getStatistics: () => api.get("/app/practice/statistics"),
  // 获取支持的语言类型
  getSupportedLanguages: () => api.get("/app/practice/supportedLanguages"),
  // 获取支持的题型
  getSupportedCategories: (language) => api.get("/app/practice/supportedCategories", { language }),
  // 获取评测配置
  getEvaluationConfig: () => api.get("/app/practice/evaluationConfig")
};
exports.courseApi = courseApi;
exports.leaderboardApi = leaderboardApi;
exports.practiceApi = practiceApi;
exports.userApi = userApi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/index.js.map
