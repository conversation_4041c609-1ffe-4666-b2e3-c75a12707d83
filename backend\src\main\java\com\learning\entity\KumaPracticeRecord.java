package com.learning.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: kuma_practice_record
 * @Author: jeecg-boot
 * @Date:   2025-06-08
 * @Version: V1.0
 */
@Data
@TableName("kuma_practice_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="kuma_practice_record对象", description="练习记录表")
public class KumaPracticeRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**系统标识*/
	@Excel(name = "系统标识", width = 15)
    @ApiModelProperty(value = "系统标识")
    private String sysSign;
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;
	/**课程ID*/
	@Excel(name = "课程ID", width = 15)
    @ApiModelProperty(value = "课程ID")
    private String courseId;
	/**课时ID*/
	@Excel(name = "课时ID", width = 15)
    @ApiModelProperty(value = "课时ID")
    private String lessonId;
	/**练习句子*/
	@Excel(name = "练习句子", width = 15)
    @ApiModelProperty(value = "练习句子")
    private String sentenceText;
	/**录音文件URL*/
	@Excel(name = "录音文件URL", width = 15)
    @ApiModelProperty(value = "录音文件URL")
    private String audioUrl;
	/**录音时长(秒)*/
	@Excel(name = "录音时长", width = 15)
    @ApiModelProperty(value = "录音时长(秒)")
    private Integer audioDuration;
	/**AI评分总分*/
	@Excel(name = "AI评分总分", width = 15)
    @ApiModelProperty(value = "AI评分总分")
    private Integer totalScore;
	/**发音得分*/
	@Excel(name = "发音得分", width = 15)
    @ApiModelProperty(value = "发音得分")
    private Integer pronunciationScore;
	/**流利度得分*/
	@Excel(name = "流利度得分", width = 15)
    @ApiModelProperty(value = "流利度得分")
    private Integer fluencyScore;
	/**语调得分*/
	@Excel(name = "语调得分", width = 15)
    @ApiModelProperty(value = "语调得分")
    private Integer toneScore;
	/**AI反馈*/
	@Excel(name = "AI反馈", width = 15)
    @ApiModelProperty(value = "AI反馈")
    private String aiFeedback;
	/**AI评测详细数据*/
	@Excel(name = "AI评测详细数据", width = 15)
    @ApiModelProperty(value = "AI评测详细数据")
    private String evaluationData;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**删除标志（0-未删除，1-已删除）*/
	@Excel(name = "删除标志", width = 15)
    @ApiModelProperty(value = "删除标志（0-未删除，1-已删除）")
    @TableLogic
    private Integer delFlag;
}
