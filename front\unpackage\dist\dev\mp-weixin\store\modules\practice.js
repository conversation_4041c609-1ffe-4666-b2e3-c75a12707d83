"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const practice = {
  namespaced: true,
  state: {
    // 当前练习课程
    currentPractice: null,
    // 练习历史记录
    practiceHistory: [],
    // 当前练习进度
    currentProgress: {
      courseId: null,
      currentSentence: 0,
      totalSentences: 0,
      completedSentences: []
    },
    // AI评分结果
    aiScoreResult: {
      totalScore: 0,
      pronunciationScore: 0,
      fluencyScore: 0,
      toneScore: 0,
      feedback: ""
    },
    // 录音状态
    recordingState: {
      isRecording: false,
      hasRecorded: false,
      audioPath: "",
      duration: 0
    },
    // 练习统计
    practiceStats: {
      totalPractices: 0,
      averageScore: 0,
      bestScore: 0,
      totalTime: 0,
      improvementRate: 0
    }
  },
  mutations: {
    // 设置当前练习
    SET_CURRENT_PRACTICE(state, practice2) {
      state.currentPractice = practice2;
    },
    // 设置练习历史
    SET_PRACTICE_HISTORY(state, history) {
      state.practiceHistory = history;
    },
    // 添加练习记录
    ADD_PRACTICE_RECORD(state, record) {
      state.practiceHistory.unshift(record);
    },
    // 设置练习进度
    SET_PRACTICE_PROGRESS(state, progress) {
      state.currentProgress = __spreadValues(__spreadValues({}, state.currentProgress), progress);
    },
    // 更新当前句子
    UPDATE_CURRENT_SENTENCE(state, sentenceIndex) {
      state.currentProgress.currentSentence = sentenceIndex;
    },
    // 标记句子完成
    MARK_SENTENCE_COMPLETED(state, sentenceIndex) {
      if (!state.currentProgress.completedSentences.includes(sentenceIndex)) {
        state.currentProgress.completedSentences.push(sentenceIndex);
      }
    },
    // 设置AI评分结果
    SET_AI_SCORE_RESULT(state, result) {
      state.aiScoreResult = __spreadValues(__spreadValues({}, state.aiScoreResult), result);
    },
    // 设置录音状态
    SET_RECORDING_STATE(state, recordingState) {
      state.recordingState = __spreadValues(__spreadValues({}, state.recordingState), recordingState);
    },
    // 重置录音状态
    RESET_RECORDING_STATE(state) {
      state.recordingState = {
        isRecording: false,
        hasRecorded: false,
        audioPath: "",
        duration: 0
      };
    },
    // 设置练习统计
    SET_PRACTICE_STATS(state, stats) {
      state.practiceStats = __spreadValues(__spreadValues({}, state.practiceStats), stats);
    },
    // 重置练习进度
    RESET_PRACTICE_PROGRESS(state) {
      state.currentProgress = {
        courseId: null,
        currentSentence: 0,
        totalSentences: 0,
        completedSentences: []
      };
    }
  },
  actions: {
    // 开始练习
    startPractice(_0, _1) {
      return __async(this, arguments, function* ({ commit }, { courseId, sentences }) {
        try {
          commit("SET_PRACTICE_PROGRESS", {
            courseId,
            currentSentence: 0,
            totalSentences: sentences.length,
            completedSentences: []
          });
          commit("RESET_RECORDING_STATE");
          return { success: true };
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/practice.js:130", "开始练习失败:", error);
          return { success: false, message: "开始练习失败" };
        }
      });
    },
    // 提交录音进行AI评分
    submitRecording(_0, _1) {
      return __async(this, arguments, function* ({ commit, rootState }, { courseId, sentenceText, audioFile }) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/practice/submit",
            method: "POST",
            data: {
              courseId,
              sentenceText,
              audioFile
            },
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            const scoreResult = response.data.data;
            commit("SET_AI_SCORE_RESULT", scoreResult);
            const practiceRecord = __spreadProps(__spreadValues({
              id: Date.now(),
              courseId,
              sentenceText
            }, scoreResult), {
              createdTime: (/* @__PURE__ */ new Date()).toISOString()
            });
            commit("ADD_PRACTICE_RECORD", practiceRecord);
            return { success: true, data: scoreResult };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/practice.js:170", "提交录音失败:", error);
          return { success: false, message: "评分失败，请重试" };
        }
      });
    },
    // 下一句练习
    nextSentence({ commit, state }) {
      const nextIndex = state.currentProgress.currentSentence + 1;
      if (nextIndex < state.currentProgress.totalSentences) {
        commit("UPDATE_CURRENT_SENTENCE", nextIndex);
        commit("RESET_RECORDING_STATE");
        return { hasNext: true, currentIndex: nextIndex };
      } else {
        return { hasNext: false, completed: true };
      }
    },
    // 完成当前句子
    completeSentence({ commit, state }) {
      const currentIndex = state.currentProgress.currentSentence;
      commit("MARK_SENTENCE_COMPLETED", currentIndex);
    },
    // 加载练习历史
    loadPracticeHistory(_0, _1) {
      return __async(this, arguments, function* ({ commit, rootState }, { courseId, page = 1, size = 20 }) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/practice/history",
            method: "GET",
            data: { courseId, page, size },
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            commit("SET_PRACTICE_HISTORY", response.data.data.list);
            return { success: true, data: response.data.data };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/practice.js:212", "加载练习历史失败:", error);
          return { success: false, message: "加载失败，请重试" };
        }
      });
    },
    // 加载练习统计
    loadPracticeStats(_0) {
      return __async(this, arguments, function* ({ commit, rootState }) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/practice/statistics",
            method: "GET",
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            commit("SET_PRACTICE_STATS", response.data.data);
            return { success: true, data: response.data.data };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/practice.js:233", "加载练习统计失败:", error);
        }
      });
    },
    // 重新录音
    retryRecording({ commit }) {
      commit("RESET_RECORDING_STATE");
      commit("SET_AI_SCORE_RESULT", {
        totalScore: 0,
        pronunciationScore: 0,
        fluencyScore: 0,
        toneScore: 0,
        feedback: ""
      });
    },
    // 完成练习
    completePractice(_0) {
      return __async(this, arguments, function* ({ commit, state, rootState }) {
        try {
          const { courseId, completedSentences, totalSentences } = state.currentProgress;
          const completionRate = completedSentences.length / totalSentences * 100;
          const response = yield common_vendor.index.request({
            url: "/api/v1/practice/complete",
            method: "POST",
            data: {
              courseId,
              completedSentences: completedSentences.length,
              totalSentences,
              completionRate
            },
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            commit("RESET_PRACTICE_PROGRESS");
            return { success: true, data: response.data.data };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/practice.js:276", "完成练习失败:", error);
          return { success: false, message: "完成练习失败" };
        }
      });
    }
  },
  getters: {
    // 获取当前练习进度百分比
    practiceProgressPercent: (state) => {
      if (state.currentProgress.totalSentences === 0)
        return 0;
      return Math.round(state.currentProgress.completedSentences.length / state.currentProgress.totalSentences * 100);
    },
    // 检查当前句子是否已完成
    isCurrentSentenceCompleted: (state) => {
      return state.currentProgress.completedSentences.includes(state.currentProgress.currentSentence);
    },
    // 获取练习完成状态
    isPracticeCompleted: (state) => {
      return state.currentProgress.completedSentences.length === state.currentProgress.totalSentences;
    },
    // 获取最近的练习记录
    recentPracticeRecords: (state) => (limit = 10) => {
      return state.practiceHistory.slice(0, limit);
    },
    // 获取指定课程的练习记录
    getPracticeRecordsByCourse: (state) => (courseId) => {
      return state.practiceHistory.filter((record) => record.courseId === courseId);
    },
    // 计算平均分数
    averageScore: (state) => {
      if (state.practiceHistory.length === 0)
        return 0;
      const totalScore = state.practiceHistory.reduce((sum, record) => sum + record.totalScore, 0);
      return Math.round(totalScore / state.practiceHistory.length);
    },
    // 获取最高分数
    bestScore: (state) => {
      if (state.practiceHistory.length === 0)
        return 0;
      return Math.max(...state.practiceHistory.map((record) => record.totalScore));
    },
    // 获取进步率
    improvementRate: (state) => {
      if (state.practiceHistory.length < 2)
        return 0;
      const recent = state.practiceHistory.slice(0, 5);
      const older = state.practiceHistory.slice(5, 10);
      if (older.length === 0)
        return 0;
      const recentAvg = recent.reduce((sum, r) => sum + r.totalScore, 0) / recent.length;
      const olderAvg = older.reduce((sum, r) => sum + r.totalScore, 0) / older.length;
      return Math.round((recentAvg - olderAvg) / olderAvg * 100);
    }
  }
};
exports.practice = practice;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/practice.js.map
