package com.learning.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.learning.entity.KumaLearningRecord;
import com.learning.service.IKumaLearningRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = "APP-学习记录管理")
@RestController
@RequestMapping("/app/learning/record")
public class LearningRecordController {

    @Autowired
    private IKumaLearningRecordService recordService;

    @AutoLog(value = "学习记录-分页列表查询")
    @ApiOperation(value = "学习记录-分页列表查询", notes = "学习记录-分页列表查询")
    @GetMapping("/list")
    public Result<IPage<KumaLearningRecord>> queryPageList(
            @RequestParam(name = "userId") String userId,
            @RequestParam(name = "courseId", required = false) String courseId,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        
        Page<KumaLearningRecord> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<KumaLearningRecord> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        queryWrapper.eq(KumaLearningRecord::getUserId, userId);
        if (courseId != null) {
            queryWrapper.eq(KumaLearningRecord::getCourseId, courseId);
        }
        queryWrapper.orderByDesc(KumaLearningRecord::getUpdateTime);
        
        IPage<KumaLearningRecord> pageList = recordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog(value = "学习记录-添加或更新")
    @ApiOperation(value = "学习记录-添加或更新", notes = "学习记录-添加或更新")
    @PostMapping("/save")
    public Result<String> save(@RequestBody KumaLearningRecord record) {
        // 检查是否已存在记录
        LambdaQueryWrapper<KumaLearningRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaLearningRecord::getUserId, record.getUserId())
                .eq(KumaLearningRecord::getCourseId, record.getCourseId())
                .eq(KumaLearningRecord::getLessonId, record.getLessonId());
        
        KumaLearningRecord existingRecord = recordService.getOne(queryWrapper);
        if (existingRecord != null) {
            // 更新记录
            record.setId(existingRecord.getId());
            recordService.updateById(record);
            return Result.OK("更新成功！");
        } else {
            // 新增记录
            recordService.save(record);
            return Result.OK("添加成功！");
        }
    }

    @AutoLog(value = "学习记录-获取课程学习进度")
    @ApiOperation(value = "学习记录-获取课程学习进度", notes = "学习记录-获取课程学习进度")
    @GetMapping("/progress")
    public Result<Integer> getCourseProgress(
            @RequestParam(name = "userId") String userId,
            @RequestParam(name = "courseId") String courseId) {
        
        LambdaQueryWrapper<KumaLearningRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaLearningRecord::getUserId, userId)
                .eq(KumaLearningRecord::getCourseId, courseId);
        
        // 计算平均进度
        double avgProgress = recordService.list(queryWrapper).stream()
                .mapToInt(KumaLearningRecord::getProgress)
                .average()
                .orElse(0.0);
        
        return Result.OK((int) avgProgress);
    }
} 