/**
 * 小程序专用入口文件
 * 解决动态导入和环境变量问题
 */

import { createSSRApp } from 'vue'
import App from './App.vue'

export function createApp() {
  const app = createSSRApp(App)

  // 小程序环境配置 - 使用统一API配置
  import { getApiBaseUrl } from '@/config/api.js'

  const baseUrl = getApiBaseUrl()
  const isDev = baseUrl.includes('localhost')

  app.config.globalProperties.$baseUrl = baseUrl
  app.config.globalProperties.$isDev = isDev

  // 全局错误处理
  app.config.errorHandler = (error, instance, info) => {
    console.error('Vue全局错误:', error, info)
    
    // 记录错误到全局应用
    const globalApp = getApp()
    if (globalApp && globalApp.addError) {
      globalApp.addError({
        type: 'vue_error',
        message: error.message,
        stack: error.stack,
        info: info,
        timestamp: Date.now()
      })
    }
  }

  // 全局请求方法 - 小程序优化版
  app.config.globalProperties.$request = (options) => {
    return new Promise((resolve, reject) => {
      const token = uni.getStorageSync('token')
      
      // 构建请求头
      const header = {
        'Content-Type': 'application/json',
        ...options.header
      }
      
      if (token) {
        header['Authorization'] = `Bearer ${token}`
      }
      
      // 添加小程序标识
      // #ifdef MP-WEIXIN
      header['Client-Type'] = 'wechat-miniprogram'
      // #endif
      
      // #ifdef MP-DINGTALK
      header['Client-Type'] = 'dingtalk-miniprogram'
      // #endif

      const requestUrl = options.url.startsWith('http') 
        ? options.url 
        : baseUrl + options.url

      console.log('小程序请求:', {
        url: requestUrl,
        method: options.method || 'GET',
        data: options.data,
        header
      })

      uni.request({
        url: requestUrl,
        method: options.method || 'GET',
        data: options.data || {},
        header,
        timeout: 10000,
        success: (res) => {
          console.log('请求成功:', res)
          
          if (res.statusCode === 200) {
            if (res.data && res.data.success !== false) {
              resolve(res.data)
            } else {
              const errorMsg = res.data?.message || '请求失败'
              uni.showToast({
                title: errorMsg,
                icon: 'none'
              })
              reject(new Error(errorMsg))
            }
          } else if (res.statusCode === 401) {
            // token过期处理
            uni.removeStorageSync('token')
            uni.removeStorageSync('userInfo')
            
            // 清除全局状态
            const globalApp = getApp()
            if (globalApp && globalApp.clearLoginInfo) {
              globalApp.clearLoginInfo()
            }
            
            uni.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none'
            })
            
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/index/index'
              })
            }, 2000)
            
            reject(new Error('登录已过期'))
          } else {
            const errorMsg = `请求失败: ${res.statusCode}`
            uni.showToast({
              title: errorMsg,
              icon: 'none'
            })
            reject(new Error(errorMsg))
          }
        },
        fail: (err) => {
          console.error('请求失败:', err)
          
          let errorMessage = '网络请求失败'
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMessage = '请求超时，请检查网络连接'
            } else if (err.errMsg.includes('fail')) {
              errorMessage = '网络连接失败，请检查网络设置'
            }
          }
          
          uni.showToast({
            title: errorMessage,
            icon: 'none'
          })
          
          reject(new Error(errorMessage))
        }
      })
    })
  }

  // 全局工具方法
  app.config.globalProperties.$utils = {
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hour = date.getHours().toString().padStart(2, '0')
      const minute = date.getMinutes().toString().padStart(2, '0')
      return `${year}-${month}-${day} ${hour}:${minute}`
    },
    
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    
    // 存储管理
    setStorage(key, value) {
      try {
        uni.setStorageSync(key, JSON.stringify(value))
      } catch (e) {
        console.error('存储失败:', e)
      }
    },
    
    getStorage(key) {
      try {
        const value = uni.getStorageSync(key)
        return value ? JSON.parse(value) : null
      } catch (e) {
        console.error('读取存储失败:', e)
        return null
      }
    },
    
    removeStorage(key) {
      try {
        uni.removeStorageSync(key)
      } catch (e) {
        console.error('删除存储失败:', e)
      }
    },
    
    // 权限检查
    checkPermission(scope) {
      return new Promise((resolve, reject) => {
        uni.getSetting({
          success: (res) => {
            if (res.authSetting[scope]) {
              resolve(true)
            } else {
              uni.authorize({
                scope,
                success: () => resolve(true),
                fail: () => reject(false)
              })
            }
          },
          fail: () => reject(false)
        })
      })
    },
    
    // 显示提示
    showToast(title, icon = 'none', duration = 2000) {
      uni.showToast({
        title,
        icon,
        duration
      })
    },
    
    // 显示加载
    showLoading(title = '加载中...') {
      uni.showLoading({
        title,
        mask: true
      })
    },
    
    // 隐藏加载
    hideLoading() {
      uni.hideLoading()
    }
  }

  return {
    app
  }
}
