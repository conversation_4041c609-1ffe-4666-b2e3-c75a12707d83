package org.jeecg.modules.kuma.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 课程表
 * <AUTHOR>
 * @since 2024-03-22
 */
@Data
@TableName("kuma_course")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "kuma_course对象", description = "课程表")
public class KumaCourse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**课程标题*/
    @Excel(name = "课程标题", width = 15)
    @ApiModelProperty(value = "课程标题")
    private String title;

    /**课程描述*/
    @Excel(name = "课程描述", width = 15)
    @ApiModelProperty(value = "课程描述")
    private String description;

    /**课程级别*/
    @Excel(name = "课程级别", width = 15, dicCode = "kuma_course_level")
    @Dict(dicCode = "kuma_course_level")
    @ApiModelProperty(value = "课程级别：beginner,intermediate,advanced,professional")
    private String level;

    /**课程序号*/
    @Excel(name = "课程序号", width = 15)
    @ApiModelProperty(value = "课程序号")
    private Integer lessonNumber;

    /**课程时长(秒)*/
    @Excel(name = "课程时长", width = 15)
    @ApiModelProperty(value = "课程时长(秒)")
    private Integer duration;

    /**音频文件URL*/
    @Excel(name = "音频文件URL", width = 15)
    @ApiModelProperty(value = "音频文件URL")
    private String audioUrl;

    /**日文原文*/
    @Excel(name = "日文原文", width = 15)
    @ApiModelProperty(value = "日文原文")
    private String transcript;

    /**中文翻译*/
    @Excel(name = "中文翻译", width = 15)
    @ApiModelProperty(value = "中文翻译")
    private String translation;

    /**重点词汇*/
    @Excel(name = "重点词汇", width = 15)
    @ApiModelProperty(value = "重点词汇")
    private String vocabulary;

    /**学习要点*/
    @Excel(name = "学习要点", width = 15)
    @ApiModelProperty(value = "学习要点")
    private String studyTips;

    /**是否免费*/
    @Excel(name = "是否免费", width = 15, dicCode = "kuma_is_free")
    @Dict(dicCode = "kuma_is_free")
    @ApiModelProperty(value = "是否免费 0-付费 1-免费")
    private Integer isFree;

    /**课程价格*/
    @Excel(name = "课程价格", width = 15)
    @ApiModelProperty(value = "课程价格")
    private BigDecimal price;

    /**排序*/
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    /**状态*/
    @Excel(name = "状态", width = 15, dicCode = "kuma_course_status")
    @Dict(dicCode = "kuma_course_status")
    @ApiModelProperty(value = "状态 0-下架 1-上架")
    private Integer status;

    /**系统标识*/
    @Excel(name = "系统标识", width = 15)
    @ApiModelProperty(value = "系统标识")
    private String sysSign;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**删除标志*/
    @ApiModelProperty(value = "删除标志 0-正常 1-删除")
    private Integer delFlag;
}
