"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 0,
      pageTitle: "",
      level: "",
      loading: true,
      completedCount: 0,
      totalCount: 0,
      progressPercent: 0,
      courseList: [],
      // 默认课程数据（加载失败时使用）
      defaultCourseList: [
        {
          id: 1,
          title: "第1课：自己紹介",
          description: "自我介绍基础对话",
          duration: 5,
          isFree: true,
          price: 0,
          progress: 100,
          status: "completed",
          iconBg: "#dcfce7",
          iconColor: "#16a34a",
          isCurrent: false
        },
        {
          id: 2,
          title: "第2课：挨拶",
          description: "日常问候用语",
          duration: 4,
          isFree: true,
          price: 0,
          progress: 100,
          status: "completed",
          iconBg: "#dbeafe",
          iconColor: "#2563eb",
          isCurrent: false
        },
        {
          id: 3,
          title: "第3课：買い物",
          description: "购物场景对话",
          duration: 6,
          isFree: false,
          price: 9.9,
          progress: 0,
          status: "locked",
          iconBg: "#fed7aa",
          iconColor: "#ea580c",
          isCurrent: false
        },
        {
          id: 4,
          title: "第4课：レストラン",
          description: "餐厅点餐对话",
          duration: 7,
          isFree: true,
          price: 0,
          progress: 45,
          status: "learning",
          iconBg: "#fce7f3",
          iconColor: "#ec4899",
          isCurrent: true
        },
        {
          id: 5,
          title: "第5课：交通",
          description: "交通出行用语",
          duration: 5,
          isFree: true,
          price: 0,
          progress: 0,
          status: "locked",
          iconBg: "#f3f4f6",
          iconColor: "#6b7280",
          isCurrent: false
        }
      ]
    };
  },
  onLoad(options) {
    this.level = options.level || "beginner";
    this.pageTitle = decodeURIComponent(options.title || "课程列表");
    this.checkStoredLevelInfo();
    this.initPage();
  },
  onShow() {
    this.checkStoredLevelInfo();
    this.loadCourseList();
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.loadCourseList().finally(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  methods: {
    initPage() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight;
    },
    // 检查存储的级别信息
    checkStoredLevelInfo() {
      try {
        const storedLevel = common_vendor.index.getStorageSync("selectedCourseLevel");
        if (storedLevel && storedLevel.timestamp) {
          const now = Date.now();
          const timeDiff = now - storedLevel.timestamp;
          if (timeDiff < 5 * 60 * 1e3) {
            this.level = storedLevel.level || this.level;
            this.pageTitle = storedLevel.title || this.pageTitle;
            common_vendor.index.__f__("log", "at pages/course/list.vue:214", "✅ 使用存储的级别信息:", {
              level: this.level,
              title: this.pageTitle
            });
            common_vendor.index.removeStorageSync("selectedCourseLevel");
          } else {
            common_vendor.index.__f__("log", "at pages/course/list.vue:222", "⚠️ 存储的级别信息已过期，清除");
            common_vendor.index.removeStorageSync("selectedCourseLevel");
          }
        }
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages/course/list.vue:227", "检查存储级别信息失败:", error);
      }
    },
    // 加载课程列表
    loadCourseList() {
      return __async(this, null, function* () {
        this.loading = true;
        try {
          common_vendor.index.__f__("log", "at pages/course/list.vue:236", "加载课程列表:", this.level);
          const response = yield api_index.courseApi.getCourseList({
            level: this.level,
            includeProgress: true
          });
          if (response.success && response.data.records) {
            this.courseList = this.formatCourseData(response.data.records);
            this.calculateProgress();
          } else {
            throw new Error(response.message || "获取课程列表失败");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/course/list.vue:251", "加载课程列表失败:", error);
        } finally {
          this.loading = false;
        }
      });
    },
    // 格式化课程数据
    formatCourseData(courseData) {
      return courseData.map((course) => ({
        id: course.id,
        title: course.title,
        description: course.description || course.subtitle,
        duration: course.duration || 5,
        isFree: course.isFree !== false,
        price: course.price || 0,
        progress: course.progress || 0,
        status: this.getCourseStatus(course),
        iconBg: this.getIconBackground(course.level),
        iconColor: this.getIconColor(course.level),
        isCurrent: course.isCurrent || false,
        lessonCount: course.lessonCount || 0,
        difficulty: course.difficulty || "beginner"
      }));
    },
    // 计算学习进度
    calculateProgress() {
      this.totalCount = this.courseList.length;
      this.completedCount = this.courseList.filter((course) => course.status === "completed").length;
      this.progressPercent = this.totalCount > 0 ? Math.round(this.completedCount / this.totalCount * 100) : 0;
    },
    // 获取课程状态
    getCourseStatus(course) {
      if (course.isCompleted)
        return "completed";
      if (course.progress > 0)
        return "learning";
      if (course.isLocked)
        return "locked";
      return "available";
    },
    // 获取图标背景色
    getIconBackground(level) {
      const colors = {
        beginner: "#dcfce7",
        intermediate: "#dbeafe",
        advanced: "#f3e8ff",
        professional: "#fed7aa"
      };
      return colors[level] || "#f3f4f6";
    },
    // 获取图标颜色
    getIconColor(level) {
      const colors = {
        beginner: "#16a34a",
        intermediate: "#2563eb",
        advanced: "#9333ea",
        professional: "#ea580c"
      };
      return colors[level] || "#6b7280";
    },
    goBack() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    },
    goToCourseDetail(course) {
      if (course.status === "locked" && !course.isFree) {
        this.showPurchaseDialog(course);
        return;
      }
      if (course.status === "locked") {
        common_vendor.index.showToast({
          title: "请先完成前面的课程",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/course/detail?id=${course.id}&title=${course.title}`
      });
    },
    showPurchaseDialog(course) {
      common_vendor.index.showModal({
        title: "购买课程",
        content: `该课程需要支付 ¥${course.price} 元，是否立即购买？`,
        confirmText: "立即购买",
        success: (res) => {
          if (res.confirm) {
            this.purchaseCourse(course);
          }
        }
      });
    },
    purchaseCourse(course) {
      common_vendor.index.__f__("log", "at pages/course/list.vue:364", "购买课程:", course.id);
      common_vendor.index.showToast({
        title: "支付功能开发中",
        icon: "none"
      });
    },
    getStatusIcon(course) {
      switch (course.status) {
        case "completed":
          return "icon-check-circle";
        case "learning":
          return "icon-play-circle";
        case "locked":
          return course.isFree ? "icon-lock" : "icon-lock";
        default:
          return "icon-lock";
      }
    },
    getStatusText(course) {
      switch (course.status) {
        case "completed":
          return "已完成";
        case "learning":
          return "进行中";
        case "locked":
          return course.isFree ? "完成前课解锁" : "高质量内容";
        default:
          return "未开始";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.statusBarHeight + "px",
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.t($data.pageTitle),
    d: common_vendor.t($data.completedCount),
    e: common_vendor.t($data.totalCount),
    f: common_vendor.t($data.progressPercent),
    g: $data.progressPercent + "%",
    h: common_vendor.f($data.courseList, (course, k0, i0) => {
      return common_vendor.e({
        a: course.iconColor,
        b: course.iconBg,
        c: common_vendor.t(course.title),
        d: common_vendor.t(course.description),
        e: course.isFree
      }, course.isFree ? {} : {
        f: common_vendor.t(course.price)
      }, {
        g: common_vendor.n($options.getStatusIcon(course)),
        h: common_vendor.t(course.duration),
        i: common_vendor.t($options.getStatusText(course)),
        j: course.progress + "%",
        k: !course.isFree ? 1 : "",
        l: course.isCurrent ? 1 : "",
        m: course.id,
        n: common_vendor.o(($event) => $options.goToCourseDetail(course), course.id)
      });
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-80d79958"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/course/list.js.map
