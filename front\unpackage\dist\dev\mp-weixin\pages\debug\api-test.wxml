<view class="api-test-container data-v-afa62ce9"><view class="header data-v-afa62ce9"><text class="title data-v-afa62ce9">🔗 API接口测试</text><text class="subtitle data-v-afa62ce9">前后端联调测试工具</text></view><view class="section data-v-afa62ce9"><view class="section-header data-v-afa62ce9"><text class="section-title data-v-afa62ce9">🌐 连接状态</text><view class="{{['status-indicator', 'data-v-afa62ce9', a && 'connected']}}"></view></view><view class="connection-info data-v-afa62ce9"><view class="info-item data-v-afa62ce9"><text class="label data-v-afa62ce9">后端地址</text><text class="value data-v-afa62ce9">{{b}}</text></view><view class="info-item data-v-afa62ce9"><text class="label data-v-afa62ce9">连接状态</text><text class="{{['value', 'data-v-afa62ce9', d && 'success', e && 'error']}}">{{c}}</text></view><view class="info-item data-v-afa62ce9"><text class="label data-v-afa62ce9">最后测试</text><text class="value data-v-afa62ce9">{{f}}</text></view></view><button class="test-btn data-v-afa62ce9" bindtap="{{h}}" disabled="{{i}}">{{g}}</button></view><view class="section data-v-afa62ce9"><view class="section-header data-v-afa62ce9"><text class="section-title data-v-afa62ce9">🔧 接口测试</text></view><view class="api-group data-v-afa62ce9"><text class="group-title data-v-afa62ce9">👤 用户接口</text><view class="api-list data-v-afa62ce9"><view wx:for="{{j}}" wx:for-item="api" wx:key="g" class="api-item data-v-afa62ce9"><view class="api-info data-v-afa62ce9"><text class="api-name data-v-afa62ce9">{{api.a}}</text><text class="api-url data-v-afa62ce9">{{api.b}} {{api.c}}</text></view><button class="api-test-btn data-v-afa62ce9" bindtap="{{api.e}}" disabled="{{api.f}}">{{api.d}}</button></view></view></view><view class="api-group data-v-afa62ce9"><text class="group-title data-v-afa62ce9">📚 课程接口</text><view class="api-list data-v-afa62ce9"><view wx:for="{{k}}" wx:for-item="api" wx:key="g" class="api-item data-v-afa62ce9"><view class="api-info data-v-afa62ce9"><text class="api-name data-v-afa62ce9">{{api.a}}</text><text class="api-url data-v-afa62ce9">{{api.b}} {{api.c}}</text></view><button class="api-test-btn data-v-afa62ce9" bindtap="{{api.e}}" disabled="{{api.f}}">{{api.d}}</button></view></view></view><view class="api-group data-v-afa62ce9"><text class="group-title data-v-afa62ce9">🎯 练习接口</text><view class="api-list data-v-afa62ce9"><view wx:for="{{l}}" wx:for-item="api" wx:key="g" class="api-item data-v-afa62ce9"><view class="api-info data-v-afa62ce9"><text class="api-name data-v-afa62ce9">{{api.a}}</text><text class="api-url data-v-afa62ce9">{{api.b}} {{api.c}}</text></view><button class="api-test-btn data-v-afa62ce9" bindtap="{{api.e}}" disabled="{{api.f}}">{{api.d}}</button></view></view></view></view><view class="section data-v-afa62ce9"><view class="section-header data-v-afa62ce9"><text class="section-title data-v-afa62ce9">📊 测试结果</text><button class="clear-btn data-v-afa62ce9" bindtap="{{m}}">清空</button></view><view class="results-list data-v-afa62ce9"><view wx:for="{{n}}" wx:for-item="result" wx:key="j" class="result-item data-v-afa62ce9"><view class="result-header data-v-afa62ce9"><text class="result-name data-v-afa62ce9">{{result.a}}</text><text class="{{['result-status', 'data-v-afa62ce9', result.c]}}">{{result.b}}</text><text class="result-time data-v-afa62ce9">{{result.d}}</text></view><view class="result-details data-v-afa62ce9"><text class="result-url data-v-afa62ce9">{{result.e}} {{result.f}}</text><text class="result-message data-v-afa62ce9">{{result.g}}</text><view wx:if="{{result.h}}" class="result-data data-v-afa62ce9"><text class="data-title data-v-afa62ce9">响应数据:</text><text class="data-content data-v-afa62ce9">{{result.i}}</text></view></view></view><view wx:if="{{o}}" class="empty-state data-v-afa62ce9"><text class="data-v-afa62ce9">暂无测试结果</text></view></view></view></view>