#!/usr/bin/env node

/**
 * CSS兼容性检查脚本
 * 检查小程序不支持的CSS语法
 */

const fs = require('fs')
const path = require('path')
const glob = require('glob')

console.log('🎨 检查CSS兼容性...\n')

let allPassed = true
let warnings = []

// 小程序不支持的CSS特性
const UNSUPPORTED_FEATURES = [
  {
    name: '通配符选择器',
    pattern: /^\s*\*\s*\{/m,
    description: '微信小程序不支持通配符选择器 *',
    severity: 'error'
  },
  {
    name: 'backdrop-filter',
    pattern: /backdrop-filter\s*:/,
    description: '微信小程序不支持 backdrop-filter 属性',
    severity: 'error'
  },
  {
    name: 'filter属性',
    pattern: /filter\s*:\s*(?!none)/,
    description: '微信小程序对 filter 属性支持有限',
    severity: 'warning'
  },
  {
    name: 'clip-path',
    pattern: /clip-path\s*:/,
    description: '微信小程序不支持 clip-path 属性',
    severity: 'error'
  },
  {
    name: 'mask属性',
    pattern: /mask\s*:/,
    description: '微信小程序不支持 mask 属性',
    severity: 'error'
  },
  {
    name: 'grid布局',
    pattern: /display\s*:\s*grid/,
    description: '微信小程序对 CSS Grid 支持有限',
    severity: 'warning'
  },
  {
    name: 'sticky定位',
    pattern: /position\s*:\s*sticky/,
    description: '微信小程序不支持 position: sticky',
    severity: 'error'
  },
  {
    name: 'CSS变量',
    pattern: /var\s*\(/,
    description: '微信小程序不支持 CSS 自定义属性（变量）',
    severity: 'error'
  },
  {
    name: 'calc()函数',
    pattern: /calc\s*\(/,
    description: '微信小程序对 calc() 函数支持有限',
    severity: 'warning'
  },
  {
    name: 'CSS伪元素',
    pattern: /::(before|after|first-line|first-letter)/,
    description: '微信小程序对某些伪元素支持有限',
    severity: 'warning'
  }
]

/**
 * 获取所有CSS/SCSS文件
 */
function getCssFiles() {
  const patterns = [
    '**/*.vue',
    '**/*.css',
    '**/*.scss',
    '**/*.sass',
    '**/*.less'
  ]
  
  let files = []
  
  patterns.forEach(pattern => {
    try {
      const matches = glob.sync(pattern, {
        cwd: process.cwd(),
        ignore: [
          'node_modules/**',
          'unpackage/**',
          'dist/**',
          '.git/**'
        ]
      })
      files = files.concat(matches)
    } catch (error) {
      console.warn(`获取文件失败: ${pattern}`, error.message)
    }
  })
  
  return [...new Set(files)] // 去重
}

/**
 * 提取Vue文件中的样式
 */
function extractStyleFromVue(content) {
  const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/g
  let styles = []
  let match
  
  while ((match = styleRegex.exec(content)) !== null) {
    styles.push(match[1])
  }
  
  return styles.join('\n')
}

/**
 * 检查单个文件
 */
function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    let cssContent = content
    
    // 如果是Vue文件，提取样式部分
    if (filePath.endsWith('.vue')) {
      cssContent = extractStyleFromVue(content)
    }
    
    const issues = []
    
    UNSUPPORTED_FEATURES.forEach(feature => {
      const matches = cssContent.match(feature.pattern)
      if (matches) {
        issues.push({
          feature: feature.name,
          description: feature.description,
          severity: feature.severity,
          matches: matches
        })
      }
    })
    
    return issues
    
  } catch (error) {
    console.warn(`读取文件失败: ${filePath}`, error.message)
    return []
  }
}

/**
 * 检查所有文件
 */
function checkAllFiles() {
  console.log('📁 扫描CSS/SCSS文件...')
  
  const files = getCssFiles()
  console.log(`找到 ${files.length} 个文件\n`)
  
  let totalIssues = 0
  let errorCount = 0
  let warningCount = 0
  
  files.forEach(file => {
    const issues = checkFile(file)
    
    if (issues.length > 0) {
      console.log(`📄 ${file}:`)
      
      issues.forEach(issue => {
        totalIssues++
        
        if (issue.severity === 'error') {
          console.log(`  ❌ ${issue.feature}: ${issue.description}`)
          errorCount++
          allPassed = false
        } else {
          console.log(`  ⚠️  ${issue.feature}: ${issue.description}`)
          warningCount++
          warnings.push(`${file}: ${issue.feature}`)
        }
      })
      
      console.log('')
    }
  })
  
  return { totalIssues, errorCount, warningCount }
}

/**
 * 生成修复建议
 */
function generateFixSuggestions() {
  console.log('💡 修复建议:\n')
  
  const suggestions = [
    {
      problem: '通配符选择器 *',
      solution: '使用具体的元素选择器，如 view, text, button 等'
    },
    {
      problem: 'backdrop-filter',
      solution: '移除或注释掉，使用其他方式实现毛玻璃效果'
    },
    {
      problem: 'CSS Grid',
      solution: '使用 Flexbox 布局替代'
    },
    {
      problem: 'position: sticky',
      solution: '使用 position: fixed 或其他定位方式'
    },
    {
      problem: 'CSS变量',
      solution: '使用 SCSS 变量替代'
    },
    {
      problem: 'calc()函数',
      solution: '使用 SCSS 计算或固定值'
    }
  ]
  
  suggestions.forEach(suggestion => {
    console.log(`🔧 ${suggestion.problem}:`)
    console.log(`   ${suggestion.solution}\n`)
  })
}

/**
 * 生成兼容性报告
 */
function generateReport(stats) {
  const report = {
    timestamp: new Date().toISOString(),
    platform: 'mp-weixin',
    status: allPassed ? 'PASSED' : 'FAILED',
    statistics: {
      totalFiles: getCssFiles().length,
      totalIssues: stats.totalIssues,
      errors: stats.errorCount,
      warnings: stats.warningCount
    },
    unsupportedFeatures: UNSUPPORTED_FEATURES.map(f => f.name),
    warnings: warnings
  }
  
  const reportPath = path.join(process.cwd(), 'docs/css-compatibility-report.json')
  
  try {
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`📊 兼容性报告已生成: ${reportPath}`)
  } catch (error) {
    console.log(`⚠️  报告生成失败: ${error.message}`)
  }
  
  return report
}

/**
 * 主函数
 */
function main() {
  try {
    const stats = checkAllFiles()
    
    console.log('='.repeat(60))
    
    if (allPassed) {
      console.log('🎉 CSS兼容性检查通过！')
      console.log('✅ 未发现小程序不兼容的CSS特性')
      
      if (warnings.length > 0) {
        console.log(`\n⚠️  发现 ${warnings.length} 个警告项目`)
        console.log('这些特性在小程序中支持有限，建议谨慎使用')
      }
      
    } else {
      console.log('❌ CSS兼容性检查失败！')
      console.log(`发现 ${stats.errorCount} 个错误，${stats.warningCount} 个警告`)
      console.log('请修复错误项目以确保小程序正常运行')
      
      generateFixSuggestions()
    }
    
    // 生成报告
    generateReport(stats)
    
    console.log('\n📱 小程序CSS兼容性指南:')
    console.log('   - 避免使用通配符选择器')
    console.log('   - 使用小程序支持的CSS属性')
    console.log('   - 优先使用Flexbox布局')
    console.log('   - 使用SCSS变量而非CSS变量')
    
    console.log('\n' + '='.repeat(60))
    
    if (!allPassed) {
      process.exit(1)
    }
    
  } catch (error) {
    console.error('❌ CSS兼容性检查过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 检查是否安装了glob模块
try {
  require('glob')
} catch (error) {
  console.error('❌ 缺少依赖模块 glob')
  console.log('请运行: npm install glob --save-dev')
  process.exit(1)
}

// 运行主函数
main()
