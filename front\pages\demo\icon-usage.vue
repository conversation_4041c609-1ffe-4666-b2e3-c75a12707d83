<template>
	<view class="container">
		<view class="header">
			<text class="title">🎨 图标使用示例</text>
			<text class="subtitle">展示各种图标的使用方法和效果</text>
		</view>
		
		<!-- 基础用法 -->
		<view class="section">
			<text class="section-title">基础用法</text>
			<view class="demo-row">
				<view class="demo-item">
					<view class="icon icon-home icon-md"></view>
					<text>CSS类方式</text>
				</view>
				<view class="demo-item">
					<image src="/static/tabbar/home.png" class="icon-image" />
					<text>Image组件</text>
				</view>
			</view>
		</view>
		
		<!-- 尺寸变体 -->
		<view class="section">
			<text class="section-title">尺寸变体</text>
			<view class="demo-row">
				<view class="demo-item">
					<view class="icon icon-home icon-xs"></view>
					<text>16px</text>
				</view>
				<view class="demo-item">
					<view class="icon icon-home icon-sm"></view>
					<text>20px</text>
				</view>
				<view class="demo-item">
					<view class="icon icon-home icon-md"></view>
					<text>24px</text>
				</view>
				<view class="demo-item">
					<view class="icon icon-home icon-lg"></view>
					<text>32px</text>
				</view>
				<view class="demo-item">
					<view class="icon icon-home icon-xl"></view>
					<text>48px</text>
				</view>
			</view>
		</view>
		
		<!-- 状态变化 -->
		<view class="section">
			<text class="section-title">状态变化</text>
			<view class="demo-row">
				<view class="demo-item">
					<view class="icon icon-home icon-normal"></view>
					<text>普通状态</text>
				</view>
				<view class="demo-item">
					<view class="icon icon-home-active icon-active"></view>
					<text>激活状态</text>
				</view>
				<view class="demo-item">
					<view class="icon icon-home icon-disabled"></view>
					<text>禁用状态</text>
				</view>
			</view>
		</view>
		
		<!-- 动画效果 -->
		<view class="section">
			<text class="section-title">动画效果</text>
			<view class="demo-row">
				<view class="demo-item" @tap="triggerBounce">
					<view class="icon icon-checkin" :class="{ 'icon-bounce': bouncing }"></view>
					<text>点击弹跳</text>
				</view>
				<view class="demo-item">
					<view class="icon icon-leaderboard icon-pulse"></view>
					<text>脉冲动画</text>
				</view>
				<view class="demo-item">
					<view class="icon icon-course icon-hover"></view>
					<text>悬停缩放</text>
				</view>
			</view>
		</view>
		
		<!-- 图标按钮 -->
		<view class="section">
			<text class="section-title">图标按钮</text>
			<view class="demo-row">
				<button 
					class="icon-button" 
					v-for="(icon, index) in tabbarIcons" 
					:key="index"
					:class="{ active: activeButton === index }"
					@tap="setActiveButton(index)"
				>
					<view :class="`icon ${icon.class} icon-md`"></view>
				</button>
			</view>
		</view>
		
		<!-- 图标列表 -->
		<view class="section">
			<text class="section-title">图标列表</text>
			<view class="icon-list">
				<view class="icon-list-item" v-for="item in listItems" :key="item.id">
					<view :class="`icon ${item.icon} icon-md`"></view>
					<view class="item-content">
						<text class="item-title">{{ item.title }}</text>
						<text class="item-desc">{{ item.description }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 图标网格 -->
		<view class="section">
			<text class="section-title">图标网格</text>
			<view class="icon-grid">
				<view 
					class="icon-grid-item" 
					v-for="icon in allIcons" 
					:key="icon.name"
					@tap="showIconInfo(icon)"
				>
					<view :class="`icon ${icon.class} icon-lg`"></view>
					<text class="label">{{ icon.name }}</text>
				</view>
			</view>
		</view>
		
		<!-- 使用代码示例 -->
		<view class="section">
			<text class="section-title">代码示例</text>
			<view class="code-block">
				<text class="code-title">HTML/Vue模板:</text>
				<text class="code-content">{{ htmlExample }}</text>
			</view>
			<view class="code-block">
				<text class="code-title">CSS样式:</text>
				<text class="code-content">{{ cssExample }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			bouncing: false,
			activeButton: 0,
			tabbarIcons: [
				{ class: 'icon-home', name: 'home' },
				{ class: 'icon-course', name: 'course' },
				{ class: 'icon-checkin', name: 'checkin' },
				{ class: 'icon-leaderboard', name: 'leaderboard' },
				{ class: 'icon-profile', name: 'profile' }
			],
			listItems: [
				{
					id: 1,
					icon: 'icon-home',
					title: '首页',
					description: '返回应用主页面'
				},
				{
					id: 2,
					icon: 'icon-course',
					title: '我的课程',
					description: '查看学习进度和课程内容'
				},
				{
					id: 3,
					icon: 'icon-checkin',
					title: '今日打卡',
					description: '完成每日学习任务'
				},
				{
					id: 4,
					icon: 'icon-profile',
					title: '个人设置',
					description: '管理账户和偏好设置'
				}
			],
			allIcons: [
				{ name: 'home', class: 'icon-home' },
				{ name: 'course', class: 'icon-course' },
				{ name: 'checkin', class: 'icon-checkin' },
				{ name: 'leaderboard', class: 'icon-leaderboard' },
				{ name: 'profile', class: 'icon-profile' }
			],
			htmlExample: `<!-- 使用CSS类 -->
<view class="icon icon-home icon-md"></view>

<!-- 使用Image组件 -->
<image src="/static/tabbar/home.png" class="icon-image" />

<!-- 图标按钮 -->
<button class="icon-button">
  <view class="icon icon-home icon-md"></view>
</button>`,
			cssExample: `.icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-home {
  background-image: url('/static/tabbar/home.png');
}`
		}
	},
	
	methods: {
		triggerBounce() {
			this.bouncing = true;
			setTimeout(() => {
				this.bouncing = false;
			}, 600);
		},
		
		setActiveButton(index) {
			this.activeButton = index;
		},
		
		showIconInfo(icon) {
			uni.showToast({
				title: `图标: ${icon.name}`,
				icon: 'none'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
@import '/static/css/icons.css';

.container {
	padding: 20px;
	background-color: #f8f9fa;
}

.header {
	text-align: center;
	margin-bottom: 30px;
	
	.title {
		display: block;
		font-size: 24px;
		font-weight: bold;
		color: #333;
		margin-bottom: 8px;
	}
	
	.subtitle {
		display: block;
		font-size: 14px;
		color: #666;
	}
}

.section {
	background: white;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	
	.section-title {
		display: block;
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 20px;
		padding-bottom: 10px;
		border-bottom: 2px solid #667eea;
	}
}

.demo-row {
	display: flex;
	gap: 20px;
	flex-wrap: wrap;
	align-items: center;
	
	.demo-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8px;
		padding: 15px;
		border: 1px solid #f0f0f0;
		border-radius: 8px;
		background: #fafafa;
		min-width: 80px;
		
		text {
			font-size: 12px;
			color: #666;
			text-align: center;
		}
	}
}

.icon-image {
	width: 24px;
	height: 24px;
}

.item-content {
	flex: 1;
	
	.item-title {
		display: block;
		font-size: 16px;
		font-weight: 500;
		color: #333;
		margin-bottom: 4px;
	}
	
	.item-desc {
		display: block;
		font-size: 14px;
		color: #666;
	}
}

.code-block {
	background: #f8f9fa;
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 15px;
	border-left: 4px solid #667eea;
	
	.code-title {
		display: block;
		font-size: 14px;
		font-weight: bold;
		color: #667eea;
		margin-bottom: 10px;
	}
	
	.code-content {
		display: block;
		font-family: 'Monaco', 'Menlo', monospace;
		font-size: 12px;
		color: #333;
		line-height: 1.5;
		white-space: pre-wrap;
	}
}

/* 响应式设计 */
@media (max-width: 768px) {
	.demo-row {
		justify-content: center;
	}
	
	.icon-grid {
		grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
		gap: 12px;
	}
}
</style>
