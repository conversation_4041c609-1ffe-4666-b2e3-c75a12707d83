import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
  ],
  define: {
    ROUTES: JSON.stringify([]),
    // 修复小程序环境变量
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
  },
  // 开发服务器配置
  server: {
    port: 3000,
    host: '0.0.0.0',
    open: true,
    cors: true,
    // 代理配置 - 统一后台接口
    proxy: {
      '/jeecg-boot': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      }
    }
  },
  // 构建配置
  build: {
    target: 'es6',
    cssCodeSplit: false,
    // 修复小程序构建问题
    rollupOptions: {
      external: [],
      output: {
        // 禁用代码分割，避免动态导入问题
        manualChunks: undefined,
        // 确保文件名稳定
        entryFileNames: '[name].js',
        chunkFileNames: '[name].js',
        assetFileNames: '[name].[ext]'
      }
    },
    // 小程序环境优化
    minify: process.env.NODE_ENV === 'production' ? 'terser' : false,
    sourcemap: process.env.NODE_ENV === 'development'
  },
  // CSS配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/uni.scss";`
      }
    }
  },
  // 路径别名
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
      '@/': resolve(__dirname, '.') + '/',
      // 修复uni-app路径问题
      '~@': resolve(__dirname, '.'),
      '~@/': resolve(__dirname, '.') + '/'
    }
  },
  // 优化配置
  optimizeDeps: {
    include: ['vue', '@dcloudio/uni-app'],
    exclude: ['@dcloudio/uni-h5']
  },
  // 小程序特定配置
  esbuild: {
    // 修复小程序环境下的语法问题
    target: 'es2015',
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : []
  }
})
