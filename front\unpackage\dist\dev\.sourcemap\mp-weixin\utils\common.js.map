{"version": 3, "file": "common.js", "sources": ["utils/common.js"], "sourcesContent": ["/**\n * 通用工具函数\n */\n\n/**\n * 安全获取App实例\n */\nexport function getAppInstance() {\n  try {\n    return getApp()\n  } catch (error) {\n    console.warn('获取App实例失败:', error)\n    return null\n  }\n}\n\n/**\n * 获取全局数据\n */\nexport function getGlobalData(key) {\n  const app = getAppInstance()\n  if (!app || !app.globalData) {\n    return key ? undefined : {}\n  }\n  return key ? app.globalData[key] : app.globalData\n}\n\n/**\n * 设置全局数据\n */\nexport function setGlobalData(key, value) {\n  const app = getAppInstance()\n  if (!app) return false\n  \n  if (!app.globalData) {\n    app.globalData = {}\n  }\n  \n  if (typeof key === 'object') {\n    Object.assign(app.globalData, key)\n  } else {\n    app.globalData[key] = value\n  }\n  return true\n}\n\n/**\n * 获取用户信息\n */\nexport function getUserInfo() {\n  const globalUserInfo = getGlobalData('userInfo')\n  if (globalUserInfo) return globalUserInfo\n  \n  try {\n    return uni.getStorageSync('userInfo')\n  } catch (error) {\n    console.error('获取用户信息失败:', error)\n    return null\n  }\n}\n\n/**\n * 获取Token\n */\nexport function getToken() {\n  const globalToken = getGlobalData('token')\n  if (globalToken) return globalToken\n  \n  try {\n    return uni.getStorageSync('token')\n  } catch (error) {\n    console.error('获取Token失败:', error)\n    return null\n  }\n}\n\n/**\n * 检查是否已登录\n */\nexport function isLoggedIn() {\n  const token = getToken()\n  const userInfo = getUserInfo()\n  return !!(token && userInfo)\n}\n\n/**\n * 格式化时间\n */\nexport function formatTime(date) {\n  if (!date) return ''\n  \n  const d = new Date(date)\n  const year = d.getFullYear()\n  const month = (d.getMonth() + 1).toString().padStart(2, '0')\n  const day = d.getDate().toString().padStart(2, '0')\n  const hour = d.getHours().toString().padStart(2, '0')\n  const minute = d.getMinutes().toString().padStart(2, '0')\n  const second = d.getSeconds().toString().padStart(2, '0')\n  \n  return `${year}-${month}-${day} ${hour}:${minute}:${second}`\n}\n\n/**\n * 格式化文件大小\n */\nexport function formatFileSize(bytes) {\n  if (bytes === 0) return '0 B'\n  \n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n/**\n * 防抖函数\n */\nexport function debounce(func, wait) {\n  let timeout\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout)\n      func(...args)\n    }\n    clearTimeout(timeout)\n    timeout = setTimeout(later, wait)\n  }\n}\n\n/**\n * 节流函数\n */\nexport function throttle(func, limit) {\n  let inThrottle\n  return function(...args) {\n    if (!inThrottle) {\n      func.apply(this, args)\n      inThrottle = true\n      setTimeout(() => inThrottle = false, limit)\n    }\n  }\n}\n\n/**\n * 深拷贝\n */\nexport function deepClone(obj) {\n  if (obj === null || typeof obj !== 'object') return obj\n  if (obj instanceof Date) return new Date(obj.getTime())\n  if (obj instanceof Array) return obj.map(item => deepClone(item))\n  if (typeof obj === 'object') {\n    const clonedObj = {}\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n}\n\n/**\n * 生成随机字符串\n */\nexport function generateRandomString(length = 8) {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n  let result = ''\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length))\n  }\n  return result\n}\n\n/**\n * 验证手机号\n */\nexport function validatePhone(phone) {\n  const phoneRegex = /^1[3-9]\\d{9}$/\n  return phoneRegex.test(phone)\n}\n\n/**\n * 验证邮箱\n */\nexport function validateEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n/**\n * 获取URL参数\n */\nexport function getUrlParams(url) {\n  const params = {}\n  const urlObj = new URL(url)\n  for (const [key, value] of urlObj.searchParams) {\n    params[key] = value\n  }\n  return params\n}\n\n/**\n * 显示加载提示\n */\nexport function showLoading(title = '加载中...') {\n  uni.showLoading({\n    title,\n    mask: true\n  })\n}\n\n/**\n * 隐藏加载提示\n */\nexport function hideLoading() {\n  uni.hideLoading()\n}\n\n/**\n * 显示成功提示\n */\nexport function showSuccess(title, duration = 2000) {\n  uni.showToast({\n    title,\n    icon: 'success',\n    duration\n  })\n}\n\n/**\n * 显示错误提示\n */\nexport function showError(title, duration = 2000) {\n  uni.showToast({\n    title,\n    icon: 'none',\n    duration\n  })\n}\n\n/**\n * 显示确认对话框\n */\nexport function showConfirm(content, title = '提示') {\n  return new Promise((resolve) => {\n    uni.showModal({\n      title,\n      content,\n      success: (res) => {\n        resolve(res.confirm)\n      },\n      fail: () => {\n        resolve(false)\n      }\n    })\n  })\n}\n"], "names": ["uni"], "mappings": ";;AAOO,SAAS,iBAAiB;AAC/B,MAAI;AACF,WAAO,OAAQ;AAAA,EAChB,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,QAAA,yBAAa,cAAc,KAAK;AAChC,WAAO;AAAA,EACR;AACH;AAKO,SAAS,cAAc,KAAK;AACjC,QAAM,MAAM,eAAgB;AAC5B,MAAI,CAAC,OAAO,CAAC,IAAI,YAAY;AAC3B,WAAO,MAAM,SAAY,CAAE;AAAA,EAC5B;AACD,SAAO,MAAM,IAAI,WAAW,GAAG,IAAI,IAAI;AACzC;AAwBO,SAAS,cAAc;AAC5B,QAAM,iBAAiB,cAAc,UAAU;AAC/C,MAAI;AAAgB,WAAO;AAE3B,MAAI;AACF,WAAOA,cAAG,MAAC,eAAe,UAAU;AAAA,EACrC,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,yBAAc,aAAa,KAAK;AAChC,WAAO;AAAA,EACR;AACH;AAKO,SAAS,WAAW;AACzB,QAAM,cAAc,cAAc,OAAO;AACzC,MAAI;AAAa,WAAO;AAExB,MAAI;AACF,WAAOA,cAAG,MAAC,eAAe,OAAO;AAAA,EAClC,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,yBAAc,cAAc,KAAK;AACjC,WAAO;AAAA,EACR;AACH;AAKO,SAAS,aAAa;AAC3B,QAAM,QAAQ,SAAU;AACxB,QAAM,WAAW,YAAa;AAC9B,SAAO,CAAC,EAAE,SAAS;AACrB;;;;;"}