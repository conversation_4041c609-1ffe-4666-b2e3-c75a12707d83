/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.page.data-v-3d21314d {
  min-height: 100vh;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  padding-bottom: 160rpx;
}
.status-bar.data-v-3d21314d {
  background: transparent;
}
.nav-bar.data-v-3d21314d {
  background: transparent;
}
.nav-bar .nav-content.data-v-3d21314d {
  height: 120rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}
.nav-bar .nav-content .nav-left.data-v-3d21314d, .nav-bar .nav-content .nav-right.data-v-3d21314d {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nav-bar .nav-content .nav-left .iconfont.data-v-3d21314d {
  color: white;
  font-size: 40rpx;
}
.nav-bar .nav-content .nav-title.data-v-3d21314d {
  flex: 1;
}
.nav-bar .nav-content .nav-title .title.data-v-3d21314d {
  display: block;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.2;
}
.nav-bar .nav-content .nav-title .subtitle.data-v-3d21314d {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}
.content.data-v-3d21314d {
  background: #f8fafc;
  border-radius: 48rpx 48rpx 0 0;
  min-height: calc(100vh - var(--status-bar-height) - 120rpx);
  padding: 40rpx 0;
}
.audio-player.data-v-3d21314d {
  margin: 0 40rpx 32rpx;
}
.audio-player .player-title.data-v-3d21314d {
  display: block;
  font-weight: bold;
  color: #333;
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.audio-player .player-duration.data-v-3d21314d {
  color: #666;
  font-size: 28rpx;
}
.audio-player .play-button.data-v-3d21314d {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  display: flex;
  align-items: center;
  justify-content: center;
}
.audio-player .play-button .iconfont.data-v-3d21314d {
  color: white;
  font-size: 48rpx;
}
.audio-player .waveform.data-v-3d21314d {
  height: 80rpx;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 30%, #e5e7eb 30%, #e5e7eb 100%);
  border-radius: 40rpx;
  position: relative;
  overflow: hidden;
  margin: 32rpx 0;
}
.audio-player .waveform .waveform-progress.data-v-3d21314d {
  height: 100%;
  background: linear-gradient(90deg, #4facfe, #00f2fe);
  border-radius: 40rpx;
  transition: width 0.3s ease;
}
.audio-player .audio-controls .time-text.data-v-3d21314d {
  color: #666;
  font-size: 28rpx;
}
.audio-player .audio-controls .control-buttons.data-v-3d21314d {
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.audio-player .audio-controls .control-buttons .iconfont.data-v-3d21314d {
  color: #666;
  font-size: 36rpx;
}
.audio-player .audio-controls .control-buttons .iconfont.active.data-v-3d21314d {
  color: #4facfe;
}
.content-tabs.data-v-3d21314d {
  margin: 0 40rpx 32rpx;
}
.content-tabs .tab-buttons.data-v-3d21314d {
  display: flex;
  background: #f1f5f9;
  border-radius: 24rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
}
.content-tabs .tab-buttons .tab-button.data-v-3d21314d {
  flex: 1;
  padding: 16rpx 32rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #64748b;
  transition: all 0.2s;
}
.content-tabs .tab-buttons .tab-button.active.data-v-3d21314d {
  background: white;
  color: #4facfe;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.content-tabs .text-content .dialogue-item.data-v-3d21314d {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f1f5f9;
}
.content-tabs .text-content .dialogue-item.data-v-3d21314d:last-child {
  border-bottom: none;
}
.content-tabs .text-content .dialogue-item .speaker.data-v-3d21314d {
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.content-tabs .text-content .dialogue-item .text.data-v-3d21314d {
  color: #333;
  line-height: 1.6;
  font-size: 32rpx;
}
.content-tabs .vocabulary-content .vocabulary-item.data-v-3d21314d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f1f5f9;
}
.content-tabs .vocabulary-content .vocabulary-item.data-v-3d21314d:last-child {
  border-bottom: none;
}
.content-tabs .vocabulary-content .vocabulary-item .word-info .word.data-v-3d21314d {
  display: block;
  font-weight: bold;
  color: #333;
  font-size: 32rpx;
  margin-bottom: 4rpx;
}
.content-tabs .vocabulary-content .vocabulary-item .word-info .pronunciation.data-v-3d21314d {
  color: #666;
  font-size: 24rpx;
}
.content-tabs .vocabulary-content .vocabulary-item .meaning.data-v-3d21314d {
  color: #333;
  font-size: 28rpx;
}
.study-tips.data-v-3d21314d {
  margin: 0 40rpx;
}
.study-tips .tips-header.data-v-3d21314d {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.study-tips .tips-header .iconfont.data-v-3d21314d {
  color: #f59e0b;
  font-size: 40rpx;
  margin-right: 16rpx;
}
.study-tips .tips-header .tips-title.data-v-3d21314d {
  font-weight: bold;
  color: #333;
  font-size: 32rpx;
}
.study-tips .tips-list .tip-item.data-v-3d21314d {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}
.study-tips .tips-list .tip-item.data-v-3d21314d:last-child {
  margin-bottom: 0;
}
.study-tips .tips-list .tip-item .tip-number.data-v-3d21314d {
  width: 48rpx;
  height: 48rpx;
  background: #dbeafe;
  color: #2563eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 24rpx;
  margin-top: 8rpx;
}
.study-tips .tips-list .tip-item .tip-content.data-v-3d21314d {
  flex: 1;
}
.study-tips .tips-list .tip-item .tip-content .tip-title.data-v-3d21314d {
  display: block;
  font-weight: 500;
  color: #333;
  font-size: 28rpx;
  margin-bottom: 4rpx;
}
.study-tips .tips-list .tip-item .tip-content .tip-desc.data-v-3d21314d {
  color: #666;
  font-size: 24rpx;
  line-height: 1.5;
}
.bottom-action.data-v-3d21314d {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
}
.bottom-action .practice-btn.data-v-3d21314d {
  width: 100%;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
  padding: 32rpx;
  border-radius: 32rpx;
  font-weight: bold;
  font-size: 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(79, 172, 254, 0.3);
  border: none;
}
.bottom-action .practice-btn .iconfont.data-v-3d21314d {
  margin-right: 16rpx;
  font-size: 36rpx;
}

/* 字体图标 */
.icon-pause.data-v-3d21314d::before {
  content: "\e012";
}
.icon-backward.data-v-3d21314d::before {
  content: "\e013";
}
.icon-forward.data-v-3d21314d::before {
  content: "\e014";
}
.icon-repeat.data-v-3d21314d::before {
  content: "\e015";
}
.icon-lightbulb.data-v-3d21314d::before {
  content: "\e016";
}
.icon-microphone.data-v-3d21314d::before {
  content: "\e017";
}