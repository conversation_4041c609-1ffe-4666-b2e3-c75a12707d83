"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const course = {
  namespaced: true,
  state: {
    // 课程列表
    courseList: [],
    // 当前课程详情
    currentCourse: null,
    // 课程级别
    levels: [
      {
        id: 1,
        key: "beginner",
        name: "初级 N5-N4",
        description: "基础对话练习",
        icon: "icon-seedling",
        iconBg: "#dcfce7",
        iconColor: "#16a34a"
      },
      {
        id: 2,
        key: "intermediate",
        name: "中级 N3-N2",
        description: "日常会话提升",
        icon: "icon-mountain",
        iconBg: "#dbeafe",
        iconColor: "#2563eb"
      },
      {
        id: 3,
        key: "advanced",
        name: "高级 N1",
        description: "商务日语精进",
        icon: "icon-crown",
        iconBg: "#f3e8ff",
        iconColor: "#9333ea"
      },
      {
        id: 4,
        key: "professional",
        name: "专业级",
        description: "新闻听力训练",
        icon: "icon-star",
        iconBg: "#fed7aa",
        iconColor: "#ea580c",
        isVip: true
      }
    ],
    // 当前选择的级别
    currentLevel: "beginner",
    // 加载状态
    loading: false,
    // 用户学习记录
    userStudyRecords: {},
    // 收藏的课程
    favoriteCourses: []
  },
  mutations: {
    // 设置课程列表
    SET_COURSE_LIST(state, courseList) {
      state.courseList = courseList;
    },
    // 设置当前课程
    SET_CURRENT_COURSE(state, course2) {
      state.currentCourse = course2;
    },
    // 设置当前级别
    SET_CURRENT_LEVEL(state, level) {
      state.currentLevel = level;
    },
    // 设置加载状态
    SET_LOADING(state, loading) {
      state.loading = loading;
    },
    // 设置用户学习记录
    SET_USER_STUDY_RECORDS(state, records) {
      state.userStudyRecords = records;
    },
    // 更新单个课程的学习记录
    UPDATE_COURSE_RECORD(state, { courseId, record }) {
      state.userStudyRecords[courseId] = record;
    },
    // 设置收藏课程
    SET_FAVORITE_COURSES(state, courses) {
      state.favoriteCourses = courses;
    },
    // 添加收藏
    ADD_FAVORITE(state, courseId) {
      if (!state.favoriteCourses.includes(courseId)) {
        state.favoriteCourses.push(courseId);
      }
    },
    // 移除收藏
    REMOVE_FAVORITE(state, courseId) {
      const index = state.favoriteCourses.indexOf(courseId);
      if (index > -1) {
        state.favoriteCourses.splice(index, 1);
      }
    }
  },
  actions: {
    // 加载课程列表
    loadCourseList(_0, _1) {
      return __async(this, arguments, function* ({ commit, rootState }, { level, page = 1, size = 20 }) {
        commit("SET_LOADING", true);
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/courses",
            method: "GET",
            data: { level, page, size },
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            commit("SET_COURSE_LIST", response.data.data.list);
            commit("SET_CURRENT_LEVEL", level);
            return { success: true, data: response.data.data };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/course.js:140", "加载课程列表失败:", error);
          return { success: false, message: "加载失败，请重试" };
        } finally {
          commit("SET_LOADING", false);
        }
      });
    },
    // 加载课程详情
    loadCourseDetail(_0, _1) {
      return __async(this, arguments, function* ({ commit, rootState }, courseId) {
        try {
          const response = yield common_vendor.index.request({
            url: `/api/v1/courses/${courseId}`,
            method: "GET",
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            commit("SET_CURRENT_COURSE", response.data.data);
            return { success: true, data: response.data.data };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/course.js:165", "加载课程详情失败:", error);
          return { success: false, message: "加载失败，请重试" };
        }
      });
    },
    // 更新学习进度
    updateStudyProgress(_0, _1) {
      return __async(this, arguments, function* ({ commit, rootState }, { courseId, progress, studyTime }) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/study/progress",
            method: "POST",
            data: { courseId, progress, studyTime },
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            commit("UPDATE_COURSE_RECORD", {
              courseId,
              record: response.data.data
            });
            return { success: true };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/course.js:192", "更新学习进度失败:", error);
          return { success: false, message: "更新失败，请重试" };
        }
      });
    },
    // 加载用户学习记录
    loadUserStudyRecords(_0) {
      return __async(this, arguments, function* ({ commit, rootState }) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/study/records",
            method: "GET",
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            const records = {};
            response.data.data.forEach((record) => {
              records[record.courseId] = record;
            });
            commit("SET_USER_STUDY_RECORDS", records);
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/course.js:216", "加载学习记录失败:", error);
        }
      });
    },
    // 购买课程
    purchaseCourse(_0, _1) {
      return __async(this, arguments, function* ({ rootState }, { courseId, paymentMethod }) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/payment/create",
            method: "POST",
            data: { courseId, paymentMethod },
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            return { success: true, data: response.data.data };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/course.js:239", "创建订单失败:", error);
          return { success: false, message: "创建订单失败，请重试" };
        }
      });
    },
    // 收藏/取消收藏课程
    toggleFavorite(_0, _1) {
      return __async(this, arguments, function* ({ commit, state, rootState }, courseId) {
        const isFavorite = state.favoriteCourses.includes(courseId);
        try {
          const response = yield common_vendor.index.request({
            url: `/api/v1/courses/${courseId}/favorite`,
            method: isFavorite ? "DELETE" : "POST",
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            if (isFavorite) {
              commit("REMOVE_FAVORITE", courseId);
            } else {
              commit("ADD_FAVORITE", courseId);
            }
            return { success: true, isFavorite: !isFavorite };
          } else {
            return { success: false, message: response.data.message };
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/course.js:268", "收藏操作失败:", error);
          return { success: false, message: "操作失败，请重试" };
        }
      });
    },
    // 加载收藏课程
    loadFavoriteCourses(_0) {
      return __async(this, arguments, function* ({ commit, rootState }) {
        try {
          const response = yield common_vendor.index.request({
            url: "/api/v1/user/favorites",
            method: "GET",
            header: {
              "Authorization": `Bearer ${rootState.user.token}`
            }
          });
          if (response.data.code === 200) {
            const courseIds = response.data.data.map((item) => item.courseId);
            commit("SET_FAVORITE_COURSES", courseIds);
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/modules/course.js:289", "加载收藏课程失败:", error);
        }
      });
    }
  },
  getters: {
    // 根据级别获取课程列表
    getCoursesByLevel: (state) => (level) => {
      return state.courseList.filter((course2) => course2.level === level);
    },
    // 获取课程学习记录
    getCourseRecord: (state) => (courseId) => {
      return state.userStudyRecords[courseId] || null;
    },
    // 检查课程是否已收藏
    isFavorite: (state) => (courseId) => {
      return state.favoriteCourses.includes(courseId);
    },
    // 获取当前级别信息
    currentLevelInfo: (state) => {
      return state.levels.find((level) => level.key === state.currentLevel);
    },
    // 获取已完成的课程数量
    completedCoursesCount: (state) => {
      return Object.values(state.userStudyRecords).filter((record) => record.isCompleted).length;
    },
    // 获取学习进度统计
    studyProgressStats: (state) => {
      const records = Object.values(state.userStudyRecords);
      const totalCourses = state.courseList.length;
      const completedCourses = records.filter((record) => record.isCompleted).length;
      const totalStudyTime = records.reduce((total, record) => total + (record.studyTime || 0), 0);
      return {
        totalCourses,
        completedCourses,
        totalStudyTime,
        completionRate: totalCourses > 0 ? Math.round(completedCourses / totalCourses * 100) : 0
      };
    }
  }
};
exports.course = course;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/course.js.map
