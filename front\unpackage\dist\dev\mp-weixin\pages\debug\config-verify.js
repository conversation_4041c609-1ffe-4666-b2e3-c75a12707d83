"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      configLoaded: false,
      baseUrl: "",
      errorMessage: ""
    };
  },
  onLoad() {
    this.testConfig();
  },
  methods: {
    // 测试配置
    testConfig() {
      try {
        "../../config/api.js".then((apiConfig) => {
          common_vendor.index.__f__("log", "at pages/debug/config-verify.vue:71", "✅ API配置导入成功:", apiConfig);
          if (apiConfig.getApiBaseUrl) {
            this.baseUrl = apiConfig.getApiBaseUrl();
            this.configLoaded = true;
            this.errorMessage = "";
            common_vendor.index.__f__("log", "at pages/debug/config-verify.vue:78", "✅ 基础URL获取成功:", this.baseUrl);
          } else {
            this.errorMessage = "getApiBaseUrl函数不存在";
            common_vendor.index.__f__("error", "at pages/debug/config-verify.vue:81", "❌ getApiBaseUrl函数不存在");
          }
        }).catch((error) => {
          this.errorMessage = `API配置导入失败: ${error.message}`;
          common_vendor.index.__f__("error", "at pages/debug/config-verify.vue:85", "❌ API配置导入失败:", error);
        });
      } catch (error) {
        this.errorMessage = `配置测试异常: ${error.message}`;
        common_vendor.index.__f__("error", "at pages/debug/config-verify.vue:89", "❌ 配置测试异常:", error);
      }
    },
    // 测试请求
    testRequest() {
      return __async(this, null, function* () {
        try {
          common_vendor.index.request({
            url: this.baseUrl + "/sys/common/403",
            method: "GET",
            success: (res) => {
              common_vendor.index.__f__("log", "at pages/debug/config-verify.vue:101", "✅ 请求测试成功:", res);
              common_vendor.index.showToast({
                title: "请求测试成功",
                icon: "success"
              });
            },
            fail: (err) => {
              common_vendor.index.__f__("log", "at pages/debug/config-verify.vue:108", "⚠️ 请求测试失败（可能是正常的）:", err);
              common_vendor.index.showToast({
                title: "请求已发送",
                icon: "none"
              });
            }
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/debug/config-verify.vue:116", "❌ 请求测试异常:", error);
          common_vendor.index.showToast({
            title: "请求测试异常",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.configLoaded ? "成功" : "失败"),
    b: common_vendor.n($data.configLoaded ? "success" : "error"),
    c: common_vendor.t($data.baseUrl || "未获取"),
    d: common_vendor.t($data.errorMessage || "无"),
    e: common_vendor.o((...args) => $options.testConfig && $options.testConfig(...args)),
    f: common_vendor.o((...args) => $options.testRequest && $options.testRequest(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-10eedd14"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/config-verify.js.map
