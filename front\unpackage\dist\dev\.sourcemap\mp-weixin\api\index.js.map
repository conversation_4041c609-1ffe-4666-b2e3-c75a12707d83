{"version": 3, "file": "index.js", "sources": ["api/index.js"], "sourcesContent": ["/**\n * API接口统一管理\n * 基于小程序优化的请求管理器\n * 集成jeecg-boot后台接口\n */\nimport { get, post, put, del, upload } from '@/utils/request.js'\nimport { API_ENDPOINTS, buildApiUrl } from '@/config/api.js'\n\n// 统一API对象\nconst api = {\n  get,\n  post,\n  put,\n  delete: del,\n  upload\n}\n\n// 用户相关接口 - jeecg-boot标准接口\nexport const userApi = {\n  // 登录 - jeecg-boot标准登录\n  login: (data) => api.post(API_ENDPOINTS.USER.LOGIN, data),\n\n  // 退出登录 - jeecg-boot标准登出\n  logout: () => api.post(API_ENDPOINTS.USER.LOGOUT),\n\n  // 获取用户基础信息 - jeecg-boot标准接口\n  getUserInfo: () => api.get(API_ENDPOINTS.USER.INFO),\n\n  // 获取部门信息 - jeecg-boot标准接口\n  getDepartInfo: () => api.get(API_ENDPOINTS.USER.DEPART_INFO),\n\n  // 修改密码 - jeecg-boot标准接口\n  updatePassword: (data) => api.put(API_ENDPOINTS.USER.UPDATE_PASSWORD, data),\n\n  // 上传头像 - jeecg-boot标准文件上传\n  uploadAvatar: (filePath) => api.upload(API_ENDPOINTS.USER.UPLOAD_AVATAR, filePath, 'file'),\n\n  // 获取用户扩展信息 - 自定义接口\n  getUserProfile: () => api.get(API_ENDPOINTS.USER.PROFILE),\n\n  // 更新用户扩展信息 - 自定义接口\n  updateUserProfile: (data) => api.put(API_ENDPOINTS.USER.UPDATE_PROFILE, data),\n\n  // 用户签到 - 自定义接口\n  checkin: () => api.post(API_ENDPOINTS.USER.CHECKIN),\n\n  // 获取签到记录 - 自定义接口\n  getCheckinHistory: (params) => api.get(API_ENDPOINTS.USER.CHECKIN_HISTORY, params),\n\n  // 获取学习统计 - 自定义接口\n  getStudyStats: () => api.get(API_ENDPOINTS.USER.STATISTICS),\n\n  // 验证码相关接口\n  getCaptcha: (key = '1629428467008') => {\n    const url = buildApiUrl(API_ENDPOINTS.USER.CAPTCHA, { key })\n    console.log('📤 请求验证码:', url)\n    return api.get(url)\n  },\n  checkCaptcha: (data) => {\n    console.log('📤 验证验证码:', data)\n    return api.post(API_ENDPOINTS.USER.CHECK_CAPTCHA, data)\n  }\n}\n\n// 课程相关接口\nexport const courseApi = {\n  // 获取课程列表\n  getCourseList: (params) => api.get('/app/course/list', params),\n\n  // 获取课程详情\n  getCourseDetail: (courseId) => api.get(`/app/course/${courseId}`),\n\n  // 获取课时列表\n  getLessonList: (courseId, params) => api.get(`/app/course/${courseId}/lessons`, params),\n\n  // 获取课时详情\n  getLessonDetail: (lessonId) => api.get(`/app/lesson/${lessonId}`),\n\n  // 开始学习课时\n  startLesson: (lessonId) => api.post(`/app/lesson/${lessonId}/start`),\n\n  // 完成课时学习\n  completeLesson: (lessonId, data) => api.post(`/app/lesson/${lessonId}/complete`, data),\n\n  // 收藏/取消收藏课程\n  toggleFavorite: (courseId, isFavorite) => {\n    return isFavorite\n      ? api.delete(`/app/course/${courseId}/favorite`)\n      : api.post(`/app/course/${courseId}/favorite`)\n  }\n}\n\n// 排行榜相关接口\nexport const leaderboardApi = {\n  // 获取积分排行榜\n  getPointsRanking: (params) => api.get('/app/leaderboard/points', params),\n\n  // 获取学习时长排行榜\n  getStudyTimeRanking: (params) => api.get('/app/leaderboard/studytime', params),\n\n  // 获取连续签到排行榜\n  getCheckinRanking: (params) => api.get('/app/leaderboard/checkin', params)\n}\n\n// 练习相关接口\nexport const practiceApi = {\n  // 提交录音练习（文件上传）\n  submitPractice: (data) => api.post('/app/practice/submit', data),\n\n  // 提交录音练习（Base64）\n  submitPracticeBase64: (data) => api.post('/app/practice/submitBase64', data),\n\n  // 获取练习历史\n  getHistory: (params) => api.get('/app/practice/history', params),\n\n  // 获取练习统计\n  getStatistics: () => api.get('/app/practice/statistics'),\n\n  // 获取支持的语言类型\n  getSupportedLanguages: () => api.get('/app/practice/supportedLanguages'),\n\n  // 获取支持的题型\n  getSupportedCategories: (language) => api.get('/app/practice/supportedCategories', { language }),\n\n  // 获取评测配置\n  getEvaluationConfig: () => api.get('/app/practice/evaluationConfig')\n}\n\n// 支付相关接口\nexport const paymentApi = {\n  // 创建订单\n  createOrder: (data) => api.post('/api/v1/payment/create', data),\n  \n  // 查询订单状态\n  getOrderStatus: (orderId) => api.get(`/api/v1/payment/status/${orderId}`),\n  \n  // 获取支付方式\n  getPaymentMethods: () => api.get('/api/v1/payment/methods')\n}\n\n// AI相关接口\nexport const aiApi = {\n  // 语音评测\n  evaluate: (data) => api.post('/api/v1/ai/evaluate', data),\n  \n  // 获取评测历史\n  getEvaluationHistory: (params) => api.get('/api/v1/ai/history', params)\n}\n\n// 系统相关接口\nexport const systemApi = {\n  // 获取系统配置\n  getConfig: () => api.get('/api/v1/system/config'),\n\n  // 获取版本信息\n  getVersion: () => api.get('/api/v1/system/version'),\n\n  // 意见反馈\n  feedback: (data) => api.post('/api/v1/system/feedback', data),\n\n  // 获取帮助文档\n  getHelp: () => api.get('/api/v1/system/help')\n}\n\n// 签到相关接口（为了向后兼容，创建别名）\nexport const checkinApi = {\n  // 每日签到\n  checkin: () => userApi.checkin(),\n\n  // 获取签到记录\n  getHistory: (params) => userApi.getCheckinHistory(params),\n\n  // 获取签到统计\n  getStatistics: () => userApi.getStudyStats(),\n\n  // 获取排行榜\n  getRanking: (params) => leaderboardApi.getCheckinRanking(params)\n}\n\n// 导出所有API\nexport default {\n  user: userApi,\n  course: courseApi,\n  checkin: checkinApi,\n  leaderboard: leaderboardApi,\n  practice: practiceApi,\n  payment: paymentApi,\n  ai: aiApi,\n  system: systemApi\n}\n"], "names": ["get", "post", "put", "del", "upload", "API_ENDPOINTS", "buildApiUrl", "uni"], "mappings": ";;;;AASA,MAAM,MAAM;AAAA,EACZ,KAAEA,cAAG;AAAA,EACL,MAAEC,cAAI;AAAA,EACN,KAAEC,cAAG;AAAA,EACH,QAAQC,cAAG;AAAA,EACb,QAAEC,cAAM;AACR;AAGY,MAAC,UAAU;AAAA;AAAA,EAErB,OAAO,CAAC,SAAS,IAAI,KAAKC,WAAAA,cAAc,KAAK,OAAO,IAAI;AAAA;AAAA,EAGxD,QAAQ,MAAM,IAAI,KAAKA,WAAAA,cAAc,KAAK,MAAM;AAAA;AAAA,EAGhD,aAAa,MAAM,IAAI,IAAIA,WAAAA,cAAc,KAAK,IAAI;AAAA;AAAA,EAGlD,eAAe,MAAM,IAAI,IAAIA,WAAAA,cAAc,KAAK,WAAW;AAAA;AAAA,EAG3D,gBAAgB,CAAC,SAAS,IAAI,IAAIA,WAAAA,cAAc,KAAK,iBAAiB,IAAI;AAAA;AAAA,EAG1E,cAAc,CAAC,aAAa,IAAI,OAAOA,WAAa,cAAC,KAAK,eAAe,UAAU,MAAM;AAAA;AAAA,EAGzF,gBAAgB,MAAM,IAAI,IAAIA,WAAAA,cAAc,KAAK,OAAO;AAAA;AAAA,EAGxD,mBAAmB,CAAC,SAAS,IAAI,IAAIA,WAAAA,cAAc,KAAK,gBAAgB,IAAI;AAAA;AAAA,EAG5E,SAAS,MAAM,IAAI,KAAKA,WAAAA,cAAc,KAAK,OAAO;AAAA;AAAA,EAGlD,mBAAmB,CAAC,WAAW,IAAI,IAAIA,WAAAA,cAAc,KAAK,iBAAiB,MAAM;AAAA;AAAA,EAGjF,eAAe,MAAM,IAAI,IAAIA,WAAAA,cAAc,KAAK,UAAU;AAAA;AAAA,EAG1D,YAAY,CAAC,MAAM,oBAAoB;AACrC,UAAM,MAAMC,WAAAA,YAAYD,WAAa,cAAC,KAAK,SAAS,EAAE,KAAK;AAC3DE,kBAAAA,MAAA,MAAA,OAAA,sBAAY,aAAa,GAAG;AAC5B,WAAO,IAAI,IAAI,GAAG;AAAA,EACnB;AAAA,EACD,cAAc,CAAC,SAAS;AACtBA,kBAAAA,MAAA,MAAA,OAAA,sBAAY,aAAa,IAAI;AAC7B,WAAO,IAAI,KAAKF,WAAAA,cAAc,KAAK,eAAe,IAAI;AAAA,EACvD;AACH;AAGY,MAAC,YAAY;AAAA;AAAA,EAEvB,eAAe,CAAC,WAAW,IAAI,IAAI,oBAAoB,MAAM;AAAA;AAAA,EAG7D,iBAAiB,CAAC,aAAa,IAAI,IAAI,eAAe,QAAQ,EAAE;AAAA;AAAA,EAGhE,eAAe,CAAC,UAAU,WAAW,IAAI,IAAI,eAAe,QAAQ,YAAY,MAAM;AAAA;AAAA,EAGtF,iBAAiB,CAAC,aAAa,IAAI,IAAI,eAAe,QAAQ,EAAE;AAAA;AAAA,EAGhE,aAAa,CAAC,aAAa,IAAI,KAAK,eAAe,QAAQ,QAAQ;AAAA;AAAA,EAGnE,gBAAgB,CAAC,UAAU,SAAS,IAAI,KAAK,eAAe,QAAQ,aAAa,IAAI;AAAA;AAAA,EAGrF,gBAAgB,CAAC,UAAU,eAAe;AACxC,WAAO,aACH,IAAI,OAAO,eAAe,QAAQ,WAAW,IAC7C,IAAI,KAAK,eAAe,QAAQ,WAAW;AAAA,EAChD;AACH;AAGY,MAAC,iBAAiB;AAAA;AAAA,EAE5B,kBAAkB,CAAC,WAAW,IAAI,IAAI,2BAA2B,MAAM;AAAA;AAAA,EAGvE,qBAAqB,CAAC,WAAW,IAAI,IAAI,8BAA8B,MAAM;AAAA;AAAA,EAG7E,mBAAmB,CAAC,WAAW,IAAI,IAAI,4BAA4B,MAAM;AAC3E;AAGY,MAAC,cAAc;AAAA;AAAA,EAEzB,gBAAgB,CAAC,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAAA;AAAA,EAG/D,sBAAsB,CAAC,SAAS,IAAI,KAAK,8BAA8B,IAAI;AAAA;AAAA,EAG3E,YAAY,CAAC,WAAW,IAAI,IAAI,yBAAyB,MAAM;AAAA;AAAA,EAG/D,eAAe,MAAM,IAAI,IAAI,0BAA0B;AAAA;AAAA,EAGvD,uBAAuB,MAAM,IAAI,IAAI,kCAAkC;AAAA;AAAA,EAGvE,wBAAwB,CAAC,aAAa,IAAI,IAAI,qCAAqC,EAAE,UAAU;AAAA;AAAA,EAG/F,qBAAqB,MAAM,IAAI,IAAI,gCAAgC;AACrE;;;;;"}