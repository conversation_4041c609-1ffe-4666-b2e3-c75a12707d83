# 错误解决方案

## 问题描述

错误信息：`TypeError: Cannot set property 'globalData' of undefined`

这个错误通常发生在尝试设置 `globalData` 时，目标对象是 `undefined`。

## 原因分析

1. **时序问题**：在 `onLaunch` 阶段，`getApp()` 可能还没有完全初始化
2. **作用域问题**：在某些情况下，`this` 指向可能不正确
3. **初始化顺序**：App实例的 `globalData` 属性可能还未创建

## 解决方案

### 1. 直接在App.vue中定义globalData（推荐）

```javascript
// App.vue
export default {
  // 直接定义全局数据
  globalData: {
    systemInfo: null,
    statusBarHeight: 0,
    navBarHeight: 44,
    windowHeight: 0,
    windowWidth: 0,
    userInfo: null,
    token: null,
    baseUrl: 'http://localhost:8080/kuma',
    version: '1.0.0',
    isLogin: false
  },
  
  onLaunch() {
    this.initApp()
  },
  
  methods: {
    initApp() {
      // 直接使用 this.globalData 而不是 getApp().globalData
      const systemInfo = uni.getSystemInfoSync()
      this.globalData.systemInfo = systemInfo
      this.globalData.statusBarHeight = systemInfo.statusBarHeight || 0
      // ...其他初始化
    }
  }
}
```

### 2. 安全的getApp()调用

```javascript
// utils/common.js
export function getAppInstance() {
  try {
    return getApp()
  } catch (error) {
    console.warn('获取App实例失败:', error)
    return null
  }
}

export function getGlobalData(key) {
  const app = getAppInstance()
  if (!app || !app.globalData) {
    return key ? undefined : {}
  }
  return key ? app.globalData[key] : app.globalData
}
```

### 3. 延迟初始化

```javascript
// 在onReady中初始化而不是onLaunch
onReady() {
  this.initGlobalData()
},

methods: {
  initGlobalData() {
    // 确保App实例已完全初始化
    setTimeout(() => {
      const app = getApp()
      if (app && app.globalData) {
        // 安全地设置全局数据
      }
    }, 100)
  }
}
```

## 最佳实践

### 1. 使用Vuex替代globalData

```javascript
// store/index.js
import { createStore } from 'vuex'

export default createStore({
  state: {
    systemInfo: null,
    userInfo: null,
    token: null
  },
  mutations: {
    SET_SYSTEM_INFO(state, info) {
      state.systemInfo = info
    },
    SET_USER_INFO(state, info) {
      state.userInfo = info
    }
  }
})
```

### 2. 使用本地存储

```javascript
// 直接使用uni.getStorageSync和uni.setStorageSync
const userInfo = uni.getStorageSync('userInfo')
uni.setStorageSync('userInfo', newUserInfo)
```

### 3. 创建全局状态管理

```javascript
// utils/state.js
class GlobalState {
  constructor() {
    this.data = {
      systemInfo: null,
      userInfo: null,
      token: null
    }
  }
  
  set(key, value) {
    this.data[key] = value
  }
  
  get(key) {
    return this.data[key]
  }
}

export default new GlobalState()
```

## 调试方法

### 1. 检查App实例

```javascript
console.log('App实例:', getApp())
console.log('globalData:', getApp()?.globalData)
```

### 2. 使用try-catch包装

```javascript
try {
  const app = getApp()
  if (app && app.globalData) {
    app.globalData.someProperty = value
  } else {
    console.warn('App实例或globalData不存在')
  }
} catch (error) {
  console.error('设置globalData失败:', error)
}
```

### 3. 添加调试页面

访问 `/pages/debug/debug` 查看应用状态和全局数据。

## 预防措施

1. **避免在onLaunch中直接操作globalData**
2. **使用this.globalData而不是getApp().globalData**
3. **添加空值检查和错误处理**
4. **考虑使用更现代的状态管理方案**

## 相关文档

- [uni-app应用生命周期](https://uniapp.dcloud.net.cn/collocation/App.html#applifecycle)
- [uni-app全局变量](https://uniapp.dcloud.net.cn/collocation/App.html#globaldata)
- [Vuex状态管理](https://vuex.vuejs.org/)

## 更新记录

- 2024-03-22: 简化globalData设置，移除复杂的安全检查
- 2024-03-22: 添加调试工具和错误处理指南
