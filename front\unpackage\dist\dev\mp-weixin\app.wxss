/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/*每个页面公共css */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/* 全局样式重置 - 小程序兼容 */
view, text, button, input, textarea, image, scroll-view {
  box-sizing: border-box;
}
page {
  background-color: #f6f6f6;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  line-height: 1.6;
}
/* 通用类 */
.container {
  padding: 0 32rpx;
}
.flex {
  display: flex;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
/* 文字大小 */
.text-xs {
  font-size: 24rpx;
}
.text-sm {
  font-size: 28rpx;
}
.text-base {
  font-size: 32rpx;
}
.text-lg {
  font-size: 36rpx;
}
.text-xl {
  font-size: 40rpx;
}
.text-2xl {
  font-size: 48rpx;
}
/* 文字颜色 */
.text-primary {
  color: #667eea;
}
.text-white {
  color: #ffffff;
}
.text-gray-600 {
  color: #666666;
}
.text-gray-800 {
  color: #333333;
}
.text-green-500 {
  color: #10b981;
}
/* 背景颜色 */
.bg-white {
  background-color: #ffffff;
}
.bg-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
/* 边距 */
.m-0 {
  margin: 0;
}
.mt-1 {
  margin-top: 8rpx;
}
.mt-2 {
  margin-top: 16rpx;
}
.mt-3 {
  margin-top: 24rpx;
}
.mt-4 {
  margin-top: 32rpx;
}
.mb-1 {
  margin-bottom: 8rpx;
}
.mb-2 {
  margin-bottom: 16rpx;
}
.mb-3 {
  margin-bottom: 24rpx;
}
.mb-4 {
  margin-bottom: 32rpx;
}
.p-0 {
  padding: 0;
}
.pt-1 {
  padding-top: 8rpx;
}
.pt-2 {
  padding-top: 16rpx;
}
.pt-3 {
  padding-top: 24rpx;
}
.pt-4 {
  padding-top: 32rpx;
}
.pb-1 {
  padding-bottom: 8rpx;
}
.pb-2 {
  padding-bottom: 16rpx;
}
.pb-3 {
  padding-bottom: 24rpx;
}
.pb-4 {
  padding-bottom: 32rpx;
}
/* 圆角 */
.rounded {
  border-radius: 8rpx;
}
.rounded-lg {
  border-radius: 16rpx;
}
.rounded-xl {
  border-radius: 24rpx;
}
.rounded-full {
  border-radius: 50%;
}
/* 阴影 */
.shadow {
  box-shadow: 0 8rpx 64rpx rgba(0, 0, 0, 0.1);
}
.shadow-lg {
  box-shadow: 0 16rpx 96rpx rgba(0, 0, 0, 0.15);
}
/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.95);
  /* backdrop-filter: blur(20rpx); 小程序不支持 */
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 48rpx;
}
/* 按钮样式 */
.btn {
  padding: 24rpx 48rpx;
  border-radius: 16px;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
}
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(103, 126, 234, 0.3);
}
/* 渐变背景 */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.gradient-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.gradient-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.gradient-warning {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}
.gradient-orange {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}