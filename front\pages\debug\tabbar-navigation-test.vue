<template>
	<view class="tabbar-test-container">
		<view class="test-header">
			<text class="test-title">TabBar跳转测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">TabBar页面列表</text>
			<view class="tabbar-list">
				<view class="tabbar-item" v-for="(page, index) in tabBarPages" :key="index">
					<text class="page-name">{{ page.name }}</text>
					<text class="page-path">{{ page.path }}</text>
					<view class="page-actions">
						<button class="action-btn success" @tap="testSwitchTab(page)">switchTab</button>
						<button class="action-btn error" @tap="testNavigateTo(page)">navigateTo</button>
						<button class="action-btn primary" @tap="testSmartNavigate(page)">智能跳转</button>
					</view>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">普通页面测试</text>
			<view class="normal-list">
				<view class="normal-item" v-for="(page, index) in normalPages" :key="index">
					<text class="page-name">{{ page.name }}</text>
					<text class="page-path">{{ page.path }}</text>
					<view class="page-actions">
						<button class="action-btn primary" @tap="testNavigateToNormal(page)">navigateTo</button>
						<button class="action-btn primary" @tap="testSmartNavigateNormal(page)">智能跳转</button>
					</view>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试日志</text>
			<scroll-view class="log-container" scroll-y>
				<view class="log-item" v-for="(log, index) in testLogs" :key="index">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message" :class="log.type">{{ log.message }}</text>
				</view>
			</scroll-view>
		</view>
		
		<view class="test-section">
			<text class="section-title">使用说明</text>
			<view class="usage-info">
				<text class="usage-title">✅ 正确做法:</text>
				<text class="usage-desc">• TabBar页面使用 switchTab 跳转</text>
				<text class="usage-desc">• 普通页面使用 navigateTo 跳转</text>
				<text class="usage-desc">• 使用智能跳转工具自动识别</text>
				
				<text class="usage-title error">❌ 错误做法:</text>
				<text class="usage-desc">• TabBar页面使用 navigateTo 跳转（会报错）</text>
				<text class="usage-desc">• switchTab 跳转普通页面（不推荐）</text>
			</view>
		</view>
	</view>
</template>

<script>
import { navigate } from '@/utils/index.js'

export default {
	data() {
		return {
			tabBarPages: [
				{ name: '首页', path: 'pages/index/index' },
				{ name: '课程列表', path: 'pages/course/list' },
				{ name: '打卡', path: 'pages/checkin/checkin' },
				{ name: '排行榜', path: 'pages/leaderboard/leaderboard' },
				{ name: '个人中心', path: 'pages/profile/profile' }
			],
			normalPages: [
				{ name: '课程详情', path: 'pages/course/detail' },
				{ name: '练习页面', path: 'pages/practice/practice' },
				{ name: '登录页面', path: 'pages/login/login' },
				{ name: 'API配置测试', path: 'pages/debug/api-config-test' }
			],
			testLogs: []
		}
	},
	
	onLoad() {
		this.addLog('TabBar跳转测试页面加载完成', 'info')
	},
	
	methods: {
		// 添加日志
		addLog(message, type = 'info') {
			const now = new Date()
			const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
			
			this.testLogs.unshift({
				time,
				message,
				type
			})
			
			if (this.testLogs.length > 20) {
				this.testLogs = this.testLogs.slice(0, 20)
			}
		},
		
		// 测试switchTab跳转TabBar页面（正确方法）
		testSwitchTab(page) {
			this.addLog(`测试switchTab跳转: ${page.name}`, 'info')
			
			uni.switchTab({
				url: `/${page.path}`,
				success: () => {
					this.addLog(`✅ switchTab跳转成功: ${page.name}`, 'success')
				},
				fail: (error) => {
					this.addLog(`❌ switchTab跳转失败: ${error.errMsg}`, 'error')
				}
			})
		},
		
		// 测试navigateTo跳转TabBar页面（错误方法，会失败）
		testNavigateTo(page) {
			this.addLog(`测试navigateTo跳转TabBar页面: ${page.name}`, 'warning')
			
			uni.navigateTo({
				url: `/${page.path}`,
				success: () => {
					this.addLog(`✅ navigateTo跳转成功: ${page.name}`, 'success')
				},
				fail: (error) => {
					this.addLog(`❌ navigateTo跳转失败: ${error.errMsg}`, 'error')
					this.addLog('这是预期的错误，TabBar页面不能用navigateTo', 'warning')
				}
			})
		},
		
		// 测试智能跳转TabBar页面
		testSmartNavigate(page) {
			this.addLog(`测试智能跳转TabBar页面: ${page.name}`, 'info')
			
			try {
				navigate.to(page.path, { test: 'param' })
				this.addLog(`✅ 智能跳转调用成功: ${page.name}`, 'success')
			} catch (error) {
				this.addLog(`❌ 智能跳转失败: ${error.message}`, 'error')
			}
		},
		
		// 测试navigateTo跳转普通页面
		testNavigateToNormal(page) {
			this.addLog(`测试navigateTo跳转普通页面: ${page.name}`, 'info')
			
			uni.navigateTo({
				url: `/${page.path}?test=param`,
				success: () => {
					this.addLog(`✅ navigateTo跳转成功: ${page.name}`, 'success')
				},
				fail: (error) => {
					this.addLog(`❌ navigateTo跳转失败: ${error.errMsg}`, 'error')
				}
			})
		},
		
		// 测试智能跳转普通页面
		testSmartNavigateNormal(page) {
			this.addLog(`测试智能跳转普通页面: ${page.name}`, 'info')
			
			try {
				navigate.to(page.path, { test: 'param' })
				this.addLog(`✅ 智能跳转调用成功: ${page.name}`, 'success')
			} catch (error) {
				this.addLog(`❌ 智能跳转失败: ${error.message}`, 'error')
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.tabbar-test-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.test-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 24rpx;
		border-bottom: 2rpx solid #eee;
		padding-bottom: 16rpx;
	}
}

.tabbar-list, .normal-list {
	.tabbar-item, .normal-item {
		border: 1rpx solid #eee;
		border-radius: 8rpx;
		padding: 24rpx;
		margin-bottom: 16rpx;
		
		.page-name {
			display: block;
			font-size: 28rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 8rpx;
		}
		
		.page-path {
			display: block;
			font-size: 24rpx;
			color: #666;
			margin-bottom: 16rpx;
			font-family: monospace;
		}
		
		.page-actions {
			display: flex;
			gap: 12rpx;
			
			.action-btn {
				flex: 1;
				height: 60rpx;
				border: none;
				border-radius: 6rpx;
				font-size: 24rpx;
				color: white;
				
				&.primary {
					background: #1890ff;
				}
				
				&.success {
					background: #52c41a;
				}
				
				&.error {
					background: #ff4d4f;
				}
				
				&:active {
					opacity: 0.8;
				}
			}
		}
	}
}

.log-container {
	height: 300rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	padding: 16rpx;
	
	.log-item {
		display: flex;
		margin-bottom: 12rpx;
		font-size: 24rpx;
		
		.log-time {
			color: #999;
			width: 120rpx;
			flex-shrink: 0;
		}
		
		.log-message {
			color: #333;
			word-break: break-all;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
			
			&.warning {
				color: #faad14;
			}
			
			&.info {
				color: #1890ff;
			}
		}
	}
}

.usage-info {
	.usage-title {
		display: block;
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin: 16rpx 0 8rpx 0;
		
		&.error {
			color: #ff4d4f;
		}
	}
	
	.usage-desc {
		display: block;
		font-size: 26rpx;
		color: #666;
		margin-bottom: 8rpx;
		padding-left: 16rpx;
	}
}
</style>
