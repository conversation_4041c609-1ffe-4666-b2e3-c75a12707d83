package com.learning.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.learning.entity.KumaCourseCategory;
import com.learning.service.IKumaCourseCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = "APP-课程分类管理")
@RestController
@RequestMapping("/app/category")
public class CourseCategoryController {

    @Autowired
    private IKumaCourseCategoryService courseCategoryService;

    @AutoLog(value = "课程分类-获取所有分类")
    @ApiOperation(value = "课程分类-获取所有分类", notes = "课程分类-获取所有分类")
    @GetMapping("/list")
    public Result<List<KumaCourseCategory>> getAllCategories() {
        LambdaQueryWrapper<KumaCourseCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KumaCourseCategory::getStatus, 1) // 只查询启用的分类
                   .orderByAsc(KumaCourseCategory::getSort); // 按排序字段升序
        
        List<KumaCourseCategory> list = courseCategoryService.list(queryWrapper);
        return Result.OK(list);
    }

    @AutoLog(value = "课程分类-通过id查询")
    @ApiOperation(value = "课程分类-通过id查询", notes = "课程分类-通过id查询")
    @GetMapping("/queryById")
    public Result<KumaCourseCategory> queryById(@RequestParam(name = "id") String id) {
        KumaCourseCategory category = courseCategoryService.getById(id);
        if (category == null) {
            return Result.error("未找到对应分类");
        }
        return Result.OK(category);
    }
}
