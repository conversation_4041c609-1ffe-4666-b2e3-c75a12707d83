"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 0,
      courseId: "",
      loading: true,
      courseDetail: null,
      courseTitle: "加载中...",
      courseSubtitle: "",
      courseDuration: 0,
      audioUrl: "",
      isPlaying: false,
      isRepeat: false,
      currentTime: 0,
      totalTime: 0,
      audioProgress: 0,
      activeTab: 0,
      tabs: [
        { name: "日文原文" },
        { name: "中文翻译" },
        { name: "重点词汇" }
      ],
      dialogues: [
        {
          speaker: "A",
          text: "いらっしゃいませ。何名様ですか？",
          translation: "欢迎光临。请问几位？"
        },
        {
          speaker: "B",
          text: "二人です。",
          translation: "两个人。"
        },
        {
          speaker: "A",
          text: "こちらのお席へどうぞ。メニューです。",
          translation: "请到这边的座位。这是菜单。"
        },
        {
          speaker: "B",
          text: "ありがとうございます。",
          translation: "谢谢。"
        }
      ],
      vocabulary: [
        {
          word: "いらっしゃいませ",
          pronunciation: "irasshaimase",
          meaning: "欢迎光临"
        },
        {
          word: "何名様",
          pronunciation: "nanmeisama",
          meaning: "几位客人"
        },
        {
          word: "メニュー",
          pronunciation: "menyuu",
          meaning: "菜单"
        }
      ],
      studyTips: [
        {
          title: "敬语的使用",
          description: "注意服务员使用的敬语表达"
        },
        {
          title: "点餐用语",
          description: "掌握常用的点餐表达方式"
        },
        {
          title: "支付方式",
          description: "了解不同支付方式的表达"
        }
      ]
    };
  },
  onLoad(options) {
    this.courseId = options.id;
    this.courseTitle = decodeURIComponent(options.title || "加载中...");
    this.initPage();
  },
  methods: {
    initPage() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight;
      this.loadCourseDetail();
    },
    // 加载课程详情
    loadCourseDetail() {
      return __async(this, null, function* () {
        if (!this.courseId) {
          common_vendor.index.showToast({
            title: "课程ID不能为空",
            icon: "none"
          });
          return;
        }
        this.loading = true;
        try {
          common_vendor.index.__f__("log", "at pages/course/detail.vue:229", "加载课程详情:", this.courseId);
          const response = yield api_index.courseApi.getCourseDetail(this.courseId);
          if (response.success && response.data) {
            this.courseDetail = response.data;
            this.updateCourseInfo(response.data);
          } else {
            throw new Error(response.message || "获取课程详情失败");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/course/detail.vue:241", "加载课程详情失败:", error);
          this.setDefaultCourseData();
          common_vendor.index.showToast({
            title: "加载失败，显示示例数据",
            icon: "none"
          });
        } finally {
          this.loading = false;
        }
      });
    },
    // 更新课程信息
    updateCourseInfo(courseData) {
      this.courseTitle = courseData.title || this.courseTitle;
      this.courseSubtitle = courseData.subtitle || courseData.description || "";
      this.courseDuration = courseData.duration || 0;
      this.audioUrl = courseData.audioUrl || "";
      this.totalTime = (courseData.duration || 0) * 60;
      if (courseData.dialogues && courseData.dialogues.length > 0) {
        this.dialogues = courseData.dialogues;
      }
      if (courseData.vocabulary && courseData.vocabulary.length > 0) {
        this.vocabulary = courseData.vocabulary;
      }
      if (courseData.studyTips && courseData.studyTips.length > 0) {
        this.studyTips = courseData.studyTips;
      }
    },
    // 设置默认课程数据
    setDefaultCourseData() {
      this.courseTitle = this.courseTitle || "第4课：レストラン";
      this.courseSubtitle = "餐厅点餐对话";
      this.courseDuration = 7;
      this.totalTime = 420;
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    togglePlay() {
      this.isPlaying = !this.isPlaying;
      if (this.isPlaying) {
        this.startAudioProgress();
      } else {
        this.pauseAudioProgress();
      }
    },
    startAudioProgress() {
      this.audioTimer = setInterval(() => {
        if (this.currentTime < this.totalTime) {
          this.currentTime += 1;
          this.audioProgress = this.currentTime / this.totalTime * 100;
        } else {
          this.isPlaying = false;
          clearInterval(this.audioTimer);
        }
      }, 1e3);
    },
    pauseAudioProgress() {
      if (this.audioTimer) {
        clearInterval(this.audioTimer);
      }
    },
    seekAudio(e) {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.detail.x - rect.left;
      const progress = clickX / rect.width;
      this.currentTime = Math.floor(this.totalTime * progress);
      this.audioProgress = progress * 100;
    },
    seekBackward() {
      this.currentTime = Math.max(0, this.currentTime - 10);
      this.audioProgress = this.currentTime / this.totalTime * 100;
    },
    seekForward() {
      this.currentTime = Math.min(this.totalTime, this.currentTime + 10);
      this.audioProgress = this.currentTime / this.totalTime * 100;
    },
    toggleRepeat() {
      this.isRepeat = !this.isRepeat;
    },
    switchTab(index) {
      this.activeTab = index;
    },
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins}:${secs.toString().padStart(2, "0")}`;
    },
    startPractice() {
      common_vendor.index.navigateTo({
        url: `/pages/practice/practice?courseId=${this.courseId}&title=${this.courseTitle}`
      });
    }
  },
  onUnload() {
    if (this.audioTimer) {
      clearInterval(this.audioTimer);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.t($data.courseTitle),
    d: common_vendor.t($data.courseSubtitle),
    e: common_vendor.t($data.courseDuration),
    f: common_vendor.n($data.isPlaying ? "icon-pause" : "icon-play"),
    g: common_vendor.o((...args) => $options.togglePlay && $options.togglePlay(...args)),
    h: $data.audioProgress + "%",
    i: common_vendor.o((...args) => $options.seekAudio && $options.seekAudio(...args)),
    j: common_vendor.t($options.formatTime($data.currentTime)),
    k: common_vendor.o((...args) => $options.seekBackward && $options.seekBackward(...args)),
    l: common_vendor.o((...args) => $options.seekForward && $options.seekForward(...args)),
    m: common_vendor.o((...args) => $options.toggleRepeat && $options.toggleRepeat(...args)),
    n: $data.isRepeat ? 1 : "",
    o: common_vendor.t($options.formatTime($data.totalTime)),
    p: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.activeTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    q: $data.activeTab === 0
  }, $data.activeTab === 0 ? {
    r: common_vendor.f($data.dialogues, (item, index, i0) => {
      return {
        a: common_vendor.t(item.speaker),
        b: common_vendor.t(item.text),
        c: index
      };
    })
  } : {}, {
    s: $data.activeTab === 1
  }, $data.activeTab === 1 ? {
    t: common_vendor.f($data.dialogues, (item, index, i0) => {
      return {
        a: common_vendor.t(item.speaker),
        b: common_vendor.t(item.translation),
        c: index
      };
    })
  } : {}, {
    v: $data.activeTab === 2
  }, $data.activeTab === 2 ? {
    w: common_vendor.f($data.vocabulary, (word, index, i0) => {
      return {
        a: common_vendor.t(word.word),
        b: common_vendor.t(word.pronunciation),
        c: common_vendor.t(word.meaning),
        d: index
      };
    })
  } : {}, {
    x: common_vendor.f($data.studyTips, (tip, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(tip.title),
        c: common_vendor.t(tip.description),
        d: index
      };
    }),
    y: common_vendor.o((...args) => $options.startPractice && $options.startPractice(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3d21314d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/course/detail.js.map
