"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  data() {
    return {
      tabBarPages: [
        { name: "首页", path: "pages/index/index" },
        { name: "课程列表", path: "pages/course/list" },
        { name: "打卡", path: "pages/checkin/checkin" },
        { name: "排行榜", path: "pages/leaderboard/leaderboard" },
        { name: "个人中心", path: "pages/profile/profile" }
      ],
      normalPages: [
        { name: "课程详情", path: "pages/course/detail" },
        { name: "练习页面", path: "pages/practice/practice" },
        { name: "登录页面", path: "pages/login/login" },
        { name: "API配置测试", path: "pages/debug/api-config-test" }
      ],
      testLogs: []
    };
  },
  onLoad() {
    this.addLog("TabBar跳转测试页面加载完成", "info");
  },
  methods: {
    // 添加日志
    addLog(message, type = "info") {
      const now = /* @__PURE__ */ new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;
      this.testLogs.unshift({
        time,
        message,
        type
      });
      if (this.testLogs.length > 20) {
        this.testLogs = this.testLogs.slice(0, 20);
      }
    },
    // 测试switchTab跳转TabBar页面（正确方法）
    testSwitchTab(page) {
      this.addLog(`测试switchTab跳转: ${page.name}`, "info");
      common_vendor.index.switchTab({
        url: `/${page.path}`,
        success: () => {
          this.addLog(`✅ switchTab跳转成功: ${page.name}`, "success");
        },
        fail: (error) => {
          this.addLog(`❌ switchTab跳转失败: ${error.errMsg}`, "error");
        }
      });
    },
    // 测试navigateTo跳转TabBar页面（错误方法，会失败）
    testNavigateTo(page) {
      this.addLog(`测试navigateTo跳转TabBar页面: ${page.name}`, "warning");
      common_vendor.index.navigateTo({
        url: `/${page.path}`,
        success: () => {
          this.addLog(`✅ navigateTo跳转成功: ${page.name}`, "success");
        },
        fail: (error) => {
          this.addLog(`❌ navigateTo跳转失败: ${error.errMsg}`, "error");
          this.addLog("这是预期的错误，TabBar页面不能用navigateTo", "warning");
        }
      });
    },
    // 测试智能跳转TabBar页面
    testSmartNavigate(page) {
      this.addLog(`测试智能跳转TabBar页面: ${page.name}`, "info");
      try {
        utils_index.navigate.to(page.path, { test: "param" });
        this.addLog(`✅ 智能跳转调用成功: ${page.name}`, "success");
      } catch (error) {
        this.addLog(`❌ 智能跳转失败: ${error.message}`, "error");
      }
    },
    // 测试navigateTo跳转普通页面
    testNavigateToNormal(page) {
      this.addLog(`测试navigateTo跳转普通页面: ${page.name}`, "info");
      common_vendor.index.navigateTo({
        url: `/${page.path}?test=param`,
        success: () => {
          this.addLog(`✅ navigateTo跳转成功: ${page.name}`, "success");
        },
        fail: (error) => {
          this.addLog(`❌ navigateTo跳转失败: ${error.errMsg}`, "error");
        }
      });
    },
    // 测试智能跳转普通页面
    testSmartNavigateNormal(page) {
      this.addLog(`测试智能跳转普通页面: ${page.name}`, "info");
      try {
        utils_index.navigate.to(page.path, { test: "param" });
        this.addLog(`✅ 智能跳转调用成功: ${page.name}`, "success");
      } catch (error) {
        this.addLog(`❌ 智能跳转失败: ${error.message}`, "error");
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.tabBarPages, (page, index, i0) => {
      return {
        a: common_vendor.t(page.name),
        b: common_vendor.t(page.path),
        c: common_vendor.o(($event) => $options.testSwitchTab(page), index),
        d: common_vendor.o(($event) => $options.testNavigateTo(page), index),
        e: common_vendor.o(($event) => $options.testSmartNavigate(page), index),
        f: index
      };
    }),
    b: common_vendor.f($data.normalPages, (page, index, i0) => {
      return {
        a: common_vendor.t(page.name),
        b: common_vendor.t(page.path),
        c: common_vendor.o(($event) => $options.testNavigateToNormal(page), index),
        d: common_vendor.o(($event) => $options.testSmartNavigateNormal(page), index),
        e: index
      };
    }),
    c: common_vendor.f($data.testLogs, (log, index, i0) => {
      return {
        a: common_vendor.t(log.time),
        b: common_vendor.t(log.message),
        c: common_vendor.n(log.type),
        d: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8392dee8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/tabbar-navigation-test.js.map
