<template>
	<view class="user-info-test-container">
		<view class="test-header">
			<text class="test-title">用户信息显示测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">认证状态检查</text>
			<view class="auth-status">
				<view class="status-item">
					<text class="status-label">登录状态:</text>
					<text class="status-value" :class="isAuthenticated ? 'success' : 'error'">
						{{ isAuthenticated ? '已登录' : '未登录' }}
					</text>
				</view>
				<view class="status-item">
					<text class="status-label">Token状态:</text>
					<text class="status-value" :class="hasToken ? 'success' : 'error'">
						{{ hasToken ? '有效' : '无效' }}
					</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">auth-mixin用户信息</text>
			<view class="user-info-display">
				<view class="info-item">
					<text class="info-label">用户ID:</text>
					<text class="info-value">{{ currentUser ? currentUser.id : '无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">用户名:</text>
					<text class="info-value">{{ currentUser ? currentUser.username : '无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">真实姓名:</text>
					<text class="info-value">{{ currentUser ? currentUser.realname : '无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">显示名称:</text>
					<text class="info-value">{{ getCurrentUserName() }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">头像:</text>
					<text class="info-value">{{ currentUser ? currentUser.avatar : '无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">手机号:</text>
					<text class="info-value">{{ currentUser ? currentUser.phone : '无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">邮箱:</text>
					<text class="info-value">{{ currentUser ? currentUser.email : '无' }}</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">本地存储用户信息</text>
			<view class="storage-info">
				<view class="info-item">
					<text class="info-label">存储状态:</text>
					<text class="info-value" :class="hasStorageUserInfo ? 'success' : 'error'">
						{{ hasStorageUserInfo ? '已存储' : '未存储' }}
					</text>
				</view>
				<view class="info-item" v-if="storageUserInfo">
					<text class="info-label">用户名:</text>
					<text class="info-value">{{ storageUserInfo.username || '无' }}</text>
				</view>
				<view class="info-item" v-if="storageUserInfo">
					<text class="info-label">真实姓名:</text>
					<text class="info-value">{{ storageUserInfo.realname || '无' }}</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">全局状态检查</text>
			<view class="global-state">
				<view class="info-item">
					<text class="info-label">globalData.isLogin:</text>
					<text class="info-value" :class="globalIsLogin ? 'success' : 'error'">
						{{ globalIsLogin ? 'true' : 'false' }}
					</text>
				</view>
				<view class="info-item">
					<text class="info-label">globalData.userInfo:</text>
					<text class="info-value" :class="hasGlobalUserInfo ? 'success' : 'error'">
						{{ hasGlobalUserInfo ? '已设置' : '未设置' }}
					</text>
				</view>
				<view class="info-item" v-if="globalUserInfo">
					<text class="info-label">全局用户名:</text>
					<text class="info-value">{{ globalUserInfo.username || '无' }}</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">页面用户信息显示测试</text>
			<view class="page-display-test">
				<view class="display-item">
					<text class="display-label">首页样式显示:</text>
					<text class="display-value">{{ getCurrentUserName() }}，加油！</text>
				</view>
				<view class="display-item">
					<text class="display-label">个人中心样式:</text>
					<view class="profile-style">
						<image class="avatar" :src="getCurrentUserAvatar()" mode="aspectFill"></image>
						<text class="name">{{ getCurrentUserName() }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">操作测试</text>
			<view class="test-buttons">
				<button class="test-btn primary" @tap="refreshUserInfo">刷新用户信息</button>
				<button class="test-btn success" @tap="checkAuthStatus">检查认证状态</button>
				<button class="test-btn warning" @tap="simulateLogin">模拟登录</button>
				<button class="test-btn error" @tap="clearUserInfo">清除用户信息</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试日志</text>
			<scroll-view class="log-container" scroll-y>
				<view class="log-item" v-for="(log, index) in testLogs" :key="index">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message" :class="log.type">{{ log.message }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import authMixin from '@/mixins/auth-mixin.js'
import { getUserInfo, setUserInfo, removeUserInfo } from '@/utils/auth.js'

export default {
	mixins: [authMixin],
	
	data() {
		return {
			hasToken: false,
			hasStorageUserInfo: false,
			storageUserInfo: null,
			globalIsLogin: false,
			hasGlobalUserInfo: false,
			globalUserInfo: null,
			testLogs: []
		}
	},
	
	onLoad() {
		this.addLog('用户信息显示测试页面加载', 'info')
		this.refreshAllInfo()
	},
	
	onShow() {
		this.refreshAllInfo()
	},
	
	methods: {
		// 添加日志
		addLog(message, type = 'info') {
			const now = new Date()
			const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
			
			this.testLogs.unshift({
				time,
				message,
				type
			})
			
			if (this.testLogs.length > 20) {
				this.testLogs = this.testLogs.slice(0, 20)
			}
		},
		
		// 刷新所有信息
		refreshAllInfo() {
			this.checkTokenStatus()
			this.checkStorageUserInfo()
			this.checkGlobalState()
			this.addLog('所有信息刷新完成', 'info')
		},
		
		// 检查Token状态
		checkTokenStatus() {
			const token = uni.getStorageSync('X-Access-Token')
			this.hasToken = !!token
		},
		
		// 检查本地存储用户信息
		checkStorageUserInfo() {
			try {
				const userInfo = getUserInfo()
				this.hasStorageUserInfo = !!userInfo
				this.storageUserInfo = userInfo
			} catch (error) {
				this.hasStorageUserInfo = false
				this.storageUserInfo = null
			}
		},
		
		// 检查全局状态
		checkGlobalState() {
			try {
				const app = getApp()
				if (app && app.globalData) {
					this.globalIsLogin = app.globalData.isLogin || false
					this.globalUserInfo = app.globalData.userInfo
					this.hasGlobalUserInfo = !!this.globalUserInfo
				}
			} catch (error) {
				this.globalIsLogin = false
				this.hasGlobalUserInfo = false
				this.globalUserInfo = null
			}
		},
		
		// 获取用户头像
		getCurrentUserAvatar() {
			if (this.currentUser && this.currentUser.avatar) {
				return this.currentUser.avatar
			}
			return '/static/images/default-avatar.png'
		},
		
		// 刷新用户信息
		refreshUserInfo() {
			this.addLog('手动刷新用户信息', 'info')
			this.checkAuthStatus()
			this.refreshAllInfo()
		},
		
		// 模拟登录
		simulateLogin() {
			this.addLog('模拟登录操作', 'warning')
			
			const mockUserInfo = {
				id: '1001',
				username: 'testuser',
				realname: '测试用户',
				avatar: '/static/images/default-avatar.png',
				phone: '13800138000',
				email: '<EMAIL>'
			}
			
			setUserInfo(mockUserInfo)
			this.addLog('模拟用户信息已保存', 'success')
			this.refreshAllInfo()
		},
		
		// 清除用户信息
		clearUserInfo() {
			this.addLog('清除用户信息', 'warning')
			removeUserInfo()
			this.refreshAllInfo()
		}
	}
}
</script>

<style lang="scss" scoped>
.user-info-test-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.test-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 24rpx;
		border-bottom: 2rpx solid #eee;
		padding-bottom: 16rpx;
	}
}

.auth-status, .user-info-display, .storage-info, .global-state {
	.status-item, .info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		
		&:last-child {
			border-bottom: none;
		}
		
		.status-label, .info-label {
			font-size: 28rpx;
			color: #666;
			width: 200rpx;
			flex-shrink: 0;
		}
		
		.status-value, .info-value {
			font-size: 28rpx;
			color: #333;
			word-break: break-all;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
		}
	}
}

.page-display-test {
	.display-item {
		margin-bottom: 24rpx;
		
		.display-label {
			display: block;
			font-size: 28rpx;
			color: #666;
			margin-bottom: 12rpx;
		}
		
		.display-value {
			font-size: 32rpx;
			color: #333;
			font-weight: bold;
		}
		
		.profile-style {
			display: flex;
			align-items: center;
			
			.avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				margin-right: 16rpx;
			}
			
			.name {
				font-size: 32rpx;
				color: #333;
				font-weight: bold;
			}
		}
	}
}

.test-buttons {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
	
	.test-btn {
		height: 80rpx;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: white;
		
		&.primary {
			background: #1890ff;
		}
		
		&.success {
			background: #52c41a;
		}
		
		&.warning {
			background: #faad14;
		}
		
		&.error {
			background: #ff4d4f;
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}

.log-container {
	height: 300rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	padding: 16rpx;
	
	.log-item {
		display: flex;
		margin-bottom: 12rpx;
		font-size: 24rpx;
		
		.log-time {
			color: #999;
			width: 120rpx;
			flex-shrink: 0;
		}
		
		.log-message {
			color: #333;
			word-break: break-all;
			
			&.success {
				color: #52c41a;
			}
			
			&.error {
				color: #ff4d4f;
			}
			
			&.warning {
				color: #faad14;
			}
			
			&.info {
				color: #1890ff;
			}
		}
	}
}
</style>
