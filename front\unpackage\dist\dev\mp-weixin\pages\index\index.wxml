<view class="page data-v-1cf27b2a"><view class="status-bar data-v-1cf27b2a" style="{{'height:' + a}}"></view><view class="content data-v-1cf27b2a" style="{{'background:' + 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'}}"><view class="header data-v-1cf27b2a"><text class="greeting data-v-1cf27b2a">こんにちは！</text><text class="subtitle data-v-1cf27b2a">今天也要加油学习日语哦～</text></view><view class="study-card card data-v-1cf27b2a"><view class="flex-between mb-4 data-v-1cf27b2a"><view class="data-v-1cf27b2a"><text class="card-title data-v-1cf27b2a">今日学习</text><text wx:if="{{b}}" class="card-subtitle data-v-1cf27b2a">{{c}}，加油！</text></view><view class="progress-ring data-v-1cf27b2a"><text class="progress-text data-v-1cf27b2a">{{d}}%</text></view></view><view class="flex-between data-v-1cf27b2a"><text class="progress-desc data-v-1cf27b2a">已完成 {{e}}/{{f}} 课程</text><view class="checkin-area data-v-1cf27b2a"><text class="{{['checkin-status', 'data-v-1cf27b2a', h && 'checked']}}" bindtap="{{i}}">{{g}}</text></view></view></view><view class="levels-section data-v-1cf27b2a"><text class="section-title data-v-1cf27b2a">选择学习级别</text><view wx:if="{{j}}" class="loading-container data-v-1cf27b2a"><view wx:for="{{k}}" wx:for-item="i" wx:key="a" class="loading-item data-v-1cf27b2a"><view class="loading-skeleton data-v-1cf27b2a"></view></view></view><block wx:else><view wx:for="{{l}}" wx:for-item="level" wx:key="j" class="level-item card data-v-1cf27b2a" bindtap="{{level.k}}"><view class="flex-between data-v-1cf27b2a"><view class="flex data-v-1cf27b2a"><view class="level-icon data-v-1cf27b2a" style="{{'background-color:' + level.c}}"><text class="{{['iconfont', 'data-v-1cf27b2a', level.a]}}" style="{{'color:' + level.b}}"></text></view><view class="level-info data-v-1cf27b2a"><text class="level-name data-v-1cf27b2a">{{level.d}}</text><text class="level-desc data-v-1cf27b2a">{{level.e}}</text></view></view><view class="level-progress data-v-1cf27b2a"><text class="progress-text data-v-1cf27b2a">{{level.f}}/{{level.g}} 课程</text><view class="progress-bar data-v-1cf27b2a"><view class="progress-fill data-v-1cf27b2a" style="{{'width:' + level.h + ';' + ('background-color:' + level.i)}}"></view></view></view></view></view></block></view></view></view>