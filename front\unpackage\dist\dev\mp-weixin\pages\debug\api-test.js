"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      isConnected: false,
      testing: false,
      lastTestTime: "",
      baseUrl: "",
      testResults: [],
      userApis: [
        { name: "获取用户信息", method: "GET", url: "/app/user/info", api: () => api_index.userApi.getUserInfo() },
        { name: "用户签到", method: "POST", url: "/app/user/checkin", api: () => api_index.userApi.checkin() },
        { name: "获取学习统计", method: "GET", url: "/app/user/statistics", api: () => api_index.userApi.getStudyStats() }
      ],
      courseApis: [
        { name: "获取课程列表", method: "GET", url: "/app/course/list", api: () => api_index.courseApi.getCourseList() },
        { name: "获取课程详情", method: "GET", url: "/app/course/1", api: () => api_index.courseApi.getCourseDetail(1) }
      ],
      practiceApis: [
        { name: "获取支持语言", method: "GET", url: "/app/practice/supportedLanguages", api: () => api_index.practiceApi.getSupportedLanguages() },
        { name: "获取评测配置", method: "GET", url: "/app/practice/evaluationConfig", api: () => api_index.practiceApi.getEvaluationConfig() },
        { name: "获取练习统计", method: "GET", url: "/app/practice/statistics", api: () => api_index.practiceApi.getStatistics() }
      ]
    };
  },
  onLoad() {
    this.baseUrl = this.$baseUrl || "http://localhost:8080/jeecg-boot";
    this.testConnection();
  },
  methods: {
    testConnection() {
      return __async(this, null, function* () {
        this.testing = true;
        try {
          const response = yield common_vendor.index.request({
            url: this.baseUrl + "/app/common/health",
            method: "GET",
            timeout: 5e3
          });
          this.isConnected = response.statusCode === 200;
          this.lastTestTime = (/* @__PURE__ */ new Date()).toLocaleString();
          if (this.isConnected) {
            common_vendor.index.showToast({
              title: "连接成功",
              icon: "success"
            });
          } else {
            common_vendor.index.showToast({
              title: "连接失败",
              icon: "none"
            });
          }
        } catch (error) {
          this.isConnected = false;
          this.lastTestTime = (/* @__PURE__ */ new Date()).toLocaleString();
          common_vendor.index.__f__("error", "at pages/debug/api-test.vue:182", "连接测试失败:", error);
          common_vendor.index.showToast({
            title: "连接失败",
            icon: "none"
          });
        } finally {
          this.testing = false;
        }
      });
    },
    testApi(apiConfig) {
      return __async(this, null, function* () {
        apiConfig.testing = true;
        const startTime = Date.now();
        try {
          const result = yield apiConfig.api();
          const endTime = Date.now();
          this.addTestResult({
            name: apiConfig.name,
            method: apiConfig.method,
            url: apiConfig.url,
            success: true,
            message: `请求成功 (${endTime - startTime}ms)`,
            data: result,
            time: /* @__PURE__ */ new Date()
          });
          common_vendor.index.showToast({
            title: "测试成功",
            icon: "success"
          });
        } catch (error) {
          this.addTestResult({
            name: apiConfig.name,
            method: apiConfig.method,
            url: apiConfig.url,
            success: false,
            message: error.message || error.errMsg || "请求失败",
            data: error,
            time: /* @__PURE__ */ new Date()
          });
          common_vendor.index.showToast({
            title: "测试失败",
            icon: "none"
          });
        } finally {
          apiConfig.testing = false;
        }
      });
    },
    addTestResult(result) {
      result.id = Date.now() + Math.random();
      this.testResults.unshift(result);
      if (this.testResults.length > 20) {
        this.testResults = this.testResults.slice(0, 20);
      }
    },
    clearResults() {
      this.testResults = [];
      common_vendor.index.showToast({
        title: "已清空结果",
        icon: "success"
      });
    },
    formatTime(date) {
      if (!date)
        return "";
      const d = new Date(date);
      return `${d.getHours().toString().padStart(2, "0")}:${d.getMinutes().toString().padStart(2, "0")}:${d.getSeconds().toString().padStart(2, "0")}`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isConnected ? 1 : "",
    b: common_vendor.t($data.baseUrl),
    c: common_vendor.t($data.isConnected ? "已连接" : "未连接"),
    d: $data.isConnected ? 1 : "",
    e: !$data.isConnected ? 1 : "",
    f: common_vendor.t($data.lastTestTime || "未测试"),
    g: common_vendor.t($data.testing ? "测试中..." : "测试连接"),
    h: common_vendor.o((...args) => $options.testConnection && $options.testConnection(...args)),
    i: $data.testing,
    j: common_vendor.f($data.userApis, (api, k0, i0) => {
      return {
        a: common_vendor.t(api.name),
        b: common_vendor.t(api.method),
        c: common_vendor.t(api.url),
        d: common_vendor.t(api.testing ? "测试中" : "测试"),
        e: common_vendor.o(($event) => $options.testApi(api), api.name),
        f: api.testing,
        g: api.name
      };
    }),
    k: common_vendor.f($data.courseApis, (api, k0, i0) => {
      return {
        a: common_vendor.t(api.name),
        b: common_vendor.t(api.method),
        c: common_vendor.t(api.url),
        d: common_vendor.t(api.testing ? "测试中" : "测试"),
        e: common_vendor.o(($event) => $options.testApi(api), api.name),
        f: api.testing,
        g: api.name
      };
    }),
    l: common_vendor.f($data.practiceApis, (api, k0, i0) => {
      return {
        a: common_vendor.t(api.name),
        b: common_vendor.t(api.method),
        c: common_vendor.t(api.url),
        d: common_vendor.t(api.testing ? "测试中" : "测试"),
        e: common_vendor.o(($event) => $options.testApi(api), api.name),
        f: api.testing,
        g: api.name
      };
    }),
    m: common_vendor.o((...args) => $options.clearResults && $options.clearResults(...args)),
    n: common_vendor.f($data.testResults, (result, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(result.name),
        b: common_vendor.t(result.success ? "成功" : "失败"),
        c: common_vendor.n(result.success ? "success" : "error"),
        d: common_vendor.t($options.formatTime(result.time)),
        e: common_vendor.t(result.method),
        f: common_vendor.t(result.url),
        g: common_vendor.t(result.message),
        h: result.data
      }, result.data ? {
        i: common_vendor.t(JSON.stringify(result.data, null, 2))
      } : {}, {
        j: result.id
      });
    }),
    o: $data.testResults.length === 0
  }, $data.testResults.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-afa62ce9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/debug/api-test.js.map
