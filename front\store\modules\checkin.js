const checkin = {
  namespaced: true,
  
  state: {
    // 今日是否已打卡
    isCheckedInToday: false,
    
    // 连续打卡天数
    continuousDays: 0,
    
    // 本月打卡记录
    monthlyRecords: [],
    
    // 打卡统计
    checkinStats: {
      totalDays: 0,
      maxContinuous: 0,
      thisMonthDays: 0,
      completionRate: 0
    },
    
    // 补打卡次数
    makeupChances: {
      total: 5, // 每月总次数
      used: 0,  // 已使用次数
      remaining: 5 // 剩余次数
    },
    
    // 排行榜数据
    leaderboard: {
      weekly: [],
      monthly: [],
      total: []
    },
    
    // 当前用户排名
    userRanking: {
      weekly: { rank: 0, days: 0 },
      monthly: { rank: 0, days: 0 },
      total: { rank: 0, days: 0 }
    }
  },
  
  mutations: {
    // 设置今日打卡状态
    SET_CHECKIN_TODAY(state, isChecked) {
      state.isCheckedInToday = isChecked
    },
    
    // 设置连续打卡天数
    SET_CONTINUOUS_DAYS(state, days) {
      state.continuousDays = days
    },
    
    // 设置月度打卡记录
    SET_MONTHLY_RECORDS(state, records) {
      state.monthlyRecords = records
    },
    
    // 添加打卡记录
    ADD_CHECKIN_RECORD(state, record) {
      const existingIndex = state.monthlyRecords.findIndex(
        r => r.date === record.date
      )
      if (existingIndex > -1) {
        state.monthlyRecords[existingIndex] = record
      } else {
        state.monthlyRecords.push(record)
      }
    },
    
    // 设置打卡统计
    SET_CHECKIN_STATS(state, stats) {
      state.checkinStats = { ...state.checkinStats, ...stats }
    },
    
    // 设置补打卡次数
    SET_MAKEUP_CHANCES(state, chances) {
      state.makeupChances = { ...state.makeupChances, ...chances }
    },
    
    // 使用补打卡次数
    USE_MAKEUP_CHANCE(state) {
      if (state.makeupChances.remaining > 0) {
        state.makeupChances.used += 1
        state.makeupChances.remaining -= 1
      }
    },
    
    // 设置排行榜数据
    SET_LEADERBOARD(state, { type, data }) {
      state.leaderboard[type] = data
    },
    
    // 设置用户排名
    SET_USER_RANKING(state, { type, ranking }) {
      state.userRanking[type] = ranking
    }
  },
  
  actions: {
    // 执行打卡
    async performCheckin({ commit, rootState }, { coursesCompleted, studyDuration }) {
      try {
        const response = await uni.request({
          url: '/api/v1/checkin',
          method: 'POST',
          data: { coursesCompleted, studyDuration },
          header: {
            'Authorization': `Bearer ${rootState.user.token}`
          }
        })
        
        if (response.data.code === 200) {
          const { continuousDays, checkinRecord } = response.data.data
          
          commit('SET_CHECKIN_TODAY', true)
          commit('SET_CONTINUOUS_DAYS', continuousDays)
          commit('ADD_CHECKIN_RECORD', checkinRecord)
          
          // 更新用户状态中的连续打卡天数
          commit('user/UPDATE_CONTINUOUS_DAYS', continuousDays, { root: true })
          
          return { success: true, data: response.data.data }
        } else {
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        console.error('打卡失败:', error)
        return { success: false, message: '打卡失败，请重试' }
      }
    },
    
    // 补打卡
    async performMakeup({ commit, state, rootState }, date) {
      if (state.makeupChances.remaining <= 0) {
        return { success: false, message: '补打卡次数已用完' }
      }
      
      try {
        const response = await uni.request({
          url: '/api/v1/checkin/makeup',
          method: 'POST',
          data: { date },
          header: {
            'Authorization': `Bearer ${rootState.user.token}`
          }
        })
        
        if (response.data.code === 200) {
          const { checkinRecord } = response.data.data
          
          commit('USE_MAKEUP_CHANCE')
          commit('ADD_CHECKIN_RECORD', { ...checkinRecord, isMakeup: true })
          
          return { success: true, data: response.data.data }
        } else {
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        console.error('补打卡失败:', error)
        return { success: false, message: '补打卡失败，请重试' }
      }
    },
    
    // 加载打卡记录
    async loadCheckinRecords({ commit, rootState }, { year, month }) {
      try {
        const response = await uni.request({
          url: '/api/v1/checkin/records',
          method: 'GET',
          data: { year, month },
          header: {
            'Authorization': `Bearer ${rootState.user.token}`
          }
        })
        
        if (response.data.code === 200) {
          commit('SET_MONTHLY_RECORDS', response.data.data.records)
          
          // 检查今日是否已打卡
          const today = new Date().toISOString().split('T')[0]
          const todayRecord = response.data.data.records.find(r => r.date === today)
          commit('SET_CHECKIN_TODAY', !!todayRecord)
          
          return { success: true, data: response.data.data }
        } else {
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        console.error('加载打卡记录失败:', error)
        return { success: false, message: '加载失败，请重试' }
      }
    },
    
    // 加载打卡统计
    async loadCheckinStats({ commit, rootState }) {
      try {
        const response = await uni.request({
          url: '/api/v1/checkin/statistics',
          method: 'GET',
          header: {
            'Authorization': `Bearer ${rootState.user.token}`
          }
        })
        
        if (response.data.code === 200) {
          const { stats, makeupChances, continuousDays } = response.data.data
          
          commit('SET_CHECKIN_STATS', stats)
          commit('SET_MAKEUP_CHANCES', makeupChances)
          commit('SET_CONTINUOUS_DAYS', continuousDays)
          
          return { success: true, data: response.data.data }
        }
      } catch (error) {
        console.error('加载打卡统计失败:', error)
      }
    },
    
    // 加载排行榜
    async loadLeaderboard({ commit, rootState }, { type = 'monthly', limit = 50 }) {
      try {
        const response = await uni.request({
          url: '/api/v1/leaderboard',
          method: 'GET',
          data: { type, limit },
          header: {
            'Authorization': `Bearer ${rootState.user.token}`
          }
        })
        
        if (response.data.code === 200) {
          const { leaderboard, userRanking } = response.data.data
          
          commit('SET_LEADERBOARD', { type, data: leaderboard })
          commit('SET_USER_RANKING', { type, ranking: userRanking })
          
          return { success: true, data: response.data.data }
        } else {
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        console.error('加载排行榜失败:', error)
        return { success: false, message: '加载失败，请重试' }
      }
    }
  },
  
  getters: {
    // 获取指定日期的打卡记录
    getRecordByDate: (state) => (date) => {
      return state.monthlyRecords.find(record => record.date === date)
    },
    
    // 检查指定日期是否已打卡
    isCheckedOnDate: (state) => (date) => {
      return state.monthlyRecords.some(record => record.date === date)
    },
    
    // 检查指定日期是否为补打卡
    isMakeupOnDate: (state) => (date) => {
      const record = state.monthlyRecords.find(record => record.date === date)
      return record ? record.isMakeup : false
    },
    
    // 获取本月打卡天数
    thisMonthCheckinDays: (state) => {
      return state.monthlyRecords.filter(record => !record.isMakeup).length
    },
    
    // 获取本月补打卡天数
    thisMonthMakeupDays: (state) => {
      return state.monthlyRecords.filter(record => record.isMakeup).length
    },
    
    // 获取本月完成率
    thisMonthCompletionRate: (state, getters) => {
      const today = new Date()
      const daysInMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate()
      const currentDay = today.getDate()
      const checkedDays = getters.thisMonthCheckinDays
      
      return currentDay > 0 ? Math.round((checkedDays / currentDay) * 100) : 0
    },
    
    // 获取排行榜前三名
    getTopThree: (state) => (type = 'monthly') => {
      return state.leaderboard[type].slice(0, 3)
    },
    
    // 获取用户在排行榜中的位置
    getUserRankInList: (state) => (type = 'monthly') => {
      const list = state.leaderboard[type]
      const userId = state.userRanking[type].userId
      return list.findIndex(user => user.id === userId)
    },
    
    // 距离下一个里程碑的天数
    daysToNextMilestone: (state) => {
      const milestones = [7, 15, 30, 50, 100, 200, 365]
      const current = state.continuousDays
      const next = milestones.find(m => m > current)
      return next ? next - current : 0
    }
  }
}

export default checkin
