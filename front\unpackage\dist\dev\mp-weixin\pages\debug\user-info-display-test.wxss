/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义项目颜色 */
/* 卡片样式 */
/* 按钮样式 */
/* 状态栏高度 */
.user-info-test-container.data-v-a7929c82 {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
}
.test-header.data-v-a7929c82 {
  text-align: center;
  margin-bottom: 40rpx;
}
.test-header .test-title.data-v-a7929c82 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-a7929c82 {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.test-section .section-title.data-v-a7929c82 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 16rpx;
}
.auth-status .status-item.data-v-a7929c82, .auth-status .info-item.data-v-a7929c82, .user-info-display .status-item.data-v-a7929c82, .user-info-display .info-item.data-v-a7929c82, .storage-info .status-item.data-v-a7929c82, .storage-info .info-item.data-v-a7929c82, .global-state .status-item.data-v-a7929c82, .global-state .info-item.data-v-a7929c82 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.auth-status .status-item.data-v-a7929c82:last-child, .auth-status .info-item.data-v-a7929c82:last-child, .user-info-display .status-item.data-v-a7929c82:last-child, .user-info-display .info-item.data-v-a7929c82:last-child, .storage-info .status-item.data-v-a7929c82:last-child, .storage-info .info-item.data-v-a7929c82:last-child, .global-state .status-item.data-v-a7929c82:last-child, .global-state .info-item.data-v-a7929c82:last-child {
  border-bottom: none;
}
.auth-status .status-item .status-label.data-v-a7929c82, .auth-status .status-item .info-label.data-v-a7929c82, .auth-status .info-item .status-label.data-v-a7929c82, .auth-status .info-item .info-label.data-v-a7929c82, .user-info-display .status-item .status-label.data-v-a7929c82, .user-info-display .status-item .info-label.data-v-a7929c82, .user-info-display .info-item .status-label.data-v-a7929c82, .user-info-display .info-item .info-label.data-v-a7929c82, .storage-info .status-item .status-label.data-v-a7929c82, .storage-info .status-item .info-label.data-v-a7929c82, .storage-info .info-item .status-label.data-v-a7929c82, .storage-info .info-item .info-label.data-v-a7929c82, .global-state .status-item .status-label.data-v-a7929c82, .global-state .status-item .info-label.data-v-a7929c82, .global-state .info-item .status-label.data-v-a7929c82, .global-state .info-item .info-label.data-v-a7929c82 {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
  flex-shrink: 0;
}
.auth-status .status-item .status-value.data-v-a7929c82, .auth-status .status-item .info-value.data-v-a7929c82, .auth-status .info-item .status-value.data-v-a7929c82, .auth-status .info-item .info-value.data-v-a7929c82, .user-info-display .status-item .status-value.data-v-a7929c82, .user-info-display .status-item .info-value.data-v-a7929c82, .user-info-display .info-item .status-value.data-v-a7929c82, .user-info-display .info-item .info-value.data-v-a7929c82, .storage-info .status-item .status-value.data-v-a7929c82, .storage-info .status-item .info-value.data-v-a7929c82, .storage-info .info-item .status-value.data-v-a7929c82, .storage-info .info-item .info-value.data-v-a7929c82, .global-state .status-item .status-value.data-v-a7929c82, .global-state .status-item .info-value.data-v-a7929c82, .global-state .info-item .status-value.data-v-a7929c82, .global-state .info-item .info-value.data-v-a7929c82 {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}
.auth-status .status-item .status-value.success.data-v-a7929c82, .auth-status .status-item .info-value.success.data-v-a7929c82, .auth-status .info-item .status-value.success.data-v-a7929c82, .auth-status .info-item .info-value.success.data-v-a7929c82, .user-info-display .status-item .status-value.success.data-v-a7929c82, .user-info-display .status-item .info-value.success.data-v-a7929c82, .user-info-display .info-item .status-value.success.data-v-a7929c82, .user-info-display .info-item .info-value.success.data-v-a7929c82, .storage-info .status-item .status-value.success.data-v-a7929c82, .storage-info .status-item .info-value.success.data-v-a7929c82, .storage-info .info-item .status-value.success.data-v-a7929c82, .storage-info .info-item .info-value.success.data-v-a7929c82, .global-state .status-item .status-value.success.data-v-a7929c82, .global-state .status-item .info-value.success.data-v-a7929c82, .global-state .info-item .status-value.success.data-v-a7929c82, .global-state .info-item .info-value.success.data-v-a7929c82 {
  color: #52c41a;
}
.auth-status .status-item .status-value.error.data-v-a7929c82, .auth-status .status-item .info-value.error.data-v-a7929c82, .auth-status .info-item .status-value.error.data-v-a7929c82, .auth-status .info-item .info-value.error.data-v-a7929c82, .user-info-display .status-item .status-value.error.data-v-a7929c82, .user-info-display .status-item .info-value.error.data-v-a7929c82, .user-info-display .info-item .status-value.error.data-v-a7929c82, .user-info-display .info-item .info-value.error.data-v-a7929c82, .storage-info .status-item .status-value.error.data-v-a7929c82, .storage-info .status-item .info-value.error.data-v-a7929c82, .storage-info .info-item .status-value.error.data-v-a7929c82, .storage-info .info-item .info-value.error.data-v-a7929c82, .global-state .status-item .status-value.error.data-v-a7929c82, .global-state .status-item .info-value.error.data-v-a7929c82, .global-state .info-item .status-value.error.data-v-a7929c82, .global-state .info-item .info-value.error.data-v-a7929c82 {
  color: #ff4d4f;
}
.page-display-test .display-item.data-v-a7929c82 {
  margin-bottom: 24rpx;
}
.page-display-test .display-item .display-label.data-v-a7929c82 {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}
.page-display-test .display-item .display-value.data-v-a7929c82 {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.page-display-test .display-item .profile-style.data-v-a7929c82 {
  display: flex;
  align-items: center;
}
.page-display-test .display-item .profile-style .avatar.data-v-a7929c82 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.page-display-test .display-item .profile-style .name.data-v-a7929c82 {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.test-buttons.data-v-a7929c82 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.test-buttons .test-btn.data-v-a7929c82 {
  height: 80rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}
.test-buttons .test-btn.primary.data-v-a7929c82 {
  background: #1890ff;
}
.test-buttons .test-btn.success.data-v-a7929c82 {
  background: #52c41a;
}
.test-buttons .test-btn.warning.data-v-a7929c82 {
  background: #faad14;
}
.test-buttons .test-btn.error.data-v-a7929c82 {
  background: #ff4d4f;
}
.test-buttons .test-btn.data-v-a7929c82:active {
  opacity: 0.8;
}
.log-container.data-v-a7929c82 {
  height: 300rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 16rpx;
}
.log-container .log-item.data-v-a7929c82 {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 24rpx;
}
.log-container .log-item .log-time.data-v-a7929c82 {
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
}
.log-container .log-item .log-message.data-v-a7929c82 {
  color: #333;
  word-break: break-all;
}
.log-container .log-item .log-message.success.data-v-a7929c82 {
  color: #52c41a;
}
.log-container .log-item .log-message.error.data-v-a7929c82 {
  color: #ff4d4f;
}
.log-container .log-item .log-message.warning.data-v-a7929c82 {
  color: #faad14;
}
.log-container .log-item .log-message.info.data-v-a7929c82 {
  color: #1890ff;
}