{"version": 3, "file": "index.js", "sources": ["utils/index.js"], "sourcesContent": ["// 工具函数集合\n\n/**\n * 时间格式化\n */\nexport const formatTime = (timestamp, format = 'YYYY-MM-DD HH:mm:ss') => {\n  if (!timestamp) return ''\n  \n  const date = new Date(timestamp)\n  const year = date.getFullYear()\n  const month = (date.getMonth() + 1).toString().padStart(2, '0')\n  const day = date.getDate().toString().padStart(2, '0')\n  const hour = date.getHours().toString().padStart(2, '0')\n  const minute = date.getMinutes().toString().padStart(2, '0')\n  const second = date.getSeconds().toString().padStart(2, '0')\n  \n  return format\n    .replace('YYYY', year)\n    .replace('MM', month)\n    .replace('DD', day)\n    .replace('HH', hour)\n    .replace('mm', minute)\n    .replace('ss', second)\n}\n\n/**\n * 相对时间格式化\n */\nexport const formatRelativeTime = (timestamp) => {\n  if (!timestamp) return ''\n  \n  const now = new Date()\n  const date = new Date(timestamp)\n  const diff = now - date\n  \n  const minute = 60 * 1000\n  const hour = 60 * minute\n  const day = 24 * hour\n  const week = 7 * day\n  const month = 30 * day\n  \n  if (diff < minute) {\n    return '刚刚'\n  } else if (diff < hour) {\n    return `${Math.floor(diff / minute)}分钟前`\n  } else if (diff < day) {\n    return `${Math.floor(diff / hour)}小时前`\n  } else if (diff < week) {\n    return `${Math.floor(diff / day)}天前`\n  } else if (diff < month) {\n    return `${Math.floor(diff / week)}周前`\n  } else {\n    return formatTime(timestamp, 'MM-DD')\n  }\n}\n\n/**\n * 防抖函数\n */\nexport const debounce = (func, wait, immediate = false) => {\n  let timeout\n  return function executedFunction(...args) {\n    const later = () => {\n      timeout = null\n      if (!immediate) func(...args)\n    }\n    const callNow = immediate && !timeout\n    clearTimeout(timeout)\n    timeout = setTimeout(later, wait)\n    if (callNow) func(...args)\n  }\n}\n\n/**\n * 节流函数\n */\nexport const throttle = (func, limit) => {\n  let inThrottle\n  return function() {\n    const args = arguments\n    const context = this\n    if (!inThrottle) {\n      func.apply(context, args)\n      inThrottle = true\n      setTimeout(() => inThrottle = false, limit)\n    }\n  }\n}\n\n/**\n * 深拷贝\n */\nexport const deepClone = (obj) => {\n  if (obj === null || typeof obj !== 'object') return obj\n  if (obj instanceof Date) return new Date(obj.getTime())\n  if (obj instanceof Array) return obj.map(item => deepClone(item))\n  if (typeof obj === 'object') {\n    const clonedObj = {}\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n}\n\n/**\n * 存储管理\n */\nexport const storage = {\n  set(key, value) {\n    try {\n      uni.setStorageSync(key, JSON.stringify(value))\n      return true\n    } catch (e) {\n      console.error('存储失败:', e)\n      return false\n    }\n  },\n  \n  get(key, defaultValue = null) {\n    try {\n      const value = uni.getStorageSync(key)\n      return value ? JSON.parse(value) : defaultValue\n    } catch (e) {\n      console.error('读取存储失败:', e)\n      return defaultValue\n    }\n  },\n  \n  remove(key) {\n    try {\n      uni.removeStorageSync(key)\n      return true\n    } catch (e) {\n      console.error('删除存储失败:', e)\n      return false\n    }\n  },\n  \n  clear() {\n    try {\n      uni.clearStorageSync()\n      return true\n    } catch (e) {\n      console.error('清空存储失败:', e)\n      return false\n    }\n  }\n}\n\n/**\n * 数字格式化\n */\nexport const formatNumber = (num, precision = 2) => {\n  if (isNaN(num)) return '0'\n  \n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(precision) + 'M'\n  } else if (num >= 1000) {\n    return (num / 1000).toFixed(precision) + 'K'\n  } else {\n    return num.toString()\n  }\n}\n\n/**\n * 文件大小格式化\n */\nexport const formatFileSize = (bytes) => {\n  if (bytes === 0) return '0 B'\n  \n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n/**\n * URL参数解析\n */\nexport const parseQuery = (url) => {\n  const query = {}\n  const queryString = url.split('?')[1]\n  \n  if (queryString) {\n    queryString.split('&').forEach(param => {\n      const [key, value] = param.split('=')\n      query[decodeURIComponent(key)] = decodeURIComponent(value || '')\n    })\n  }\n  \n  return query\n}\n\n/**\n * 生成随机字符串\n */\nexport const randomString = (length = 8) => {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n  let result = ''\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length))\n  }\n  return result\n}\n\n/**\n * 验证手机号\n */\nexport const validatePhone = (phone) => {\n  const reg = /^1[3-9]\\d{9}$/\n  return reg.test(phone)\n}\n\n/**\n * 验证邮箱\n */\nexport const validateEmail = (email) => {\n  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/\n  return reg.test(email)\n}\n\n/**\n * 获取系统信息\n */\nexport const getSystemInfo = () => {\n  try {\n    return uni.getSystemInfoSync()\n  } catch (e) {\n    console.error('获取系统信息失败:', e)\n    return {}\n  }\n}\n\n/**\n * 权限检查\n */\nexport const checkPermission = (scope) => {\n  return new Promise((resolve, reject) => {\n    uni.getSetting({\n      success: (res) => {\n        if (res.authSetting[scope]) {\n          resolve(true)\n        } else {\n          uni.authorize({\n            scope,\n            success: () => resolve(true),\n            fail: () => reject(false)\n          })\n        }\n      },\n      fail: () => reject(false)\n    })\n  })\n}\n\n/**\n * 图片压缩\n */\nexport const compressImage = (src, quality = 0.8) => {\n  return new Promise((resolve, reject) => {\n    uni.compressImage({\n      src,\n      quality,\n      success: resolve,\n      fail: reject\n    })\n  })\n}\n\n/**\n * 选择图片\n */\nexport const chooseImage = (options = {}) => {\n  const defaultOptions = {\n    count: 1,\n    sizeType: ['compressed'],\n    sourceType: ['album', 'camera']\n  }\n  \n  return new Promise((resolve, reject) => {\n    uni.chooseImage({\n      ...defaultOptions,\n      ...options,\n      success: resolve,\n      fail: reject\n    })\n  })\n}\n\n/**\n * 预览图片\n */\nexport const previewImage = (urls, current = 0) => {\n  uni.previewImage({\n    urls: Array.isArray(urls) ? urls : [urls],\n    current: typeof current === 'number' ? urls[current] : current\n  })\n}\n\n/**\n * 复制到剪贴板\n */\nexport const copyToClipboard = (text) => {\n  return new Promise((resolve, reject) => {\n    uni.setClipboardData({\n      data: text,\n      success: () => {\n        uni.showToast({\n          title: '复制成功',\n          icon: 'success'\n        })\n        resolve(true)\n      },\n      fail: reject\n    })\n  })\n}\n\n/**\n * 震动反馈\n */\nexport const vibrate = (type = 'short') => {\n  if (type === 'long') {\n    uni.vibrateLong()\n  } else {\n    uni.vibrateShort()\n  }\n}\n\n/**\n * 页面跳转封装 - 智能识别tabBar页面\n */\nexport const navigate = {\n  // tabBar页面列表\n  tabBarPages: [\n    'pages/index/index',\n    'pages/course/list',\n    'pages/checkin/checkin',\n    'pages/leaderboard/leaderboard',\n    'pages/profile/profile'\n  ],\n\n  // 智能跳转 - 自动识别tabBar页面\n  to: (url, params = {}) => {\n    const cleanUrl = url.startsWith('/') ? url.substring(1) : url\n    const isTabBarPage = navigate.tabBarPages.includes(cleanUrl)\n\n    if (isTabBarPage) {\n      // tabBar页面使用switchTab，不支持参数\n      if (Object.keys(params).length > 0) {\n        console.warn('⚠️ tabBar页面不支持URL参数，参数将被忽略:', params)\n        // 可以将参数存储到本地存储\n        uni.setStorageSync('pageParams', {\n          url: cleanUrl,\n          params,\n          timestamp: Date.now()\n        })\n      }\n\n      uni.switchTab({\n        url: `/${cleanUrl}`,\n        success: () => {\n          console.log('✅ 成功跳转到tabBar页面:', cleanUrl)\n        },\n        fail: (error) => {\n          console.error('❌ 跳转tabBar页面失败:', error)\n        }\n      })\n    } else {\n      // 普通页面使用navigateTo\n      const query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')\n      const fullUrl = query ? `/${cleanUrl}?${query}` : `/${cleanUrl}`\n\n      uni.navigateTo({\n        url: fullUrl,\n        success: () => {\n          console.log('✅ 成功跳转到普通页面:', fullUrl)\n        },\n        fail: (error) => {\n          console.error('❌ 跳转普通页面失败:', error)\n        }\n      })\n    }\n  },\n\n  back: (delta = 1) => {\n    uni.navigateBack({ delta })\n  },\n\n  redirect: (url, params = {}) => {\n    const cleanUrl = url.startsWith('/') ? url.substring(1) : url\n    const query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')\n    const fullUrl = query ? `/${cleanUrl}?${query}` : `/${cleanUrl}`\n    uni.redirectTo({ url: fullUrl })\n  },\n\n  reLaunch: (url, params = {}) => {\n    const cleanUrl = url.startsWith('/') ? url.substring(1) : url\n    const query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')\n    const fullUrl = query ? `/${cleanUrl}?${query}` : `/${cleanUrl}`\n    uni.reLaunch({ url: fullUrl })\n  },\n\n  // 专门的tabBar跳转方法\n  switchTab: (url, params = {}) => {\n    const cleanUrl = url.startsWith('/') ? url.substring(1) : url\n\n    if (Object.keys(params).length > 0) {\n      // 将参数存储到本地存储\n      uni.setStorageSync('pageParams', {\n        url: cleanUrl,\n        params,\n        timestamp: Date.now()\n      })\n    }\n\n    uni.switchTab({\n      url: `/${cleanUrl}`,\n      success: () => {\n        console.log('✅ 成功跳转到tabBar页面:', cleanUrl)\n      },\n      fail: (error) => {\n        console.error('❌ 跳转tabBar页面失败:', error)\n      }\n    })\n  },\n  \n  switchTab: (url) => {\n    uni.switchTab({ url })\n  }\n}\n"], "names": ["uni"], "mappings": ";;AAgVY,MAAC,WAAW;AAAA;AAAA,EAEtB,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,IAAI,CAAC,KAAK,SAAS,OAAO;AACxB,UAAM,WAAW,IAAI,WAAW,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI;AAC1D,UAAM,eAAe,SAAS,YAAY,SAAS,QAAQ;AAE3D,QAAI,cAAc;AAEhB,UAAI,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG;AAClCA,sBAAAA,6CAAa,+BAA+B,MAAM;AAElDA,sBAAG,MAAC,eAAe,cAAc;AAAA,UAC/B,KAAK;AAAA,UACL;AAAA,UACA,WAAW,KAAK,IAAK;AAAA,QAC/B,CAAS;AAAA,MACF;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK,IAAI,QAAQ;AAAA,QACjB,SAAS,MAAM;AACbA,wBAAAA,MAAA,MAAA,OAAA,yBAAY,oBAAoB,QAAQ;AAAA,QACzC;AAAA,QACD,MAAM,CAAC,UAAU;AACfA,wBAAAA,8CAAc,mBAAmB,KAAK;AAAA,QACvC;AAAA,MACT,CAAO;AAAA,IACP,OAAW;AAEL,YAAM,QAAQ,OAAO,KAAK,MAAM,EAAE,IAAI,SAAO,GAAG,GAAG,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG;AAClG,YAAM,UAAU,QAAQ,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,QAAQ;AAE9DA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,SAAS,MAAM;AACbA,wBAAAA,MAAY,MAAA,OAAA,yBAAA,gBAAgB,OAAO;AAAA,QACpC;AAAA,QACD,MAAM,CAAC,UAAU;AACfA,wBAAAA,MAAA,MAAA,SAAA,yBAAc,eAAe,KAAK;AAAA,QACnC;AAAA,MACT,CAAO;AAAA,IACF;AAAA,EACF;AAAA,EAED,MAAM,CAAC,QAAQ,MAAM;AACnBA,wBAAI,aAAa,EAAE,OAAO;AAAA,EAC3B;AAAA,EAED,UAAU,CAAC,KAAK,SAAS,OAAO;AAC9B,UAAM,WAAW,IAAI,WAAW,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI;AAC1D,UAAM,QAAQ,OAAO,KAAK,MAAM,EAAE,IAAI,SAAO,GAAG,GAAG,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG;AAClG,UAAM,UAAU,QAAQ,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,QAAQ;AAC9DA,kBAAAA,MAAI,WAAW,EAAE,KAAK,QAAO,CAAE;AAAA,EAChC;AAAA,EAED,UAAU,CAAC,KAAK,SAAS,OAAO;AAC9B,UAAM,WAAW,IAAI,WAAW,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI;AAC1D,UAAM,QAAQ,OAAO,KAAK,MAAM,EAAE,IAAI,SAAO,GAAG,GAAG,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG;AAClG,UAAM,UAAU,QAAQ,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,QAAQ;AAC9DA,kBAAAA,MAAI,SAAS,EAAE,KAAK,QAAO,CAAE;AAAA,EAC9B;AAAA;AAAA,EAGD,WAAW,CAAC,KAAK,SAAS,OAAO;AAC/B,UAAM,WAAW,IAAI,WAAW,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI;AAE1D,QAAI,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG;AAElCA,oBAAG,MAAC,eAAe,cAAc;AAAA,QAC/B,KAAK;AAAA,QACL;AAAA,QACA,WAAW,KAAK,IAAK;AAAA,MAC7B,CAAO;AAAA,IACF;AAEDA,kBAAAA,MAAI,UAAU;AAAA,MACZ,KAAK,IAAI,QAAQ;AAAA,MACjB,SAAS,MAAM;AACbA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,oBAAoB,QAAQ;AAAA,MACzC;AAAA,MACD,MAAM,CAAC,UAAU;AACfA,sBAAAA,MAAc,MAAA,SAAA,yBAAA,mBAAmB,KAAK;AAAA,MACvC;AAAA,IACP,CAAK;AAAA,EACF;AAAA,EAED,WAAW,CAAC,QAAQ;AAClBA,wBAAI,UAAU,EAAE,KAAK;AAAA,EACtB;AACH;;"}