"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "MpPageWrapper",
  props: {
    // 页面标题
    title: {
      type: String,
      default: "口语便利店"
    },
    // 是否显示导航栏
    showNavBar: {
      type: Boolean,
      default: true
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      default: true
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 加载文字
    loadingText: {
      type: String,
      default: "加载中..."
    },
    // 错误状态
    hasError: {
      type: Boolean,
      default: false
    },
    // 错误信息
    errorMessage: {
      type: String,
      default: "加载失败"
    }
  },
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 0
    };
  },
  mounted() {
    this.initSystemInfo();
  },
  methods: {
    // 初始化系统信息
    initSystemInfo() {
      try {
        const app = getApp();
        if (app && app.globalData) {
          this.statusBarHeight = app.globalData.statusBarHeight || 0;
          this.navBarHeight = app.globalData.navBarHeight || 44;
        } else {
          const systemInfo = common_vendor.index.getSystemInfoSync();
          this.statusBarHeight = systemInfo.statusBarHeight || 0;
          this.navBarHeight = (systemInfo.statusBarHeight || 0) + 44;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/mp-page-wrapper/mp-page-wrapper.vue:108", "获取系统信息失败:", error);
        this.statusBarHeight = 0;
        this.navBarHeight = 44;
      }
    },
    // 处理返回
    handleBack() {
      try {
        const pages = getCurrentPages();
        if (pages && pages.length > 1) {
          common_vendor.index.navigateBack();
        } else {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("warn", "at components/mp-page-wrapper/mp-page-wrapper.vue:126", "处理返回失败:", error);
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    },
    // 处理重试
    handleRetry() {
      this.$emit("retry");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.showNavBar
  }, $props.showNavBar ? common_vendor.e({
    b: $props.showBack
  }, $props.showBack ? {
    c: common_vendor.o((...args) => $options.handleBack && $options.handleBack(...args))
  } : {}, {
    d: common_vendor.t($props.title),
    e: $data.statusBarHeight + "px"
  }) : {}, {
    f: $props.loading
  }, $props.loading ? {
    g: common_vendor.t($props.loadingText)
  } : $props.hasError ? {
    i: common_vendor.t($props.errorMessage),
    j: common_vendor.o((...args) => $options.handleRetry && $options.handleRetry(...args))
  } : {}, {
    h: $props.hasError,
    k: $props.showNavBar ? $data.navBarHeight + "px" : "0"
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4fd45c90"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/mp-page-wrapper/mp-page-wrapper.js.map
